{"name": "next-js-boilerplate", "version": "3.25.0", "scripts": {"dev": "next dev", "build": "next build", "build:test": "cross-env NODE_ENV=test next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next .swc out coverage", "lint": "next lint", "format": "next lint --fix && prettier '**/*.{json,yaml}' --write --ignore-path .gitignore", "check-types": "tsc --noEmit --pretty", "test": "jest", "commit": "cz", "prepare": "husky install"}, "dependencies": {"@blocknote/core": "^0.30.0", "@blocknote/mantine": "^0.30.0", "@blocknote/react": "^0.30.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@hookform/resolvers": "^3.3.2", "@neysf/qiyu-web-sdk": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^4.35.3", "@tinymce/tinymce-react": "^3.14.0", "@tiptap/core": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@xrenders/schema-builder": "1.0.0-alpha.13", "antd-mobile": "https://pkg.pr.new/ant-design/ant-design-mobile/antd-mobile@6872", "axios": "^1.5.0", "bluebird": "^3.7.2", "canvas": "^2.11.2", "chart.js": "^4.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "compare-versions": "^6.1.1", "compressorjs": "^1.2.1", "date-fns": "^2.30.0", "esdk-obs-browserjs": "^3.23.5", "esdk-obs-nodejs": "^3.23.11", "eventemitter3": "^5.0.1", "fast-deep-equal": "^3.1.3", "form-render-mobile": "^1.0.14", "framer-motion": "^12.4.7", "gray-matter": "^4.0.3", "html2canvas": "^1.4.1", "immer": "^10.0.2", "jotai": "^2.11.0", "js-cookie": "^3.0.5", "konva": "^9.2.1", "less": "^4.2.0", "lodash-es": "^4.17.21", "lucide-react": "^0.471.1", "motion": "11.17.0", "nanoid": "^5.0.3", "next": "15.3.4", "nuqs": "^2.4.1", "plyr-react": "^5.3.0", "prosemirror-model": "1.25.1", "prosemirror-view": "^1.38.0", "puppeteer": "^21.7.0", "qrcode": "^1.5.3", "rc-slider": "^10.2.1", "rc-tooltip": "^5.3.1", "rc-upload": "^4.4.0", "rc-util": "^5.37.0", "react": "19.1.0", "react-arborist": "^3.4.0", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.54.2", "react-icons": "^4.11.0", "react-modern-drawer": "^1.2.2", "react-player": "^2.16.0", "react-qr-barcode-scanner": "^2.0.0", "react-responsive-carousel": "^3.2.23", "react-signature-canvas": "^1.0.6", "recharts": "^2.15.0", "remark": "^14.0.3", "remark-html": "^15.0.2", "remark-parse": "^10.0.2", "sharp": "^0.33.5", "styled-jsx": "^5.1.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tinymce": "^5.10.7", "unified": "^10.1.2", "use-immer": "^0.9.0", "weixin-js-sdk": "^1.6.0", "zod": "^3.24.2", "zustand": "^4.4.1"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@commitlint/cli": "^18.2.0", "@commitlint/config-conventional": "^18.1.0", "@commitlint/cz-commitlint": "^18.2.0", "@next/bundle-analyzer": "15.3.4", "@svgr/webpack": "^8.0.1", "@tailwindcss/forms": "^0.5.6", "@tanstack/react-query-devtools": "^4.29.3", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@types/bluebird": "^3.5.38", "@types/jest": "^29.5.6", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.11", "@types/node": "^20.8.9", "@types/qrcode": "^1.5.2", "@types/react": "19.1.8", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "19.1.6", "@types/react-signature-canvas": "^1.0.2", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "autoprefixer": "^10.4.16", "commitizen": "^4.3.0", "cross-env": "^7.0.3", "cross-spawn": "^7.0.3", "css-loader": "^6.8.1", "cssnano": "^6.0.1", "dotenv-cli": "^7.3.0", "encoding": "^0.1.13", "eslint": "^8.52.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jest-dom": "^5.1.0", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.13.0", "eslint-plugin-testing-library": "^6.1.0", "eslint-plugin-unused-imports": "^3.0.0", "http-server": "^14.1.1", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "less-loader": "^11.1.3", "lint-staged": "^15.0.2", "next-build-id": "^3.0.0", "next-router-mock": "^0.8.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "postcss-px-to-viewport": "^1.1.1", "postcss-px-to-viewport-8-plugin": "^1.2.3", "prettier": "^3.0.3", "prettier-eslint": "^16.3.0", "prettier-plugin-tailwindcss": "^0.1.13", "rimraf": "^5.0.5", "sass": "^1.66.1", "style-loader": "^3.3.3", "tailwindcss": "^3.3.5", "tsx": "^3.14.0", "typescript": "^5.2.2"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "overrides": {"prosemirror-model": "1.25.1"}, "pnpm": {"overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}}