**小牛错题本第三方共享信息清单**

为保障小牛错题本App相关功能的实现与应用安全稳定的运行，我们可能会接入由第三方提供的软件开发包（SDK）实现相关目的。

我们会对合作方获取信息的软件工具开发包（SDK）进行严格的安全监测，以保护数据安全。

我们对接入的相关第三方SDK在目录中列明。

请注意，第三方SDK可能因为其版本升级、策略调整等原因导致数据处理类型存在一定变化，请以其公示的官方说明为准。

**第三方****SDK****列表如下：**

1.微信SDK（腾讯开发平台sdk,微信分享SDK,微信支付SDK）

使用目的：帮助用户在应用内使用微信分享，微信支付

收集数据类型：WLAN状态信息、定位信息、终端设备唯一标识符

官方说明：https://developers.weixin.qq.com/doc/oplatform/Mobile\_App/Access\_Guide/Android.html

2.友盟umengSDK（含啄木鸟SDK组件）

使用目的：统计分析及性能监控

收集数据类型：

（1）移动统计U-APP 我们的产品集成友盟+SDK，友盟+SDK需要收集您的设备Mac地址、唯一设备识别码（IMEI/android ID/IDFA/OPENUDID/GUID、ICCID、SIM 卡 IMSI 信息、SN个人信息）以提供统计分析服务，并通过地理位置校准报表数据准确性，提供基础反作弊能力

（2）应用性能监控U-APM 友盟+SDK采集设备标识符(IMEI/Mac/android ID/IDFA/OPENUDID/GUID、SIM 卡 IMSI 信息)，用于唯一标识设备，以便提供设备应用性能监控服务；通过采集位置信息提供反作弊服务，剔除作弊设备，排查应用性能崩溃原因

官方隐私政策：https://www.umeng.com/page/policy

3.Zxing二维码SDK

使用目的：提供应用内二维码识别和生成功能。

收集数据类型：网络状态信息。

官网链接：https://github.com/zxing/zxing

4.华为更新

使用目的：提供华为系统内应用自动升级服务。

收集数据类型：读写外部存储。

官网链接：https://www.huawei.com/cn/

5.友盟组件化基础库

使用目的：用户行为分析，辅助统计新增、活跃用户等数据。

收集数据类型：设备ID信息（IMEI/IMSI）、Mac地址、位置信息、读写外部存储。

官网链接：https://www.umeng.com/

6.设备标识生成库

使用目的：提供生成唯一的设备标识。

收集数据类型：设备ID信息（IMEI、IMSI、Android ID）、网络状态信息、读写外部存储。

官网链接：https://open.alipay.com/

7.华为云OBS服务 SDK

使用目的：通过华为云提供的对象存储服务（OBS）API，为用户提供安全、高可靠的云存储服务

收集数据类型：设备型号、操作系统版本、设备设置、设备环境等软件特征信息。

官网隐私政策：https://www.huaweicloud.com/declaration/sa\_prp.html

8.卓信ID SDK

涉及信息：设备弱特征（不具备唯一性和稳定性的特征），含设备信息(设备制造商、设备型号、设备系统信息)、设备网络信息(设备联网方式和状态信息)、设备环境信息(屏幕亮度、电池状态及所在国家、设备应用信息（设备常见应用的版本等安装信息））

使用目的：提供设备标识与安全风控服务

合作方主体：中国信息通信研究院

收集方式：SDK收集

合作方官网链接：https://zxid.caict.ac.cn

合作方隐私政策链接：https://zxid.caict.ac.cn/privacy

官方隐私政策：https://docs.getui.com/privacy/

9.易盾应用加固SDK

使用目的：对JAR/AAR包加固，提高安全性。

收集信息：设备信息：安卓系统版本，CPU架构应用信息：本应用包名和签名

官方隐私政策说明：https://dun.163.com/clause/privacy

10.移动安全联盟SDK

使用目的：调用设备标识符

收集信息：设备制造商、设备型号、设备品牌；设备网络运营商名称、App包名及签名信息，或在对应应用商店的APPID

官方隐私政策说明：http://www.msa-alliance.cn/col.jsp?id=122

11.OPPO推送

使用目的：推送

收集数据类型：IMEI、文件读写、任务信息

官方隐私政策：http://open.oppomobile.com/wiki/doc#id=10196

12.小米推送

使用目的：推送

收集数据类型：设备型号、网络类型、设备存储、设备运行进程信息、应用版本、SDK 版本，以及您国家或地区的设置会帮助我们为您提供更好的推送服务和体验，同时，我们会使用您的设置信息，包括是否屏蔽通知栏、是否设置锁屏弹出消息，来为您提供服务。我们还会收集使用推送服务的应用信息（包名、版本号、应用名称、首次安装时间、上次完成更新的时间），以确保您在卸载应用后不会再接收到和该应用相关的通知。

官方隐私政策：https://dev.mi.com/console/doc/detail?pld=1822

13.中国电信一键登录

使用目的：根据用户选择，为用户提供一键登录功能

收集信息：网络状态权限、网络IP地址、IMSI、网络类型

官方隐私政策说明：https://e.189.cn/sdk/agreement/detail.do?hidetop=true

14.个推推送

使用目的：推送，用于创建内部用户编号以帮助平台更准确的推送信息，避免重复投递信息，对用户造成影响

收集数据类型：为了保障您的用户收到消息的及时性，在您使用消息推送服务时，需要收集您的信息，具体信息和目的如下：

（1）设备信息：设备识别码（IMEI、MAC、AndroidID、OAID、IMSI、ICCID、GAID、MEID、BSSID、SSID 个人信息，具体字段因软硬件版本不同而存在差异）、设备平台、设备厂商用于生成唯一的推送目标 ID（CID）和设备 ID（GID），以此保证消息推送的准确下发和消息设备的准确识别。设备品牌、设备型号及系统版本用于优化通道资源的有效性，提升消息推送的到达率和稳定性。此外，为确保 CID 的唯一性、提升消息推送稳定性和准确性，还需您授权存储权限（WRITE\_EXTERNAL\_STORAGE、READ\_EXTERNAL\_STORAGE）和设备权限（READ\_PHONE\_STATE），既文件读写。

（2）应用列表信息和任务信息：我们采用合并链路技术进行技术推送，当一个设备有多个 APP 的推送链路同时活跃时，我们会随机合并成一条链路，以达到为用户省电省流量的目的，因此需获得应用列表信息；同时我们提供智能推送功能，通过该信息推荐更符合您用户需要的推送内容，减少无用推送信息对您用户的打扰，您可以选择开启或关闭这项服务，但是关闭这项服务会导致电量和流量消耗加大。

（3）网络信息以及位置相关信息：为了最大程度保持网络连接的稳定性，建立长链接，我们需要了解设备的网络状态和变化，从而实现稳定连续的推送服务。我们提供应景推送功能，位置相关信息将有助于我们为您提供线下场景的精细化推送，可以为您推荐更符合您用户需要的推送内容，减少无用推送消息对您用户的打扰。

（4）为了保障您的用户收到消息的及时性，在您使用消息推送服务时，您可以授权我们进行链路调节，相互促活被关闭的 SDK 推送进程，从而保障推送消息的有效触达。

15.友盟组件化基础库

使用目的：用户行为分析，辅助统计新增、活跃用户等数据

收集数据类型：设备ID信息（IMEI/IMSI）、Mac地址、位置信息、读写外部存储

官网链接：https://www.umeng.com/

16.支付宝SDK

1.支付宝SDK

使用目的：帮助用户在应用内使用支付宝支付

收集数据类型：WLAN状态信息、定位信息、终端设备唯一标识符

官方说明：https://opendocs.alipay.com/open/204