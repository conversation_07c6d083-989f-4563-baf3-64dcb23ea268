为保障掌心智校 App 相关功能的实现与应用安全稳定的运行，我们可能会接入由第三方提供的软件开发包（SDK）实现相关目的。
我们会对合作方获取信息的软件工具开发包（SDK）进行严格的安全监测，以保护数据安全。

我们对接入的相关第三方 SDK 在目录中列明。

请注意，第三方 SDK 可能因为其版本升级、策略调整等原因导致数据处理类型存在一定变化，请以其公示的官方说明为准。

**第三方 SDK 列表如下：**

1.微信登录、分享 SDK（腾讯开发平台 sdk）

使用目的：帮助用户在应用内使用微信登录、分享。

收集数据类型：WLAN 状态信息、定位信息、终端设备唯一标识符

官方说明：https://developers.weixin.qq.com/doc/oplatform/Mobile\_App/Access\_Guide/Android.html

2.腾讯 X5 内核网页浏览组件

使用目的：提供丰富的浏览功能扩展，包括文件、视频等，提升用户浏览体验

收集数据类型：为了能够更好的为您提供服务，TBS 会默认收集用户的国际设备识别码（IMEI）、国际移动用户识别码（IMSI）、网卡地址（MAC Address）、用户机型、屏幕尺寸、操作系统版本号、目标 API（targetAPI）、网络类型(ApnType)、网络变化监听广播以及 Wifi 的 mac 地址。由于在初始化过程中可能会收集这些信息，建议您在获得用户授权后再初始化和使用 TBS 相关能力。

官方隐私政策：https://x5.tencent.com/tbs/guide/develop.html

3.友盟 umengSDK（含啄木鸟 SDK 组件）

使用目的：统计分析及性能监控

收集数据类型：

（1）移动统计 U-APP 我们的产品集成友盟+SDK，友盟+SDK 需要收集您的设备 Mac 地址、唯一设备识别码（IMEI/android ID/IDFA/OPENUDID/GUID、SIM 卡 IMSI 、ICCID信息）以提供统计分析服务，并通过地理位置校准报表数据准确性，提供基础反作弊能力

（2）应用性能监控 U-APM 友盟+SDK 采集设备标识符(IMEI/Mac/android ID/IDFA/OPENUDID/GUID、SIM 卡 IMSI 、ICCID信息)，用于唯一标识设备，以便提供设备应用性能监控服务；通过采集位置信息提供反作弊服务，剔除作弊设备，排查应用性能崩溃原因

官方隐私政策：https://www.umeng.com/page/policy

4.华为推送

使用目的：推送

收集数据类型：用户 ID、应用 ID、应用包名、服务器公网 IP 地址、AAID（应用匿名标识）、应用 Token、Topic 订阅关系、消息下发记录、Token 申请记录、显示/点击/关闭打点上报记录、缓存消息内容、用户的标识符 HMS Core openId

官方隐私政策：https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/privacy-statement-0000001050042021

5.OPPO 推送

使用目的：推送

收集数据类型：IMEI、文件读写、任务信息

官方隐私政策：https://open.oppomobile.com/wiki/doc#id=10196

6.小米推送

使用目的：推送

收集数据类型：设备型号、网络类型、设备存储、设备运行进程信息、应用版本、SDK 版本，以及您国家或地区的设置会帮助我们为您提供更好的推送服务和体验，同时，我们会使用您的设置信息，包括是否屏蔽通知栏、是否设置锁屏弹出消息，来为您提供服务。我们还会收集使用推送服务的应用信息（包名、版本号、应用名称、首次安装时间、上次完成更新的时间），以确保您在卸载应用后不会再接收到和该应用相关的通知。

官方隐私政策：https://dev.mi.com/console/doc/detail?pId=1822

7.vivo 推送

使用目的：推送

收集数据类型：IMEI、文件读写

官方隐私政策：https://dev.vivo.com.cn/documentCenter/doc/233

8.网易易盾 SDK

使用目的：登录场景使用行为验证和号码认证

收集数据类型：设备唯一识别码（设备序列号、AndroidID、IDFV、IDFA）、设备品牌、设备名称、设备型号及设备系统类型、详细设置及版本信息、网络类型、网络制式、厂商、IP 地址。

官方隐私政策：https://dun.163.com/clause/privacy

9.今日头条穿山甲联盟广告

使用目的：更好地实现广告信息的精准投放，提供与用户尽可能相关的广告信息

收集数据类型：硬件型号、操作系统版本号、IP 地址、SIM 卡信息、WLAN 接入点、软件版本号、网络接入方式、类型、状态、网络质量数据、操作、使用、服务日志、GPS

官方隐私政策：https://www.pangle.cn/privacy

10.广点通

使用目的：更好地实现广告信息的精准投放，提供与用户尽可能相关的广告信息

收集数据类型：系统版本名、系统版本号、设备型号、分辨率、屏幕 DPI 设备生产商、网络类型、系统语言、时区、时间戳、User Agent 信息、设备标识信息、地理位置信息、广告交付数据

官方隐私政策：https://imgcache.qq.com/gdt/cdn/adn/uniondoc/ylh\_sdk\_privacy\_statement.html

11.快手广告联盟 SDK

使用目的：更好地实现广告信息的精准投放，提供与用户尽可能相关的广告信息

收集数据类型：系统版本名、系统版本号、设备型号、分辨率、屏幕 DPI 设备生产商、网络类型、系统语言、时区、时间戳、User Agent 信息、设备标识信息、地理位置信息、广告交付数据

官方隐私政策：https://www.kuaishou.com/about/policy?tab=privacy

12.七牛 Qiniu SDK（包括 PLPlayerkit/PLShortVideoKit）

使用目的：用于文件上传下载；快速实现短视频拍摄、剪辑、编辑、合成、分发功能。

收集数据类型：厂商、机型、系统版本、网络类型、Idfv（仅 iOS）、ANDROID ID 信息（仅安卓手机）、加速度传感器、陀螺仪传感器。

官方隐私政策：https://developer.qiniu.com/pili/8027/sdk-privacy-policy

13.友盟组件化基础库

使用目的：基于设备信息用于生成脱敏的终端用户设备标识，以确保提供SDK服务，用户行为分析，辅助统计新增、活跃用户等数据，提供地区数据报表。

收集数据类型：设备 ID 信息（IMEI/MAC/AndroidID/OAID/IDFA/OpenUDID/GUID/SIM卡IMSI/ICCID/IP）、Mac 地址、位置信息、读写外部存储。

官网链接：https://www.umeng.com/

14.华为 HMS

使用目的：华为推送服务需要依赖。

收集数据类型：设备 ID 信息（Android ID）、运营商信息、网络状态信息。

官网链接：https://www.huawei.com/cn/

15.设备标识生成库

使用目的：提供生成唯一的设备标识。

收集数据类型：设备 ID 信息（IMEI、IMSI、Android ID）、网络状态信息、读写外部存储。

官网链接：https://open.alipay.com/

16.阿里 Quinox 框架

使用目的：提供开机启动初始化的框架，加快 app 初始化速度。

收集数据类型：无。

官网链接：https://www.aliyun.com/

17.阿里 HttpDns

使用目的：阿里云的配套服务，可以调用阿里云的产品和服务。

收集数据类型：网络状态信息。

官网链接：https://www.aliyun.com/

18.TopOn SDK

使用目的：更好地实现广告信息的精准投放，提供与用户尽可能相关的广告信息

收集数据类型：收集个人信息类型：国家，时区和区域设置（国家和首选语言），网络连接类型，IP 地址，SDK 版本，时间戳，网络状态如 WiFi，移动广告标识符（如苹果 IDFA 或谷歌广告 ID），设备事件信息如系统崩溃，您提出请求的日期和时间以及来源网址，User-agent，SDK 应用包名，IDFV。平台业务合作伙伴还可能会对中国大陆地区的用户收集国际移动设备识别码（IMEI）、安卓 ID。

官网隐私政策：https://docs.toponad.com/#/zh-cn/android/NetworkAccess/SDK\_Policy/TopOn\_SDK\_Privacy\_Policy\_CN

19.BeiZi SDK

使用目的：更好地实现广告信息的精准投放，提供与用户尽可能相关的广告信息

收集数据类型：收集设备品牌、设备型号、设备时区、设备语言、系统版本、开发者应用名、应用版本号、应用包名、网络类型、UserAgent 信息、网络状态、崩溃信息、性能数据、屏幕高宽、屏幕方向、屏幕 DPI 信息、系统更新时间、开机时间、USB 调试模式、运营商、cpu、应用的进程名称、应用的进程信息、运行状态、设备 ID（OAID、GAID、IMEI、IDFA、IDFV）

官网隐私政策：http://sdkdoc.beizi.biz/#/zh-cn/guide/AllPrivacy

20.华为云 OBS 服务 SDK

使用目的：通过华为云提供的对象存储服务（OBS）API，为用户提供安全、高可靠的云存储服务

收集数据类型：设备型号、操作系统版本、设备设置、设备环境等软件特征信息。

官网隐私政策：https://www.huaweicloud.com/declaration/sa\_prp.html

21.个推推送

使用目的：推送，用于创建内部用户编号以帮助平台更准确的推送信息，避免重复投递消息，对用户造成影响

收集数据类型：为了保障您的用户收到消息的及时性，在您使用消息推送服务时，需要收集您的信息，具体信息和目的如下：

（1）设备信息：设备识别码（IMEI、MAC、AndroidID、OAID、IMSI、ICCID、GAID、MEID、BSSID、SSID 个人信息，具体字段因软硬件版本不同而存在差异）、设备平台、设备厂商用于生成唯一的推送目标 ID（CID）和设备 ID（GID），以此保证消息推送的准确下发和消息设备的准确识别。设备品牌、设备型号及系统版本用于优化通道资源的有效性，提升消息推送的到达率和稳定性。此外，为确保 CID 的唯一性、提升消息推送稳定性和准确性，还需您授权存储权限（WRITE_EXTERNAL_STORAGE、READ_EXTERNAL_STORAGE）和设备权限（READ_PHONE_STATE），既文件读写。

（2）应用列表信息和任务信息：我们采用合并链路技术进行技术推送，当一个设备有多个 APP 的推送链路同时活跃时，我们会随机合并成一条链路，以达到为用户省电省流量的目的，因此需获得应用列表信息；同时我们提供智能推送功能，通过该信息推荐更符合您用户需要的推送内容，减少无用推送信息对您用户的打扰，您可以选择开启或关闭这项服务，但是关闭这项服务会导致电量和流量消耗加大。

（3）网络信息以及位置相关信息：为了最大程度保持网络连接的稳定性，建立长链接，我们需要了解设备的网络状态和变化，从而实现稳定连续的推送服务。我们提供应景推送功能，位置相关信息将有助于我们为您提供线下场景的精细化推送，可以为您推荐更符合您用户需要的推送内容，减少无用推送消息对您用户的打扰。

（4）为了保障您的用户收到消息的及时性，在您使用消息推送服务时，您可以授权我们进行链路调节，相互促活被关闭的 SDK 推送进程，从而保障推送消息的有效触达。

22.卓信 ID SDK

涉及信息：设备弱特征（不具备唯一性和稳定性的特征），含设备信息(设备制造商、设备型号、设备系统信息)、设备网络信息(设备联网方式和状态信息)、设备环境信息(屏幕亮度、电池状态及所在国家、设备应用信息（设备常见应用的版本等安装信息））

使用目的：提供设备标识与安全风控服务

合作方主体：中国信息通信研究院

收集方式：SDK 收集

合作方官网链接：https://zxid.caict.ac.cn

合作方隐私政策链接：https://zxid.caict.ac.cn/privacy

官方隐私政策：https://docs.getui.com/privacy/

23.易盾应用加固 SDK

使用目的：对 JAR/AAR 包加固，提高安全性。

收集信息：设备信息：安卓系统版本，CPU 架构应用信息：本应用包名和签名

官方隐私政策说明：https://dun.163.com/clause/privacy

24.移动安全联盟 SDK

使用目的：调用设备标识符

收集信息：设备制造商、设备型号、设备品牌；设备网络运营商名称、App 包名及签名信息，或在对应应用商店的 APPID

官方隐私政策说明：http://www.msa-alliance.cn/col.jsp?id=122

25.中国电信一键登录 SDK

使用目的：根据用户选择，为用户提供一键登录功能

收集信息：网络状态权限、网络 IP 地址、IMSI、网络类型

官方隐私政策说明：https://e.189.cn/sdk/agreement/detail.do?hidetop=true

26.高德地图

使用目的：获取校车行动轨迹，方便用户更快选择位置

收集数据类型：WLAN状态信息、定位信息、终端设备唯一标识符

官方隐私政策：https://lbs.amap.com/pages/privacy

27.360加固SDK

使用目的：用于检测App故障和诊断，App安全加固保护，盗版检测服务，威胁环境检测服务，以便帮助用户快速解决异常情况

收集信息：网络类型、系统运行语言环境、操作系统版本名称、操作系统版本、机型、系统版本、厂商、终端主板名称、品牌、设备内存大小、手机屏幕分辨率、运行环境

隐私权政策链接：https://jiagu.360.cn/#/global/help/322

28.网易云信即时通讯

使用目的：提供消息投递、多端消息同步、消息漫游功能，同时为了预防安全风险，准确识别违反法律法规的情况

收集信息：唯一设备识别码（APNS Token、设备序列号、AndroidID、OAID、GAID、IDFV、CID、MAC地址）；设备运行状态（SIM卡状态、USB状态、充电状态、电池容量及电量、锁屏时间、启动时间）；设备信息（设备类型、设备品牌及型号、设备厂商、设备所运行系统版本、名称、编辑版本信息、系统内核信息、CPU型号、设备内存及存储大小、屏幕亮度及分辨率、设备输入装置、设备架构、基带信息、字体HASH、HOSTNAME、辅助功能列表、系统进程列表）；日志信息（设备运行状态（CPU、内存运行状态）、浏览器类型及版本信息、网络连接类型及状态、WIFISSID、WIFIBSSID、IP地址、运营商信息、网络代理、云信通信服务运行日志信息）。

隐私权政策链接：https://yunxin.163.com/clauses?serviceType=3

29.章鱼Octopus SDK

使用目的：更好地实现用户设备信息调整广告投放策略，提供统计分析服务，进行基础的分析，实现广告正常显示与交互功能，收集崩溃信息，以此来优化代码缺陷，最大程度减少App崩溃，通过收集SDK运行过程中性能数据，以优化SDK的性能，统计广告数据，以用于广告主统计投放结果。包括不限于：请求、展示 、点击 、转化等，用于广告流程分析，获取信息只用于功能实现并不会收集与保存信息

收集信息：
[双端]设备品牌，设备型号，设备时区，设备语言，系统版本，开发者应用名，应用版本号，应用包名，网络类型，UserAgent信息，网络状态，崩溃信息，性能数据，屏幕高宽，屏幕方向，屏幕DPI信息，系统更新时间，开机时间，磁盘空间，物理内存，运营商；
[仅Android]设备ID（OAID、GAID），IMEI 用户授权才收集，USB调试模式；cpu，运行状态；
[仅ios]设备ID（IDFA 用户授权才收集），IDFV

隐私权政策链接：https://doc.adintl.cn/#/zh-cn/guide/UsePrivacy

30.上海领页 SDK

使用目的：更好地实现广告信息的精准投放，提供与用户尽可能相关的广告信息,还会用于监测归因,减少App崩溃、提供稳定可靠的服务，保证网络服务有效性及稳定性。

收集信息：
[双端]设备品牌、型号、软件系统版本、屏幕密度、屏幕分辨率、设备语言、设备时区、sim卡信息(mcc&mnc)、CPU信息、可用存储空间大小等基础信息
[仅Android]AndroidID、OAID
[仅iOS]手机系统重启时间、磁盘总空间、系统总内存空间、IDFV
可选信息:
[双端]设备MAC地址(如为iOS端，则仅适用于iOS3200以下版本)
[仅Android]设备标识符(如IMEI、IMSI、ICCID、GAID(仅GMS服务)、MEID、硬件序列号build serial，具体字段因软硬件版本不同而存在差异)
[仅iOS]设备标识符(如IDFA，具体字段因软硬件版本不同而存在差异)

隐私权政策链接：https://document.wxcjgg.cn/

31.优推广告平台 SDK

使用目的：更好地实现广告投放及广告反作弊以及广告精确投放，保证网络服务有效性及稳定性，减少App崩溃、提供稳定可靠的服务

收集信息：
[双端]设备品牌、型号、软件系统版本、屏幕密度、屏幕分辨率、设备语言、设备时区等基础信息、运营商信息、网络状态、IP地址
[仅Android]AndroidID、OAID
[仅iOS]磁盘总空间、系统总内存空间、IDFV
可选信息：
[仅Android]设备MAC地址、设备标识符（如IMEI、MEID，具体字段因软硬件版本不同而存在差异）
[仅iOS]设备标识符（如IDFA，具体字段因软硬件版本不同而存在差异）

隐私权政策链接：http://youtui.gameley.com/agreement.html#ytpolice

32.海纳嗨数 SDK

使用目的：为了更好的提供产品服务以及满足用户需求，我们严格遵守法律、法规的规定，按照规定收集、使用终端用户因为使用服务而产生的信息，无论通过上述何种方式收集和使用终端用户的个人信息，我们通常会要求得终端用户同意的情况下进行个人信息收集，收集到的信息仅做产品服务以及用户体验优化使用。

收集信息： [双端]设备属性信息（IMEI、AndroidID、OAID、IDFA、IDFV、UUID、IMSI、Mac地址）、设备连接信息（浏览器类型、电信运营商、设备品牌）、位置信息、唯一应用程序编号

隐私权政策链接：https://himanual.haishuu.com/docs/hicloud/hicloud-1ehlhbuicrhm5

33.优量汇SDK（GdtAd）

使用目的：广告投放、广告归因、广告反作弊和广告效果优化，为提供更好的广告体验，优量汇SDK读取安装应用列表/读取已安装包名列表的收集行为会在在静默状态下或后台运行时收集；在静默状态下或在后台运行时，优量汇SDK读取已安装包名列表，以提供更好的广告体验。

收集信息：粗略位置信息、设备信息（如设备制造商、设备型号、操作系统版本等）、设备标识符（如IMEI、AndroidID、OAID、IDFA等）、应用信息（宿主应用的包名、版本号）、广告数据（如曝光、点击数据等）、应用安装信息/应用安装列表/读取已安装包名列表

隐私权政策链接：https://e.qq.com/dev/help_detail.html?cid=2005&pid=5983

34.百度移动广告SDK

使用目的：提供广告投放、广告监测、广告归因、反作弊功保障服务稳定安全。

收集信息：设备品牌、型号、软件系统版本、分辨率、网络信号强度、磁盘总空间、系统内存空间、屏幕宽高，屏幕像素密度，系统版本号，设备厂商，设备型号，手机运营商、SIM卡信息、手机重启信息、手机系统更新时间、手机系统创建时间、IDFA（仅iOS端采集）、OAID等基础信息、应用包名、应用签名、应用前后台状态、运行进程信息、操作记录、点击、展示、转化等交互数据、崩溃数据、性能数据。

隐私权政策链接：https://union.baidu.com/bqt/#/legal/policies