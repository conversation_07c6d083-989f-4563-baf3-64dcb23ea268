!function(){function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,l(r.key),r)}}function i(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e,n){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,n){if(e){if("string"==typeof e)return t(e,n);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}(e))||n&&e&&"number"==typeof e.length){r&&(e=r);var i=0,a=function(){};return{s:a,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){c=!0,o=e},f:function(){try{s||null==r.return||r.return()}finally{if(c)throw o}}}}function o(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){o(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}var h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function d(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var f,p,g={exports:{}};f=g,function(){if(f.exports)return function(e){f.exports=e()};if("undefined"!=typeof window)return function(e){window.MobileDetect=e()};throw new Error("unknown environment")}()((function(){var e,t={mobileDetectRules:{phones:{iPhone:"\\biPhone\\b|\\biPod\\b",BlackBerry:"BlackBerry|\\bBB10\\b|rim[0-9]+|\\b(BBA100|BBB100|BBD100|BBE100|BBF100|STH100)\\b-[0-9]+",Pixel:"; \\bPixel\\b",HTC:"HTC|HTC.*(Sensation|Evo|Vision|Explorer|6800|8100|8900|A7272|S510e|C110e|Legend|Desire|T8282)|APX515CKT|Qtek9090|APA9292KT|HD_mini|Sensation.*Z710e|PG86100|Z715e|Desire.*(A8181|HD)|ADR6200|ADR6400L|ADR6425|001HT|Inspire 4G|Android.*\\bEVO\\b|T-Mobile G1|Z520m|Android [0-9.]+; Pixel",Nexus:"Nexus One|Nexus S|Galaxy.*Nexus|Android.*Nexus.*Mobile|Nexus 4|Nexus 5|Nexus 5X|Nexus 6",Dell:"Dell[;]? (Streak|Aero|Venue|Venue Pro|Flash|Smoke|Mini 3iX)|XCD28|XCD35|\\b001DL\\b|\\b101DL\\b|\\bGS01\\b",Motorola:"Motorola|DROIDX|DROID BIONIC|\\bDroid\\b.*Build|Android.*Xoom|HRI39|MOT-|A1260|A1680|A555|A853|A855|A953|A955|A956|Motorola.*ELECTRIFY|Motorola.*i1|i867|i940|MB200|MB300|MB501|MB502|MB508|MB511|MB520|MB525|MB526|MB611|MB612|MB632|MB810|MB855|MB860|MB861|MB865|MB870|ME501|ME502|ME511|ME525|ME600|ME632|ME722|ME811|ME860|ME863|ME865|MT620|MT710|MT716|MT720|MT810|MT870|MT917|Motorola.*TITANIUM|WX435|WX445|XT300|XT301|XT311|XT316|XT317|XT319|XT320|XT390|XT502|XT530|XT531|XT532|XT535|XT603|XT610|XT611|XT615|XT681|XT701|XT702|XT711|XT720|XT800|XT806|XT860|XT862|XT875|XT882|XT883|XT894|XT901|XT907|XT909|XT910|XT912|XT928|XT926|XT915|XT919|XT925|XT1021|\\bMoto E\\b|XT1068|XT1092|XT1052",Samsung:"\\bSamsung\\b|SM-G950F|SM-G955F|SM-G9250|GT-19300|SGH-I337|BGT-S5230|GT-B2100|GT-B2700|GT-B2710|GT-B3210|GT-B3310|GT-B3410|GT-B3730|GT-B3740|GT-B5510|GT-B5512|GT-B5722|GT-B6520|GT-B7300|GT-B7320|GT-B7330|GT-B7350|GT-B7510|GT-B7722|GT-B7800|GT-C3010|GT-C3011|GT-C3060|GT-C3200|GT-C3212|GT-C3212I|GT-C3262|GT-C3222|GT-C3300|GT-C3300K|GT-C3303|GT-C3303K|GT-C3310|GT-C3322|GT-C3330|GT-C3350|GT-C3500|GT-C3510|GT-C3530|GT-C3630|GT-C3780|GT-C5010|GT-C5212|GT-C6620|GT-C6625|GT-C6712|GT-E1050|GT-E1070|GT-E1075|GT-E1080|GT-E1081|GT-E1085|GT-E1087|GT-E1100|GT-E1107|GT-E1110|GT-E1120|GT-E1125|GT-E1130|GT-E1160|GT-E1170|GT-E1175|GT-E1180|GT-E1182|GT-E1200|GT-E1210|GT-E1225|GT-E1230|GT-E1390|GT-E2100|GT-E2120|GT-E2121|GT-E2152|GT-E2220|GT-E2222|GT-E2230|GT-E2232|GT-E2250|GT-E2370|GT-E2550|GT-E2652|GT-E3210|GT-E3213|GT-I5500|GT-I5503|GT-I5700|GT-I5800|GT-I5801|GT-I6410|GT-I6420|GT-I7110|GT-I7410|GT-I7500|GT-I8000|GT-I8150|GT-I8160|GT-I8190|GT-I8320|GT-I8330|GT-I8350|GT-I8530|GT-I8700|GT-I8703|GT-I8910|GT-I9000|GT-I9001|GT-I9003|GT-I9010|GT-I9020|GT-I9023|GT-I9070|GT-I9082|GT-I9100|GT-I9103|GT-I9220|GT-I9250|GT-I9300|GT-I9305|GT-I9500|GT-I9505|GT-M3510|GT-M5650|GT-M7500|GT-M7600|GT-M7603|GT-M8800|GT-M8910|GT-N7000|GT-S3110|GT-S3310|GT-S3350|GT-S3353|GT-S3370|GT-S3650|GT-S3653|GT-S3770|GT-S3850|GT-S5210|GT-S5220|GT-S5229|GT-S5230|GT-S5233|GT-S5250|GT-S5253|GT-S5260|GT-S5263|GT-S5270|GT-S5300|GT-S5330|GT-S5350|GT-S5360|GT-S5363|GT-S5369|GT-S5380|GT-S5380D|GT-S5560|GT-S5570|GT-S5600|GT-S5603|GT-S5610|GT-S5620|GT-S5660|GT-S5670|GT-S5690|GT-S5750|GT-S5780|GT-S5830|GT-S5839|GT-S6102|GT-S6500|GT-S7070|GT-S7200|GT-S7220|GT-S7230|GT-S7233|GT-S7250|GT-S7500|GT-S7530|GT-S7550|GT-S7562|GT-S7710|GT-S8000|GT-S8003|GT-S8500|GT-S8530|GT-S8600|SCH-A310|SCH-A530|SCH-A570|SCH-A610|SCH-A630|SCH-A650|SCH-A790|SCH-A795|SCH-A850|SCH-A870|SCH-A890|SCH-A930|SCH-A950|SCH-A970|SCH-A990|SCH-I100|SCH-I110|SCH-I400|SCH-I405|SCH-I500|SCH-I510|SCH-I515|SCH-I600|SCH-I730|SCH-I760|SCH-I770|SCH-I830|SCH-I910|SCH-I920|SCH-I959|SCH-LC11|SCH-N150|SCH-N300|SCH-R100|SCH-R300|SCH-R351|SCH-R400|SCH-R410|SCH-T300|SCH-U310|SCH-U320|SCH-U350|SCH-U360|SCH-U365|SCH-U370|SCH-U380|SCH-U410|SCH-U430|SCH-U450|SCH-U460|SCH-U470|SCH-U490|SCH-U540|SCH-U550|SCH-U620|SCH-U640|SCH-U650|SCH-U660|SCH-U700|SCH-U740|SCH-U750|SCH-U810|SCH-U820|SCH-U900|SCH-U940|SCH-U960|SCS-26UC|SGH-A107|SGH-A117|SGH-A127|SGH-A137|SGH-A157|SGH-A167|SGH-A177|SGH-A187|SGH-A197|SGH-A227|SGH-A237|SGH-A257|SGH-A437|SGH-A517|SGH-A597|SGH-A637|SGH-A657|SGH-A667|SGH-A687|SGH-A697|SGH-A707|SGH-A717|SGH-A727|SGH-A737|SGH-A747|SGH-A767|SGH-A777|SGH-A797|SGH-A817|SGH-A827|SGH-A837|SGH-A847|SGH-A867|SGH-A877|SGH-A887|SGH-A897|SGH-A927|SGH-B100|SGH-B130|SGH-B200|SGH-B220|SGH-C100|SGH-C110|SGH-C120|SGH-C130|SGH-C140|SGH-C160|SGH-C170|SGH-C180|SGH-C200|SGH-C207|SGH-C210|SGH-C225|SGH-C230|SGH-C417|SGH-C450|SGH-D307|SGH-D347|SGH-D357|SGH-D407|SGH-D415|SGH-D780|SGH-D807|SGH-D980|SGH-E105|SGH-E200|SGH-E315|SGH-E316|SGH-E317|SGH-E335|SGH-E590|SGH-E635|SGH-E715|SGH-E890|SGH-F300|SGH-F480|SGH-I200|SGH-I300|SGH-I320|SGH-I550|SGH-I577|SGH-I600|SGH-I607|SGH-I617|SGH-I627|SGH-I637|SGH-I677|SGH-I700|SGH-I717|SGH-I727|SGH-i747M|SGH-I777|SGH-I780|SGH-I827|SGH-I847|SGH-I857|SGH-I896|SGH-I897|SGH-I900|SGH-I907|SGH-I917|SGH-I927|SGH-I937|SGH-I997|SGH-J150|SGH-J200|SGH-L170|SGH-L700|SGH-M110|SGH-M150|SGH-M200|SGH-N105|SGH-N500|SGH-N600|SGH-N620|SGH-N625|SGH-N700|SGH-N710|SGH-P107|SGH-P207|SGH-P300|SGH-P310|SGH-P520|SGH-P735|SGH-P777|SGH-Q105|SGH-R210|SGH-R220|SGH-R225|SGH-S105|SGH-S307|SGH-T109|SGH-T119|SGH-T139|SGH-T209|SGH-T219|SGH-T229|SGH-T239|SGH-T249|SGH-T259|SGH-T309|SGH-T319|SGH-T329|SGH-T339|SGH-T349|SGH-T359|SGH-T369|SGH-T379|SGH-T409|SGH-T429|SGH-T439|SGH-T459|SGH-T469|SGH-T479|SGH-T499|SGH-T509|SGH-T519|SGH-T539|SGH-T559|SGH-T589|SGH-T609|SGH-T619|SGH-T629|SGH-T639|SGH-T659|SGH-T669|SGH-T679|SGH-T709|SGH-T719|SGH-T729|SGH-T739|SGH-T746|SGH-T749|SGH-T759|SGH-T769|SGH-T809|SGH-T819|SGH-T839|SGH-T919|SGH-T929|SGH-T939|SGH-T959|SGH-T989|SGH-U100|SGH-U200|SGH-U800|SGH-V205|SGH-V206|SGH-X100|SGH-X105|SGH-X120|SGH-X140|SGH-X426|SGH-X427|SGH-X475|SGH-X495|SGH-X497|SGH-X507|SGH-X600|SGH-X610|SGH-X620|SGH-X630|SGH-X700|SGH-X820|SGH-X890|SGH-Z130|SGH-Z150|SGH-Z170|SGH-ZX10|SGH-ZX20|SHW-M110|SPH-A120|SPH-A400|SPH-A420|SPH-A460|SPH-A500|SPH-A560|SPH-A600|SPH-A620|SPH-A660|SPH-A700|SPH-A740|SPH-A760|SPH-A790|SPH-A800|SPH-A820|SPH-A840|SPH-A880|SPH-A900|SPH-A940|SPH-A960|SPH-D600|SPH-D700|SPH-D710|SPH-D720|SPH-I300|SPH-I325|SPH-I330|SPH-I350|SPH-I500|SPH-I600|SPH-I700|SPH-L700|SPH-M100|SPH-M220|SPH-M240|SPH-M300|SPH-M305|SPH-M320|SPH-M330|SPH-M350|SPH-M360|SPH-M370|SPH-M380|SPH-M510|SPH-M540|SPH-M550|SPH-M560|SPH-M570|SPH-M580|SPH-M610|SPH-M620|SPH-M630|SPH-M800|SPH-M810|SPH-M850|SPH-M900|SPH-M910|SPH-M920|SPH-M930|SPH-N100|SPH-N200|SPH-N240|SPH-N300|SPH-N400|SPH-Z400|SWC-E100|SCH-i909|GT-N7100|GT-N7105|SCH-I535|SM-N900A|SGH-I317|SGH-T999L|GT-S5360B|GT-I8262|GT-S6802|GT-S6312|GT-S6310|GT-S5312|GT-S5310|GT-I9105|GT-I8510|GT-S6790N|SM-G7105|SM-N9005|GT-S5301|GT-I9295|GT-I9195|SM-C101|GT-S7392|GT-S7560|GT-B7610|GT-I5510|GT-S7582|GT-S7530E|GT-I8750|SM-G9006V|SM-G9008V|SM-G9009D|SM-G900A|SM-G900D|SM-G900F|SM-G900H|SM-G900I|SM-G900J|SM-G900K|SM-G900L|SM-G900M|SM-G900P|SM-G900R4|SM-G900S|SM-G900T|SM-G900V|SM-G900W8|SHV-E160K|SCH-P709|SCH-P729|SM-T2558|GT-I9205|SM-G9350|SM-J120F|SM-G920F|SM-G920V|SM-G930F|SM-N910C|SM-A310F|GT-I9190|SM-J500FN|SM-G903F|SM-J330F|SM-G610F|SM-G981B|SM-G892A|SM-A530F",LG:"\\bLG\\b;|LG[- ]?(C800|C900|E400|E610|E900|E-900|F160|F180K|F180L|F180S|730|855|L160|LS740|LS840|LS970|LU6200|MS690|MS695|MS770|MS840|MS870|MS910|P500|P700|P705|VM696|AS680|AS695|AX840|C729|E970|GS505|272|C395|E739BK|E960|L55C|L75C|LS696|LS860|P769BK|P350|P500|P509|P870|UN272|US730|VS840|VS950|LN272|LN510|LS670|LS855|LW690|MN270|MN510|P509|P769|P930|UN200|UN270|UN510|UN610|US670|US740|US760|UX265|UX840|VN271|VN530|VS660|VS700|VS740|VS750|VS910|VS920|VS930|VX9200|VX11000|AX840A|LW770|P506|P925|P999|E612|D955|D802|MS323|M257)|LM-G710",Sony:"SonyST|SonyLT|SonyEricsson|SonyEricssonLT15iv|LT18i|E10i|LT28h|LT26w|SonyEricssonMT27i|C5303|C6902|C6903|C6906|C6943|D2533|SOV34|601SO|F8332",Asus:"Asus.*Galaxy|PadFone.*Mobile",Xiaomi:"^(?!.*\\bx11\\b).*xiaomi.*$|POCOPHONE F1|MI 8|Redmi Note 9S|Redmi Note 5A Prime|N2G47H|M2001J2G|M2001J2I|M1805E10A|M2004J11G|M1902F1G|M2002J9G|M2004J19G|M2003J6A1G",NokiaLumia:"Lumia [0-9]{3,4}",Micromax:"Micromax.*\\b(A210|A92|A88|A72|A111|A110Q|A115|A116|A110|A90S|A26|A51|A35|A54|A25|A27|A89|A68|A65|A57|A90)\\b",Palm:"PalmSource|Palm",Vertu:"Vertu|Vertu.*Ltd|Vertu.*Ascent|Vertu.*Ayxta|Vertu.*Constellation(F|Quest)?|Vertu.*Monika|Vertu.*Signature",Pantech:"PANTECH|IM-A850S|IM-A840S|IM-A830L|IM-A830K|IM-A830S|IM-A820L|IM-A810K|IM-A810S|IM-A800S|IM-T100K|IM-A725L|IM-A780L|IM-A775C|IM-A770K|IM-A760S|IM-A750K|IM-A740S|IM-A730S|IM-A720L|IM-A710K|IM-A690L|IM-A690S|IM-A650S|IM-A630K|IM-A600S|VEGA PTL21|PT003|P8010|ADR910L|P6030|P6020|P9070|P4100|P9060|P5000|CDM8992|TXT8045|ADR8995|IS11PT|P2030|P6010|P8000|PT002|IS06|CDM8999|P9050|PT001|TXT8040|P2020|P9020|P2000|P7040|P7000|C790",Fly:"IQ230|IQ444|IQ450|IQ440|IQ442|IQ441|IQ245|IQ256|IQ236|IQ255|IQ235|IQ245|IQ275|IQ240|IQ285|IQ280|IQ270|IQ260|IQ250",Wiko:"KITE 4G|HIGHWAY|GETAWAY|STAIRWAY|DARKSIDE|DARKFULL|DARKNIGHT|DARKMOON|SLIDE|WAX 4G|RAINBOW|BLOOM|SUNSET|GOA(?!nna)|LENNY|BARRY|IGGY|OZZY|CINK FIVE|CINK PEAX|CINK PEAX 2|CINK SLIM|CINK SLIM 2|CINK +|CINK KING|CINK PEAX|CINK SLIM|SUBLIM",iMobile:"i-mobile (IQ|i-STYLE|idea|ZAA|Hitz)",SimValley:"\\b(SP-80|XT-930|SX-340|XT-930|SX-310|SP-360|SP60|SPT-800|SP-120|SPT-800|SP-140|SPX-5|SPX-8|SP-100|SPX-8|SPX-12)\\b",Wolfgang:"AT-B24D|AT-AS50HD|AT-AS40W|AT-AS55HD|AT-AS45q2|AT-B26D|AT-AS50Q",Alcatel:"Alcatel",Nintendo:"Nintendo (3DS|Switch)",Amoi:"Amoi",INQ:"INQ",OnePlus:"ONEPLUS",GenericPhone:"Tapatalk|PDA;|SAGEM|\\bmmp\\b|pocket|\\bpsp\\b|symbian|Smartphone|smartfon|treo|up.browser|up.link|vodafone|\\bwap\\b|nokia|Series40|Series60|S60|SonyEricsson|N900|MAUI.*WAP.*Browser"},tablets:{iPad:"iPad|iPad.*Mobile",NexusTablet:"Android.*Nexus[\\s]+(7|9|10)",GoogleTablet:"Android.*Pixel C",SamsungTablet:"SAMSUNG.*Tablet|Galaxy.*Tab|SC-01C|GT-P1000|GT-P1003|GT-P1010|GT-P3105|GT-P6210|GT-P6800|GT-P6810|GT-P7100|GT-P7300|GT-P7310|GT-P7500|GT-P7510|SCH-I800|SCH-I815|SCH-I905|SGH-I957|SGH-I987|SGH-T849|SGH-T859|SGH-T869|SPH-P100|GT-P3100|GT-P3108|GT-P3110|GT-P5100|GT-P5110|GT-P6200|GT-P7320|GT-P7511|GT-N8000|GT-P8510|SGH-I497|SPH-P500|SGH-T779|SCH-I705|SCH-I915|GT-N8013|GT-P3113|GT-P5113|GT-P8110|GT-N8010|GT-N8005|GT-N8020|GT-P1013|GT-P6201|GT-P7501|GT-N5100|GT-N5105|GT-N5110|SHV-E140K|SHV-E140L|SHV-E140S|SHV-E150S|SHV-E230K|SHV-E230L|SHV-E230S|SHW-M180K|SHW-M180L|SHW-M180S|SHW-M180W|SHW-M300W|SHW-M305W|SHW-M380K|SHW-M380S|SHW-M380W|SHW-M430W|SHW-M480K|SHW-M480S|SHW-M480W|SHW-M485W|SHW-M486W|SHW-M500W|GT-I9228|SCH-P739|SCH-I925|GT-I9200|GT-P5200|GT-P5210|GT-P5210X|SM-T311|SM-T310|SM-T310X|SM-T210|SM-T210R|SM-T211|SM-P600|SM-P601|SM-P605|SM-P900|SM-P901|SM-T217|SM-T217A|SM-T217S|SM-P6000|SM-T3100|SGH-I467|XE500|SM-T110|GT-P5220|GT-I9200X|GT-N5110X|GT-N5120|SM-P905|SM-T111|SM-T2105|SM-T315|SM-T320|SM-T320X|SM-T321|SM-T520|SM-T525|SM-T530NU|SM-T230NU|SM-T330NU|SM-T900|XE500T1C|SM-P605V|SM-P905V|SM-T337V|SM-T537V|SM-T707V|SM-T807V|SM-P600X|SM-P900X|SM-T210X|SM-T230|SM-T230X|SM-T325|GT-P7503|SM-T531|SM-T330|SM-T530|SM-T705|SM-T705C|SM-T535|SM-T331|SM-T800|SM-T700|SM-T537|SM-T807|SM-P907A|SM-T337A|SM-T537A|SM-T707A|SM-T807A|SM-T237|SM-T807P|SM-P607T|SM-T217T|SM-T337T|SM-T807T|SM-T116NQ|SM-T116BU|SM-P550|SM-T350|SM-T550|SM-T9000|SM-P9000|SM-T705Y|SM-T805|GT-P3113|SM-T710|SM-T810|SM-T815|SM-T360|SM-T533|SM-T113|SM-T335|SM-T715|SM-T560|SM-T670|SM-T677|SM-T377|SM-T567|SM-T357T|SM-T555|SM-T561|SM-T713|SM-T719|SM-T813|SM-T819|SM-T580|SM-T355Y?|SM-T280|SM-T817A|SM-T820|SM-W700|SM-P580|SM-T587|SM-P350|SM-P555M|SM-P355M|SM-T113NU|SM-T815Y|SM-T585|SM-T285|SM-T825|SM-W708|SM-T835|SM-T830|SM-T837V|SM-T720|SM-T510|SM-T387V|SM-P610|SM-T290|SM-T515|SM-T590|SM-T595|SM-T725|SM-T817P|SM-P585N0|SM-T395|SM-T295|SM-T865|SM-P610N|SM-P615|SM-T970|SM-T380|SM-T5950|SM-T905|SM-T231|SM-T500|SM-T860",Kindle:"Kindle|Silk.*Accelerated|Android.*\\b(KFOT|KFTT|KFJWI|KFJWA|KFOTE|KFSOWI|KFTHWI|KFTHWA|KFAPWI|KFAPWA|WFJWAE|KFSAWA|KFSAWI|KFASWI|KFARWI|KFFOWI|KFGIWI|KFMEWI)\\b|Android.*Silk/[0-9.]+ like Chrome/[0-9.]+ (?!Mobile)",SurfaceTablet:"Windows NT [0-9.]+; ARM;.*(Tablet|ARMBJS)",HPTablet:"HP Slate (7|8|10)|HP ElitePad 900|hp-tablet|EliteBook.*Touch|HP 8|Slate 21|HP SlateBook 10",AsusTablet:"^.*PadFone((?!Mobile).)*$|Transformer|TF101|TF101G|TF300T|TF300TG|TF300TL|TF700T|TF700KL|TF701T|TF810C|ME171|ME301T|ME302C|ME371MG|ME370T|ME372MG|ME172V|ME173X|ME400C|Slider SL101|\\bK00F\\b|\\bK00C\\b|\\bK00E\\b|\\bK00L\\b|TX201LA|ME176C|ME102A|\\bM80TA\\b|ME372CL|ME560CG|ME372CG|ME302KL| K010 | K011 | K017 | K01E |ME572C|ME103K|ME170C|ME171C|\\bME70C\\b|ME581C|ME581CL|ME8510C|ME181C|P01Y|PO1MA|P01Z|\\bP027\\b|\\bP024\\b|\\bP00C\\b",BlackBerryTablet:"PlayBook|RIM Tablet",HTCtablet:"HTC_Flyer_P512|HTC Flyer|HTC Jetstream|HTC-P715a|HTC EVO View 4G|PG41200|PG09410",MotorolaTablet:"xoom|sholest|MZ615|MZ605|MZ505|MZ601|MZ602|MZ603|MZ604|MZ606|MZ607|MZ608|MZ609|MZ615|MZ616|MZ617",NookTablet:"Android.*Nook|NookColor|nook browser|BNRV200|BNRV200A|BNTV250|BNTV250A|BNTV400|BNTV600|LogicPD Zoom2",AcerTablet:"Android.*; \\b(A100|A101|A110|A200|A210|A211|A500|A501|A510|A511|A700|A701|W500|W500P|W501|W501P|W510|W511|W700|G100|G100W|B1-A71|B1-710|B1-711|A1-810|A1-811|A1-830)\\b|W3-810|\\bA3-A10\\b|\\bA3-A11\\b|\\bA3-A20\\b|\\bA3-A30|A3-A40",ToshibaTablet:"Android.*(AT100|AT105|AT200|AT205|AT270|AT275|AT300|AT305|AT1S5|AT500|AT570|AT700|AT830)|TOSHIBA.*FOLIO",LGTablet:"\\bL-06C|LG-V909|LG-V900|LG-V700|LG-V510|LG-V500|LG-V410|LG-V400|LG-VK810\\b",FujitsuTablet:"Android.*\\b(F-01D|F-02F|F-05E|F-10D|M532|Q572)\\b",PrestigioTablet:"PMP3170B|PMP3270B|PMP3470B|PMP7170B|PMP3370B|PMP3570C|PMP5870C|PMP3670B|PMP5570C|PMP5770D|PMP3970B|PMP3870C|PMP5580C|PMP5880D|PMP5780D|PMP5588C|PMP7280C|PMP7280C3G|PMP7280|PMP7880D|PMP5597D|PMP5597|PMP7100D|PER3464|PER3274|PER3574|PER3884|PER5274|PER5474|PMP5097CPRO|PMP5097|PMP7380D|PMP5297C|PMP5297C_QUAD|PMP812E|PMP812E3G|PMP812F|PMP810E|PMP880TD|PMT3017|PMT3037|PMT3047|PMT3057|PMT7008|PMT5887|PMT5001|PMT5002",LenovoTablet:"Lenovo TAB|Idea(Tab|Pad)( A1|A10| K1|)|ThinkPad([ ]+)?Tablet|YT3-850M|YT3-X90L|YT3-X90F|YT3-X90X|Lenovo.*(S2109|S2110|S5000|S6000|K3011|A3000|A3500|A1000|A2107|A2109|A1107|A5500|A7600|B6000|B8000|B8080)(-|)(FL|F|HV|H|)|TB-X103F|TB-X304X|TB-X304F|TB-X304L|TB-X505F|TB-X505L|TB-X505X|TB-X605F|TB-X605L|TB-8703F|TB-8703X|TB-8703N|TB-8704N|TB-8704F|TB-8704X|TB-8704V|TB-7304F|TB-7304I|TB-7304X|Tab2A7-10F|Tab2A7-20F|TB2-X30L|YT3-X50L|YT3-X50F|YT3-X50M|YT-X705F|YT-X703F|YT-X703L|YT-X705L|YT-X705X|TB2-X30F|TB2-X30L|TB2-X30M|A2107A-F|A2107A-H|TB3-730F|TB3-730M|TB3-730X|TB-7504F|TB-7504X|TB-X704F|TB-X104F|TB3-X70F|TB-X705F|TB-8504F|TB3-X70L|TB3-710F|TB-X704L",DellTablet:"Venue 11|Venue 8|Venue 7|Dell Streak 10|Dell Streak 7",YarvikTablet:"Android.*\\b(TAB210|TAB211|TAB224|TAB250|TAB260|TAB264|TAB310|TAB360|TAB364|TAB410|TAB411|TAB420|TAB424|TAB450|TAB460|TAB461|TAB464|TAB465|TAB467|TAB468|TAB07-100|TAB07-101|TAB07-150|TAB07-151|TAB07-152|TAB07-200|TAB07-201-3G|TAB07-210|TAB07-211|TAB07-212|TAB07-214|TAB07-220|TAB07-400|TAB07-485|TAB08-150|TAB08-200|TAB08-201-3G|TAB08-201-30|TAB09-100|TAB09-211|TAB09-410|TAB10-150|TAB10-201|TAB10-211|TAB10-400|TAB10-410|TAB13-201|TAB274EUK|TAB275EUK|TAB374EUK|TAB462EUK|TAB474EUK|TAB9-200)\\b",MedionTablet:"Android.*\\bOYO\\b|LIFE.*(P9212|P9514|P9516|S9512)|LIFETAB",ArnovaTablet:"97G4|AN10G2|AN7bG3|AN7fG3|AN8G3|AN8cG3|AN7G3|AN9G3|AN7dG3|AN7dG3ST|AN7dG3ChildPad|AN10bG3|AN10bG3DT|AN9G2",IntensoTablet:"INM8002KP|INM1010FP|INM805ND|Intenso Tab|TAB1004",IRUTablet:"M702pro",MegafonTablet:"MegaFon V9|\\bZTE V9\\b|Android.*\\bMT7A\\b",EbodaTablet:"E-Boda (Supreme|Impresspeed|Izzycomm|Essential)",AllViewTablet:"Allview.*(Viva|Alldro|City|Speed|All TV|Frenzy|Quasar|Shine|TX1|AX1|AX2)",ArchosTablet:"\\b(101G9|80G9|A101IT)\\b|Qilive 97R|Archos5|\\bARCHOS (70|79|80|90|97|101|FAMILYPAD|)(b|c|)(G10| Cobalt| TITANIUM(HD|)| Xenon| Neon|XSK| 2| XS 2| PLATINUM| CARBON|GAMEPAD)\\b",AinolTablet:"NOVO7|NOVO8|NOVO10|Novo7Aurora|Novo7Basic|NOVO7PALADIN|novo9-Spark",NokiaLumiaTablet:"Lumia 2520",SonyTablet:"Sony.*Tablet|Xperia Tablet|Sony Tablet S|SO-03E|SGPT12|SGPT13|SGPT114|SGPT121|SGPT122|SGPT123|SGPT111|SGPT112|SGPT113|SGPT131|SGPT132|SGPT133|SGPT211|SGPT212|SGPT213|SGP311|SGP312|SGP321|EBRD1101|EBRD1102|EBRD1201|SGP351|SGP341|SGP511|SGP512|SGP521|SGP541|SGP551|SGP621|SGP641|SGP612|SOT31|SGP771|SGP611|SGP612|SGP712",PhilipsTablet:"\\b(PI2010|PI3000|PI3100|PI3105|PI3110|PI3205|PI3210|PI3900|PI4010|PI7000|PI7100)\\b",CubeTablet:"Android.*(K8GT|U9GT|U10GT|U16GT|U17GT|U18GT|U19GT|U20GT|U23GT|U30GT)|CUBE U8GT",CobyTablet:"MID1042|MID1045|MID1125|MID1126|MID7012|MID7014|MID7015|MID7034|MID7035|MID7036|MID7042|MID7048|MID7127|MID8042|MID8048|MID8127|MID9042|MID9740|MID9742|MID7022|MID7010",MIDTablet:"M9701|M9000|M9100|M806|M1052|M806|T703|MID701|MID713|MID710|MID727|MID760|MID830|MID728|MID933|MID125|MID810|MID732|MID120|MID930|MID800|MID731|MID900|MID100|MID820|MID735|MID980|MID130|MID833|MID737|MID960|MID135|MID860|MID736|MID140|MID930|MID835|MID733|MID4X10",MSITablet:"MSI \\b(Primo 73K|Primo 73L|Primo 81L|Primo 77|Primo 93|Primo 75|Primo 76|Primo 73|Primo 81|Primo 91|Primo 90|Enjoy 71|Enjoy 7|Enjoy 10)\\b",SMiTTablet:"Android.*(\\bMID\\b|MID-560|MTV-T1200|MTV-PND531|MTV-P1101|MTV-PND530)",RockChipTablet:"Android.*(RK2818|RK2808A|RK2918|RK3066)|RK2738|RK2808A",FlyTablet:"IQ310|Fly Vision",bqTablet:"Android.*(bq)?.*\\b(Elcano|Curie|Edison|Maxwell|Kepler|Pascal|Tesla|Hypatia|Platon|Newton|Livingstone|Cervantes|Avant|Aquaris ([E|M]10|M8))\\b|Maxwell.*Lite|Maxwell.*Plus",HuaweiTablet:"MediaPad|MediaPad 7 Youth|IDEOS S7|S7-201c|S7-202u|S7-101|S7-103|S7-104|S7-105|S7-106|S7-201|S7-Slim|M2-A01L|BAH-L09|BAH-W09|AGS-L09|CMR-AL19",NecTablet:"\\bN-06D|\\bN-08D",PantechTablet:"Pantech.*P4100",BronchoTablet:"Broncho.*(N701|N708|N802|a710)",VersusTablet:"TOUCHPAD.*[78910]|\\bTOUCHTAB\\b",ZyncTablet:"z1000|Z99 2G|z930|z990|z909|Z919|z900",PositivoTablet:"TB07STA|TB10STA|TB07FTA|TB10FTA",NabiTablet:"Android.*\\bNabi",KoboTablet:"Kobo Touch|\\bK080\\b|\\bVox\\b Build|\\bArc\\b Build",DanewTablet:"DSlide.*\\b(700|701R|702|703R|704|802|970|971|972|973|974|1010|1012)\\b",TexetTablet:"NaviPad|TB-772A|TM-7045|TM-7055|TM-9750|TM-7016|TM-7024|TM-7026|TM-7041|TM-7043|TM-7047|TM-8041|TM-9741|TM-9747|TM-9748|TM-9751|TM-7022|TM-7021|TM-7020|TM-7011|TM-7010|TM-7023|TM-7025|TM-7037W|TM-7038W|TM-7027W|TM-9720|TM-9725|TM-9737W|TM-1020|TM-9738W|TM-9740|TM-9743W|TB-807A|TB-771A|TB-727A|TB-725A|TB-719A|TB-823A|TB-805A|TB-723A|TB-715A|TB-707A|TB-705A|TB-709A|TB-711A|TB-890HD|TB-880HD|TB-790HD|TB-780HD|TB-770HD|TB-721HD|TB-710HD|TB-434HD|TB-860HD|TB-840HD|TB-760HD|TB-750HD|TB-740HD|TB-730HD|TB-722HD|TB-720HD|TB-700HD|TB-500HD|TB-470HD|TB-431HD|TB-430HD|TB-506|TB-504|TB-446|TB-436|TB-416|TB-146SE|TB-126SE",PlaystationTablet:"Playstation.*(Portable|Vita)",TrekstorTablet:"ST10416-1|VT10416-1|ST70408-1|ST702xx-1|ST702xx-2|ST80208|ST97216|ST70104-2|VT10416-2|ST10216-2A|SurfTab",PyleAudioTablet:"\\b(PTBL10CEU|PTBL10C|PTBL72BC|PTBL72BCEU|PTBL7CEU|PTBL7C|PTBL92BC|PTBL92BCEU|PTBL9CEU|PTBL9CUK|PTBL9C)\\b",AdvanTablet:"Android.* \\b(E3A|T3X|T5C|T5B|T3E|T3C|T3B|T1J|T1F|T2A|T1H|T1i|E1C|T1-E|T5-A|T4|E1-B|T2Ci|T1-B|T1-D|O1-A|E1-A|T1-A|T3A|T4i)\\b ",DanyTechTablet:"Genius Tab G3|Genius Tab S2|Genius Tab Q3|Genius Tab G4|Genius Tab Q4|Genius Tab G-II|Genius TAB GII|Genius TAB GIII|Genius Tab S1",GalapadTablet:"Android [0-9.]+; [a-z-]+; \\bG1\\b",MicromaxTablet:"Funbook|Micromax.*\\b(P250|P560|P360|P362|P600|P300|P350|P500|P275)\\b",KarbonnTablet:"Android.*\\b(A39|A37|A34|ST8|ST10|ST7|Smart Tab3|Smart Tab2)\\b",AllFineTablet:"Fine7 Genius|Fine7 Shine|Fine7 Air|Fine8 Style|Fine9 More|Fine10 Joy|Fine11 Wide",PROSCANTablet:"\\b(PEM63|PLT1023G|PLT1041|PLT1044|PLT1044G|PLT1091|PLT4311|PLT4311PL|PLT4315|PLT7030|PLT7033|PLT7033D|PLT7035|PLT7035D|PLT7044K|PLT7045K|PLT7045KB|PLT7071KG|PLT7072|PLT7223G|PLT7225G|PLT7777G|PLT7810K|PLT7849G|PLT7851G|PLT7852G|PLT8015|PLT8031|PLT8034|PLT8036|PLT8080K|PLT8082|PLT8088|PLT8223G|PLT8234G|PLT8235G|PLT8816K|PLT9011|PLT9045K|PLT9233G|PLT9735|PLT9760G|PLT9770G)\\b",YONESTablet:"BQ1078|BC1003|BC1077|RK9702|BC9730|BC9001|IT9001|BC7008|BC7010|BC708|BC728|BC7012|BC7030|BC7027|BC7026",ChangJiaTablet:"TPC7102|TPC7103|TPC7105|TPC7106|TPC7107|TPC7201|TPC7203|TPC7205|TPC7210|TPC7708|TPC7709|TPC7712|TPC7110|TPC8101|TPC8103|TPC8105|TPC8106|TPC8203|TPC8205|TPC8503|TPC9106|TPC9701|TPC97101|TPC97103|TPC97105|TPC97106|TPC97111|TPC97113|TPC97203|TPC97603|TPC97809|TPC97205|TPC10101|TPC10103|TPC10106|TPC10111|TPC10203|TPC10205|TPC10503",GUTablet:"TX-A1301|TX-M9002|Q702|kf026",PointOfViewTablet:"TAB-P506|TAB-navi-7-3G-M|TAB-P517|TAB-P-527|TAB-P701|TAB-P703|TAB-P721|TAB-P731N|TAB-P741|TAB-P825|TAB-P905|TAB-P925|TAB-PR945|TAB-PL1015|TAB-P1025|TAB-PI1045|TAB-P1325|TAB-PROTAB[0-9]+|TAB-PROTAB25|TAB-PROTAB26|TAB-PROTAB27|TAB-PROTAB26XL|TAB-PROTAB2-IPS9|TAB-PROTAB30-IPS9|TAB-PROTAB25XXL|TAB-PROTAB26-IPS10|TAB-PROTAB30-IPS10",OvermaxTablet:"OV-(SteelCore|NewBase|Basecore|Baseone|Exellen|Quattor|EduTab|Solution|ACTION|BasicTab|TeddyTab|MagicTab|Stream|TB-08|TB-09)|Qualcore 1027",HCLTablet:"HCL.*Tablet|Connect-3G-2.0|Connect-2G-2.0|ME Tablet U1|ME Tablet U2|ME Tablet G1|ME Tablet X1|ME Tablet Y2|ME Tablet Sync",DPSTablet:"DPS Dream 9|DPS Dual 7",VistureTablet:"V97 HD|i75 3G|Visture V4( HD)?|Visture V5( HD)?|Visture V10",CrestaTablet:"CTP(-)?810|CTP(-)?818|CTP(-)?828|CTP(-)?838|CTP(-)?888|CTP(-)?978|CTP(-)?980|CTP(-)?987|CTP(-)?988|CTP(-)?989",MediatekTablet:"\\bMT8125|MT8389|MT8135|MT8377\\b",ConcordeTablet:"Concorde([ ]+)?Tab|ConCorde ReadMan",GoCleverTablet:"GOCLEVER TAB|A7GOCLEVER|M1042|M7841|M742|R1042BK|R1041|TAB A975|TAB A7842|TAB A741|TAB A741L|TAB M723G|TAB M721|TAB A1021|TAB I921|TAB R721|TAB I720|TAB T76|TAB R70|TAB R76.2|TAB R106|TAB R83.2|TAB M813G|TAB I721|GCTA722|TAB I70|TAB I71|TAB S73|TAB R73|TAB R74|TAB R93|TAB R75|TAB R76.1|TAB A73|TAB A93|TAB A93.2|TAB T72|TAB R83|TAB R974|TAB R973|TAB A101|TAB A103|TAB A104|TAB A104.2|R105BK|M713G|A972BK|TAB A971|TAB R974.2|TAB R104|TAB R83.3|TAB A1042",ModecomTablet:"FreeTAB 9000|FreeTAB 7.4|FreeTAB 7004|FreeTAB 7800|FreeTAB 2096|FreeTAB 7.5|FreeTAB 1014|FreeTAB 1001 |FreeTAB 8001|FreeTAB 9706|FreeTAB 9702|FreeTAB 7003|FreeTAB 7002|FreeTAB 1002|FreeTAB 7801|FreeTAB 1331|FreeTAB 1004|FreeTAB 8002|FreeTAB 8014|FreeTAB 9704|FreeTAB 1003",VoninoTablet:"\\b(Argus[ _]?S|Diamond[ _]?79HD|Emerald[ _]?78E|Luna[ _]?70C|Onyx[ _]?S|Onyx[ _]?Z|Orin[ _]?HD|Orin[ _]?S|Otis[ _]?S|SpeedStar[ _]?S|Magnet[ _]?M9|Primus[ _]?94[ _]?3G|Primus[ _]?94HD|Primus[ _]?QS|Android.*\\bQ8\\b|Sirius[ _]?EVO[ _]?QS|Sirius[ _]?QS|Spirit[ _]?S)\\b",ECSTablet:"V07OT2|TM105A|S10OT1|TR10CS1",StorexTablet:"eZee[_']?(Tab|Go)[0-9]+|TabLC7|Looney Tunes Tab",VodafoneTablet:"SmartTab([ ]+)?[0-9]+|SmartTabII10|SmartTabII7|VF-1497|VFD 1400",EssentielBTablet:"Smart[ ']?TAB[ ]+?[0-9]+|Family[ ']?TAB2",RossMoorTablet:"RM-790|RM-997|RMD-878G|RMD-974R|RMT-705A|RMT-701|RME-601|RMT-501|RMT-711",iMobileTablet:"i-mobile i-note",TolinoTablet:"tolino tab [0-9.]+|tolino shine",AudioSonicTablet:"\\bC-22Q|T7-QC|T-17B|T-17P\\b",AMPETablet:"Android.* A78 ",SkkTablet:"Android.* (SKYPAD|PHOENIX|CYCLOPS)",TecnoTablet:"TECNO P9|TECNO DP8D",JXDTablet:"Android.* \\b(F3000|A3300|JXD5000|JXD3000|JXD2000|JXD300B|JXD300|S5800|S7800|S602b|S5110b|S7300|S5300|S602|S603|S5100|S5110|S601|S7100a|P3000F|P3000s|P101|P200s|P1000m|P200m|P9100|P1000s|S6600b|S908|P1000|P300|S18|S6600|S9100)\\b",iJoyTablet:"Tablet (Spirit 7|Essentia|Galatea|Fusion|Onix 7|Landa|Titan|Scooby|Deox|Stella|Themis|Argon|Unique 7|Sygnus|Hexen|Finity 7|Cream|Cream X2|Jade|Neon 7|Neron 7|Kandy|Scape|Saphyr 7|Rebel|Biox|Rebel|Rebel 8GB|Myst|Draco 7|Myst|Tab7-004|Myst|Tadeo Jones|Tablet Boing|Arrow|Draco Dual Cam|Aurix|Mint|Amity|Revolution|Finity 9|Neon 9|T9w|Amity 4GB Dual Cam|Stone 4GB|Stone 8GB|Andromeda|Silken|X2|Andromeda II|Halley|Flame|Saphyr 9,7|Touch 8|Planet|Triton|Unique 10|Hexen 10|Memphis 4GB|Memphis 8GB|Onix 10)",FX2Tablet:"FX2 PAD7|FX2 PAD10",XoroTablet:"KidsPAD 701|PAD[ ]?712|PAD[ ]?714|PAD[ ]?716|PAD[ ]?717|PAD[ ]?718|PAD[ ]?720|PAD[ ]?721|PAD[ ]?722|PAD[ ]?790|PAD[ ]?792|PAD[ ]?900|PAD[ ]?9715D|PAD[ ]?9716DR|PAD[ ]?9718DR|PAD[ ]?9719QR|PAD[ ]?9720QR|TelePAD1030|Telepad1032|TelePAD730|TelePAD731|TelePAD732|TelePAD735Q|TelePAD830|TelePAD9730|TelePAD795|MegaPAD 1331|MegaPAD 1851|MegaPAD 2151",ViewsonicTablet:"ViewPad 10pi|ViewPad 10e|ViewPad 10s|ViewPad E72|ViewPad7|ViewPad E100|ViewPad 7e|ViewSonic VB733|VB100a",VerizonTablet:"QTAQZ3|QTAIR7|QTAQTZ3|QTASUN1|QTASUN2|QTAXIA1",OdysTablet:"LOOX|XENO10|ODYS[ -](Space|EVO|Xpress|NOON)|\\bXELIO\\b|Xelio10Pro|XELIO7PHONETAB|XELIO10EXTREME|XELIOPT2|NEO_QUAD10",CaptivaTablet:"CAPTIVA PAD",IconbitTablet:"NetTAB|NT-3702|NT-3702S|NT-3702S|NT-3603P|NT-3603P|NT-0704S|NT-0704S|NT-3805C|NT-3805C|NT-0806C|NT-0806C|NT-0909T|NT-0909T|NT-0907S|NT-0907S|NT-0902S|NT-0902S",TeclastTablet:"T98 4G|\\bP80\\b|\\bX90HD\\b|X98 Air|X98 Air 3G|\\bX89\\b|P80 3G|\\bX80h\\b|P98 Air|\\bX89HD\\b|P98 3G|\\bP90HD\\b|P89 3G|X98 3G|\\bP70h\\b|P79HD 3G|G18d 3G|\\bP79HD\\b|\\bP89s\\b|\\bA88\\b|\\bP10HD\\b|\\bP19HD\\b|G18 3G|\\bP78HD\\b|\\bA78\\b|\\bP75\\b|G17s 3G|G17h 3G|\\bP85t\\b|\\bP90\\b|\\bP11\\b|\\bP98t\\b|\\bP98HD\\b|\\bG18d\\b|\\bP85s\\b|\\bP11HD\\b|\\bP88s\\b|\\bA80HD\\b|\\bA80se\\b|\\bA10h\\b|\\bP89\\b|\\bP78s\\b|\\bG18\\b|\\bP85\\b|\\bA70h\\b|\\bA70\\b|\\bG17\\b|\\bP18\\b|\\bA80s\\b|\\bA11s\\b|\\bP88HD\\b|\\bA80h\\b|\\bP76s\\b|\\bP76h\\b|\\bP98\\b|\\bA10HD\\b|\\bP78\\b|\\bP88\\b|\\bA11\\b|\\bA10t\\b|\\bP76a\\b|\\bP76t\\b|\\bP76e\\b|\\bP85HD\\b|\\bP85a\\b|\\bP86\\b|\\bP75HD\\b|\\bP76v\\b|\\bA12\\b|\\bP75a\\b|\\bA15\\b|\\bP76Ti\\b|\\bP81HD\\b|\\bA10\\b|\\bT760VE\\b|\\bT720HD\\b|\\bP76\\b|\\bP73\\b|\\bP71\\b|\\bP72\\b|\\bT720SE\\b|\\bC520Ti\\b|\\bT760\\b|\\bT720VE\\b|T720-3GE|T720-WiFi",OndaTablet:"\\b(V975i|Vi30|VX530|V701|Vi60|V701s|Vi50|V801s|V719|Vx610w|VX610W|V819i|Vi10|VX580W|Vi10|V711s|V813|V811|V820w|V820|Vi20|V711|VI30W|V712|V891w|V972|V819w|V820w|Vi60|V820w|V711|V813s|V801|V819|V975s|V801|V819|V819|V818|V811|V712|V975m|V101w|V961w|V812|V818|V971|V971s|V919|V989|V116w|V102w|V973|Vi40)\\b[\\s]+|V10 \\b4G\\b",JaytechTablet:"TPC-PA762",BlaupunktTablet:"Endeavour 800NG|Endeavour 1010",DigmaTablet:"\\b(iDx10|iDx9|iDx8|iDx7|iDxD7|iDxD8|iDsQ8|iDsQ7|iDsQ8|iDsD10|iDnD7|3TS804H|iDsQ11|iDj7|iDs10)\\b",EvolioTablet:"ARIA_Mini_wifi|Aria[ _]Mini|Evolio X10|Evolio X7|Evolio X8|\\bEvotab\\b|\\bNeura\\b",LavaTablet:"QPAD E704|\\bIvoryS\\b|E-TAB IVORY|\\bE-TAB\\b",AocTablet:"MW0811|MW0812|MW0922|MTK8382|MW1031|MW0831|MW0821|MW0931|MW0712",MpmanTablet:"MP11 OCTA|MP10 OCTA|MPQC1114|MPQC1004|MPQC994|MPQC974|MPQC973|MPQC804|MPQC784|MPQC780|\\bMPG7\\b|MPDCG75|MPDCG71|MPDC1006|MP101DC|MPDC9000|MPDC905|MPDC706HD|MPDC706|MPDC705|MPDC110|MPDC100|MPDC99|MPDC97|MPDC88|MPDC8|MPDC77|MP709|MID701|MID711|MID170|MPDC703|MPQC1010",CelkonTablet:"CT695|CT888|CT[\\s]?910|CT7 Tab|CT9 Tab|CT3 Tab|CT2 Tab|CT1 Tab|C820|C720|\\bCT-1\\b",WolderTablet:"miTab \\b(DIAMOND|SPACE|BROOKLYN|NEO|FLY|MANHATTAN|FUNK|EVOLUTION|SKY|GOCAR|IRON|GENIUS|POP|MINT|EPSILON|BROADWAY|JUMP|HOP|LEGEND|NEW AGE|LINE|ADVANCE|FEEL|FOLLOW|LIKE|LINK|LIVE|THINK|FREEDOM|CHICAGO|CLEVELAND|BALTIMORE-GH|IOWA|BOSTON|SEATTLE|PHOENIX|DALLAS|IN 101|MasterChef)\\b",MediacomTablet:"M-MPI10C3G|M-SP10EG|M-SP10EGP|M-SP10HXAH|M-SP7HXAH|M-SP10HXBH|M-SP8HXAH|M-SP8MXA",MiTablet:"\\bMI PAD\\b|\\bHM NOTE 1W\\b",NibiruTablet:"Nibiru M1|Nibiru Jupiter One",NexoTablet:"NEXO NOVA|NEXO 10|NEXO AVIO|NEXO FREE|NEXO GO|NEXO EVO|NEXO 3G|NEXO SMART|NEXO KIDDO|NEXO MOBI",LeaderTablet:"TBLT10Q|TBLT10I|TBL-10WDKB|TBL-10WDKBO2013|TBL-W230V2|TBL-W450|TBL-W500|SV572|TBLT7I|TBA-AC7-8G|TBLT79|TBL-8W16|TBL-10W32|TBL-10WKB|TBL-W100",UbislateTablet:"UbiSlate[\\s]?7C",PocketBookTablet:"Pocketbook",KocasoTablet:"\\b(TB-1207)\\b",HisenseTablet:"\\b(F5281|E2371)\\b",Hudl:"Hudl HT7S3|Hudl 2",TelstraTablet:"T-Hub2",GenericTablet:"Android.*\\b97D\\b|Tablet(?!.*PC)|BNTV250A|MID-WCDMA|LogicPD Zoom2|\\bA7EB\\b|CatNova8|A1_07|CT704|CT1002|\\bM721\\b|rk30sdk|\\bEVOTAB\\b|M758A|ET904|ALUMIUM10|Smartfren Tab|Endeavour 1010|Tablet-PC-4|Tagi Tab|\\bM6pro\\b|CT1020W|arc 10HD|\\bTP750\\b|\\bQTAQZ3\\b|WVT101|TM1088|KT107"},oss:{AndroidOS:"Android",BlackBerryOS:"blackberry|\\bBB10\\b|rim tablet os",PalmOS:"PalmOS|avantgo|blazer|elaine|hiptop|palm|plucker|xiino",SymbianOS:"Symbian|SymbOS|Series60|Series40|SYB-[0-9]+|\\bS60\\b",WindowsMobileOS:"Windows CE.*(PPC|Smartphone|Mobile|[0-9]{3}x[0-9]{3})|Windows Mobile|Windows Phone [0-9.]+|WCE;",WindowsPhoneOS:"Windows Phone 10.0|Windows Phone 8.1|Windows Phone 8.0|Windows Phone OS|XBLWP7|ZuneWP7|Windows NT 6.[23]; ARM;",iOS:"\\biPhone.*Mobile|\\biPod|\\biPad|AppleCoreMedia",iPadOS:"CPU OS 13",SailfishOS:"Sailfish",MeeGoOS:"MeeGo",MaemoOS:"Maemo",JavaOS:"J2ME/|\\bMIDP\\b|\\bCLDC\\b",webOS:"webOS|hpwOS",badaOS:"\\bBada\\b",BREWOS:"BREW"},uas:{Chrome:"\\bCrMo\\b|CriOS|Android.*Chrome/[.0-9]* (Mobile)?",Dolfin:"\\bDolfin\\b",Opera:"Opera.*Mini|Opera.*Mobi|Android.*Opera|Mobile.*OPR/[0-9.]+$|Coast/[0-9.]+",Skyfire:"Skyfire",Edge:"\\bEdgiOS\\b|Mobile Safari/[.0-9]* Edge",IE:"IEMobile|MSIEMobile",Firefox:"fennec|firefox.*maemo|(Mobile|Tablet).*Firefox|Firefox.*Mobile|FxiOS",Bolt:"bolt",TeaShark:"teashark",Blazer:"Blazer",Safari:"Version((?!\\bEdgiOS\\b).)*Mobile.*Safari|Safari.*Mobile|MobileSafari",WeChat:"\\bMicroMessenger\\b",UCBrowser:"UC.*Browser|UCWEB",baiduboxapp:"baiduboxapp",baidubrowser:"baidubrowser",DiigoBrowser:"DiigoBrowser",Mercury:"\\bMercury\\b",ObigoBrowser:"Obigo",NetFront:"NF-Browser",GenericBrowser:"NokiaBrowser|OviBrowser|OneBrowser|TwonkyBeamBrowser|SEMC.*Browser|FlyFlow|Minimo|NetFront|Novarra-Vision|MQQBrowser|MicroMessenger",PaleMoon:"Android.*PaleMoon|Mobile.*PaleMoon"},props:{Mobile:"Mobile/[VER]",Build:"Build/[VER]",Version:"Version/[VER]",VendorID:"VendorID/[VER]",iPad:"iPad.*CPU[a-z ]+[VER]",iPhone:"iPhone.*CPU[a-z ]+[VER]",iPod:"iPod.*CPU[a-z ]+[VER]",Kindle:"Kindle/[VER]",Chrome:["Chrome/[VER]","CriOS/[VER]","CrMo/[VER]"],Coast:["Coast/[VER]"],Dolfin:"Dolfin/[VER]",Firefox:["Firefox/[VER]","FxiOS/[VER]"],Fennec:"Fennec/[VER]",Edge:"Edge/[VER]",IE:["IEMobile/[VER];","IEMobile [VER]","MSIE [VER];","Trident/[0-9.]+;.*rv:[VER]"],NetFront:"NetFront/[VER]",NokiaBrowser:"NokiaBrowser/[VER]",Opera:[" OPR/[VER]","Opera Mini/[VER]","Version/[VER]"],"Opera Mini":"Opera Mini/[VER]","Opera Mobi":"Version/[VER]",UCBrowser:["UCWEB[VER]","UC.*Browser/[VER]"],MQQBrowser:"MQQBrowser/[VER]",MicroMessenger:"MicroMessenger/[VER]",baiduboxapp:"baiduboxapp/[VER]",baidubrowser:"baidubrowser/[VER]",SamsungBrowser:"SamsungBrowser/[VER]",Iron:"Iron/[VER]",Safari:["Version/[VER]","Safari/[VER]"],Skyfire:"Skyfire/[VER]",Tizen:"Tizen/[VER]",Webkit:"webkit[ /][VER]",PaleMoon:"PaleMoon/[VER]",SailfishBrowser:"SailfishBrowser/[VER]",Gecko:"Gecko/[VER]",Trident:"Trident/[VER]",Presto:"Presto/[VER]",Goanna:"Goanna/[VER]",iOS:" \\bi?OS\\b [VER][ ;]{1}",Android:"Android [VER]",Sailfish:"Sailfish [VER]",BlackBerry:["BlackBerry[\\w]+/[VER]","BlackBerry.*Version/[VER]","Version/[VER]"],BREW:"BREW [VER]",Java:"Java/[VER]","Windows Phone OS":["Windows Phone OS [VER]","Windows Phone [VER]"],"Windows Phone":"Windows Phone [VER]","Windows CE":"Windows CE/[VER]","Windows NT":"Windows NT [VER]",Symbian:["SymbianOS/[VER]","Symbian/[VER]"],webOS:["webOS/[VER]","hpwOS/[VER];"]},utils:{Bot:"Googlebot|facebookexternalhit|Google-AMPHTML|s~amp-validator|AdsBot-Google|Google Keyword Suggestion|Facebot|YandexBot|YandexMobileBot|bingbot|ia_archiver|AhrefsBot|Ezooms|GSLFbot|WBSearchBot|Twitterbot|TweetmemeBot|Twikle|PaperLiBot|Wotbox|UnwindFetchor|Exabot|MJ12bot|YandexImages|TurnitinBot|Pingdom|contentkingapp|AspiegelBot",MobileBot:"Googlebot-Mobile|AdsBot-Google-Mobile|YahooSeeker/M1A1-R2D2",DesktopMode:"WPDesktop",TV:"SonyDTV|HbbTV",WebKit:"(webkit)[ /]([\\w.]+)",Console:"\\b(Nintendo|Nintendo WiiU|Nintendo 3DS|Nintendo Switch|PLAYSTATION|Xbox)\\b",Watch:"SM-V700"}},detectMobileBrowsers:{fullPattern:/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,shortPattern:/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,tabletPattern:/android|ipad|playbook|silk/i}},n=Object.prototype.hasOwnProperty;function r(e,t){return null!=e&&null!=t&&e.toLowerCase()===t.toLowerCase()}function i(e,t){var n,r,i=e.length;if(!i||!t)return!1;for(n=t.toLowerCase(),r=0;r<i;++r)if(n===e[r].toLowerCase())return!0;return!1}function a(e){for(var t in e)n.call(e,t)&&(e[t]=new RegExp(e[t],"i"))}function o(e,t){this.ua=function(e){return(e||"").substr(0,500)}(e),this._cache={},this.maxPhoneWidth=t||600}return t.FALLBACK_PHONE="UnknownPhone",t.FALLBACK_TABLET="UnknownTablet",t.FALLBACK_MOBILE="UnknownMobile",e="isArray"in Array?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},function(){var r,i,o,s,c,l,u=t.mobileDetectRules;for(r in u.props)if(n.call(u.props,r)){for(i=u.props[r],e(i)||(i=[i]),c=i.length,s=0;s<c;++s)(l=(o=i[s]).indexOf("[VER]"))>=0&&(o=o.substring(0,l)+"([\\w._\\+]+)"+o.substring(l+5)),i[s]=new RegExp(o,"i");u.props[r]=i}a(u.oss),a(u.phones),a(u.tablets),a(u.uas),a(u.utils),u.oss0={WindowsPhoneOS:u.oss.WindowsPhoneOS,WindowsMobileOS:u.oss.WindowsMobileOS}}(),t.findMatch=function(e,t){for(var r in e)if(n.call(e,r)&&e[r].test(t))return r;return null},t.findMatches=function(e,t){var r=[];for(var i in e)n.call(e,i)&&e[i].test(t)&&r.push(i);return r},t.getVersionStr=function(e,r){var i,a,o,s,c=t.mobileDetectRules.props;if(n.call(c,e))for(o=(i=c[e]).length,a=0;a<o;++a)if(null!==(s=i[a].exec(r)))return s[1];return null},t.getVersion=function(e,n){var r=t.getVersionStr(e,n);return r?t.prepareVersionNo(r):NaN},t.prepareVersionNo=function(e){var t;return 1===(t=e.split(/[a-z._ \/\-]/i)).length&&(e=t[0]),t.length>1&&(e=t[0]+".",t.shift(),e+=t.join("")),Number(e)},t.isMobileFallback=function(e){return t.detectMobileBrowsers.fullPattern.test(e)||t.detectMobileBrowsers.shortPattern.test(e.substr(0,4))},t.isTabletFallback=function(e){return t.detectMobileBrowsers.tabletPattern.test(e)},t.prepareDetectionCache=function(e,n,r){if(e.mobile===p){var i,a,s;if(a=t.findMatch(t.mobileDetectRules.tablets,n))return e.mobile=e.tablet=a,void(e.phone=null);if(i=t.findMatch(t.mobileDetectRules.phones,n))return e.mobile=e.phone=i,void(e.tablet=null);t.isMobileFallback(n)?(s=o.isPhoneSized(r))===p?(e.mobile=t.FALLBACK_MOBILE,e.tablet=e.phone=null):s?(e.mobile=e.phone=t.FALLBACK_PHONE,e.tablet=null):(e.mobile=e.tablet=t.FALLBACK_TABLET,e.phone=null):t.isTabletFallback(n)?(e.mobile=e.tablet=t.FALLBACK_TABLET,e.phone=null):e.mobile=e.tablet=e.phone=null}},t.mobileGrade=function(e){var t=null!==e.mobile();return e.os("iOS")&&e.version("iPad")>=4.3||e.os("iOS")&&e.version("iPhone")>=3.1||e.os("iOS")&&e.version("iPod")>=3.1||e.version("Android")>2.1&&e.is("Webkit")||e.version("Windows Phone OS")>=7||e.is("BlackBerry")&&e.version("BlackBerry")>=6||e.match("Playbook.*Tablet")||e.version("webOS")>=1.4&&e.match("Palm|Pre|Pixi")||e.match("hp.*TouchPad")||e.is("Firefox")&&e.version("Firefox")>=12||e.is("Chrome")&&e.is("AndroidOS")&&e.version("Android")>=4||e.is("Skyfire")&&e.version("Skyfire")>=4.1&&e.is("AndroidOS")&&e.version("Android")>=2.3||e.is("Opera")&&e.version("Opera Mobi")>11&&e.is("AndroidOS")||e.is("MeeGoOS")||e.is("Tizen")||e.is("Dolfin")&&e.version("Bada")>=2||(e.is("UC Browser")||e.is("Dolfin"))&&e.version("Android")>=2.3||e.match("Kindle Fire")||e.is("Kindle")&&e.version("Kindle")>=3||e.is("AndroidOS")&&e.is("NookTablet")||e.version("Chrome")>=11&&!t||e.version("Safari")>=5&&!t||e.version("Firefox")>=4&&!t||e.version("MSIE")>=7&&!t||e.version("Opera")>=10&&!t?"A":e.os("iOS")&&e.version("iPad")<4.3||e.os("iOS")&&e.version("iPhone")<3.1||e.os("iOS")&&e.version("iPod")<3.1||e.is("Blackberry")&&e.version("BlackBerry")>=5&&e.version("BlackBerry")<6||e.version("Opera Mini")>=5&&e.version("Opera Mini")<=6.5&&(e.version("Android")>=2.3||e.is("iOS"))||e.match("NokiaN8|NokiaC7|N97.*Series60|Symbian/3")||e.version("Opera Mobi")>=11&&e.is("SymbianOS")?"B":(e.version("BlackBerry")<5||e.match("MSIEMobile|Windows CE.*Mobile")||e.version("Windows Mobile"),"C")},t.detectOS=function(e){return t.findMatch(t.mobileDetectRules.oss0,e)||t.findMatch(t.mobileDetectRules.oss,e)},t.getDeviceSmallerSide=function(){return window.screen.width<window.screen.height?window.screen.width:window.screen.height},o.prototype={constructor:o,mobile:function(){return t.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.mobile},phone:function(){return t.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.phone},tablet:function(){return t.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.tablet},userAgent:function(){return this._cache.userAgent===p&&(this._cache.userAgent=t.findMatch(t.mobileDetectRules.uas,this.ua)),this._cache.userAgent},userAgents:function(){return this._cache.userAgents===p&&(this._cache.userAgents=t.findMatches(t.mobileDetectRules.uas,this.ua)),this._cache.userAgents},os:function(){return this._cache.os===p&&(this._cache.os=t.detectOS(this.ua)),this._cache.os},version:function(e){return t.getVersion(e,this.ua)},versionStr:function(e){return t.getVersionStr(e,this.ua)},is:function(e){return i(this.userAgents(),e)||r(e,this.os())||r(e,this.phone())||r(e,this.tablet())||i(t.findMatches(t.mobileDetectRules.utils,this.ua),e)},match:function(e){return e instanceof RegExp||(e=new RegExp(e,"i")),e.test(this.ua)},isPhoneSized:function(e){return o.isPhoneSized(e||this.maxPhoneWidth)},mobileGrade:function(){return this._cache.grade===p&&(this._cache.grade=t.mobileGrade(this)),this._cache.grade}},"undefined"!=typeof window&&window.screen?o.isPhoneSized=function(e){return e<0?p:t.getDeviceSmallerSide()<=e}:o.isPhoneSized=function(){},o._impl=t,o.version="1.4.5 2021-03-13",o}));var T=d(g.exports),m=5120,v=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],S=["www.baidu.","m.baidu.","m.sm.cn","so.com","sogou.com","youdao.com","google.","yahoo.com/","bing.com/","ask.com/"],b=["weibo.com","renren.com","kaixin001.com","douban.com","qzone.qq.com","zhihu.com","tieba.baidu.com","weixin.qq.com"],y={baidu:["wd","word","kw","keyword"],google:"q",bing:"q",yahoo:"p",sogou:["query","keyword"],so:"q",sm:"q"},P="hinasdk_domain_test",A=Array.prototype.slice,k=Object.prototype.toString,M=Array.prototype.forEach,E=Object.prototype.hasOwnProperty,H=window,C=H.location,G=H.screen,w=H.localStorage,B=H.history,I=H.navigator,D=function(){return i((function e(){n(this,e)}),null,[{key:"log",value:function(){if(!this.showLog)return!1;if("object"===("undefined"==typeof console?"undefined":u(console))&&console.log)try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}}},{key:"error",value:function(){if("object"===("undefined"==typeof console?"undefined":u(console))&&console.error)try{return console.error.apply(console,arguments)}catch(e){console.error(arguments[0])}}}])}(),O=function(){return i((function e(){n(this,e)}),null,[{key:"checkProtocolIsSame",value:function(e,t){try{if(x.URL(e).protocol!==x.URL(t).protocol)return!1}catch(e){return D.log("The _.URL method is not supported"),!1}return!0}},{key:"checkServerUrl",value:function(){return x.check.isString(this.serverUrl)&&""!==x.trim(this.serverUrl)?(x.check.isString(this.serverUrl)&&""!==this.serverUrl&&!this.checkProtocolIsSame(this.serverUrl,C.href)&&D.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。\n因为：1、https 下面发送 http 的图片请求会失败。2、http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。"),!0):(D.log("当前 serverUrl 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 serverUrl！"),!1)}},{key:"checkAjax",value:function(e){if(e===this.serverUrl)return!1;x.check.isString(e)&&""!==e&&!this.checkProtocolIsSame(e,C.href)&&D.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。因为 http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。")}}])}(),x={MAX_REFERRER_STRING_LENGTH:2e3};x.MAX_STRING_LENGTH=m,x.PV_LIB_VERSION="3.1.8",x.EPM_LIB_VERSION="1.0.0",x.utmTypes=v,x.searchTypes=S,x.socialTypes=b,x.searchKeywords=y,x.each=function(e,t,n){if(null!==e)if(M&&e.forEach===M)e.forEach(t,n);else if(e.length===+e.length){for(var r=0,i=e.length;r<i;r++)if(r in e&&!1===t.call(n,e[r],r,e))return}else for(var a in e)if(E.call(e,a)&&!1===t.call(n,e[a],a,e))return},x.map=function(e,t){var n=[];return null==e?n:Array.prototype.map&&e.map===Array.prototype.map?e.map(t):(x.each(e,(function(e,r,i){n.push(t(e,r,i))})),n)},x.extend=function(e){return x.each(A.call(arguments,1),(function(t){for(var n in t)void 0!==t[n]&&((x.check.isString(t[n])||x.check.isDate(t[n]))&&x.transformUTCTime(t[n])?e[n]=x.transformUTCTime(t[n]):e[n]=t[n])})),e},x.transformUTCTime=function(e){if(x.check.isDate(e)||null!=e&&e.includes("GMT")||/^\d{4}-\d{2}-\d{2}(?: |T)\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(e)){var t=new Date(e);return isNaN(t)?(D.log("Invalid date string: "+e),e):(t.setHours(t.getHours()-t.getTimezoneOffset()/60),t.toISOString().replace("T"," ").replace("Z"," ").slice(0,-5))}return!1},x.indexOf=function(e,t){var n=e.indexOf;if(n)return n.call(e,t);for(var r=0;r<e.length;r++)if(t===e[r])return r;return-1},x.trim=function(e){return e.replace(/(^[\s\uFEFF\xA0]+)|([\s\uFEFF\xA0]+$)/g,"")},x.arrayify=function(e){return Array.isArray(e)?e:[e]},x.isNil=function(e){return null==e},x.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+function(e){return e<10?"00"+e:e<100?"0"+e:e}(e.getMilliseconds())},x.formatTimeZone=function(e,t){if("number"!=typeof t)return e;var n=e.getTime(),r=6e4*e.getTimezoneOffset();return new Date(n+r+36e5*t)},x.formatJsonString=function(e){try{return JSON.stringify(e,null,8)}catch(t){return JSON.stringify(e)}},x.searchObjDate=function(e,t){(x.check.isObject(e)||x.check.isArray(e))&&x.each(e,(function(n,r){x.check.isObject(n)||x.check.isArray(n)?x.searchObjDate(e[r],t):x.check.isDate(n)&&(e[r]=x.formatDate(x.formatTimeZone(n,t)))}))},x.paramType=function(e){return Object.prototype.toString.call(e).replace("[object ","").replace("]","")},x.check={cutString:function(e,t){return x.check.isString(e)&&e.length>t?e.substring(0,t):e},isUndefined:function(e){return void 0===e},isObject:function(e){return"[object Object]"===k.call(e)&&null!==e},isEmptyObject:function(e){if(x.check.isObject(e)){for(var t in e)if(E.call(e,t))return!1;return!0}return!1},isArray:function(e){return"[object Array]"===k.call(e)},isString:function(e){return"[object String]"===k.call(e)},isDate:function(e){return"[object Date]"===k.call(e)},isNumber:function(e){return"[object Number]"===k.call(e)},isBoolean:function(e){return"[object Boolean]"===k.call(e)},isFunction:function(e){if(!e)return!1;var t=Object.prototype.toString.call(e);return"[object Function]"===t||"[object AsyncFunction]"===t},isJSONString:function(e){try{JSON.parse(e)}catch(e){return!1}return!0},isElement:function(e){return!(!e||1!==e.nodeType)}},x.checkUrlIsRegexp=function(e,t){var n;try{n=x.URL(C.href)}catch(e){return console.error("URL 解析失败",e),!1}try{return(t?new RegExp(e,t):new RegExp(e)).test(n.href)}catch(e){return console.error("创建或执行正则表达式时发生异常",e),!1}},x.UUID=function(){var e=function(){for(var e=1*new Date,t=0;e===1*new Date;)t++;return e.toString(16)+t.toString(16)};return function(){var t=String(G.height*G.width);t=t&&/\d{5,}/.test(t)?t.toString(16):String(31242*Math.random()).replace(".","").slice(0,8);var n=e()+"-"+Math.random().toString(16).replace(".","")+"-"+function(){var e,t,n=I.userAgent,r=[],i=0;function a(e,t){var n,i=0;for(n=0;n<t.length;n++)i|=r[n]<<8*n;return e^i}for(e=0;e<n.length;e++)t=n.charCodeAt(e),r.unshift(255&t),r.length>=4&&(i=a(i,r),r=[]);return r.length>0&&(i=a(i,r)),i.toString(16)}()+"-"+t+"-"+e();return n||(String(Math.random())+String(Math.random())+String(Math.random())).slice(2,15)}}(),x.getReferrer=function(e){var t=e||document.referrer;return"string"!=typeof t?"referrer exception"+String(t):(0===t.indexOf("https://www.baidu.com/")&&(t=t.split("?")[0]),"string"==typeof(t=t.slice(0,2e3))?t:"")},x.getCookielDomain=function(e){e=e||C.hostname,x.check.isString(e)&&e.match(/^[a-zA-Z0-9\u4e00-\u9fa5\-\.]+$/)||(e="");var t=e.split(".");if(x.check.isArray(t)&&t.length>=2&&!/^(\d+\.)+\d+$/.test(e))for(var n="."+t.splice(t.length-1,1);t.length>0;)if(n="."+t.splice(t.length-1,1)+n,document.cookie=P+"=true; path=/; domain="+n,-1!==document.cookie.indexOf(P+"=true")){var r=new Date;return r.setTime(r.getTime()-1e3),document.cookie=P+"=true; expires="+r.toGMTString()+"; path=/; SameSite=Lax; domain="+n,n}return""},x.getCurrentDomain=function(e){var t=x.getCookielDomain();return""===e||""===t?"url解析失败":t},x.hashCode=function(e){if("string"!=typeof e)return 0;var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return t},x.base64Decode=function(e){var t="";try{t=decodeURIComponent(escape(atob(e)))}catch(n){t=e}return t},x.base64Encode=function(e){try{return btoa(unescape(encodeURIComponent(e)))}catch(t){return e}},x.decodeURIComponent=function(e){var t="";try{t=decodeURIComponent(e)}catch(n){t=e}return t},x.encodeURIComponent=function(e){var t="";try{t=encodeURIComponent(e)}catch(n){t=e}return t},x.cookie={get:function(e){for(var t=e+"=",n=document.cookie.split(";"),r=0;r<n.length;r++){for(var i=n[r];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(t))return x.decodeURIComponent(i.substring(t.length,i.length))}return null},set:function(e,t,n,r,i,a,o){var s=o,c="",l="",u="";if(0!==(n=null==n?73e3:n)){var h=new Date;"s"===String(n).slice(-1)?h.setTime(h.getTime()+1e3*Number(String(n).slice(0,-1))):h.setTime(h.getTime()+24*n*60*60*1e3),c="; expires="+h.toGMTString()}function d(e){return!!e&&e.replace(/\r\n/g,"")}x.check.isString(i)&&""!==i&&(u="; SameSite="+i),a&&(l="; secure");var f="",p="",g="";e&&(f=d(e)),p=t&&x.check.isString(t)?d(t):t,s&&(g=d(s)),f&&p&&(document.cookie=f+"="+encodeURIComponent(p)+c+"; path=/"+g+u+l)},setDomain:function(e,t,n,r){var i="";if(r=!!x.check.isUndefined(r)||r){var a=x.getCurrentDomain(C.href);"url解析失败"===a&&(a=""),i=a?"; domain="+a:""}return this.set(e,t,n,r,null,null,i)},remove:function(e,t){this.set(e,"1",-1,t)},isSupport:function(e,t){e=e||"cookie_support_test",t=t||"1";var n=this;return I.cookieEnabled&&(n.set(e,t),n.get(e)===t&&(n.remove(e),!0))}},x.localStorage={get:function(e){return w.getItem(e)},parse:function(e){var t;try{t=JSON.parse(x.localStorage.get(e))||null}catch(e){D.log("parse localStorage failed")}return t},set:function(e,t){try{w.setItem(e,t)}catch(e){D.log("localStorage is not support")}},remove:function(e){w.removeItem(e)},isSupport:function(){var e=!0;try{var t="__localStorageSupport__",n="testIsSupportStorage";x.localStorage.set(t,n),x.localStorage.get(t)!==n&&(e=!1),x.localStorage.remove(t)}catch(t){e=!1}return e},key:function(e){return w.key(e)},length:w.length},x.memory={data:{},get:function(e){var t=this.data[e];return x.check.isUndefined(t)?null:x.check.isUndefined(t.expireTime)?t:t.expireTime<x.now()?null:t.value},set:function(e,t,n){if(n){var r,i=x.now();r="s"===String(n).slice(-1)?i+1e3*Number(String(n).slice(0,-1)):i+24*n*60*60*1e3,this.data[e]={value:t,expireTime:r}}this.data[e]=t},setDomain:function(e,t,n){this.set(e,t,n)}},x.now=function(){return Date.now&&x.check.isFunction(Date.now)?Date.now():(new Date).getTime()},x.getRandom=function(){return(new Date).getTime()+"_"+Math.floor(1e6*Math.random())},x.get32RandomString=function(){for(var e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",t="",n=0;n<32;n++){t+=e[Math.floor(62*Math.random())]}return t},x.safeJSONParse=function(e){var t=null;try{t=JSON.parse(e)}catch(t){return e}return t},x.saveObjectVal=function(e,t,n){x.check.isString(t)||(t=JSON.stringify(t)),x.localStorage.set(e,t)},x.readObjectVal=function(e,t){var n=x.localStorage.get(e);return n?x.safeJSONParse(n):null},x.stripEmptyProperties=function(e){var t={};return x.each(e,(function(e,n){x.check.isString(e)&&e.length>0&&(t[n]=e)})),t},x.info={os:function(){var e=I.userAgent;return/Windows/i.test(e)?/Phone/.test(e)||/WPDesktop/.test(e)?"Windows Phone":"Windows":/(iPhone|iPad|iPod)/.test(e)&&!window.MSStream?"iOS":/Android/.test(e)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(e)?"BlackBerry":/Mac/i.test(e)?"Mac OS X":/Linux/.test(e)?"Linux":/CrOS/.test(e)?"Chrome OS":"取值异常"},browser:function(){var e={type:"",version:""};try{var t,n=null===(t=I.userAgent)||void 0===t?void 0:t.toLowerCase(),r=[];if(null!==n.match(/baidubrowser/)?(e.type="baidu",r.push(/baidubrowser\/([\d.]+)/)):null!==n.match(/bidubrowser/)?(e.type="baidu",r.push(/bidubrowser\/([\d.]+)/)):null!==n.match(/edg/)?(e.type="edge",r.push(/edg\/([\d.]+)/)):null!==n.match(/edgios/)?(e.type="edge",r.push(/edgios\/([\d.]+)/)):null!==n.match(/liebaofast/)?(e.type="liebao",r.push(/liebaofast\/([\d.]+)/)):null!==n.match(/sogoumobilebrowser/)?(e.type="sogou",r.push(/sogoumobilebrowser\/([\d.]+)/)):null!==n.match(/lbbrowser/)?(e.type="liebao",r.push(/lbbrowser\/([\d.]+)/)):null!==n.match(/crios/)?(e.type="chrome",r.push(/crios\/([\d.]+)/)):null!==n.match(/qihoobrowser/)?(e.type="360",r.push(/qihoobrowser\/([\d.]+)/)):null!==n.match(/mxios/)?(e.type="maxthon",r.push(/mxios\/([\d.]+)/)):null!==n.match(/fxios/)?(e.type="firefox",r.push(/fxios\/([\d.\w]+)/)):null!==n.match(/edge/)?(e.type="edge",r.push(/edge\/([\d.]+)/)):null!==n.match(/metasr/)?(e.type="sogou",r.push(/metasr ([\d.]+)/)):null!==n.match(/micromessenger/)?(e.type="micromessenger",r.push(/micromessenger\/([\d.]+)/)):null!==n.match(/mqqbrowser/)?(e.type="qq",r.push(/mqqbrowser\/([\d.]+)/)):null!==n.match(/qqbrowserlite/)?(e.type="qq",r.push(/qqbrowserlite\/([\d.]+)/)):null!==n.match(/tencenttraveler/)?(e.type="qq",r.push(/tencenttraveler\/([\d.]+)/)):null!==n.match(/qqbrowser/)?(e.type="qq",r.push(/qqbrowser\/([\d.]+)/)):null!==n.match(/maxthon/)?(e.type="maxthon",r.push(/maxthon\/([\d.]+)/)):null!==n.match(/ubrowser/)?(e.type="uc",r.push(/ubrowser\/([\d.]+)/)):null!==n.match(/ucbrowser/)?(e.type="uc",r.push(/ucbrowser\/([\d.]+)/)):null!==n.match(/firefox/)?(e.type="firefox",r.push(/firefox\/([\d.]+)/)):null!==n.match(/opera/)?(e.type="opera",r.push(/opera\/([\d.]+)/)):null!==n.match(/opr/)?(e.type="opera",r.push(/opr\/([\d.]+)/)):null!==n.match(/chrome/)?(e.type="chrome",r.push(/chrome\/([\d.]+)/)):null!==n.match(/safari/)?(e.type="safari",r.push(/version\/([\d.]+)/)):null===n.match(/trident/)&&null===n.match(/msie/)||(e.type="ie"),"ie"===e.type){var i=n.match(/trident\/([\d.]+)/)?n.match(/trident\/([\d.]+)/)[1]:"",a=n.match(/msie ([\d.]+)/)?n.match(/msie ([\d.]+)/)[1]:"";""!==i?e.version=String(parseInt(i)+4):""!==a&&(e.version=a)}else r&&(e.version=n.match(r[0])?n.match(r[0])[1]:"")}catch(t){e.type="取值异常",D.log("getting browser info failed due to ",t)}return e},modelInfo:function(){var e=new T(I.userAgent),t=e.os();if("iOS"===t)return e.mobile();if("AndroidOS"===t)return e.mobile();var n=I.userAgent.match(/Mac/);if(n)return n[0];var r=I.userAgent.match(/Windows/);if(r)return r[0];var i=I.userAgent.match(/Linux/);return i?i[0]:"取值异常"},osVersion:function(){var e=I.userAgent;if(/Windows/i.test(e)){var t=/Windows NT ([\d.]+)/;return e.match(t)?e.match(t)[1]:""}if(/(iPhone|iPad|iPod)/.test(e)&&!window.MSStream){var n=/OS ([\d_]+)/;return e.match(n)?e.match(n)[1].replace(/_/g,"."):""}if(/Android/.test(e)){var r=/Android ([\d.]+)/;return e.match(r)?e.match(r)[1]:""}if(/Mac/i.test(e)){var i=/Mac OS X ([\d_]+)/;return e.match(i)?e.match(i)[1].replace(/_/g,"."):""}return""},properties:function(e){var t,n=x.info.browser();return x.extend({H_os:x.info.os(),H_os_version:x.info.osVersion(),H_lib_version:x.PV_LIB_VERSION,H_lib:"js",H_lib_method:"code",H_screen_height:Number(G.height)||0,H_screen_width:Number(G.width)||0,H_browser:n.type,H_browser_version:n.version,H_network_type:x.info.networkType(),H_language:x.check.isString(I.language)?null===(t=I.language)||void 0===t?void 0:t.toLowerCase():"取值异常",H_model:x.info.modelInfo()},e)},epmProperties:function(){return this.properties({H_lib_version:x.EPM_LIB_VERSION,H_url:C.href,H_title:document.title})},pageProperties:function(){var e=x.getReferrer(),t=x.getCurrentDomain(C.href);return x.stripEmptyProperties({H_referrer:e,H_referrer_host:e?x.getHostname(e):"",H_url:C.href,H_url_host:x.getHostname(C.href,"url_host取值异常"),H_url_domain:t,H_url_path:C.pathname,H_url_hash:C.hash,H_title:document.title})},getElementInfo:function(e,t){var n;if(!x.check.isElement(e))return{};var r=null===(n=e.tagName)||void 0===n?void 0:n.toLowerCase(),i={H_element_type:r,H_element_name:e.getAttribute("name"),H_element_id:e.getAttribute("id"),H_element_target_url:e.getAttribute("href"),H_element_class_name:x.check.isString(e.className)?e.className:null,H_element_content:x.getElementContent(e,r,t)};return x.stripEmptyProperties(i)},networkType:function(){if(void 0===I.connection)return"unknown";var e=I.connection;return e.effectiveType?e.effectiveType:e.type?e.type:"取值异常"}},x.getElementContent=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("input"===t)return(["button","submit"].includes(e.type)||n)&&e.value||"";var r="";return e.textContent?r=x.trim(e.textContent):e.innerText&&(r=x.trim(e.innerText)),r&&(r=r.replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)),r||""},x.getHostname=function(e,t){if(t&&"string"==typeof t||(t="hostname解析异常"),!e)return t;var n=null;try{n=x.URL(e).hostname}catch(e){D.log("getHostname传入的url参数不合法！")}return n||t},x.isReferralTraffic=function(e){return""===(e=e||document.referrer)||x.getCookielDomain(x.getHostname(e))!==x.getCookielDomain()},x.getUtm=function(){var e={};return x.each(v,(function(t){var n=x.getQueryParam(C.href,t);n.length&&(e[t]=n)})),e},x.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=x.decodeURIComponent(e);var n=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===n||n&&"string"!=typeof n[1]&&n[1].length?"":x.decodeURIComponent(n[1])};var L=function(){return i((function e(t){n(this,e),this.ele=t}),[{key:"addClass",value:function(e){return-1===(" "+this.ele.className+" ").indexOf(" "+e+" ")&&(this.ele.className=this.ele.className+(""===this.ele.className?"":" ")+e),this}},{key:"removeClass",value:function(e){var t=" "+this.ele.className+" ";return-1!==t.indexOf(" "+e+" ")&&(this.ele.className=t.replace(" "+e+" "," ").slice(1,-1)),this}},{key:"hasClass",value:function(e){return-1!==(" "+this.ele.className+" ").indexOf(" "+e+" ")}},{key:"attr",value:function(e,t){return"string"==typeof e&&x.check.isUndefined(t)?this.ele.getAttribute(e):("string"==typeof e&&(t=String(t),this.ele.setAttribute(e,t)),this)}},{key:"offset",value:function(){var e=this.ele.getBoundingClientRect();if(e.width||e.height){var t=this.ele.ownerDocument.documentElement;return{top:e.top+window.pageYOffset-t.clientTop,left:e.left+window.pageXOffset-t.clientLeft}}return{top:0,left:0}}},{key:"getSize",value:function(){if(!window.getComputedStyle)return{width:this.ele.offsetWidth,height:this.ele.offsetHeight};try{var e=this.ele.getBoundingClientRect();return{width:e.width,height:e.height}}catch(e){return{width:0,height:0}}}},{key:"getStyle",value:function(e){return this.ele.currentStyle?this.ele.currentStyle[e]:this.ele.ownerDocument.defaultView.getComputedStyle(this.ele,null).getPropertyValue(e)}},{key:"wrap",value:function(e){var t=document.createElement(e);return this.ele.parentNode.insertBefore(t,this.ele),t.appendChild(this.ele),x.getDomElementInfo(t)}},{key:"getCssStyle",value:function(e){var t=this.ele.style.getPropertyValue(e);if(t)return t;var n=null;if("function"==typeof window.getMatchedCSSRules&&(n=window.getMatchedCSSRules(this.ele)),!n||!x.check.isArray(n))return null;for(var r=n.length-1;r>=0;r--){if(t=n[r].style.getPropertyValue(e))return t}}},{key:"sibling",value:function(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}},{key:"next",value:function(){return this.sibling(this.ele,"nextSibling")}},{key:"prev",value:function(){return this.sibling(this.ele,"previousSibling")}},{key:"siblingsFn",value:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}},{key:"siblings",value:function(){return this.siblingsFn((this.ele.parentNode||{}).firstChild,this.ele)}},{key:"children",value:function(){return this.siblingsFn(this.ele.firstChild)}},{key:"parent",value:function(){var e=this.ele.parentNode;return e=e&&11!==e.nodeType?e:null,x.getDomElementInfo(e)}},{key:"previousElementSibling",value:function(){var e=this.ele;if("previousElementSibling"in document.documentElement)return x.getDomElementInfo(e.previousElementSibling);for(;e=e.previousSibling;)if(1===e.nodeType)return x.getDomElementInfo(e);return x.getDomElementInfo(null)}},{key:"getSameTypeSiblings",value:function(){for(var e,t=this.ele,n=t.parentNode,r=null===(e=t.tagName)||void 0===e?void 0:e.toLowerCase(),i=[],a=0;a<n.children.length;a++){var o,s=n.children[a];1===s.nodeType&&(null===(o=s.tagName)||void 0===o?void 0:o.toLowerCase())===r&&i.push(n.children[a])}return i}},{key:"getParents",value:function(){try{var e=this.ele;if(!x.check.isElement(e))return[];var t=[e];if(null===e||null===e.parentElement)return[];for(;null!==e.parentElement;)e=e.parentElement,t.push(e);return t}catch(e){return[]}}}])}();x.getDomElementInfo=function(e){return new L(e)},x.addEvent=function(e,t,n,r){function i(e){return e&&(e.preventDefault=i.preventDefault,e.stopPropagation=i.stopPropagation,e._getPath=i._getPath),e}i._getPath=function(){return this.path||x.getDomElementInfo(this.target).getParents()},i.preventDefault=function(){this.returnValue=!1},i.stopPropagation=function(){this.cancelBubble=!0};(function(e,t,n){if(void 0===r&&"click"===t&&(r=!0),e&&e.addEventListener)e.addEventListener(t,(function(e){e._getPath=i._getPath,n.call(this,e)}),r);else{var a="on"+t,o=e[a];e[a]=function(e,t,n,r){var a=function(a){if(a=a||i(window.event)){a.target=a.srcElement;var o,s,c=!0;return"function"==typeof n&&(o=n(a)),s=t.call(e,a),"beforeunload"!==r?(!1!==o&&!1!==s||(c=!1),c):void 0}};return a}(e,n,o,t)}}).apply(null,arguments)},x.addCaptureEvent=function(e,t,n){return this.addEvent(e,t,n,"click"===t)},x.hasCircularReference=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if("object"!==u(e)||null===e)return!1;if(t.has(e))return!0;for(var n in t.add(e),e)if(x.hasCircularReference(e[n],t))return!0;return t.delete(e),!1},x.parseSuperProperties=function(e){var t=e.properties||{},n=JSON.parse(JSON.stringify(e));x.check.isObject(t)&&x.each(t,(function(e,r){if(x.check.isFunction(e))try{t[r]=e(n),x.check.isFunction(t[r])&&(D.log("属性--"+r+" 格式不满足要求, 已被删除"),delete t[r])}catch(e){delete t[r],D.log("属性--"+r+" 格式不满足要求, 已被删除")}}))},x.checkMaxProperties=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1024,n=e.properties||{};x.check.isObject(n)&&x.each(n,(function(e,r){if(x.check.isString(e))try{t>m&&(t=m),e.length>t&&(n[r]=x.check.cutString(e,t),D.log("属性--"+r+" 内容过长，已被截断:"+n[r]))}catch(e){}}))},x.getURLSearchParams=function(e){for(var t={},n=(e=e||"").substring(1).split("&"),r=0;r<n.length;r++){var i=n[r].indexOf("=");if(-1!==i){var a=n[r].substring(0,i),o=n[r].substring(i+1);a=x.decodeURIComponent(a),o=x.decodeURIComponent(o),t[a]=o}}return t},x.urlParse=function(e){var t=function(e){this._fields={Username:4,Password:5,Port:7,Protocol:2,Host:6,Path:8,URL:0,QueryString:9,Fragment:10},this._values={},this._regex=/^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/,void 0!==e&&this._parse(e)};return t.prototype.setUrl=function(e){this._parse(e)},t.prototype._initValues=function(){for(var e in this._fields)this._values[e]=""},t.prototype.addQueryString=function(e){if("object"!==u(e))return!1;var t=this._values.QueryString||"";for(var n in e)t=new RegExp(n+"[^&]+").test(t)?t.replace(new RegExp(n+"[^&]+"),n+"="+e[n]):"&"===t.slice(-1)?t+n+"="+e[n]:""===t?n+"="+e[n]:t+"&"+n+"="+e[n];this._values.QueryString=t},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:"",e+=this._values.Fragment?"#"+this._values.Fragment:""},t.prototype._parse=function(e){this._initValues();var t=this._regex.exec(e);t||D.i("URLParser::_parse -> Invalid URL");var n=e.split("#"),r=n[0],i=n.slice(1).join("#");for(var a in t=this._regex.exec(r),this._fields)void 0!==t[this._fields[a]]&&(this._values[a]=t[this._fields[a]]);this._values.Hostname=this._values.Host.replace(/:\d+$/,""),this._values.Origin=this._values.Protocol+"://"+this._values.Hostname,this._values.Fragment=i},new t(e)},x.URL=function(e){var t,n={};if("function"==typeof window.URL&&function(){try{return"http://modernizr.com/"===new URL("http://modernizr.com/").href}catch(e){return!1}}())(n=new URL(e)).searchParams||(n.searchParams=(t=x.getURLSearchParams(n.search),{get:function(e){return t[e]}}));else{x.check.isString(e)||(e=String(e)),e=x.trim(e);if(!1===/^https?:\/\/.+/.test(e))return void D.log("Invalid URL");var r=x.urlParse(e);n.hash=r._values.Fragment,n.host=r._values.Host?r._values.Host+(r._values.Port?":"+r._values.Port:""):"",n.href=r._values.URL,n.password=r._values.Password,n.pathname=r._values.Path,n.port=r._values.Port,n.search=r._values.QueryString?"?"+r._values.QueryString:"",n.username=r._values.Username,n.hostname=r._values.Hostname,n.protocol=r._values.Protocol?r._values.Protocol+":":"",n.origin=r._values.Origin?r._values.Origin+(r._values.Port?":"+r._values.Port:""):"",n.searchParams=function(){var e=x.getURLSearchParams("?"+r._values.QueryString);return{get:function(t){return e[t]}}}()}return n};var R=function(){return i((function e(){n(this,e)}),null,[{key:"getSourceFromReferrer",value:function(){function e(e,t){for(var n=0;n<e.length;n++)if(-1!==t.split("?")[0].indexOf(e[n]))return!0}var t="("+v.join("|")+")\\=[^&]+",n=document.referrer||"",r=C.href;if(r){var i=r.match(new RegExp(t));return i&&i[0]?"付费广告流量":e(S,n)?"自然搜索流量":e(b,n)?"社交网站流量":""===n?"直接流量":"引荐流量"}return"获取url异常"}},{key:"getReferSearchEngine",value:function(e){var t=x.getHostname(e);if(!t||"hostname解析异常"===t)return"";var n={baidu:[/^.*\.baidu\.com$/],bing:[/^.*\.bing\.com$/],google:[/^www\.google\.com$/,/^www\.google\.com\.[a-z]{2}$/,/^www\.google\.[a-z]{2}$/],sm:[/^m\.sm\.cn$/],so:[/^.+\.so\.com$/],sogou:[/^.*\.sogou\.com$/],yahoo:[/^.*\.yahoo\.com$/]},r={};for(var i in n)r[i]=n[i].map((function(e){return new RegExp(e)}));for(var o in r){var s,c=a(r[o]);try{for(c.s();!(s=c.n()).done;){if(s.value.test(t))return o}}catch(e){c.e(e)}finally{c.f()}}return"未知搜索引擎"}},{key:"getKeywordFromReferrer",value:function(e,t){if(e=e||document.referrer,document&&x.check.isString(e)){if(0===e.indexOf("http")){var n=this.getReferSearchEngine(e),r=x.getURLSearchParams(e);if(x.check.isEmptyObject(r))return"未取到值";var i=null;for(var a in y)if(n===a&&x.check.isObject(r))if(i=y[a],x.check.isArray(i))for(a=0;a<i.length;a++){var o=r[i[a]];if(o)return t?{active:o}:o}else if(r[i])return t?{active:r[i]}:r[i];return"未取到值"}return""===e?"未取到值_直接打开":"未取到值_非http的url"}return"取值异常_referrer异常_"+String(e)}}])}();function N(e){return!!x.check.isFunction(e)||!(!e||!x.check.isObject(e))&&N(e.callback)}var F=function(){return i((function e(){return n(this,e),e.instance||(e.instance=this,this.events={}),e.instance}),[{key:"on",value:function(e,t){if(!e||!t)return!1;if(!N(t))throw new Error("callback must be a function");return this.events[e]=this.events[e]||[],x.check.isObject(t)?this.events[e].push(t):this.events[e].push({callback:t,once:!1}),this}},{key:"prepend",value:function(e,t){if(!e||!t)return!1;if(!N(t))throw new Error("callback must be a fcuntion");return this.events[e]=this.events[e]||[],x.check.isObject(t)?this.events[e].unshift(t):this.events[e].unshift({callback:t,once:!1}),this}},{key:"prependOnce",value:function(e,t){return this.prepend(e,{callback:t,once:!0})}},{key:"once",value:function(e,t){return this.on(e,{callback:t,once:!0})}},{key:"off",value:function(e,t){var n=this.events[e];if(!n)return!1;if(x.check.isNumber(t))n.splice(t,1);else if(x.check.isFunction(t))for(var r=0;r<n.length;r++)n[r]&&n[r].callback===t&&n.splice(r,1);return this}},{key:"emit",value:function(e,t){var n=this.events[e];if(!n)return!1;var r,i=a(n);try{for(i.s();!(r=i.n()).done;){var o=r.value;x.check.isObject(o)&&(o.callback.call(this,t||{}),o.once&&this.off(e,o.callback))}}catch(e){i.e(e)}finally{i.f()}return this}},{key:"clear",value:function(e){e&&this.events(e)?this.events[e]=[]:this.events={}}},{key:"getEvent",value:function(e){return e&&this.events[e]?this.events[e]:this.events}}])}();x.mitt=new F,x.initUrlChange=function(){var e=C.href,t=B.pushState,n=B.replaceState;x.check.isFunction(t)&&(B.pushState=function(){t.apply(B,arguments),x.mitt.emit("urlChange",e),e=C.href}),x.check.isFunction(n)&&(B.replaceState=function(){n.apply(B,arguments),x.mitt.emit("urlChange",e),e=C.href});var r=t?"popstate":"hashchange";x.addEvent(window,r,(function(){x.mitt.emit("urlChange",e),e=C.href}))},x.listenPageState=function(e){({visibleHandler:x.check.isFunction(e.visible)?e.visible:function(){},hiddenHandler:x.check.isFunction(e.hidden)?e.hidden:function(){},visibilityChange:null,hidden:null,isSupport:function(){return!x.check.isUndefined(document[this.hidden])},init:function(){x.check.isUndefined(document.hidden)?x.check.isUndefined(document.msHidden)?x.check.isUndefined(document.webkitHidden)?x.check.isUndefined(document.mozHidden)||(this.hidden="mozHidden",this.visibilityChange="mozvisibilitychange"):(this.hidden="webkitHidden",this.visibilityChange="webkitvisibilitychange"):(this.hidden="msHidden",this.visibilityChange="msvisibilitychange"):(this.hidden="hidden",this.visibilityChange="visibilitychange"),this.listen()},listen:function(){var e=this;this.isSupport()?x.addEvent(document,this.visibilityChange,(function(){document[e.hidden]?e.hiddenHandler():e.visibleHandler()})):(x.addEvent(window,"focus",this.visibleHandler),x.addEvent(window,"blur",this.hiddenHandler))}}).init()},x.hash=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},x.getDomBySelector=function(e){if("string"!=typeof e)return console.error("Selector must be a string"),null;var t=document.querySelector(e);return t||(console.warn("No element found for selector:",e),null)},x.getUA=function(){var e=I.userAgent;return{ie:e.indexOf("MSIE ")>-1&&parseInt(e.split("MSIE ")[1])}},x.encodeSelector=function(e){return x.base64Encode(e).replace(/[+\/=]/g,(function(e){return"_"}))},x.loadScript=function(e){var t={success:function(){},error:function(){},appendCall:function(e){document.head.appendChild(e)}},n=c(c({},t),e),r=null;"css"===n.type?((r=document.createElement("link")).rel="stylesheet",r.href=n.url):"js"===n.type&&((r=document.createElement("script")).async=!0,r.charset="UTF-8",r.src=n.url,r.type="text/javascript"),r.onload=r.onreadystatechange=function(){r.readyState&&"loaded"!==r.readyState&&"complete"!==r.readyState||(n.success(),r.onload=r.onreadystatechange=null)},r.onerror=function(){n.error(),r.onerror=null},n.appendCall(r)},x.checkUrlIsMatch=function(e,t){try{var n=x.URL(C.href),r=x.URL(e);switch(t){case"STRICT":return C.href===r.href;case"FUZZY":return n.host===r.host&&n.pathname===r.pathname;default:return console.error("link_match_type 字段异常",t),!1}}catch(e){return console.error("URL 解析失败",e),!1}},x.secCheck={isHttpUrl:function(e){if("string"!=typeof e)return!1;var t=/^https?:\/\/.+/.test(e);return t||console.log("Invalid URL"),t},removeScriptProtocol:function(e){if("string"!=typeof e)return"";for(var t=/^\s*javascript/i;t.test(e);)e=e.replace(t,"");return e}},x.dfMapping=function(e){for(var t="t6KJCZa5pDdQ9khoEM3Tj70fbP2eLSyc4BrsYugARqFIw1mzlGNVXOHiWvxUn8",n={},r=0;r<62;r++){var i=t.charAt(r),a=t.charAt(61-r);n[i]=a}for(var o="",s=0;s<input.length;s++){var c=input.charAt(s);o+=c in n?n[c]:c}return o},x.rot13obfs=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:13;return String(e).split("").map((function(e){var n=e.charCodeAt(0);if(n<126){var r=(n+t)%126;return r<32&&(r+=32),String.fromCharCode(r)}return e})).join("")};var U={dataEnc:"data:enc;",userEncrypt:function(e){return"dfm-enc-"+x.dfMapping(e)},userDecrypt:function(e){return 0===e.indexOf(dataEnc)?(e=e.substring(dataEnc.length),e=x.rot13obfs(e=String(e),113)):0===e.indexOf("dfm-enc-")&&(e=e.substring(8),e=x.dfMapping(e)),e},userDecryptIfNeeded:function(e){if(x.check.isString(e)||""===e)return e;if(0!==e.indexOf("dfm-enc-")&&0!==e.indexOf(dataEnc))return e;var t=userDecrypt(e);return t||e}};function V(e){O.checkAjax(e.url),e.timeout=e.timeout||2e4;var t=void 0!==window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest?new XMLHttpRequest:void 0!==window.XDomainRequest?new(0,window.XDomainRequest):null;if(!t)return!1;var n,r=(e=x.extend({success:function(){},error:function(){}},e)).success,i=e.error,a=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];n&&(clearTimeout(n),n=null,e&&(t.onreadystatechange=null,t.onload=null,t.onerror=null))};e.success=function(e){r(e),a()},e.error=function(e){i(e),a()},n=setTimeout((function(){try{t&&x.check.isObject(t)&&t.abort&&t.abort()}catch(e){D.log(e)}a(!0)}),e.timeout);var o=function(e){return e?x.safeJSONParse(e):""};"undefined"!=typeof XDomainRequest&&t instanceof window.XDomainRequest&&(t.onload=function(){e.success&&e.success(o(t.responseText)),t.onreadystatechange=null,t.onload=null,t.onerror=null},t.onerror=function(){e.error&&e.error(o(t.responseText),t.status),t.onreadystatechange=null,t.onerror=null,t.onload=null}),t.open("post",e.url,!0),e.credentials&&(t.withCredentials=!0),t.setRequestHeader&&(t.setRequestHeader("Content-type",e.contentType||"application/x-www-form-urlencoded"),e.projectKey&&t.setRequestHeader("project-key",e.projectKey)),t.onreadystatechange=function(){try{4===t.readyState&&(t.status>=200&&t.status<300||304===t.status?e.success(o(t.responseText)):e.error("网络异常, 请求失败",t.status),t.onreadystatechange=null,t.onload=null)}catch(e){t.onreadystatechange=null,t.onload=null}},t.send(e.data||null)}x.kit=U,x.checkUrlIsMatch=function(e,t){var n;try{n=x.URL(e)}catch(e){return D.log("Failed to parse control URL",e),!1}if("STRICT"===t)return C.href===n.href;if("FUZZY"===t){var r;try{r=x.URL(C.href)}catch(e){return D.log("Failed to parse current URL",e),!1}return r.host===n.host&&r.pathname===n.pathname}return D.log("Invalid link_match_type",t),!1},x.getStorageData=function(e){if(!x.localStorage.isSupport())return D.log("A/B Testing 初始化失败，浏览器不支持 localStorage"),!1;var t=x.localStorage.get(e);x.check.isString(t)&&(t=x.kit.userDecryptIfNeeded(t));try{t=JSON.parse(t)}catch(e){D.log("A/B Testing 初始化失败，无法解析存储数据",e)}return t},x.storeData=function(e,t,n){var r=JSON.stringify(e);n&&(r=x.userEncrypt(r)),x.localStorage.set(t,r)};var j=function(){return i((function e(t){n(this,e),this.callback=t.callback,this.serverUrl=t.serverUrl,this.data=t.data,this.dataSendTimeout=t.dataSendTimeout}),[{key:"run",value:function(){var e=this;V({url:this.serverUrl,data:this.data,timeout:this.dataSendTimeout,success:function(){return e.cb("success")},error:function(t){return e.cb("fail",t)}})}},{key:"cb",value:function(e,t){if(this.callback){if(!x.check.isFunction(this.callback))return void D.log("sdk callback is not a function");var n=new URLSearchParams(this.data).get("data"),r=x.base64Decode(n);if("track"===r.type&&"H_WebPageLeave"===r.event)return;this.callback(this.serverUrl,r,e,t)}}}])}(),X=function(){return i((function e(t){n(this,e),this.callback=t.callback,this.serverUrl=t.serverUrl,this.data=t.data,this.dataSendTimeout=t.dataSendTimeout,this.img=document.createElement("img"),this.img.width=1,this.img.height=1,t.imgUseCrossOrigin&&(this.img.crossOrigin="anonymous")}),[{key:"run",value:function(){var e=this,t=function(t,n){e.img&&!e.img.complete&&(e.img.complete=!0,setTimeout((function(){var t=x.info.browser().type;e.img.src="ie"===t?"about:blank":""}),e.dataSendTimeout)),e.cb(t,n)};-1!==this.serverUrl.indexOf("?")?this.img.src=this.serverUrl+"&"+this.data:this.img.src=this.serverUrl+"?"+this.data,this.img.onload=function(){this.onload=null,t("success")},this.img.onerror=function(){this.onerror=null,t("error")},this.img.onabort=function(){this.onabort=null,t("abort")}}},{key:"cb",value:function(e){if(this.callback){if(!x.check.isFunction(this.callback))return void D.log("sdk callback is not a function");var t=new URLSearchParams(this.data).get("data");this.callback(this.serverUrl,x.base64Decode(t),e)}}}])}(),W=function(){return i((function e(t){n(this,e),this.callback=t.callback,this.serverUrl=t.serverUrl,this.data=t.data}),[{key:"run",value:function(){var e=this;if(x.check.isObject(navigator)&&x.check.isFunction(navigator.sendBeacon)){var t=new FormData;t.append("data",x.base64Encode(this.data)),navigator.sendBeacon(this.serverUrl,t)}setTimeout((function(){e.cb()}))}},{key:"cb",value:function(){if(this.callback){if(!x.check.isFunction(this.callback))return void D.log("sdk callback is not a function");this.callback()}}}])}(),K="hinasdk_data_",q=function(){return i((function e(t){n(this,e),this.timer=null,this.sendTimeStamp=0,this.batchConfig=x.extend({dataSendTimeout:6e3,sendInterval:6e3,storageLimit:200},t.batchSend),this.tabKey="hinasdk_tab",this.config=t}),[{key:"batchInterval",value:function(){var e=this;this.timer=setTimeout((function(){e.recycle(),e.send(),clearTimeout(e.timer),e.batchInterval()}),this.batchConfig.sendInterval)}},{key:"request",value:function(e,t){var n=this;0!=(e=e.filter((function(e){return null!=e}))).length&&V({url:this.config.serverUrl,data:"data_list="+encodeURIComponent(x.base64Encode(JSON.stringify(e))),timeout:this.batchConfig.dataSendTimeout,success:function(){n.remove(t),n.sendTimeStamp=0},error:function(){n.sendTimeStamp=0}})}},{key:"send",value:function(){if(!(this.sendTimeStamp&&x.now()-this.sendTimeStamp<this.batchConfig.dataSendTimeout)){var e=x.localStorage.get(this.tabKey);if(e){this.sendTimeStamp=x.now();var t=x.safeJSONParse(e)||[];if(t.length){for(var n=[],r=[],i=t.length<this.batchConfig.storageLimit?t.length:this.batchConfig.storageLimit,a=0;a<i;a++){var o=x.readObjectVal(t[a].dataKey);r.push(t[a].dataKey),n.push(o)}this.request(n,r)}}}}},{key:"remove",value:function(e){var t=x.localStorage.get(this.tabKey);if(t){for(var n=x.safeJSONParse(t)||[],r=null==n?void 0:n.map((function(e){return e.dataKey})),i=0;i<e.length;i++){var a=x.indexOf(r,e[i]);a>-1&&(n.splice(a,1),x.localStorage.remove(e[i]))}x.localStorage.set(this.tabKey,JSON.stringify(n))}}},{key:"add",value:function(e){var t=K+x.getRandom(),n=x.localStorage.get(this.tabKey);if((n=null==n?[]:x.safeJSONParse(n)||[]).push({dataKey:t,expireTime:x.now()+2*this.batchConfig.sendInterval}),x.localStorage.set(this.tabKey,JSON.stringify(n)),x.saveObjectVal(t,e),n.length>this.batchConfig.storageLimit){for(var r=n.slice(0,20),i=[],a=0;a<r.length;a++){var o=x.readObjectVal(r[a].dataKey);i.push(o)}this.request(i,r.map((function(e){return e.dataKey})))}"track_signup"!==e.type&&"H_pageview"!==e.event||this.send()}},{key:"recycle",value:function(){var e=x.localStorage.get(this.tabKey);if(e){var t=x.safeJSONParse(e)||[];if(t.length){for(var n=[],r=0;r<t.length;r++)x.now()>t[r].expireTime&&n.push(t[r].dataKey);this.remove(n)}else{for(var i=[],a=0;a<x.localStorage.length;a++){var o=x.localStorage.key(a);0===(null==o?void 0:o.indexOf(K))&&i.push({dataKey:o,expireTime:x.now()+2*this.batchConfig.sendInterval})}i.length>0&&x.localStorage.set(this.tabKey,JSON.stringify(i))}}}}])}();function z(){return x.cookie.isSupport()?null!=x.cookie.get("hinasdk_isNewUser"):null!=x.memory.get("hinasdk_isNewUser")}var J={name:"hinasdk_crossdata",state:{deviceId:null,accountId:null,firstId:null,sessionId:null,firstVisitTime:x.now(),props:{},sessionIdUpdateTime:null},isFirstTime:!1,isFirstDay:z(),isFirstVisit:!0,isSetFirstVisit:!0,load:function(){var e=null,t=null;if(x.cookie.isSupport()?this.storage=x.cookie:(D.log("Cookie storage is not supported, SDK internal cache has been enabled"),this.storage=x.memory),x.localStorage.isSupport()?this.storageLocal=x.localStorage:D.log("localStorage is not supported, SDK internal cache has been enabled"),e||(e=this.storage.get(this.name)),t||(t=this.storageLocal.get(this.name)),e&&x.check.isJSONString(e)&&(this.state=x.extend({},JSON.parse(e))),t&&x.check.isJSONString(t)&&(this.state=x.extend({},JSON.parse(t))),e||t?(this.save(),this.isSetFirstVisit=!1,this.isFirstVisit=!1):(this.isSetFirstVisit=!0,this.isFirstVisit=!0),this.isFirstVisit){var n=new Date,r={h:23-n.getHours(),m:59-n.getMinutes(),s:59-n.getSeconds()};this.storage.set("hinasdk_isNewUser",!0,3600*r.h+60*r.m+r.s+"s"),this.isFirstDay=!0,this.isFirstTime=!0}else this.checkIsFirstTime=function(e){"track"===e.type&&"H_pageview"===e.event&&(e.properties.H_is_first_time=!1)};if(!this.getAccountId()){var i=x.UUID();this.setDeviceId(i)}},checkIsFirstTime:function(e){"track"===e.type&&"H_pageview"===e.event&&(z()&&this.isFirstTime?(e.properties.H_is_first_time=!0,this.isFirstTime=!1):e.properties.H_is_first_time=!1)},checkIsFirstSign:function(e){"track"===e.type&&(z()&&this.isFirstDay?e.properties.H_is_first_day=!0:(this.isFirstDay=!1,e.properties.H_is_first_day=!1))},setDeviceId:function(e){this.state.deviceId?D.log("Current deviceId is "+this.getDeviceId()+", it has been set"):this.set("deviceId",e)},setAccountId:function(e){e&&""!==e&&this.set("accountId",e)},getDeviceId:function(){return this.state.deviceId},getAccountId:function(){return this.state.__accountId||this.state.accountId},getCookie:function(){return this.storage.get(this.name)},setProps:function(e,t){var n={};for(var r in n=t?e:x.extend(this.state.props||{},e))"string"==typeof n[r]&&(n[r]=n[r].slice(0,x.MAX_REFERRER_STRING_LENGTH));this.set("props",n)},getFirstId:function(){return this.state.__firstId||this.state.firstId},getSessionId:function(){return this.state.sessionId},getSessionIdUpdateTime:function(){return this.state.sessionIdUpdateTime||this.getSessionId().split("_")[0]},setSessionId:function(e){this.set("sessionId",e)},getAnonymousId:function(){var e=this.getAccountId(),t=this.getFirstId();return e&&t?t:this.getDeviceId()},getOriginUnionId:function(e){var t,n={};t=e?e.first_id:this.getFirstId();var r=e.account_id||this.getAccountId();return t&&r?(n.login_id=r,n.anonymous_id=t):n.anonymous_id=t||this.getFirstId(),n},getUnionId:function(e){return this.getOriginUnionId(e)},change:function(e,t){this.state["__"+e]=t},set:function(e,t){this.state=this.state||{},["accountId","firstId"].indexOf(e)>-1&&delete this.state["__"+e],this.state[e]=t,this.save()},save:function(){this.storage.setDomain(this.name,JSON.stringify(this.state),null,!0),this.storageLocal.set(this.name,JSON.stringify(this.state))},clear:function(){this.state={},this.save()}},Q={pageProp:{},currentProps:{},register:function(e){x.extend(Q.currentProps,e)},getPresetProperties:function(){var e=window.innerHeight||document.documentElement.clientHeight||document.body&&document.body.clientHeight||0,t=window.innerWidth||document.documentElement.clientWidth||document.body&&document.body.clientWidth||0,n={H_timezone_offset:(new Date).getTimezoneOffset(),H_viewport_height:e,H_viewport_width:t};return x.extend(n,x.info.properties()),n},getPageProperties:function(){return x.extend(this.pageProp,x.info.pageProperties()),this.pageProp},getUmtsParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=x.getUtm(),r={},i={};return x.each(n,(function(n,a,o){x.utmTypes.includes(a)?r[e+a]=o[a]:i[t+a]=o[a]})),J.state.props&&J.state.props.H_latest_utm_source&&x.extend(J.state.props,r),{allUtms:r,otherUtms:i}},clearPageRegister:function(e){if(Array.isArray(e)&&e.length>0)e.forEach((function(e){"string"==typeof e&&e in Q.currentProps&&delete Q.currentProps[e]}));else if(!0===e)for(var t in Q.currentProps)delete Q.currentProps[t]}};function Y(e){var t=J.state.props||{};if(x.each(e.presetProperties,(function(e,n){if(-1===n.indexOf("latest_"))return!1;if(n=n.slice(7),e){var r=x.getCurrentDomain(window.location.href);if("utm"!==n&&"url解析失败"===r)t["H_latest_"+n]="url的domain解析失败";else if(x.isReferralTraffic(document.referrer))switch(n){case"traffic_source_type":t.H_latest_traffic_source_type=R.getSourceFromReferrer();break;case"referrer":t.H_latest_referrer=Q.pageProp.referrer||"";break;case"search_keyword":R.getKeywordFromReferrer()&&(t.H_latest_search_keyword=R.getKeywordFromReferrer())}}})),e.presetProperties.latest_utm){var n=Q.getUmtsParams("H_latest_",""),r=n.allUtms,i=n.otherUtms;x.check.isEmptyObject(r)||x.extend(t,r),x.check.isEmptyObject(i)||x.extend(t,i)}Q.register(t),J.setProps(t)}function Z(e,t,n){if(J.isSetFirstVisit){var r,i=Q.getUmtsParams("H_","").allUtms,a=x.getReferrer(null,t);e.call(n,x.extend(c({H_first_visit_time:x.now(),H_first_referrer:a,H_first_host:a?x.getHostname(a,"取值异常"):"",H_first_browser_language:x.check.isString(navigator.languages[1])?null===(r=navigator.languages[1])||void 0===r?void 0:r.toLowerCase():"取值异常",H_first_traffic_source_type:R.getSourceFromReferrer(),H_first_search_keyword:R.getKeywordFromReferrer()},i))),J.isSetFirstVisit=!1}}function $(e,t){var n=Q.getUmtsParams("H_",""),r=n.allUtms,i=n.otherUtms,a={anonymous_id:J.getAnonymousId(),properties:c({device_id:J.getDeviceId()},Q.currentProps),type:e.type,event:e.event,time:x.now(),_track_id:Number(String(x.getRandom()).slice(2,5)+String(x.getRandom()).slice(2,4)+String(x.now()).slice(-4))};return J.getAccountId()!==J.getAnonymousId()&&J.getAccountId()&&(a.account_id=J.getAccountId()),e.type&&"user"===e.type.slice(0,4)?a.properties=x.extend(a.properties,e.properties):a.properties=x.extend(a.properties,r,i,Q.currentProps,Q.getPageProperties(),Q.getPresetProperties(),e.properties),x.parseSuperProperties(a),x.checkMaxProperties(a,t.config.maxStringLength),J.checkIsFirstSign(a),J.checkIsFirstTime(a),a}var ee=window.location,te=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"],ne=["a","div","input","button","textarea"],re=function(){return i((function e(t,r,i){n(this,e),this.autoTrackIsUsed=!1,this.otherTag=[],this.isTrackList={a:!0,button:!0},this.autoTrackConfig=x.extend({clickAutoTrack:!0,stayAutoTrack:!0,isCollectUrl:function(){return!0},isCollectElement:function(){return!0},isCollectInput:function(){return!1},addCustomProperty:function(){},stayDelayTime:4e3,maxStayPageDuration:18e3,maxStringLength:1024,collectTags:{div:!1},trackAttr:[]},t),this.stayAutoTrackConfig=x.extend({isCollectUrl:function(){return!0}},r),this.ctx=i,this.load(t)}),[{key:"load",value:function(e){var t=this;if(x.check.isArray(e.trackAttr)?(this.autoTrackConfig.trackAttr=e.trackAttr.filter((function(e){return x.check.isString(e)})),this.autoTrackConfig.trackAttr.push("hn-click")):this.autoTrackConfig.trackAttr=["hn-click"],x.check.isObject(e.collectTags)){if(!0===e.collectTags.div)this.autoTrackConfig.collectTags.div={ignoreTags:te,maxLevel:1};else if(x.check.isObject(e.collectTags.div))if(this.autoTrackConfig.collectTags.div.ignoreTags=te,x.check.isNumber(e.collectTags.div.maxLevel)){[1,2,3].includes(e.collectTags.div.maxLevel)||(this.autoTrackConfig.collectTags.div.maxLevel=1)}else this.autoTrackConfig.collectTags.div.maxLevel=1;else this.autoTrackConfig.collectTags.div=!1;x.each(e.collectTags,(function(e,n){"div"!==n&&e&&t.otherTag.push(n)})),!0===this.autoTrackConfig.clickAutoTrack&&x.each(this.otherTag,(function(e){e in t.isTrackList&&(t.isTrackList[e]=!0)}))}else this.autoTrackConfig.collectTags={div:!1}}},{key:"autoTrack",value:function(e,t){this.ctx.track("H_pageview",x.extend({H_referrer:x.getReferrer(null,!0),H_url:ee.href,H_url_path:ee.pathname,H_url_hash:ee.hash,H_title:document.title},e||{}),t),Z(this.ctx.userSetOnce,!0,this.ctx),this.autoTrackIsUsed=!0}},{key:"autoTrackSinglePage",value:function(e,t){var n;n=this.autoTrackIsUsed?ee.href:x.getReferrer(),this.ctx.track("H_pageview",x.extend({H_referrer:n,url:ee.href,H_url_path:ee.pathname,H_url_hash:ee.hash,H_title:document.title},e||{}),t),Z(this.ctx.userSetOnce,!1,this.ctx)}},{key:"listenSinglePage",value:function(){var e=this,t=this.ctx.getConfig("isSinglePage");t&&x.mitt.on("hasInit",(function(){e.onUrlChange((function(n){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(n!==ee.href){Q.pageProp.H_referrer=n;var r=x.extend({H_url:ee.href,H_referrer:n},t);e.autoTrack(r)}};if(x.check.isBoolean(t))r();else if(x.check.isFunction(t)){var i=t();x.check.isObject(i)?r(i):!0===i&&r()}}))}))}},{key:"initWebClick",value:function(){var e=this;if(!0===this.autoTrackConfig.clickAutoTrack){var t=!0;x.check.isFunction(this.autoTrackConfig.isCollectUrl)&&this.onUrlChange((function(){t=!!e.autoTrackConfig.isCollectUrl()})),x.addCaptureEvent(document,"click",(function(n){if(t){var r=n||window.event;if(r){var i=r.target||r.srcElement,a=e.getTargetElement(i,r);a&&e.emitClick(r,a)}}}))}}},{key:"emitClick",value:function(e,t,n,r){if(x.check.isFunction(this.autoTrackConfig.isCollectElement)&&!this.autoTrackConfig.isCollectElement(t))return!1;var i=this.getClickElementInfo(t);if(x.check.isFunction(this.autoTrackConfig.addCustomProperty)){var a=this.autoTrackConfig.addCustomProperty(t);x.check.isObject(a)&&(i=x.extend(i,a))}i=x.extend(i,this.getPageXYInfo(e,t),n||{}),this.ctx.track("H_WebClick",i,r)}},{key:"initWebStay",value:function(){var e=this;if(!0===this.autoTrackConfig.stayAutoTrack){var t=!0;x.check.isFunction(this.stayAutoTrackConfig.isCollectUrl)&&this.onUrlChange((function(){t=!!e.stayAutoTrackConfig.isCollectUrl()}));var n,r=this.autoTrackConfig.stayDelayTime,i=this.autoTrackConfig.maxStayPageDuration,a=this.ctx,o=(n={timer:null,timeout:1e3,callback:function(e,t){var n,o=(null===(n=document.documentElement)||void 0===n?void 0:n.scrollTop)||window.pageYOffset||document.body.scrollTop||0,s=e.H_viewport_position,c=new Date,l=c-this.nowTime;(l>r&&o-s!=0||t)&&(x.extend(e,{event_duration:Math.min(parseInt(l)/1e3,i)},x.info.pageProperties()),a.track("H_WebStay",e)),this.nowTime=c},run:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t={};if(!n.timer){var r,i=(null===(r=document.documentElement)||void 0===r?void 0:r.scrollTop)||window.pageYOffset||document.body.scrollTop||0;t.H_viewport_position=Math.round(i)||0,e?(n.callback(t,!0),n.timer=null):n.timer=setTimeout((function(){n.callback(t,!1),n.timer=null}),n.timeout)}}},n);o.nowTime=new Date,x.addCaptureEvent(window,"scroll",(function(){t&&o.run()})),x.addCaptureEvent(window,"unload",(function(){t&&o.run(!0)}))}}},{key:"onUrlChange",value:function(e){x.check.isFunction(e)&&(e(),x.mitt.on("urlChange",e))}},{key:"getTargetElement",value:function(e,t){var n;if(!x.check.isElement(e))return null;if(!x.check.isString(e.tagName))return null;var r,i,a=null===(n=e.tagName)||void 0===n?void 0:n.toLowerCase();if(["body","html"].includes(a))return null;if(["a","button","input","textarea"].concat(this.otherTag).includes(a))return e;if("div"===a&&this.autoTrackConfig.collectTags.div&&this.isDivLevelValid(e)&&(((null===(r=this.autoTrackConfig.collectTags)||void 0===r||null===(i=r.div)||void 0===i?void 0:i.maxLevel)||1)>1||this.isCollectableDiv(e)))return e;if(this.isStyleTag(a)&&this.autoTrackConfig.collectTags.div){var o=this.getCollectableParent(e);if(o&&this.isDivLevelValid(o))return o}return this.hasElement({event:(null==t?void 0:t.originalEvent)||t,element:e})||null}},{key:"isDivLevelValid",value:function(e){for(var t,n,r=(null===(t=this.autoTrackConfig.collectTags)||void 0===t||null===(n=t.div)||void 0===n?void 0:n.maxLevel)||1,i=e.getElementsByTagName("div"),a=i.length-1;a>=0;a--)if(this.getDivLevel(i[a],e)>r)return!1;return!0}},{key:"getDivLevel",value:function(e,t){var n=this.getElementPath(e,!0,t).split(" > "),r=0;return n.forEach((function(e){"div"===e&&r++})),r}},{key:"getElementPath",value:function(e,t,n){for(var r=[];e.parentNode&&x.check.isElement(e);){if(e.id&&!t&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.id)){var i;r.unshift((null===(i=e.tagName)||void 0===i?void 0:i.toLowerCase())+"#"+e.id);break}if(n&&e===n){var a;r.unshift(null===(a=e.tagName)||void 0===a?void 0:a.toLowerCase());break}if(e===document.body){r.unshift("body");break}var o;r.unshift(null===(o=e.tagName)||void 0===o?void 0:o.toLowerCase()),e=e.parentNode}return r.join(" > ")}},{key:"isCollectableDiv",value:function(e){try{var t=e.children||[];if(0===(null==t?void 0:t.length))return!0;for(var n=0;n<t.length;n++){var r,i,a;if(1===t[n].nodeType){var o=null===(r=t[n].tagName)||void 0===r?void 0:r.toLowerCase(),s=(null===(i=this.autoTrackConfig.collectTags)||void 0===i||null===(a=i.div)||void 0===a?void 0:a.maxLevel)||1;if(!("div"===o&&s>1||this.isStyleTag(o)))return!1;if(!this.isCollectableDiv(t[n]))return!1}}return!0}catch(e){D.log(e)}return!1}},{key:"getCollectableParent",value:function(e){try{var t,n,r,i=e.parentNode,a=i?null===(t=i.tagName)||void 0===t?void 0:t.toLowerCase():"";if("body"===a)return!1;var o=(null===(n=this.autoTrackConfig.collectTags)||void 0===n||null===(r=n.div)||void 0===r?void 0:r.maxLevel)||1;if("div"===a&&(o>1||this.isCollectableDiv(i)))return i;if(i&&this.isStyleTag(a))return this.getCollectableParent(i)}catch(e){D.log(e)}return!1}},{key:"isStyleTag",value:function(e){var t,n,r;return!ne.includes(e)&&!(null===(t=this.autoTrackConfig.collectTags)||void 0===t||null===(n=t.div)||void 0===n||null===(r=n.ignoreTags)||void 0===r||!r.includes(e))}},{key:"hasElement",value:function(e){var t;if(e.event){var n=e.event;t=n.path||(null==n?void 0:n._getPath())}else e.element&&(t=x.getDomElementInfo(e.element).getParents());var r=this.autoTrackConfig.trackAttr;if(x.check.isArray(t)&&t.length>0){var i,o=a(t);try{for(o.s();!(i=o.n()).done;){var s,c=i.value,l=c.tagName&&(null===(s=c.tagName)||void 0===s?void 0:s.toLowerCase());if(x.check.isElement(c)&&1===c.nodeType&&(this.isTrackList[l]||this.hasAttributes(c,r)))return c}}catch(e){o.e(e)}finally{o.f()}}}},{key:"hasAttribute",value:function(e,t){return e.hasAttribute?e.hasAttribute(t):e.attributes?!(null===(n=e.attributes[t])||void 0===n||!n.value):void 0;var n}},{key:"hasAttributes",value:function(e,t){if(x.check.isArray(t)){for(var n=!1,r=0;r<t.length;r++){if(this.hasAttribute(e,t[r])){n=!0;break}}return n}}},{key:"getPageXYInfo",value:function(e,t){if(!e)return{};function n(){return{scrollLeft:document.body.scrollLeft||document.documentElement.scrollLeft||0,scrollTop:document.body.scrollTop||document.documentElement.scrollTop||0}}function r(){if(document.documentElement.getBoundingClientRect){var e=t.getBoundingClientRect();return{targetEleX:e.left+n().scrollLeft||0,targetEleY:e.top+n().scrollTop||0}}}function i(e){return Number(Number(e).toFixed(3))}return function(e){var t=e.pageX||e.clientX+n().scrollLeft||e.offsetX+r().targetEleX||0,a=e.pageY||e.clientY+n().scrollTop||e.offsetY+r().targetEleY||0;return{H_page_x:i(t),H_page_y:i(a)}}(e)}},{key:"getClickElementInfo",value:function(e){var t=this.autoTrackConfig.isCollectInput(),n=this.getDomSelector(e),r=x.info.getElementInfo(e,t);return r.H_element_selector=n||"",r.H_element_path=this.getElementPath(e,!1),r}},{key:"getDomSelector",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!e||!e.parentNode||!e.parentNode.children)return!1;var r=null===(t=e.nodeName)||void 0===t?void 0:t.toLowerCase();return e&&"body"!==r&&1===e.nodeType?(n.unshift(this.getSelector(e)),null!=e&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))?n.join(" > "):this.getDomSelector(e.parentNode,n)):(n.unshift("body"),n.join(" > "))}},{key:"getSelector",value:function(e){var t,n,r=null===(t=e.tagName)||void 0===t?void 0:t.toLowerCase(),i=-1;return 9!==(null==e||null===(n=e.parentNode)||void 0===n?void 0:n.nodeType)&&(i=this.getDomIndex(e)),null!=e&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))?"#"+e.getAttribute("id"):r+(~i?":nth-of-type("+(i+1)+")":"")}},{key:"getDomIndex",value:function(e){var t;if(!e.parentNode)return-1;for(var n=0,r=null===(t=e.tagName)||void 0===t?void 0:t.toLowerCase(),i=e.parentNode.children,a=0;a<i.length;a++){var o,s;if((null===(o=i[a])||void 0===o||null===(s=o.tagName)||void 0===s?void 0:s.toLowerCase())===r){if(i[a]===e)return n;n++}}return-1}}])}(),ie=x.check,ae=ie.isArray,oe=ie.isBoolean,se=ie.isDate;ie.isElement;var ce=ie.isEmptyObject;ie.isFunction,ie.isJSONString;var le=ie.isNumber,ue=ie.isObject,he=ie.isString;ie.isUndefined;var de=function(){return i((function e(t){n(this,e),this.HinaABTest=t,this.valueTypeList=["Number","String","Object","Boolean"],this.regName=/^((?!^distinct_id$|^account_id$|^original_id$|^time$|^properties$|^id$|^first_id$|^second_id$|^users$|^events$|^event$|^user_id$|^date$|^datetime$|^user_tag.*|^user_group.*)[a-zA-Z_][a-zA-Z\d_]*)$/i}),[{key:"valueType",value:function(e,t){switch(t){case"Number":return le(e);case"String":return he(e);case"Object":return ue(e);case"Boolean":return oe(e);default:return!1}}},{key:"para",value:function(e,t,n){var r=this,i={verify_success:!0,para:null};return x.each(n,(function(n,a){if("essential"===n)switch(a){case"abtestExperimentId":void 0===t.abtestExperimentId&&(D.log("".concat(e,"方法调用失败，abtestExperimentId参数未正确配置！abtestExperimentId: ").concat(t.abtestExperimentId)),i.verify_success=!1);break;case"paramName":he(t.paramName)&&0!==t.paramName.length||(D.log("".concat(e,"方法调用失败，paramName参数未正确配置！paramName: ").concat(t.paramName)),i.verify_success=!1);break;case"valueType":he(t.valueType)&&r.valueTypeList.includes(t.valueType)||(D.log("".concat(e,"方法调用失败，valueType配置错误: ").concat(t.valueType)),i.verify_success=!1);break;case"defaultValue":void 0===t.defaultValue?(D.log("".concat(e,"方法调用失败，defaultValue参数未配置")),i.verify_success=!1):r.valueType(t.defaultValue,t.valueType)||(D.log("".concat(e,"方法调用失败，defaultValue类型必须与valueType一致！"),t.defaultValue,t.valueType),i.verify_success=!1);break;case"callback":x.check.isFunction(t.callback)||(D.log("".concat(e,"方法调用失败，callback参数未正确配置")),i.verify_success=!1);break;default:i.verify_success=!1}else if("not_essential"===n)switch(a){case"timeoutMilliseconds":t.timeoutMilliseconds=t.timeoutMilliseconds||r.HinaABTest.para.timeoutMilliseconds||r.HinaABTest.defaultPara.timeoutMilliseconds,(!le(t.timeoutMilliseconds)||t.timeoutMilliseconds<=0)&&(r.HinaABTest.log("timeoutMilliseconds 参数错误: ".concat(t.timeoutMilliseconds)),t.timeoutMilliseconds=r.HinaABTest.para.timeoutMilliseconds),t.timeoutMilliseconds<200&&(t.timeoutMilliseconds=200);break;case"properties":t.properties=ue(t.properties)?t.properties:{};break;default:i.verify_success=!1}})),i.para=t,i}},{key:"resolveCustomProperties",value:function(e){var t=this,n={verify_success:!0,para:null},r=e.custom_properties;return!ue(r)||ce(r)?(delete e.custom_properties,n.para=e,n):(x.each(r,(function(e,i){if((!he(i)||!t.regName.test(i)||i.length>100)&&(t.HinaABTest.log("属性名 [".concat(i,"] 无效")),n.verify_success=!1),ae(e)){var a=!0;x.each(e,(function(e){he(e)||(a=!1)})),a||(t.HinaABTest.log("属性值类型只能是数组，但是只允许包含字符串项。属性 [".concat(i,"] 的值 ").concat(JSON.stringify(e)," 无效")),n.verify_success=!1)}else he(e)||le(e)||oe(e)||ae(e)||se(e)||(t.HinaABTest.log("属性 [".concat(i,"] 的值 [").concat(JSON.stringify(e),"] 无效")),n.verify_success=!1);se(e)?r[i]=x.formatDate(e):he(e)?r[i]=e:r[i]=JSON.stringify(e)})),e.custom_properties=r,n.para=e,n)}}])}(),fe=function(){return i((function e(t){n(this,e),this.HinaABTest=t}),[{key:"init",value:function(e,t){var n=this;this.HinaABTest.fetchData.init(e,t);!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.visible,n=void 0===t?function(){}:t,r=e.hidden,i=void 0===r?function(){}:r,a=[{hidden:"hidden",change:"visibilitychange"},{hidden:"mozHidden",change:"mozvisibilitychange"},{hidden:"msHidden",change:"msvisibilitychange"},{hidden:"webkitHidden",change:"webkitvisibilitychange"}].find((function(e){return e.hidden in document}))||{},o=function(){document[a.hidden]?i():n()};a.hidden?document.addEventListener(a.change,o,!0):(window.addEventListener("focus",n,!0),window.addEventListener("blur",i,!0))}({visible:function(){D.log("页面显示"),n.HinaABTest.fetchData.start()},hiddenFun:function(){D.log("页面隐藏"),n.HinaABTest.fetchData.stop()}}),x.mitt.on("changeDistinctId",(function(){n.HinaABTest.fetchData.clearExpsCache(),n.HinaABTest.updateStorage(),n.HinaABTest.fetchData.getServerData()}))}}])}(),pe=x.check,ge=pe.isArray,Te=pe.isObject,me=pe.isString;var ve=function(){return i((function t(){n(this,t),o(this,"dealResponseData",(function(t,n){x.check.isObject(t)?"SUCCESS"===e.status?x.isArray(t.results)&&(this.fetchData.updateExpsCache(t),this.updateLocalData(n)):"FAILED"===t.status&&this.log("获取试验失败：error_type：".concat(t.error_type,"，error：").concat(t.error)):this.log("试验数据解析失败，response：",t)})),this.libVersion="1.0.0",this.pluginVersion="1.0.0",this.pluginName="HinaABTest",this.isShowAccountId=!1,this.isInit=!1,this.para={},this.defaultPara={url:"",path:"",projectKey:"",timeoutMilliseconds:3e3,updateInterval:6e5,collectBridgeStatus:!0,encryptCookie:!1},this.state={platform:"",storage:{name:"hina_webjs_sdk_abtest",latestName:"hina_webjs_sdk_abtest_latest"}},this.bridgeState="",this.codeExpData={},this.results=[],this.outList=[],this.triggerList={},this.outTriggerList={},this.defaultTrackConfig={triggerSwitch:!0,propertySetSwitch:!1,triggerContentExt:["abtest_experiment_version","abtest_experiment_result_id"]},this.trackConfig={},this.updateTime=null,this.hd=null,this.normalStore=new Se(this),this.verifyStore=new de(this)}),[{key:"init",value:function(e,t){var n=this;if(this.isInit)return D.log("A/B Testing SDK 重复初始化！只有第一次初始化有效！"),!1;var r=x.getQueryParam(location.href,"showAccountId");if(!this.isShowAccountId&&"true"===r){var i=J.getAccountId();console.log("%cAccountId","color: #26bfa5;",i),this.isShowAccountId=!0}this.isInit=!0;n.initHinaABTest(e,t)}},{key:"initHinaABTest",value:function(e,t){return e?(this.hd=e,function(e){e._.extend(x,e._)}(e),x.check.isObject(t)?(x.localStorage.isSupport()||D.log("A/B Testing 初始化失败，浏览器不支持 localStorage"),this.abtestingPara=t,this.fetchData=new be(this),this.store=new fe(this),this.initTest(),!0):(D.log("A/B Testing SDK 初始化失败，请传入正确的初始化参数! para:",t),!1)):(console.error("A/B Testing 初始化失败，Web JS SDK 没有初始化完成"),!1)}},{key:"initTest",value:function(){this.normalStore.init(this.abtestingPara),this.registerProperty()}},{key:"asyncFetchABTest",value:function(){D.error("asyncFetchABTest 调用失败，A/B Testing 未初始化")}},{key:"fastFetchABTest",value:function(){D.error("fastFetchABTest 调用失败，A/B Testing 未初始化")}},{key:"fetchCacheABTest",value:function(){D.error("fetchCacheABTest 调用失败，A/B Testing 未初始化")}},{key:"searchLocalExp",value:function(e,t){return(this.codeExpData[e]||[]).find((function(e){return e.name===t}))}},{key:"getExpResult",value:function(e,t){var n=e.defaultValue,r=t||this.searchLocalExp(e.abtestExperimentId,e.paramName);return x.check.isObject(r)?x.check.isObject(r.jsConfig)&&(r.jsConfig.type===e.valueType?n=r.jsConfig.value:D.log("实验结果类型与代码期望类型不一致，paramName：".concat(e.paramName,"，当前返回类型为：").concat(r.jsConfig.type,"，代码期望类型为：").concat(e.valueType))):D.log("本地数据未查询到实验数据，实验参数名称：".concat(e.paramName)),this.trackOutTestTrigger("CODE",e),n}},{key:"trackOutTestTrigger",value:function(e,t){var n=this.outList,r=!1,i={},a=this;if(ge(n)&&n.length>0&&(x.each(n,(function(n){Te(n)&&!r&&("CODE"===e&&Te(n.variables)?x.each(n.variables,(function(e){var o=a.getRelativeValue(e.value,e.type);!r&&o&&Te(e)&&t.paramName===e.name&&o.type===t.valueType&&(r=!0,i=n)})):"LINK"===e&&me(n.control_link)&&me(n.link_match_type)?("REGEXP"===n.link_match_type?x.checkUrlIsRegexp(n.control_link,n.regexp_flags):x.checkUrlIsMatch(n.control_link,n.link_match_type))&&(r=!0,i=n):"VISUAL"===e&&me(n.control_link)&&me(n.link_match_type)&&x.checkUrlIsMatch(n.control_link,n.link_match_type)&&(r=!0,i=n))})),r)){var o=a.hd.store.getDistinctId();i.subject_id&&i.subject_name&&(o="".concat(i.subject_name).concat(i.subject_id)),a.sendTriggerEvent("outTriggerList",o,i,{})}}},{key:"registerProperty",value:function(){if(this.trackConfig.propertySetSwitch){var e=this.hd.store.getUnionId(),t=["USER".concat(e.login_id||e.anonymous_id),"DEVICE".concat(e.anonymous_id)],n=this.hd.bridge.is_verify_success?this.getLatestSubject():[],r=[],i=[];x.each(this.triggerList,(function(e,i){(t.includes(i)||n.includes(i))&&x.check.isArray(e)&&x.each(e,(function(e){e.result_id&&r.push(e.result_id)}))})),x.check.isArray(this.results)&&x.each(this.results,(function(e){e.abtest_experiment_result_id&&i.push(e.abtest_experiment_result_id)})),r.length>0&&this.hd.registerPage({abtest_result:r}),i.length>0&&this.hd.registerPage({abtest_dispatch_result:i})}else this.hd.clearPageRegister(["abtest_result","abtest_dispatch_result"])}},{key:"getRelativeValue",value:function(e,t){var n={},r={INTEGER:function(e){var t=parseFloat(e);isNaN(t)?D.log("原始数据 INTEGER 类型解析异常",e):(n.value=t,n.type="Number")},STRING:function(e){x.check.isString(e)?(n.value=e,n.type="String"):D.log("原始数据 STRING 类型解析异常",e)},JSON:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e){try{var t=JSON.parse(e);x.check.isObject(t)?(n.value=t,n.type="Object"):D.log("原始数据 JSON 类型解析异常",e)}catch(t){D.log("原始数据 JSON 类型解析异常",e)}})),BOOLEAN:function(e){"true"===e.toLocaleLowerCase()?(n.value=!0,n.type="Boolean"):"false"===e.toLocaleLowerCase()?(n.value=!1,n.type="Boolean"):D.log("原始数据 BOOLEAN 类型解析异常",e)}};try{r[t]?r[t](e):D.log("试验数据类型解析失败","".concat(t,", ").concat(e))}catch(n){D.log(n,e,t)}return n}},{key:"updateStorage",value:function(e){var t=(new Date).getTime(),n={result:this.results,updateTime:t,triggerList:this.triggerList,distinct_id:e||this.hd.store.getAccountId(),outTriggerList:this.outTriggerList,outList:this.outList,trackConfig:this.trackConfig},r=JSON.stringify(n);x.localStorage.set(this.state.storage.name,r)}},{key:"dealCodeExpResponseData",value:function(e,t){x.check.isObject(e)?"SUCCESS"===e.status?x.check.isArray(e.results)&&(this.fetchData.updateExpsCache(e),this.updateCodeExpLocalData(t)):"FAILED"===e.status&&D.log("获取实验失败：error_type: ".concat(e.error_ype,", error: ").concat(e.error)):D.log("实验数据解析失败，response: ".concat(e))}},{key:"updateCodeExpLocalData",value:function(e){this.analyzeCodeExpData(),this.updateStorage(e)}},{key:"analyzeCodeExpData",value:function(){x.check.isArray(this.results)&&(this.resolveVariables(),this.registerProperty())}},{key:"resolveVariables",value:function(){var e=this;this.codeExpData={};var t=x.check.isObject,n=x.check.isArray;this.results.forEach((function(r){t(r)&&r.variables&&n(r.variables)&&(e.codeExpData[r.abtest_experiment_id]=r.variables,r.variables.forEach((function(n){t(n)&&n.name&&n.value&&(n.jsConfig=e.getRelativeValue(n.value,n.type))})))}))}}])}(),Se=function(){return i((function e(t){n(this,e),this.HinaABTest=t,this.para=t.para}),[{key:"init",value:function(e){if(!this.setPara(e))return!1;this.bridgeState="ab_no_host_bridge";var t=navigator.userAgent;this.HinaABTest.state.platform=/Android|webOS|iPhone|iPod|BlackBerry/i.test(t)?"H5":"Web",D.log("A/B Testing SDK 初始化成功，试验 URL:",e.url),this.HinaABTest.store.init(this.getResultFromServer,this)}},{key:"setPara",value:function(e){var t=x.getQueryParam(e.url,"project-key");if(!t)return D.log("A/B Testing SDK 初始化失败，请使用正确的 URL（必须包含 project-key）！"),!1;e.projectKey=t;var n=this.HinaABTest.verifyStore.para("A/B Testing SDK 初始化",e,{timeoutMilliseconds:"not_essential"});return this.HinaABTest.para=c(c({},this.HinaABTest.defaultPara),n.para),x.check.isBoolean(this.HinaABTest.para.collectBridgeStatus)||(this.HinaABTest.para.collectBridgeStatus=!0),x.check.isNumber(this.HinaABTest.para.updateInterval)||(this.HinaABTest.para.updateInterval=6e5),this.HinaABTest.hd.config.encryptCookie&&(this.HinaABTest.para.encryptCookie=!0),this.HinaABTest.hd.config.sdkId&&(this.HinaABTest.state.storage.name+="_".concat(this.HinaABTest.hd.config.sdkId)),!0}},{key:"asyncFetch",value:function(e){var t=this,n=this;this.getCodeExpResultFromServer({para:e,suc:function(r){if(x.check.isObject(r)&&"SUCCESS"===r.status){var i=n.HinaABTest.getExpResult(e);e.callback(i)}else e.callback(e.defaultValue);t.sendHaData(e)},err:function(t){e.callback(e.defaultValue)}})}},{key:"createRequestData",value:function(e){var t=this.HinaABTest,n=t.hd.storeState,r="";x.check.isEmptyObject(n)||(r=J.getAnonymousId());var i={anonymous_id:r,platform:t.state.platform,abtest_lib_version:t.state.libVersion,properties:{H_is_first_day:z()}};return x.check.isObject(e.properties)&&(i.properties=c(c({},i.properties),e.properties)),x.check.isObject(e.custom_properties)&&(i.customProperties=c({},e.customProperties),i.paramName=e.paramName),J.getFirstId()&&(i.login_id=J.getAccountId()),i}},{key:"getCodeExpResultFromServer",value:function(e){this.getResultFromServer(e,!0)}},{key:"getResultFromServer",value:function(e,t){var n=this,r=x.check.isObject(e)&&e.para||{},i=e.suc,a=e.err,o=this.createRequestData(r);D.log("向服务器发起试验请求");var s=J.getAccountId();V({url:this.HinaABTest.abtestingPara.url,type:"POST",data:JSON.stringify(o),credentials:!1,contentType:"application/json",projectKey:this.HinaABTest.abtestingPara.projectKey,timeout:r.timeoutMilliseconds||this.HinaABTest.para.timeoutMilliseconds,cors:!0,success:function(e){t?n.HinaABTest.dealCodeExpResponseData(e,s):n.HinaABTest.dealResponseData(e,s),i&&i(e),n.HinaABTest.fetchData.setNextFetch()},error:function(e){D.log("服务器请求发送失败",e),a&&a(),n.HinaABTest.fetchData.setNextFetch()}})}},{key:"asyncFetchABTest",value:function(e){if(!x.check.isObject(e))return D.log("asyncFetchABTest 调用失败，参数未正确配置"),!1;var t=this.HinaABTest.verifyStore.para("asyncFetchABTest",e,{paramName:"essential",valueType:"essential",defaultValue:"essential",callback:"essential",timeoutMilliseconds:"not_essential",properties:"not_essential"});if(t.verify_success){e=t.para;var n=this.HinaABTest.verifyStore.resolveCustomProperties(e);n.verify_success?(e=n.para,this.HinaABTest.normalStore.asyncFetch(e)):e.callback(e.defaultValue)}else e.callback(e.defaultValue)}},{key:"fetchCacheABTest",value:function(e){if(x.check.isObject(e)){var t=this.HinaABTest.verifyStore.para("fetchCacheABTest",e,{paramName:"essential",valueType:"essential",defaultValue:"essential"});return t.verify_success?this.HinaABTest.getExpResult(t.para):void 0}D.log("fetchCacheABTest 调用失败，参数未正确配置")}},{key:"fastFetchABTest",value:function(e){if(!x.check.isObject(e))return D.log("fastFetchABTest 调用失败，参数未正确配置"),!1;var t=this.HinaABTest.verifyStore.para("fastFetchABTest",e,{abtestExperimentId:"essential",paramName:"essential",valueType:"essential",defaultValue:"essential",callback:"essential",timeoutMilliseconds:"not_essential",properties:"not_essential"});if(t.verify_success){e=t.para;var n=this.HinaABTest.searchLocalExp(e.abtestExperimentId,e.paramName);if(x.check.isObject(n)){var r=this.HinaABTest.getExpResult(e,n);e.callback(r),this.sendHaData(e)}else{var i=this.HinaABTest.verifyStore.resolveCustomProperties(e);i.verify_success?(e=i.para,D.log("fastFetchABTest 缓存中未读取到数据，发起请求"),this.HinaABTest.normalStore.asyncFetch(e)):(e.callback(e.defaultValue),this.sendHaData(e))}}}},{key:"sendHaData",value:function(e){var t=e.abtestExperimentId,n=this.HinaABTest.results.find((function(e){return e.abtest_experiment_id.toString()===t.toString()})).abtest_experiment_group_id||null;t&&n&&this.HinaABTest.hd.track("H_ABTestTrigger",{H_abtest_experiment_id:t,H_abtest_experiment_group_id:n})}}])}(),be=function(){return i((function e(t){n(this,e),this.timer=null,this.method=null,this.context=null,this.HinaABTest=t}),[{key:"init",value:function(e,t){this.method=e,this.context=t,this.start(!0)}},{key:"updateExpsCache",value:function(e){var t=e.results,n=void 0===t?[]:t,r=e.triggerList,i=e.outList,a=e.outTriggerList,o=e.trackConfig,s=this.HinaABTest;s.results=n,x.check.isArray(i)&&(s.outList=i),x.check.isObject(r)&&(s.triggerList=r),x.check.isObject(a)&&(s.outTriggerList=a),n.forEach((function(e){Te(e)&&e.variables&&ge(e.variables)&&(s.codeExpData[e.abtest_experiment_id]=e.variables,e.variables.forEach((function(e){Te(e)&&e.name&&e.value&&(e.jsConfig=s.getRelativeValue(e.value,e.type))})))})),s.trackConfig=x.check.isObject(o)?o:s.defaultTrackConfig}},{key:"clearExpsCache",value:function(){var e=this.HinaABTest;e.codeExpData={},e.results=[],this.outList=[],this.trackConfig=e.defaultTrackConfig}},{key:"setNextFetch",value:function(e){var t=this,n=this.HinaABTest.para.updateInterval;this.clearFetchTimer(this.timer),this.timer=setTimeout((function(){t.getServerData(e)}),n)}},{key:"clearFetchTimer",value:function(){clearTimeout(this.timer),this.timer=null}},{key:"getServerData",value:function(e){var t=this.HinaABTest;this.method.call(this.context,{suc:function(n){e&&"ab_bridge_ok"===t.bridgeState&&(n=n.data)},err:function(){}})}},{key:"start",value:function(e){var t=Date.now(),n=x.getStorageData(this.HinaABTest.state.storage.name,this.HinaABTest.hd),r=this.HinaABTest.hd.store.getAccountId();if(n&&x.check.isObject(n)&&n.distinct_id===r){var i=n.updateTime;if(i&&x.check.isNumber(i)&&t-i>0&&t-i<this.HinaABTest.para.updateInterval){e&&this.updateExpsCache(n),D.log("数据未更新",i,t);var a=t-i,o=this.HinaABTest.para.updateInterval-a;this.setNextFetch(o)}else D.log("缓存数据超时",i,t),this.getServerData(e)}else{var s=this.HinaABTest.updateTime;s&&x.check.isNumber(s)&&t-s>0&&t-s<this.HinaABTest.para.updateInterval?D.log("数据未更新",s,t):this.getServerData(e)}}},{key:"stop",value:function(){D.log("清空拉取定时器"),this.clearFetchTimer()}}])}();window.MutationObserver||(window.MutationObserver=function(e){var t=this;this.callback=e,this.elements=[],this.observer=function(e){t.callback([{type:"childList",addedNodes:[e.target]}])}},MutationObserver.prototype.observe=function(e,t){this.elements.push(e),e.addEventListener("DOMNodeInserted",this.observer)},MutationObserver.prototype.disconnect=function(){var e=this;this.elements.forEach((function(t){t.removeEventListener("DOMNodeInserted",e.observer)}))});var ye=window.location,_e=function(){return i((function e(){n(this,e),this.ctx={},this.option={},this.isInited=!1}),[{key:"init",value:function(e,t){this.ctx=e,this.option=t,this.isInited||(this.isInited=!0,x.check.isObject(t)&&x.check.isArray(t.linker)&&t.linker.length>0?(this.setRefferId(t),this.addClickListen(),this.option=function(e){for(var t=e.length,n=[],r=0;r<t;r++)/[A-Za-z0-9]+\./.test(e[r].part_url)&&x.check.isBoolean(e[r].after_hash)?n.push(e[r]):D.log("The configuration of linker "+(r+1)+" is not supported.Please check format");return n}(t.linker)):D.log("siteLinker plugin: Please configure the linker parameter"))}},{key:"getPartUrl",value:function(e){var t=this.option.length;if(t)for(var n=0;n<t;n++)if(e.indexOf(this.option[n].part_url)>-1)return!0;return!1}},{key:"getPartHash",value:function(e){var t=this.option.length;if(t)for(var n=0;n<t;n++)if(e.indexOf(this.option[n].part_url)>-1)return this.option[n].after_hash;return!1}},{key:"getCurrentId",value:function(){var e=this.ctx.store.getAccountId()||"",t=this.ctx.store.getFirstId()||""?"f"+e:"d"+e;return x.encodeURIComponent(t)}},{key:"rewriteUrl",value:function(e,t){var n=/([^?#]+)(\?[^#]*)?(#.*)?/.exec(e),r="";if(n){var i,a=n[1]||"",o=n[2]||"",s=n[3]||"",c="_hnsdk="+this.getCurrentId();if(this.getPartHash(e))i=s.indexOf("_hnsdk"),r=s.indexOf("?")>-1?i>-1?a+o+"#"+s.substring(1,i)+l(s.substring(i,s.length)):a+o+"#"+s.substring(1)+"&"+c:a+o+"#"+s.substring(1)+"?"+c;else i=o.indexOf("_hnsdk"),r=/^\?(\w)+/.test(o)?i>-1?a+"?"+l(o.substring(1))+s:a+"?"+o.substring(1)+"&"+c+s:a+"?"+o.substring(1)+c+s;return t&&(t.href=r),r}function l(e){var t=e.split("&"),n=[];return x.each(t,(function(e){e.indexOf("_hnsdk=")>-1?n.push(c):n.push(e)})),n.join("&")}}},{key:"getUrlId",value:function(){var e=ye.href.match(/_hnsdk=([aufd][^\?\#\&\=]+)/);return x.check.isArray(e)&&e[1]?x.decodeURIComponent(e[1]):""}},{key:"setRefferId",value:function(e){var t=this.ctx.store.getAccountId(),n=this.getUrlId();if(""===n)return!1;var r="d"===n.substring(0,1);(n=n.substring(1))!==t&&(r?(this.ctx.setDeviceUId(n,!0),this.ctx.store.getFirstId()&&this.ctx.sendRequest({anonymous_id:n,account_id:t,type:"track_signup",event:"H_SignUp",properties:{}})):this.ctx.store.getFirstId()&&!e.re_login||this.ctx.setUserUId(n))}},{key:"addClickListen",value:function(){var e=this,t=function(t){var n,r,i,a,o=t.target,s=null===(n=o.tagName)||void 0===n?void 0:n.toLowerCase(),c=o.parentNode,l=null==c||null===(r=c.tagName)||void 0===r?void 0:r.toLowerCase();if("a"===s&&o.href||"a"===l&&c.href){"a"===s&&o.href?(i=o.href,a=o):(i=c.href,a=c);var u=x.URL(i).protocol;"http:"!==u&&"https:"!==u||e.getPartUrl(i)&&e.rewriteUrl(i,a)}};x.addEvent(document,"mousedown",t),window.PointerEvent&&"maxTouchPoints"in window.navigator&&window.navigator.maxTouchPoints>=0&&x.addEvent(document,"pointerdown",t)}}])}(),Pe=function(){return i((function e(){n(this,e),this.ctx={},this.option={},this.isInited=!1}),[{key:"init",value:function(e,t){var n=this;this.ctx=e,this.option=t;var r=function(){var e=0,t=window.performance||window.webkitPerformance||window.msPerformance||window.mozPerformance,i=window.location,a={H_url:i.href,H_title:document.title,H_url_path:i.pathname,H_url_hash:i.hash,H_referrer:x.getReferrer(null,!0)};if(t){if(e=n.getDuration(t)||n.getDurationCompatible(t),n.getPageSize(t,a),e>0){var o,s,c=1800;if(x.check.isObject(n.option)&&null!==(o=n.option)&&void 0!==o&&o.max_duration)c=null===(s=n.option)||void 0===s?void 0:s.max_duration;e=Number((e/1e3).toFixed(3)),(!x.check.isNumber(c)||c<=0||e<=c)&&(a.event_duration=e)}n.isInited||(n.ctx.track("H_WebPageLoad",a),n.isInited=!0),window.removeEventListener&&window.removeEventListener("load",r)}else D.log("your browser not support performance API")};"complete"===document.readyState?r():window.addEventListener&&window.addEventListener("load",r)}},{key:"getPageSize",value:function(e,t){if(e.getEntries&&x.check.isFunction(e.getEntries)){for(var n=e.getEntries(),r=0,i=0;i<n.length;i++)"transferSize"in n[i]&&(r+=n[i].transferSize);x.check.isNumber(r)&&r>=0&&r<10737418240&&(t.H_page_resource_size=Number((r/1024).toFixed(3)))}}},{key:"getDurationCompatible",value:function(e){var t=0;if(e.timing){var n=e.timing;0!==n.fetchStart&&x.check.isNumber(n.fetchStart)&&0!==n.domContentLoadedEventEnd&&x.check.isNumber(n.domContentLoadedEventEnd)?t=n.domContentLoadedEventEnd-n.fetchStart:D.log("performance data parsing exception")}return t}},{key:"getDuration",value:function(e){var t=0;x.check.isFunction(e.getEntriesByType)&&(t=((e.getEntriesByType("navigation")||[{}])[0]||{}).domContentLoadedEventEnd||0);return t}}])}(),Ae=function(){return i((function e(){n(this,e),this.startTime=x.now(),this.currentPageUrl=document.referrer,this.url=ye.href,this.title=document.title||"",this.pageShowStatus=!0,this.pageHiddenStatus=!1,this.timer=null,this.heartbeatIntervalTime=5e3,this.heartbeatIntervalTimer=null,this.pageId=null,this.maxDuration=432e3,this.storageName="hinasdk_pageleave_"}),[{key:"init",value:function(e,t){if(this.ctx=e,t){this.option=t;var n=t.heartbeat_interval_time;n&&x.check.isNumber(1*n)&&1*n>0&&(this.heartbeatIntervalTime=1e3*n);var r=t.max_duration;r&&x.check.isNumber(1*r)&&1*r>0&&(this.maxDuration=r)}this.pageId=Number(String(x.getRandom()).slice(2,5)+String(x.getRandom()).slice(2,4)+String(x.now()).slice(-4)),this.addPageLeaveEventListener(),document.hidden?this.pageShowStatus=!1:this.addHeartBeatInterval()}},{key:"refreshPageEndTimer",value:function(){var e=this;this.timer&&(clearTimeout(this.timer),this.timer=null),this.timer=setTimeout((function(){e.pageHiddenStatus=!1}),5e3)}},{key:"hiddenStatusHandler",value:function(){clearTimeout(this.timer),this.timer=null,this.pageHiddenStatus=!1}},{key:"pageStartHandler",value:function(){this.startTime=x.now(),!0===document.hidden?this.pageShowStatus=!1:this.pageShowStatus=!0,this.url=ye.href,this.title=document.title}},{key:"pageEndHandler",value:function(){if(!this.pageHiddenStatus){var e=this.getPageLeaveProperties();this.pageShowStatus||delete e.event_duration,this.pageShowStatus=!1,this.pageHiddenStatus=!0,this.isCollectUrl(this.url)&&this.ctx.track("H_WebPageLeave",e),this.refreshPageEndTimer(),this.delHeartBeatData()}}},{key:"addPageLeaveEventListener",value:function(){this.addPageStartListener(),this.addPageSwitchListener(),this.addSinglePageListener(),this.addPageEndListener()}},{key:"addPageStartListener",value:function(){var e=this;"onpageshow"in window&&x.addEvent(window,"pageshow",(function(){e.pageStartHandler(),e.hiddenStatusHandler()}))}},{key:"addPageSwitchListener",value:function(){var e=this;x.listenPageState({visible:function(){e.pageStartHandler(),e.hiddenStatusHandler(),e.addHeartBeatInterval()},hidden:function(){e.url=ye.href,e.title=document.title,e.pageEndHandler(),e.stopHeartBeatInterval()}})}},{key:"addSinglePageListener",value:function(){var e=this;x.mitt.prepend("urlChange",(function(t){t!==ye.href&&(e.url=t,e.pageEndHandler(),e.stopHeartBeatInterval(),e.currentPageUrl=t,e.pageStartHandler(),e.hiddenStatusHandler(),e.addHeartBeatInterval())}))}},{key:"addPageEndListener",value:function(){var e=this;x.each(["pagehide","beforeunload","unload"],(function(t){"on"+t in window&&x.addEvent(window,t,(function(){e.pageEndHandler(),e.stopHeartBeatInterval()}))}))}},{key:"addHeartBeatInterval",value:function(){x.localStorage.isSupport()&&this.startHeartBeatInterval()}},{key:"startHeartBeatInterval",value:function(){var e=this;this.heartbeatIntervalTimer&&this.stopHeartBeatInterval();var t=!0;this.isCollectUrl(this.url)||(t=!1),t&&(this.heartbeatIntervalTimer=setInterval((function(){e.saveHeartBeatData()}),this.heartbeatIntervalTime),this.saveHeartBeatData("first")),this.reissueHeartBeatData()}},{key:"reissueHeartBeatData",value:function(){for(var e=x.localStorage.length-1;e>=0;e--){var t=x.localStorage.key(e);if(t&&t!==this.storageName+this.pageId&&t.indexOf(this.storageName)>-1){var n=x.readObjectVal(t);x.check.isObject(n)&&x.now()-n.time>n.heartbeat_interval_time+5e3&&(delete n.heartbeat_interval_time,this.ctx.sendRequest(n),this.delHeartBeatData(t))}}}},{key:"stopHeartBeatInterval",value:function(){this.heartbeatIntervalTimer&&clearInterval(this.heartbeatIntervalTimer),this.heartbeatIntervalTimer=null}},{key:"saveHeartBeatData",value:function(e){var t=this.getPageLeaveProperties();t.H_time=x.now(),"first"===e&&(t.event_duration=3);var n=$({type:"track",event:"H_WebPageLeave",properties:t},this.ctx);n.heartbeat_interval_time=this.heartbeatIntervalTime,x.saveObjectVal(this.storageName+this.pageId,n)}},{key:"delHeartBeatData",value:function(e){x.localStorage.isSupport()&&x.localStorage.remove(e||this.storageName+this.pageId)}},{key:"isCollectUrl",value:function(e){var t,n;return!x.check.isFunction(null===(t=this.option)||void 0===t?void 0:t.isCollectUrl)||(!x.check.isString(e)||(null===(n=this.option)||void 0===n?void 0:n.isCollectUrl(e)))}},{key:"getPageLeaveProperties",value:function(){var e,t,n,r=(x.now()-this.startTime)/1e3;(!x.check.isNumber(r)||r<0||r>this.maxDuration)&&(r=0),r=Number(r.toFixed(3));var i=x.getReferrer(this.currentPageUrl),a=(null===(e=document.documentElement)||void 0===e?void 0:e.scrollTop)||window.pageYOffset||(null===(t=document.body)||void 0===t?void 0:t.scrollTop)||0;a=Math.round(a)||0;var o={H_title:this.title,H_url:this.url,H_url_path:x.URL(this.url).pathname,H_url_hash:x.URL(this.url).hash,H_referrer_host:i?x.getHostname(i):"",H_referrer:i,H_viewport_position:a};return r&&(o.event_duration=r),o=x.extend(o,null===(n=this.option)||void 0===n?void 0:n.custom_props)}}])}(),ke={SiteLinker:_e,PageLoad:Pe,PageLeave:Ae,HinaABTest:ve},Me={name:"",showLog:!1,autoTrackConfig:{clickAutoTrack:!1,stayAutoTrack:!1,pageviewAutoTrack:!1,pageLeaveAutoTrack:!1},stayAutoTrackConfig:{},imgUseCrossOrigin:!1,isSinglePage:!1,maxStringLength:1024,batchSend:!1,appJsBridge:!1,sendType:"image",dataSendTimeout:3e3,isTrackDeviceId:!1,presetProperties:{latest_utm:!0,latest_utm_source:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,url:!0,title:!0}},Ee=function(){return i((function e(){return n(this,e),e.instance||(e.instance=this,this.config={},this.initialized=!1,this._=x),e.instance}),[{key:"setConfig",value:function(e){x.check.isObject(e)&&x.extend(this.config,e)}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"init",value:function(e){if(x.check.isEmptyObject(this.config)){if(this.setConfig(x.extend({},Me,e)),D.showLog=this.getConfig("showLog"),O.serverUrl=this.getConfig("serverUrl"),!O.checkServerUrl(this.getConfig.serverUrl))return;x.initUrlChange(),J.load(this.config),Y(this.config),this.store=J;var t=this.getConfig("sendType");["image","ajax","beacon"].includes(t)||this.setConfig({sendType:"image"}),(!0===this.getConfig("batchSend")||x.check.isObject(this.getConfig("batchSend")))&&(this.batchSender=new q(this.config),this.batchSender.batchInterval());var n=this.getConfig("autoTrackConfig"),r=this.getConfig("stayAutoTrackConfig"),i=n.pageviewAutoTrack,a=n.pageLeaveAutoTrack,o=new re(n,r,this);this.autoTrackInstance=o,o.initWebClick(),o.initWebStay(),"auto"===i?o.autoTrack():("singlePage"===i||this.getConfig("isSinglePage"))&&(this.config.isSinglePage=!0,o.listenSinglePage()),a&&(x.check.isObject(a)?this.use("PageLeave",a):this.use("PageLeave")),this.initialized=!0,D.log("hinaSDK initialized successfully"),x.mitt.emit("hasInit")}else D.log("hinaSDK has been initialized")}},{key:"sendRequest",value:function(e,t){if((e=$(e,this)).send_time=x.now(),D.log(e),this.getConfig("appJsBridge")){var n=window.Hina_Cloud_H5_Bridge||{};if(x.check.isObject(n)&&n.track)return n.track(e.event,e.type,JSON.stringify(e)),x.check.isFunction(t)&&t(),void D.log("The data has been sent to the Android side");if("iOS"===x.info.os()){var r,i,a=null===(r=window.webkit)||void 0===r||null===(i=r.messageHandlers)||void 0===i?void 0:i.hinaNativeTracker;if(null!=a&&a.postMessage){var o=JSON.stringify({eventName:e.event,eventType:e.type,properties:e});return a.postMessage(o),x.check.isFunction(t)&&t(),void D.log("The data has been sent to the iOS side")}}D.log("The app JSBridge data transmission has failed.")}if(this.getConfig("batchSend"))new q(this.config).add(e);else{x.check.isString(e)||(e=JSON.stringify(e));var s=x.base64Encode(e),c="crc="+x.hashCode(s),l="data="+x.encodeURIComponent(s)+"&ext="+x.encodeURIComponent(c),u=this.getConfig("sendType"),h={callback:this.getConfig("globalCallback"),data:l,serverUrl:this.getConfig("serverUrl"),dataSendTimeout:this.getConfig("dataSendTimeout")};switch(u){case"ajax":new j(h).run();break;case"beacon":new W(h).run();break;default:new X(x.extend(h,{imgUseCrossOrigin:this.getConfig("imgUseCrossOrigin")})).run()}}}},{key:"quick",value:function(e){for(var t={autoTrack:this.autoTrackInstance.autoTrack,autoTrackSinglePage:this.autoTrackInstance.autoTrackSinglePage},n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t[e].call(this.autoTrackInstance,r)}},{key:"track",value:function(e,t,n){var r=x.check.isFunction(n)?n:function(){};x.check.isString(e)&&(x.check.isObject(t)||x.check.isUndefined(t))?this.sendRequest({type:"track",event:e,properties:t},r):D.log("eventName must be a sting and properties must be an object")}},{key:"setUserUId",value:function(e,t){if(e&&""!==e)if(x.check.isNumber(e)||x.check.isString(e)){e=String(e);var n=this.store.getFirstId(),r=this.store.getAccountId(),i=this.store.getAnonymousId(),a=this.store.getDeviceId();e!==r?(n||this.store.set("firstId",i||a),this.store.setAccountId(e),this.sendRequest({account_id:this.store.getAccountId(),type:"track_signup",event:"H_SignUp",properties:{}},t)):console.log("setUserUId: uid is equal to account_id, , failed to execute setUserUId")}else D.log("setUserUId: uid must be string or number");else D.log("setUserUId: uid is empty")}},{key:"getDeviceUId",value:function(){return this.store.getAnonymousId()}},{key:"setDeviceUId",value:function(e,t){var n=this.store.getFirstId();if(x.check.isUndefined(e)){var r=x.UUID();n?this.store.set("firstId",r):this.store.setAccountId(r)}else(x.check.isNumber(e)||x.check.isString(e))&&(e=String(e),!0===t?n?this.store.set("firstId",e):this.store.set("accountId",e):n?this.store.change("firstId",e):this.store.change("accountId",e))}},{key:"userSet",value:function(e,t){x.check.isObject(e)&&!x.check.isEmptyObject(e)&&this.sendRequest({type:"user_set",properties:e},t)}},{key:"userSetOnce",value:function(e,t){x.check.isObject(e)&&!x.check.isEmptyObject(e)&&this.sendRequest({type:"user_setOnce",properties:e},t)}},{key:"userAdd",value:function(e,t){x.check.isString(e)&&(e=o({},e,1));x.check.isObject(e)&&!x.check.isEmptyObject(e)&&function(e){for(var t in e)if(t in e&&!/-*\d+/.test(String(e[t])))return D.log("userAdd: value is must be number"),!1;return!0}(e)&&this.sendRequest({type:"user_add",properties:e},t)}},{key:"userUnset",value:function(e,t){var n={};x.check.isString(e)&&(e=[e]);x.check.isArray(e)?(x.each(e,(function(e){x.check.isString(e)?n[e]=!0:D.log("userUnset: value inside the array must be string and have already been filtered out",e)})),this.sendRequest({type:"user_unset",properties:n},t)):D.log("userUnset: param must be an array or string")}},{key:"userDelete",value:function(e){this.sendRequest({type:"user_delete"},e),this.store.setAccountId(null),this.store.set("firstId","")}},{key:"registerCommonProperties",value:function(e){x.extend(Q.currentProps,e)}},{key:"getPresetProperties",value:function(){return e=Q.getUmtsParams("H_",""),t=e.allUtms,n=e.otherUtms,r={H_is_first_day:J.isFirstDay,H_is_first_time:J.isFirstTime,device_id:J.getDeviceId(),anonymous_id:J.getAnonymousId(),account_id:J.getAccountId(),properties:c({},Q.currentProps)},x.extend(r.properties,t,n,Q.getPresetProperties(),Q.getPageProperties()),r;var e,t,n,r}},{key:"clearPageRegister",value:function(e){return Q.clearPageRegister(e)}},{key:"use",value:function(e,t){if(x.check.isString(e)||x.check.isObject(e)){var n,r;if(x.check.isObject(e)&&(n=e,r=e.name),x.check.isString(e)){if(!(e in ke))return void D.log("pluginName not found");n=ke[e],r=e}n=new n,window[r]=n,n.init&&x.check.isFunction(n.init)&&n.init(this,t)}else D.log("pluginName must be string or object")}}])}(),He=new Proxy(new Ee,{get:function(e,t){return x.check.isFunction(e[t])?function(){if(e.initialized||"init"===t){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e[t].apply(e,r)}console.log("sdk not yet initialized!")}:e[t]}});window.hinaDataStatistic=He;var Ce,Ge,we,Be,Ie=-1,De=function(e){addEventListener("pageshow",(function(t){t.persisted&&(Ie=t.timeStamp,e(t))}),!0)},Oe=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},xe=function(){var e=Oe();return e&&e.activationStart||0},Le=function(e,t){var n=Oe(),r="navigate";return Ie>=0?r="back-forward-cache":n&&(document.prerendering||xe()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},Re=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},Ne=function(e,t,n,r){var i,a;return function(o){t.value>=0&&(o||r)&&((a=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=a,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},Fe=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},Ue=function(e){var t=function(t){"pagehide"!==t.type&&"hidden"!==document.visibilityState||e(t)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},Ve=function(e){var t=!1;return function(n){t||(e(n),t=!0)}},je=-1,Xe=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},We=function(e){"hidden"===document.visibilityState&&je>-1&&(je="visibilitychange"===e.type?e.timeStamp:0,qe())},Ke=function(){addEventListener("visibilitychange",We,!0),addEventListener("prerenderingchange",We,!0)},qe=function(){removeEventListener("visibilitychange",We,!0),removeEventListener("prerenderingchange",We,!0)},ze=function(){return je<0&&(je=Xe(),Ke(),De((function(){setTimeout((function(){je=Xe(),Ke()}),0)}))),{get firstHiddenTime(){return je}}},Je=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},Qe=[1800,3e3],Ye=function(e,t){t=t||{},Je((function(){var n,r=ze(),i=Le("FCP"),a=Re("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(a.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-xe(),0),i.entries.push(e),n(!0)))}))}));a&&(n=Ne(e,i,Qe,t.reportAllChanges),De((function(r){i=Le("FCP"),n=Ne(e,i,Qe,t.reportAllChanges),Fe((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},Ze=[.1,.25],$e=[1800,3e3],et={passive:!0,capture:!0},tt=new Date,nt=function(e,t){Ce||(Ce=t,Ge=e,we=new Date,at(removeEventListener),rt())},rt=function(){if(Ge>=0&&Ge<we-tt){var e={entryType:"first-input",name:Ce.type,target:Ce.target,cancelable:Ce.cancelable,startTime:Ce.timeStamp,processingStart:Ce.timeStamp+Ge};Be.forEach((function(t){t(e)})),Be=[]}},it=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){nt(e,t),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,et),removeEventListener("pointercancel",r,et)};addEventListener("pointerup",n,et),addEventListener("pointercancel",r,et)}(t,e):nt(t,e)}},at=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,it,et)}))},ot=[100,300],st=[2500,4e3],ct={},lt=window,ut=lt.PerformanceObserver,ht=lt.performance,dt=function(){return i((function e(t,r){n(this,e),this.config=t,this.ctx=r,this.submitData={},this.performanceCallback=this.performanceCallback.bind(this)}),[{key:"init",value:function(){this.initVitails()}},{key:"setSubmitData",value:function(e,t){"string"==typeof e?this.submitData[e]=t:"object"===u(e)&&(this.submitData=_.extend(this.config,e))}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"onLongTask",value:function(e){ut.supportedEntryTypes.includes("longtask")&&new ut((function(t){var n=t.getEntries(),r=n.length,i=0;n.forEach((function(e){i+=Math.round(e.duration)})),e({H_long_task_time:i,H_long_task:r})})).observe({entryTypes:["longtask"]})}},{key:"onPageLoad",value:function(){var e=this;Re("navigation",(function(t){t.forEach((function(t){var n={H_page_load_time:Math.round(t.loadEventEnd-t.startTime)};if(0!==n.H_page_load_time){e.ctx.sendRequest("track","H_performance_page",n);var r={H_url:t.name,H_first_byte_time:Math.round(t.responseStart-t.startTime),H_unload_time:Math.round(t.unloadEventEnd-t.unloadEventStart),H_redirect_time:Math.round(t.redirectEnd-t.redirectStart),H_cache_check_time:Math.round(t.domainLookupStart-t.fetchStart),H_dns_time:Math.round(t.domainLookupEnd-t.domainLookupStart),H_tcp_time:Math.round(t.connectEnd-t.connectStart),H_ssl_time:t.secureConnectionStart>0?Math.round(t.connectEnd-t.secureConnectionStart):0,H_first_byte_response_time:Math.round(t.responseStart-t.requestStart),H_dom_ready_time:Math.round(t.domContentLoadedEventEnd-t.fetchStart),H_content_transfer_time:Math.round(t.responseEnd-t.responseStart),H_dom_parsing_time:Math.round(t.domInteractive-t.responseEnd),H_page_load_time:Math.round(t.loadEventEnd-t.startTime),H_resource_load_time:Math.round(t.responseEnd-t.startTime),H_load_event_time:Math.round(t.loadEventEnd-t.startTime)};e.ctx.sendRequest("track","H_performance_loading",r)}}))}))}},{key:"onPageUnlaod",value:function(){window.addEventListener("beforeunload",(function(){J.setSessionId("")}))}},{key:"initVitails",value:function(){!function(e,t){t=t||{},Je((function(){var n,r=ze(),i=Le("FP"),a=Re("paint",(function(e){e.forEach((function(e){"first-paint"===e.name&&(a.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-xe(),0),i.entries.push(e),n(!0)))}))}));a&&(n=Ne(e,i,$e,t.reportAllChanges),De((function(r){i=Le("FP"),n=Ne(e,i,$e,t.reportAllChanges),Fe((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))}(this.performanceCallback),Ye(this.performanceCallback),function(e,t){t=t||{},Ye(Ve((function(){var n,r=Le("CLS",0),i=0,a=[],o=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=a[0],n=a[a.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,a.push(e)):(i=e.value,a=[e])}})),i>r.value&&(r.value=i,r.entries=a,n())},s=Re("layout-shift",o);s&&(n=Ne(e,r,Ze,t.reportAllChanges),Ue((function(){o(s.takeRecords()),n(!0)})),De((function(){i=0,r=Le("CLS",0),n=Ne(e,r,Ze,t.reportAllChanges),Fe((function(){return n()}))})),setTimeout(n,0))})))}(this.performanceCallback),function(e,t){t=t||{},Je((function(){var n,r=ze(),i=Le("FID"),a=function(e){e.startTime<r.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),n(!0))},o=function(e){e.forEach(a)},s=Re("first-input",o);n=Ne(e,i,ot,t.reportAllChanges),s&&Ue(Ve((function(){o(s.takeRecords()),s.disconnect()}))),s&&De((function(){var r;i=Le("FID"),n=Ne(e,i,ot,t.reportAllChanges),Be=[],Ge=-1,Ce=null,at(addEventListener),r=a,Be.push(r),rt()}))}))}(this.performanceCallback),this.onLongTask(this.performanceCallback),this.onPageLoad(this.performanceCallback),this.onPageUnlaod(),function(e,t){t=t||{},Je((function(){var n,r=ze(),i=Le("LCP"),a=function(e){var t=e[e.length-1];t&&t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-xe(),0),i.entries=[t],n())},o=Re("largest-contentful-paint",a);if(o){n=Ne(e,i,st,t.reportAllChanges);var s=Ve((function(){ct[i.id]||(a(o.takeRecords()),o.disconnect(),ct[i.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,s,!0)})),Ue(s),De((function(r){i=Le("LCP"),n=Ne(e,i,st,t.reportAllChanges),Fe((function(){i.value=performance.now()-r.timeStamp,ct[i.id]=!0,n(!0)}))}))}}))}(this.performanceCallback)}},{key:"getMetricTime",value:function(){if(ht&&ht.timing)return new Promise((function(e){if(ut.supportedEntryTypes.includes("navigation"))Re("navigation",(function(t){t.forEach((function(t){var n={H_url:t.name,H_first_byte_time:Math.round(t.responseStart-t.startTime),H_unload_time:Math.round(t.unloadEventEnd-t.unloadEventStart),H_redirect_time:Math.round(t.redirectEnd-t.redirectStart),H_cache_check_time:Math.round(t.domainLookupStart-t.fetchStart),H_dns_time:Math.round(t.domainLookupEnd-t.domainLookupStart),H_tcp_time:Math.round(t.connectEnd-t.connectStart),H_ssl_time:t.secureConnectionStart>0?Math.round(t.connectEnd-t.secureConnectionStart):0,H_first_byte_response_time:Math.round(t.responseStart-t.requestStart),H_dom_ready_time:Math.round(t.domContentLoadedEventEnd-t.fetchStart),H_content_transfer_time:Math.round(t.responseEnd-t.responseStart),H_dom_parsing_time:Math.round(t.domInteractive-t.responseEnd),H_page_load_time:Math.round(t.loadEventEnd-t.startTime),H_resource_load_time:Math.round(t.responseEnd-t.startTime),H_load_event_time:Math.round(t.loadEventEnd-t.startTime)};e(n)}))}));else{var t=ht.timing,n={H_url:location.href,H_first_byte_time:Math.round(t.responseStart-t.startTime),H_unload_time:Math.round(t.unloadEventEnd-t.unloadEventStart),H_redirect_time:Math.round(t.redirectEnd-t.redirectStart),H_cache_check_time:Math.round(t.domainLookupStart-t.fetchStart),H_dns_time:Math.round(t.domainLookupEnd-t.domainLookupStart),H_tcp_time:Math.round(t.connectEnd-t.connectStart),H_ssl_time:t.secureConnectionStart>0?Math.round(t.connectEnd-t.secureConnectionStart):0,H_first_byte_response_time:Math.round(t.responseStart-t.requestStart),H_dom_ready_time:Math.round(t.domContentLoadedEventEnd-t.fetchStart),H_content_transfer_time:Math.round(t.responseEnd-t.responseStart),H_dom_parsing_time:Math.round(t.domInteractive-t.responseEnd),H_page_load_time:Math.round(t.loadEventEnd-t.fetchStart),H_resource_load_time:Math.round(t.responseEnd-t.startTime),H_load_event_time:Math.round(t.loadEventEnd-t.startTime)};e(n)}}));console.log("performance api is not supported in your browser")}},{key:"getSourceTime",value:function(){if(ht&&ht.getEntries)return new Promise((function(e){var t=ht.getEntries(),n=[];t&&0!==(null==t?void 0:t.length)||e(n),t.forEach((function(e){var t={};"resource"===e.entryType&&(t.name=e.name,t.initiatorType=e.initiatorType,t.nextHopProtocol=e.nextHopProtocol,t.redirectTime=(e.redirectEnd-e.redirectStart).toFixed(2),t.dnsTime=(e.domainLookupEnd-e.domainLookupStart).toFixed(2),t.tcpTime=(e.connectEnd-e.connectStart).toFixed(2),t.firstByteResponseTime=(e.responseStart-e.requestStart).toFixed(2),t.totalResponseTime=(e.responseEnd-e.requestStart).toFixed(2),n.push(t))})),e(n)}));console.log("performance.getEntries api is not supported in your browser")}},{key:"performanceCallback",value:function(e){var t=e.name,n=e.value,r=Math.round(n),i={};i="FP"===t?{H_first_paint_time:r}:"FCP"===t?{H_first_contentful_paint_time:r}:"LCP"===t?{H_largest_contentful_paint_time:r}:"FID"===t?{H_first_input_delay:r}:"CLS"===t?{H_cumulative_layout_shift:n}:e,this.ctx.sendRequest("track","H_performance_page",i)}}])}(),ft={exports:{}},pt={exports:{}};!function(e){function t(e,t){for(var n=0;n<e.length;++n)if(e[n]===t)return n;return-1}function n(e,n){var r=[],i=[];return null==n&&(n=function(e,n){return r[0]===n?"[Circular ~]":"[Circular ~."+i.slice(0,t(r,n)).join(".")+"]"}),function(a,o){if(r.length>0){var s=t(r,this);~s?r.splice(s+1):r.push(this),~s?i.splice(s,1/0,a):i.push(a),~t(r,o)&&(o=n.call(this,a,o))}else r.push(o);return null==e?o instanceof Error?function(e){var t={stack:e.stack,message:e.message,name:e.name};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}(o):o:e.call(this,a,o)}}(e.exports=function(e,t,r,i){return JSON.stringify(e,n(t,i),r)}).getSerialize=n}(pt);var gt=pt.exports,Tt=gt,mt="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{};function vt(e){return void 0===e}function St(e){return"[object Object]"===Object.prototype.toString.call(e)}function bt(e){return"[object String]"===Object.prototype.toString.call(e)}function yt(e){return"[object Array]"===Object.prototype.toString.call(e)}function _t(){if(!("fetch"in mt))return!1;try{return new Headers,new Request(""),new Response,!0}catch(e){return!1}}function Pt(e,t){var n,r;if(vt(e.length))for(n in e)kt(e,n)&&t.call(null,n,e[n]);else if(r=e.length)for(n=0;n<r;n++)t.call(null,n,e[n])}function At(e,t){if("number"!=typeof t)throw new Error("2nd argument to `truncate` function should be a number");return"string"!=typeof e||0===t||e.length<=t?e:e.substr(0,t)+"…"}function kt(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Mt(e){for(var t,n=[],r=0,i=e.length;r<i;r++)bt(t=e[r])?n.push(t.replace(/([.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")):t&&t.source&&n.push(t.source);return new RegExp(n.join("|"),"i")}function Et(e){var t,n,r,i,a,o=[];if(!e||!e.tagName)return"";if(o.push(e.tagName.toLowerCase()),e.id&&o.push("#"+e.id),(t=e.className)&&bt(t))for(n=t.split(/\s+/),a=0;a<n.length;a++)o.push("."+n[a]);var s=["type","name","title","alt"];for(a=0;a<s.length;a++)r=s[a],(i=e.getAttribute(r))&&o.push("["+r+'="'+i+'"]');return o.join("")}function Ht(e,t){return!!(!!e^!!t)}function Ct(e,t){if(Ht(e,t))return!1;var n,r,i=e.frames,a=t.frames;if(void 0===i||void 0===a)return!1;if(i.length!==a.length)return!1;for(var o=0;o<i.length;o++)if(n=i[o],r=a[o],n.filename!==r.filename||n.lineno!==r.lineno||n.colno!==r.colno||n.function!==r.function)return!1;return!0}function Gt(e){return function(e){return~-encodeURI(e).split(/%..|./).length}(JSON.stringify(e))}function wt(e){if("string"==typeof e){return At(e,40)}if("number"==typeof e||"boolean"==typeof e||void 0===e)return e;var t=Object.prototype.toString.call(e);return"[object Object]"===t?"[Object]":"[object Array]"===t?"[Array]":"[object Function]"===t?e.name?"[Function: "+e.name+"]":"[Function]":e}function Bt(e,t){return 0===t?wt(e):St(e)?Object.keys(e).reduce((function(n,r){return n[r]=Bt(e[r],t-1),n}),{}):Array.isArray(e)?e.map((function(e){return Bt(e,t-1)})):wt(e)}var It={isObject:function(e){return"object"==typeof e&&null!==e},isError:function(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return e instanceof Error}},isErrorEvent:function(e){return"[object ErrorEvent]"===Object.prototype.toString.call(e)},isDOMError:function(e){return"[object DOMError]"===Object.prototype.toString.call(e)},isDOMException:function(e){return"[object DOMException]"===Object.prototype.toString.call(e)},isUndefined:vt,isFunction:function(e){return"function"==typeof e},isPlainObject:St,isString:bt,isArray:yt,isEmptyObject:function(e){if(!St(e))return!1;for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},supportsErrorEvent:function(){try{return new ErrorEvent(""),!0}catch(e){return!1}},supportsDOMError:function(){try{return new DOMError(""),!0}catch(e){return!1}},supportsDOMException:function(){try{return new DOMException(""),!0}catch(e){return!1}},supportsFetch:_t,supportsReferrerPolicy:function(){if(!_t())return!1;try{return new Request("pickleRick",{referrerPolicy:"origin"}),!0}catch(e){return!1}},supportsPromiseRejectionEvent:function(){return"function"==typeof PromiseRejectionEvent},wrappedCallback:function(e){return function(t,n){var r=e(t)||t;return n&&n(r)||r}},each:Pt,objectMerge:function(e,t){return t?(Pt(t,(function(t,n){e[t]=n})),e):e},truncate:At,objectFrozen:function(e){return!!Object.isFrozen&&Object.isFrozen(e)},hasKey:kt,joinRegExp:Mt,urlencode:function(e){var t=[];return Pt(e,(function(e,n){t.push(encodeURIComponent(e)+"="+encodeURIComponent(n))})),t.join("&")},uuid4:function(){var e=mt.crypto||mt.msCrypto;if(!vt(e)&&e.getRandomValues){var t=new Uint16Array(8);e.getRandomValues(t),t[3]=4095&t[3]|16384,t[4]=16383&t[4]|32768;var n=function(e){for(var t=e.toString(16);t.length<4;)t="0"+t;return t};return n(t[0])+n(t[1])+n(t[2])+n(t[3])+n(t[4])+n(t[5])+n(t[6])+n(t[7])}return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},htmlTreeAsString:function(e){for(var t,n=[],r=0,i=0;e&&r++<5&&!("html"===(t=Et(e))||r>1&&i+3*n.length+t.length>=80);)n.push(t),i+=t.length,e=e.parentNode;return n.reverse().join(" > ")},htmlElementAsString:Et,isSameException:function(e,t){return!Ht(e,t)&&(e=e.values[0],t=t.values[0],e.type===t.type&&e.value===t.value&&(!function(e,t){return vt(e)&&vt(t)}(e.stacktrace,t.stacktrace)&&Ct(e.stacktrace,t.stacktrace)))},isSameStacktrace:Ct,parseUrl:function(e){if("string"!=typeof e)return{};var t=e.match(/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/),n=t[6]||"",r=t[8]||"";return{protocol:t[2],host:t[4],path:t[5],relative:t[5]+n+r}},fill:function(e,t,n,r){if(null!=e){var i=e[t];e[t]=n(i),e[t].__raven__=!0,e[t].__orig__=i,r&&r.push([e,t,i])}},safeJoin:function(e,t){if(!yt(e))return"";for(var n=[],r=0;r<e.length;r++)try{n.push(String(e[r]))}catch(e){n.push("[value cannot be serialized]")}return n.join(t)},serializeException:function e(t,n,r){if(!St(t))return t;r="number"!=typeof(n="number"!=typeof n?3:n)?51200:r;var i=Bt(t,n);return Gt(Tt(i))>r?e(t,n-1):i},serializeKeysForMessage:function(e,t){if("number"==typeof e||"string"==typeof e)return e.toString();if(!Array.isArray(e))return"";if(0===(e=e.filter((function(e){return"string"==typeof e}))).length)return"[object has no keys]";if(t="number"!=typeof t?40:t,e[0].length>=t)return e[0];for(var n=e.length;n>0;n--){var r=e.slice(0,n).join(", ");if(!(r.length>t))return n===e.length?r:r+"…"}return""},sanitize:function(e,t){if(!yt(t)||yt(t)&&0===t.length)return e;var n,r=Mt(t);try{n=JSON.parse(Tt(e))}catch(t){return e}return function e(t){return yt(t)?t.map((function(t){return e(t)})):St(t)?Object.keys(t).reduce((function(n,i){return r.test(i)?n[i]="********":n[i]=e(t[i]),n}),{}):t}(n)}},Dt=It,Ot={collectWindowErrors:!0,debug:!1},xt="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{},Lt=[].slice,Rt="?",Nt=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;function Ft(){return"undefined"==typeof document||null==document.location?"":document.location.href}Ot.report=function(){var e,t,n=[],r=null,i=null,a=null;function o(e,t){var r=null;if(!t||Ot.collectWindowErrors){for(var i in n)if(n.hasOwnProperty(i))try{n[i].apply(null,[e].concat(Lt.call(arguments,2)))}catch(e){r=e}if(r)throw r}}function s(t,n,r,i,s){var l=Dt.isErrorEvent(s)?s.error:s,u=Dt.isErrorEvent(t)?t.message:t;if(a)Ot.computeStackTrace.augmentStackTraceWithInitialElement(a,n,r,u),c();else if(l&&Dt.isError(l))o(Ot.computeStackTrace(l),!0);else{var h,d={url:n,line:r,column:i},f=void 0;if("[object String]"==={}.toString.call(u))(h=u.match(Nt))&&(f=h[1],u=h[2]);d.func=Rt,o({name:f,message:u,url:Ft(),stack:[d]},!0)}return!!e&&e.apply(this,arguments)}function c(){var e=a,t=r;r=null,a=null,i=null,o.apply(null,[e,!1].concat(t))}function l(e,t){var n=Lt.call(arguments,1);if(a){if(i===e)return;c()}var o=Ot.computeStackTrace(e);if(a=o,i=e,r=n,setTimeout((function(){i===e&&c()}),o.incomplete?2e3:0),!1!==t)throw e}return l.subscribe=function(r){!function(){if(t)return;e=xt.onerror,xt.onerror=s,t=!0}(),n.push(r)},l.unsubscribe=function(e){for(var t=n.length-1;t>=0;--t)n[t]===e&&n.splice(t,1)},l.uninstall=function(){!function(){if(!t)return;xt.onerror=e,t=!1,e=void 0}(),n=[]},l}(),Ot.computeStackTrace=function(){function e(e){if(void 0!==e.stack&&e.stack){var t,n,r,i=/^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|[a-z]:|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,a=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx(?:-web)|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,o=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|moz-extension).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js))(?::(\d+))?(?::(\d+))?\s*$/i,s=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,c=/\((\S*)(?::(\d+))(?::(\d+))\)/,l=e.stack.split("\n"),u=[];/^(.*) is undefined$/.exec(e.message);for(var h=0,d=l.length;h<d;++h){if(n=i.exec(l[h])){var f=n[2]&&0===n[2].indexOf("native");n[2]&&0===n[2].indexOf("eval")&&(t=c.exec(n[2]))&&(n[2]=t[1],n[3]=t[2],n[4]=t[3]),r={url:f?null:n[2],func:n[1]||Rt,args:f?[n[2]]:[],line:n[3]?+n[3]:null,column:n[4]?+n[4]:null}}else if(n=a.exec(l[h]))r={url:n[2],func:n[1]||Rt,args:[],line:+n[3],column:n[4]?+n[4]:null};else{if(!(n=o.exec(l[h])))continue;n[3]&&n[3].indexOf(" > eval")>-1&&(t=s.exec(n[3]))?(n[3]=t[1],n[4]=t[2],n[5]=null):0!==h||n[5]||void 0===e.columnNumber||(u[0].column=e.columnNumber+1),r={url:n[3],func:n[1]||Rt,args:n[2]?n[2].split(","):[],line:n[4]?+n[4]:null,column:n[5]?+n[5]:null}}if(!r.func&&r.line&&(r.func=Rt),r.url&&"blob:"===r.url.substr(0,5)){var p=new XMLHttpRequest;if(p.open("GET",r.url,!1),p.send(null),200===p.status){var g=p.responseText||"",T=(g=g.slice(-300)).match(/\/\/# sourceMappingURL=(.*)$/);if(T){var m=T[1];"~"===m.charAt(0)&&(m=("undefined"==typeof document||null==document.location?"":document.location.origin?document.location.origin:document.location.protocol+"//"+document.location.hostname+(document.location.port?":"+document.location.port:""))+m.slice(1)),r.url=m.slice(0,-4)}}}u.push(r)}return u.length?{name:e.name,message:e.message,url:Ft(),stack:u}:null}}function t(e,t,n,r){var i={url:t,line:n};if(i.url&&i.line){if(e.incomplete=!1,i.func||(i.func=Rt),e.stack.length>0&&e.stack[0].url===i.url){if(e.stack[0].line===i.line)return!1;if(!e.stack[0].line&&e.stack[0].func===i.func)return e.stack[0].line=i.line,!1}return e.stack.unshift(i),e.partial=!0,!0}return e.incomplete=!0,!1}function n(e,i){for(var a,o,s=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,c=[],l={},u=!1,h=n.caller;h&&!u;h=h.caller)if(h!==r&&h!==Ot.report){if(o={url:null,func:Rt,line:null,column:null},h.name?o.func=h.name:(a=s.exec(h.toString()))&&(o.func=a[1]),void 0===o.func)try{o.func=a.input.substring(0,a.input.indexOf("{"))}catch(e){}l[""+h]?u=!0:l[""+h]=!0,c.push(o)}i&&c.splice(0,i);var d={name:e.name,message:e.message,url:Ft(),stack:c};return t(d,e.sourceURL||e.fileName,e.line||e.lineNumber,e.message||e.description),d}function r(t,r){var i=null;r=null==r?0:+r;try{if(i=e(t))return i}catch(e){if(Ot.debug)throw e}try{if(i=n(t,r+1))return i}catch(e){if(Ot.debug)throw e}return{name:t.name,message:t.message,url:Ft()}}return r.augmentStackTraceWithInitialElement=t,r.computeStackTraceFromStackProp=e,r}();var Ut=Ot;function Vt(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function jt(e,t,n,r,i,a){return Vt((o=Vt(Vt(t,e),Vt(r,a)))<<(s=i)|o>>>32-s,n);var o,s}function Xt(e,t,n,r,i,a,o){return jt(t&n|~t&r,e,t,i,a,o)}function Wt(e,t,n,r,i,a,o){return jt(t&r|n&~r,e,t,i,a,o)}function Kt(e,t,n,r,i,a,o){return jt(t^n^r,e,t,i,a,o)}function qt(e,t,n,r,i,a,o){return jt(n^(t|~r),e,t,i,a,o)}function zt(e,t){var n,r,i,a,o;e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;var s=1732584193,c=-271733879,l=-1732584194,u=271733878;for(n=0;n<e.length;n+=16)r=s,i=c,a=l,o=u,s=Xt(s,c,l,u,e[n],7,-680876936),u=Xt(u,s,c,l,e[n+1],12,-389564586),l=Xt(l,u,s,c,e[n+2],17,606105819),c=Xt(c,l,u,s,e[n+3],22,-1044525330),s=Xt(s,c,l,u,e[n+4],7,-176418897),u=Xt(u,s,c,l,e[n+5],12,1200080426),l=Xt(l,u,s,c,e[n+6],17,-1473231341),c=Xt(c,l,u,s,e[n+7],22,-45705983),s=Xt(s,c,l,u,e[n+8],7,1770035416),u=Xt(u,s,c,l,e[n+9],12,-1958414417),l=Xt(l,u,s,c,e[n+10],17,-42063),c=Xt(c,l,u,s,e[n+11],22,-1990404162),s=Xt(s,c,l,u,e[n+12],7,1804603682),u=Xt(u,s,c,l,e[n+13],12,-40341101),l=Xt(l,u,s,c,e[n+14],17,-1502002290),s=Wt(s,c=Xt(c,l,u,s,e[n+15],22,1236535329),l,u,e[n+1],5,-165796510),u=Wt(u,s,c,l,e[n+6],9,-1069501632),l=Wt(l,u,s,c,e[n+11],14,643717713),c=Wt(c,l,u,s,e[n],20,-373897302),s=Wt(s,c,l,u,e[n+5],5,-701558691),u=Wt(u,s,c,l,e[n+10],9,38016083),l=Wt(l,u,s,c,e[n+15],14,-660478335),c=Wt(c,l,u,s,e[n+4],20,-405537848),s=Wt(s,c,l,u,e[n+9],5,568446438),u=Wt(u,s,c,l,e[n+14],9,-1019803690),l=Wt(l,u,s,c,e[n+3],14,-187363961),c=Wt(c,l,u,s,e[n+8],20,1163531501),s=Wt(s,c,l,u,e[n+13],5,-1444681467),u=Wt(u,s,c,l,e[n+2],9,-51403784),l=Wt(l,u,s,c,e[n+7],14,1735328473),s=Kt(s,c=Wt(c,l,u,s,e[n+12],20,-1926607734),l,u,e[n+5],4,-378558),u=Kt(u,s,c,l,e[n+8],11,-2022574463),l=Kt(l,u,s,c,e[n+11],16,1839030562),c=Kt(c,l,u,s,e[n+14],23,-35309556),s=Kt(s,c,l,u,e[n+1],4,-1530992060),u=Kt(u,s,c,l,e[n+4],11,1272893353),l=Kt(l,u,s,c,e[n+7],16,-155497632),c=Kt(c,l,u,s,e[n+10],23,-1094730640),s=Kt(s,c,l,u,e[n+13],4,681279174),u=Kt(u,s,c,l,e[n],11,-358537222),l=Kt(l,u,s,c,e[n+3],16,-722521979),c=Kt(c,l,u,s,e[n+6],23,76029189),s=Kt(s,c,l,u,e[n+9],4,-640364487),u=Kt(u,s,c,l,e[n+12],11,-421815835),l=Kt(l,u,s,c,e[n+15],16,530742520),s=qt(s,c=Kt(c,l,u,s,e[n+2],23,-995338651),l,u,e[n],6,-198630844),u=qt(u,s,c,l,e[n+7],10,1126891415),l=qt(l,u,s,c,e[n+14],15,-1416354905),c=qt(c,l,u,s,e[n+5],21,-57434055),s=qt(s,c,l,u,e[n+12],6,1700485571),u=qt(u,s,c,l,e[n+3],10,-1894986606),l=qt(l,u,s,c,e[n+10],15,-1051523),c=qt(c,l,u,s,e[n+1],21,-2054922799),s=qt(s,c,l,u,e[n+8],6,1873313359),u=qt(u,s,c,l,e[n+15],10,-30611744),l=qt(l,u,s,c,e[n+6],15,-1560198380),c=qt(c,l,u,s,e[n+13],21,1309151649),s=qt(s,c,l,u,e[n+4],6,-145523070),u=qt(u,s,c,l,e[n+11],10,-1120210379),l=qt(l,u,s,c,e[n+2],15,718787259),c=qt(c,l,u,s,e[n+9],21,-343485551),s=Vt(s,r),c=Vt(c,i),l=Vt(l,a),u=Vt(u,o);return[s,c,l,u]}function Jt(e){var t,n="",r=32*e.length;for(t=0;t<r;t+=8)n+=String.fromCharCode(e[t>>5]>>>t%32&255);return n}function Qt(e){var t,n=[];for(n[(e.length>>2)-1]=void 0,t=0;t<n.length;t+=1)n[t]=0;var r=8*e.length;for(t=0;t<r;t+=8)n[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return n}function Yt(e){var t,n,r="0123456789abcdef",i="";for(n=0;n<e.length;n+=1)t=e.charCodeAt(n),i+=r.charAt(t>>>4&15)+r.charAt(15&t);return i}function Zt(e){return unescape(encodeURIComponent(e))}function $t(e){return function(e){return Jt(zt(Qt(e),8*e.length))}(Zt(e))}function en(e,t){return function(e,t){var n,r,i=Qt(e),a=[],o=[];for(a[15]=o[15]=void 0,i.length>16&&(i=zt(i,8*e.length)),n=0;n<16;n+=1)a[n]=909522486^i[n],o[n]=1549556828^i[n];return r=zt(a.concat(Qt(t)),512+8*t.length),Jt(zt(o.concat(r),640))}(Zt(e),Zt(t))}var tn=function(e,t,n){return t?n?en(t,e):function(e,t){return Yt(en(e,t))}(t,e):n?$t(e):function(e){return Yt($t(e))}(e)};function nn(e){this.name="RavenConfigError",this.message=e}nn.prototype=new Error,nn.prototype.constructor=nn;var rn=It,an=function(e,t,n){var r=e[t],i=e;if(t in e){var a="warn"===t?"warning":t;e[t]=function(){var e=[].slice.call(arguments),o=rn.safeJoin(e," "),s={level:a,logger:"console",extra:{arguments:e}};"assert"===t?!1===e[0]&&(o="Assertion failed: "+(rn.safeJoin(e.slice(1)," ")||"console.assert"),s.extra.arguments=e.slice(1),n&&n(o,s)):n&&n(o,s),r&&Function.prototype.apply.call(r,i,e)}}},on=Ut,sn=gt,cn=tn,ln=nn,un=It.isErrorEvent,hn=It.isDOMError,dn=It.isDOMException,fn=It.isError,pn=It.isObject,gn=It.isPlainObject,Tn=It.isUndefined,mn=It.isFunction,vn=It.isString,Sn=It.isArray,bn=It.isEmptyObject,yn=It.each,_n=It.objectMerge,Pn=It.truncate,An=It.objectFrozen,kn=It.hasKey,Mn=It.joinRegExp,En=It.urlencode,Hn=It.uuid4,Cn=It.htmlTreeAsString,Gn=It.isSameException,wn=It.isSameStacktrace,Bn=It.parseUrl,In=It.fill,Dn=It.supportsFetch,On=It.supportsReferrerPolicy,xn=It.serializeKeysForMessage,Ln=It.serializeException,Rn=It.sanitize,Nn=an,Fn="source protocol user pass host port path".split(" "),Un=/^(?:(\w+):)?\/\/(?:(\w+)(:\w+)?@)?([\w\.-]+)(?::(\d+))?(\/.*)/;function Vn(){return+new Date}var jn="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{},Xn=jn.document,Wn=jn.navigator;function Kn(e,t){return mn(t)?function(n){return t(n,e)}:t}function qn(){for(var e in this._hasJSON=!("object"!=typeof JSON||!JSON.stringify),this._hasDocument=!Tn(Xn),this._hasNavigator=!Tn(Wn),this._lastCapturedException=null,this._lastData=null,this._lastEventId=null,this._globalServer=null,this._globalKey=null,this._globalProject=null,this._globalContext={},this._globalOptions={release:jn.SENTRY_RELEASE&&jn.SENTRY_RELEASE.id,logger:"javascript",ignoreErrors:[],ignoreUrls:[],whitelistUrls:[],includePaths:[],headers:null,collectWindowErrors:!0,captureUnhandledRejections:!0,maxMessageLength:0,maxUrlLength:250,stackTraceLimit:50,autoBreadcrumbs:!0,instrument:!0,sampleRate:1,sanitizeKeys:[]},this._fetchDefaults={method:"POST",referrerPolicy:On()?"origin":""},this._ignoreOnError=0,this._isRavenInstalled=!1,this._originalErrorStackTraceLimit=Error.stackTraceLimit,this._originalConsole=jn.console||{},this._originalConsoleMethods={},this._plugins=[],this._startTime=Vn(),this._wrappedBuiltIns=[],this._breadcrumbs=[],this._lastCapturedEvent=null,this._keypressTimeout,this._location=jn.location,this._lastHref=this._location&&this._location.href,this._resetBackoff(),this._originalConsole)this._originalConsoleMethods[e]=this._originalConsole[e]}qn.prototype={VERSION:"3.27.2",debug:!1,TraceKit:on,config:function(e,t){var n=this;if(n._globalServer)return this._logDebug("error","Error: Raven has already been configured"),n;if(!e)return n;var r=n._globalOptions;t&&yn(t,(function(e,t){"tags"===e||"extra"===e||"user"===e?n._globalContext[e]=t:r[e]=t})),n.setDSN(e),r.ignoreErrors.push(/^Script error\.?$/),r.ignoreErrors.push(/^Javascript error: Script error\.? on line 0$/),r.ignoreErrors=Mn(r.ignoreErrors),r.ignoreUrls=!!r.ignoreUrls.length&&Mn(r.ignoreUrls),r.whitelistUrls=!!r.whitelistUrls.length&&Mn(r.whitelistUrls),r.includePaths=Mn(r.includePaths),r.maxBreadcrumbs=Math.max(0,Math.min(r.maxBreadcrumbs||100,100));var i={xhr:!0,console:!0,dom:!0,location:!0,sentry:!0},a=r.autoBreadcrumbs;"[object Object]"==={}.toString.call(a)?a=_n(i,a):!1!==a&&(a=i),r.autoBreadcrumbs=a;var o={tryCatch:!0},s=r.instrument;return"[object Object]"==={}.toString.call(s)?s=_n(o,s):!1!==s&&(s=o),r.instrument=s,on.collectWindowErrors=!!r.collectWindowErrors,n},install:function(){var e=this;return e.isSetup()&&!e._isRavenInstalled&&(on.report.subscribe((function(){e._handleOnErrorStackInfo.apply(e,arguments)})),e._globalOptions.captureUnhandledRejections&&e._attachPromiseRejectionHandler(),e._patchFunctionToString(),e._globalOptions.instrument&&e._globalOptions.instrument.tryCatch&&e._instrumentTryCatch(),e._globalOptions.autoBreadcrumbs&&e._instrumentBreadcrumbs(),e._drainPlugins(),e._isRavenInstalled=!0),Error.stackTraceLimit=e._globalOptions.stackTraceLimit,this},setDSN:function(e){var t=this,n=t._parseDSN(e),r=n.path.lastIndexOf("/"),i=n.path.substr(1,r);t._dsn=e,t._globalKey=n.user,t._globalSecret=n.pass&&n.pass.substr(1),t._globalProject=n.path.substr(r+1),t._globalServer=t._getGlobalServer(n),t._globalEndpoint=t._globalServer+"/"+i+"api/"+t._globalProject+"/store/",this._resetBackoff()},context:function(e,t,n){return mn(e)&&(n=t||[],t=e,e={}),this.wrap(e,t).apply(this,n)},wrap:function(e,t,n){var r=this;if(Tn(t)&&!mn(e))return e;if(mn(e)&&(t=e,e=void 0),!mn(t))return t;try{if(t.__raven__)return t;if(t.__raven_wrapper__)return t.__raven_wrapper__}catch(e){return t}function i(){var i=[],a=arguments.length,o=!e||e&&!1!==e.deep;for(n&&mn(n)&&n.apply(this,arguments);a--;)i[a]=o?r.wrap(e,arguments[a]):arguments[a];try{return t.apply(this,i)}catch(t){throw r._ignoreNextOnError(),r.captureException(t,e),t}}for(var a in t)kn(t,a)&&(i[a]=t[a]);return i.prototype=t.prototype,t.__raven_wrapper__=i,i.__raven__=!0,i.__orig__=t,i},uninstall:function(){return on.report.uninstall(),this._detachPromiseRejectionHandler(),this._unpatchFunctionToString(),this._restoreBuiltIns(),this._restoreConsole(),Error.stackTraceLimit=this._originalErrorStackTraceLimit,this._isRavenInstalled=!1,this},_promiseRejectionHandler:function(e){this._logDebug("debug","Raven caught unhandled promise rejection:",e),this.captureException(e.reason,{mechanism:{type:"onunhandledrejection",handled:!1}})},_attachPromiseRejectionHandler:function(){return this._promiseRejectionHandler=this._promiseRejectionHandler.bind(this),jn.addEventListener&&jn.addEventListener("unhandledrejection",this._promiseRejectionHandler),this},_detachPromiseRejectionHandler:function(){return jn.removeEventListener&&jn.removeEventListener("unhandledrejection",this._promiseRejectionHandler),this},captureException:function(e,t){if(t=_n({trimHeadFrames:0},t||{}),un(e)&&e.error)e=e.error;else{if(hn(e)||dn(e)){var n=e.name||(hn(e)?"DOMError":"DOMException"),r=e.message?n+": "+e.message:n;return this.captureMessage(r,_n(t,{stacktrace:!0,trimHeadFrames:t.trimHeadFrames+1}))}if(fn(e));else{if(!gn(e))return this.captureMessage(e,_n(t,{stacktrace:!0,trimHeadFrames:t.trimHeadFrames+1}));t=this._getCaptureExceptionOptionsFromPlainObject(t,e),e=new Error(t.message)}}this._lastCapturedException=e;try{var i=on.computeStackTrace(e);this._handleStackInfo(i,t)}catch(t){if(e!==t)throw t}return this},_getCaptureExceptionOptionsFromPlainObject:function(e,t){var n=Object.keys(t).sort(),r=_n(e,{message:"Non-Error exception captured with keys: "+xn(n),fingerprint:[cn(n)],extra:e.extra||{}});return r.extra.__serialized__=Ln(t),r},captureMessage:function(e,t){if(!this._globalOptions.ignoreErrors.test||!this._globalOptions.ignoreErrors.test(e)){var n,r=_n({message:e+=""},t=t||{});try{throw new Error(e)}catch(e){n=e}n.name=null;var i=on.computeStackTrace(n),a=Sn(i.stack)&&i.stack[1];a&&"Raven.captureException"===a.func&&(a=i.stack[2]);var o=a&&a.url||"";if((!this._globalOptions.ignoreUrls.test||!this._globalOptions.ignoreUrls.test(o))&&(!this._globalOptions.whitelistUrls.test||this._globalOptions.whitelistUrls.test(o))){if(this._globalOptions.stacktrace||t.stacktrace||""===r.message){r.fingerprint=null==r.fingerprint?e:r.fingerprint,(t=_n({trimHeadFrames:0},t)).trimHeadFrames+=1;var s=this._prepareFrames(i,t);r.stacktrace={frames:s.reverse()}}return r.fingerprint&&(r.fingerprint=Sn(r.fingerprint)?r.fingerprint:[r.fingerprint]),this._send(r),this}}},captureBreadcrumb:function(e){var t=_n({timestamp:Vn()/1e3},e);if(mn(this._globalOptions.breadcrumbCallback)){var n=this._globalOptions.breadcrumbCallback(t);if(pn(n)&&!bn(n))t=n;else if(!1===n)return this}return this._breadcrumbs.push(t),this._breadcrumbs.length>this._globalOptions.maxBreadcrumbs&&this._breadcrumbs.shift(),this},addPlugin:function(e){var t=[].slice.call(arguments,1);return this._plugins.push([e,t]),this._isRavenInstalled&&this._drainPlugins(),this},setUserContext:function(e){return this._globalContext.user=e,this},setExtraContext:function(e){return this._mergeContext("extra",e),this},setTagsContext:function(e){return this._mergeContext("tags",e),this},clearContext:function(){return this._globalContext={},this},getContext:function(){return JSON.parse(sn(this._globalContext))},setEnvironment:function(e){return this._globalOptions.environment=e,this},setRelease:function(e){return this._globalOptions.release=e,this},setDataCallback:function(e){var t=this._globalOptions.dataCallback;return this._globalOptions.dataCallback=Kn(t,e),this},setBreadcrumbCallback:function(e){var t=this._globalOptions.breadcrumbCallback;return this._globalOptions.breadcrumbCallback=Kn(t,e),this},setShouldSendCallback:function(e){var t=this._globalOptions.shouldSendCallback;return this._globalOptions.shouldSendCallback=Kn(t,e),this},setTransport:function(e){return this._globalOptions.transport=e,this},lastException:function(){return this._lastCapturedException},lastEventId:function(){return this._lastEventId},isSetup:function(){return!!this._hasJSON&&(!!this._globalServer||(this.ravenNotConfiguredError||(this.ravenNotConfiguredError=!0,this._logDebug("error","Error: Raven has not been configured.")),!1))},afterLoad:function(){var e=jn.RavenConfig;e&&this.config(e.dsn,e.config).install()},showReportDialog:function(e){if(Xn){if(!(e=_n({eventId:this.lastEventId(),dsn:this._dsn,user:this._globalContext.user||{}},e)).eventId)throw new ln("Missing eventId");if(!e.dsn)throw new ln("Missing DSN");var t=encodeURIComponent,n=[];for(var r in e)if("user"===r){var i=e.user;i.name&&n.push("name="+t(i.name)),i.email&&n.push("email="+t(i.email))}else n.push(t(r)+"="+t(e[r]));var a=this._getGlobalServer(this._parseDSN(e.dsn)),o=Xn.createElement("script");o.async=!0,o.src=a+"/api/embed/error-page/?"+n.join("&"),(Xn.head||Xn.body).appendChild(o)}},_ignoreNextOnError:function(){var e=this;this._ignoreOnError+=1,setTimeout((function(){e._ignoreOnError-=1}))},_triggerEvent:function(e,t){var n,r;if(this._hasDocument){for(r in t=t||{},e="raven"+e.substr(0,1).toUpperCase()+e.substr(1),Xn.createEvent?(n=Xn.createEvent("HTMLEvents")).initEvent(e,!0,!0):(n=Xn.createEventObject()).eventType=e,t)kn(t,r)&&(n[r]=t[r]);if(Xn.createEvent)Xn.dispatchEvent(n);else try{Xn.fireEvent("on"+n.eventType.toLowerCase(),n)}catch(e){}}},_breadcrumbEventHandler:function(e){var t=this;return function(n){if(t._keypressTimeout=null,t._lastCapturedEvent!==n){var r;t._lastCapturedEvent=n;try{r=Cn(n.target)}catch(e){r="<unknown>"}t.captureBreadcrumb({category:"ui."+e,message:r})}}},_keypressEventHandler:function(){var e=this;return function(t){var n;try{n=t.target}catch(e){return}var r=n&&n.tagName;if(r&&("INPUT"===r||"TEXTAREA"===r||n.isContentEditable)){var i=e._keypressTimeout;i||e._breadcrumbEventHandler("input")(t),clearTimeout(i),e._keypressTimeout=setTimeout((function(){e._keypressTimeout=null}),1e3)}}},_captureUrlChange:function(e,t){var n=Bn(this._location.href),r=Bn(t),i=Bn(e);this._lastHref=t,n.protocol===r.protocol&&n.host===r.host&&(t=r.relative),n.protocol===i.protocol&&n.host===i.host&&(e=i.relative),this.captureBreadcrumb({category:"navigation",data:{to:t,from:e}})},_patchFunctionToString:function(){var e=this;e._originalFunctionToString=Function.prototype.toString,Function.prototype.toString=function(){return"function"==typeof this&&this.__raven__?e._originalFunctionToString.apply(this.__orig__,arguments):e._originalFunctionToString.apply(this,arguments)}},_unpatchFunctionToString:function(){this._originalFunctionToString&&(Function.prototype.toString=this._originalFunctionToString)},_instrumentTryCatch:function(){var e=this,t=e._wrappedBuiltIns;function n(t){return function(n,r){for(var i=new Array(arguments.length),a=0;a<i.length;++a)i[a]=arguments[a];var o=i[0];return mn(o)&&(i[0]=e.wrap({mechanism:{type:"instrument",data:{function:t.name||"<anonymous>"}}},o)),t.apply?t.apply(this,i):t(i[0],i[1])}}var r=this._globalOptions.autoBreadcrumbs;function i(n){var i=jn[n]&&jn[n].prototype;i&&i.hasOwnProperty&&i.hasOwnProperty("addEventListener")&&(In(i,"addEventListener",(function(t){return function(i,a,o,s){try{a&&a.handleEvent&&(a.handleEvent=e.wrap({mechanism:{type:"instrument",data:{target:n,function:"handleEvent",handler:a&&a.name||"<anonymous>"}}},a.handleEvent))}catch(e){}var c,l,u;return r&&r.dom&&("EventTarget"===n||"Node"===n)&&(l=e._breadcrumbEventHandler("click"),u=e._keypressEventHandler(),c=function(e){if(e){var t;try{t=e.type}catch(e){return}return"click"===t?l(e):"keypress"===t?u(e):void 0}}),t.call(this,i,e.wrap({mechanism:{type:"instrument",data:{target:n,function:"addEventListener",handler:a&&a.name||"<anonymous>"}}},a,c),o,s)}}),t),In(i,"removeEventListener",(function(e){return function(t,n,r,i){try{n=n&&(n.__raven_wrapper__?n.__raven_wrapper__:n)}catch(e){}return e.call(this,t,n,r,i)}}),t))}In(jn,"setTimeout",n,t),In(jn,"setInterval",n,t),jn.requestAnimationFrame&&In(jn,"requestAnimationFrame",(function(t){return function(n){return t(e.wrap({mechanism:{type:"instrument",data:{function:"requestAnimationFrame",handler:t&&t.name||"<anonymous>"}}},n))}}),t);for(var a=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],o=0;o<a.length;o++)i(a[o])},_instrumentBreadcrumbs:function(){var e=this,t=this._globalOptions.autoBreadcrumbs,n=e._wrappedBuiltIns;function r(t,n){t in n&&mn(n[t])&&In(n,t,(function(n){return e.wrap({mechanism:{type:"instrument",data:{function:t,handler:n&&n.name||"<anonymous>"}}},n)}))}if(t.xhr&&"XMLHttpRequest"in jn){var i=jn.XMLHttpRequest&&jn.XMLHttpRequest.prototype;In(i,"open",(function(t){return function(n,r){return vn(r)&&-1===r.indexOf(e._globalKey)&&(this.__raven_xhr={method:n,url:r,status_code:null}),t.apply(this,arguments)}}),n),In(i,"send",(function(t){return function(){var n=this;function i(){if(n.__raven_xhr&&4===n.readyState){try{n.__raven_xhr.status_code=n.status}catch(e){}e.captureBreadcrumb({type:"http",category:"xhr",data:n.__raven_xhr})}}for(var a=["onload","onerror","onprogress"],o=0;o<a.length;o++)r(a[o],n);return"onreadystatechange"in n&&mn(n.onreadystatechange)?In(n,"onreadystatechange",(function(t){return e.wrap({mechanism:{type:"instrument",data:{function:"onreadystatechange",handler:t&&t.name||"<anonymous>"}}},t,i)})):n.onreadystatechange=i,t.apply(this,arguments)}}),n)}t.xhr&&Dn()&&In(jn,"fetch",(function(t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;++r)n[r]=arguments[r];var i,a=n[0],o="GET";if("string"==typeof a?i=a:"Request"in jn&&a instanceof jn.Request?(i=a.url,a.method&&(o=a.method)):i=""+a,-1!==i.indexOf(e._globalKey))return t.apply(this,n);n[1]&&n[1].method&&(o=n[1].method);var s={method:o,url:i,status_code:null};return t.apply(this,n).then((function(t){return s.status_code=t.status,e.captureBreadcrumb({type:"http",category:"fetch",data:s}),t})).catch((function(t){throw e.captureBreadcrumb({type:"http",category:"fetch",data:s,level:"error"}),t}))}}),n),t.dom&&this._hasDocument&&(Xn.addEventListener?(Xn.addEventListener("click",e._breadcrumbEventHandler("click"),!1),Xn.addEventListener("keypress",e._keypressEventHandler(),!1)):Xn.attachEvent&&(Xn.attachEvent("onclick",e._breadcrumbEventHandler("click")),Xn.attachEvent("onkeypress",e._keypressEventHandler())));var a=jn.chrome,o=!(a&&a.app&&a.app.runtime)&&jn.history&&jn.history.pushState&&jn.history.replaceState;if(t.location&&o){var s=jn.onpopstate;jn.onpopstate=function(){var t=e._location.href;if(e._captureUrlChange(e._lastHref,t),s)return s.apply(this,arguments)};var c=function(t){return function(){var n=arguments.length>2?arguments[2]:void 0;return n&&e._captureUrlChange(e._lastHref,n+""),t.apply(this,arguments)}};In(jn.history,"pushState",c,n),In(jn.history,"replaceState",c,n)}if(t.console&&"console"in jn&&console.log){var l=function(t,n){e.captureBreadcrumb({message:t,level:n.level,category:"console"})};yn(["debug","info","warn","error","log"],(function(e,t){Nn(console,t,l)}))}},_restoreBuiltIns:function(){for(var e;this._wrappedBuiltIns.length;){var t=(e=this._wrappedBuiltIns.shift())[0],n=e[1],r=e[2];t[n]=r}},_restoreConsole:function(){for(var e in this._originalConsoleMethods)this._originalConsole[e]=this._originalConsoleMethods[e]},_drainPlugins:function(){var e=this;yn(this._plugins,(function(t,n){var r=n[0],i=n[1];r.apply(e,[e].concat(i))}))},_parseDSN:function(e){var t=Un.exec(e),n={},r=7;try{for(;r--;)n[Fn[r]]=t[r]||""}catch(t){throw new ln("Invalid DSN: "+e)}if(n.pass&&!this._globalOptions.allowSecretKey)throw new ln("Do not specify your secret key in the DSN. See: http://bit.ly/raven-secret-key");return n},_getGlobalServer:function(e){var t="//"+e.host+(e.port?":"+e.port:"");return e.protocol&&(t=e.protocol+":"+t),t},_handleOnErrorStackInfo:function(e,t){(t=t||{}).mechanism=t.mechanism||{type:"onerror",handled:!1},this._ignoreOnError||this._handleStackInfo(e,t)},_handleStackInfo:function(e,t){var n=this._prepareFrames(e,t);this._triggerEvent("handle",{stackInfo:e,options:t}),this._processException(e.name,e.message,e.url,e.lineno,n,t)},_prepareFrames:function(e,t){var n=this,r=[];if(e.stack&&e.stack.length&&(yn(e.stack,(function(t,i){var a=n._normalizeFrame(i,e.url);a&&r.push(a)})),t&&t.trimHeadFrames))for(var i=0;i<t.trimHeadFrames&&i<r.length;i++)r[i].in_app=!1;return r=r.slice(0,this._globalOptions.stackTraceLimit)},_normalizeFrame:function(e,t){var n={filename:e.url,lineno:e.line,colno:e.column,function:e.func||"?"};return e.url||(n.filename=t),n.in_app=!(this._globalOptions.includePaths.test&&!this._globalOptions.includePaths.test(n.filename)||/(Raven|TraceKit)\./.test(n.function)||/raven\.(min\.)?js$/.test(n.filename)),n},_processException:function(e,t,n,r,i,a){var o,s=(e?e+": ":"")+(t||"");if((!this._globalOptions.ignoreErrors.test||!this._globalOptions.ignoreErrors.test(t)&&!this._globalOptions.ignoreErrors.test(s))&&(i&&i.length?(n=i[0].filename||n,i.reverse(),o={frames:i}):n&&(o={frames:[{filename:n,lineno:r,in_app:!0}]}),(!this._globalOptions.ignoreUrls.test||!this._globalOptions.ignoreUrls.test(n))&&(!this._globalOptions.whitelistUrls.test||this._globalOptions.whitelistUrls.test(n)))){var c=_n({exception:{values:[{type:e,value:t,stacktrace:o}]},transaction:n},a),l=c.exception.values[0];null==l.type&&""===l.value&&(l.value="Unrecoverable error caught"),!c.exception.mechanism&&c.mechanism&&(c.exception.mechanism=c.mechanism,delete c.mechanism),c.exception.mechanism=_n({type:"generic",handled:!0},c.exception.mechanism||{}),this._send(c)}},_trimPacket:function(e){var t=this._globalOptions.maxMessageLength;if(e.message&&(e.message=Pn(e.message,t)),e.exception){var n=e.exception.values[0];n.value=Pn(n.value,t)}var r=e.request;return r&&(r.url&&(r.url=Pn(r.url,this._globalOptions.maxUrlLength)),r.Referer&&(r.Referer=Pn(r.Referer,this._globalOptions.maxUrlLength))),e.breadcrumbs&&e.breadcrumbs.values&&this._trimBreadcrumbs(e.breadcrumbs),e},_trimBreadcrumbs:function(e){for(var t,n,r,i=["to","from","url"],a=0;a<e.values.length;++a)if((n=e.values[a]).hasOwnProperty("data")&&pn(n.data)&&!An(n.data)){r=_n({},n.data);for(var o=0;o<i.length;++o)t=i[o],r.hasOwnProperty(t)&&r[t]&&(r[t]=Pn(r[t],this._globalOptions.maxUrlLength));e.values[a].data=r}},_getHttpData:function(){if(this._hasNavigator||this._hasDocument){var e={};return this._hasNavigator&&Wn.userAgent&&(e.headers={"User-Agent":Wn.userAgent}),jn.location&&jn.location.href&&(e.url=jn.location.href),this._hasDocument&&Xn.referrer&&(e.headers||(e.headers={}),e.headers.Referer=Xn.referrer),e}},_resetBackoff:function(){this._backoffDuration=0,this._backoffStart=null},_shouldBackoff:function(){return this._backoffDuration&&Vn()-this._backoffStart<this._backoffDuration},_isRepeatData:function(e){var t=this._lastData;return!(!t||e.message!==t.message||e.transaction!==t.transaction)&&(e.stacktrace||t.stacktrace?wn(e.stacktrace,t.stacktrace):e.exception||t.exception?Gn(e.exception,t.exception):!e.fingerprint&&!t.fingerprint||Boolean(e.fingerprint&&t.fingerprint)&&JSON.stringify(e.fingerprint)===JSON.stringify(t.fingerprint))},_setBackoffState:function(e){if(!this._shouldBackoff()){var t=e.status;if(400===t||401===t||429===t){var n;try{n=Dn()?e.headers.get("Retry-After"):e.getResponseHeader("Retry-After"),n=1e3*parseInt(n,10)}catch(e){}this._backoffDuration=n||(2*this._backoffDuration||1e3),this._backoffStart=Vn()}}},_send:function(e){var t=this._globalOptions,n={project:this._globalProject,logger:t.logger,platform:"javascript"},r=this._getHttpData();r&&(n.request=r),e.trimHeadFrames&&delete e.trimHeadFrames,(e=_n(n,e)).tags=_n(_n({},this._globalContext.tags),e.tags),e.extra=_n(_n({},this._globalContext.extra),e.extra),e.extra["session:duration"]=Vn()-this._startTime,this._breadcrumbs&&this._breadcrumbs.length>0&&(e.breadcrumbs={values:[].slice.call(this._breadcrumbs,0)}),this._globalContext.user&&(e.user=this._globalContext.user),t.environment&&(e.environment=t.environment),t.release&&(e.release=t.release),t.serverName&&(e.server_name=t.serverName),e=this._sanitizeData(e),Object.keys(e).forEach((function(t){(null==e[t]||""===e[t]||bn(e[t]))&&delete e[t]})),mn(t.dataCallback)&&(e=t.dataCallback(e)||e),e&&!bn(e)&&(mn(t.shouldSendCallback)&&!t.shouldSendCallback(e)||(this._shouldBackoff()?this._logDebug("warn","Raven dropped error due to backoff: ",e):"number"==typeof t.sampleRate?Math.random()<t.sampleRate&&this._sendProcessedPayload(e):this._sendProcessedPayload(e)))},_sanitizeData:function(e){return Rn(e,this._globalOptions.sanitizeKeys)},_getUuid:function(){return Hn()},_sendProcessedPayload:function(e,t){var n=this,r=this._globalOptions;if(this.isSetup())if(e=this._trimPacket(e),this._globalOptions.allowDuplicates||!this._isRepeatData(e)){this._lastEventId=e.event_id||(e.event_id=this._getUuid()),this._lastData=e,this._logDebug("debug","Raven about to send:",e);var i={sentry_version:"7",sentry_client:"raven-js/"+this.VERSION,sentry_key:this._globalKey};this._globalSecret&&(i.sentry_secret=this._globalSecret);var a=e.exception&&e.exception.values[0];this._globalOptions.autoBreadcrumbs&&this._globalOptions.autoBreadcrumbs.sentry&&this.captureBreadcrumb({category:"sentry",message:a?(a.type?a.type+": ":"")+a.value:e.message,event_id:e.event_id,level:e.level||"error"});var o=this._globalEndpoint;(r.transport||this._makeRequest).call(this,{url:o,auth:i,data:e,options:r,onSuccess:function(){n._resetBackoff(),n._triggerEvent("success",{data:e,src:o}),t&&t()},onError:function(r){n._logDebug("error","Raven transport failed to send: ",r),r.request&&n._setBackoffState(r.request),n._triggerEvent("failure",{data:e,src:o}),r=r||new Error("Raven send failed (no additional details provided)"),t&&t(r)}})}else this._logDebug("warn","Raven dropped repeat event: ",e)},_makeRequest:function(e){var t=e.url+"?"+En(e.auth),n=null,r={};if(e.options.headers&&(n=this._evaluateHash(e.options.headers)),e.options.fetchParameters&&(r=this._evaluateHash(e.options.fetchParameters)),Dn()){r.body=sn(e.data);var i=_n({},this._fetchDefaults),a=_n(i,r);return n&&(a.headers=n),jn.fetch(t,a).then((function(t){if(t.ok)e.onSuccess&&e.onSuccess();else{var n=new Error("Sentry error code: "+t.status);n.request=t,e.onError&&e.onError(n)}})).catch((function(){e.onError&&e.onError(new Error("Sentry error code: network unavailable"))}))}var o=jn.XMLHttpRequest&&new jn.XMLHttpRequest;o&&(("withCredentials"in o||"undefined"!=typeof XDomainRequest)&&("withCredentials"in o?o.onreadystatechange=function(){if(4===o.readyState)if(200===o.status)e.onSuccess&&e.onSuccess();else if(e.onError){var t=new Error("Sentry error code: "+o.status);t.request=o,e.onError(t)}}:(o=new XDomainRequest,t=t.replace(/^https?:/,""),e.onSuccess&&(o.onload=e.onSuccess),e.onError&&(o.onerror=function(){var t=new Error("Sentry error code: XDomainRequest");t.request=o,e.onError(t)})),o.open("POST",t),n&&yn(n,(function(e,t){o.setRequestHeader(e,t)})),o.send(sn(e.data))))},_evaluateHash:function(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];t[n]="function"==typeof r?r():r}return t},_logDebug:function(e){this._originalConsoleMethods[e]&&(this.debug||this._globalOptions.debug)&&Function.prototype.apply.call(this._originalConsoleMethods[e],this._originalConsole,[].slice.call(arguments,1))},_mergeContext:function(e,t){Tn(t)?delete this._globalContext[e]:this._globalContext[e]=_n(this._globalContext[e]||{},t)}},qn.prototype.setUser=qn.prototype.setUserContext,qn.prototype.setReleaseContext=qn.prototype.setRelease;var zn=qn,Jn="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{},Qn=Jn.Raven,Yn=new zn;Yn.noConflict=function(){return Jn.Raven=Qn,Yn},Yn.afterLoad(),ft.exports=Yn,ft.exports.Client=zn;var Zn=d(ft.exports),$n=function(){return i((function e(t,r){return n(this,e),e.instance||(e.instance=this,this.config=t,this.ctx=r,this.initialized=!1),e.instance}),[{key:"setConfig",value:function(e){x.check.isObject(e)&&x.extend(this.config,e)}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"init",value:function(){var e=this.getConfig("serverUrl");x.check.isString(e)&&""!==x.trim(e)?(this.initCommon(),this.initialized=!0,D.log("hinaEpmSDK initialized successfully")):D.log("当前 serverUrl 为空或不正确，请配置正确的 serverUrl！")}},{key:"initCommon",value:function(){this.ravenInit()}},{key:"ravenInit",value:function(){var e=this,t=this.config,n=t.sourcemapVersion,r=t.serverUrl;n||(n="1.0.0"),Zn.config(r).install(),Zn.setTransport((function(t){var r,i,a,o,s=t.data.exception,c={H_js_error_type:(null==s||null===(r=s.values)||void 0===r||null===(i=r[0])||void 0===i?void 0:i.type)||"",H_js_error_summary:(null==s||null===(a=s.values)||void 0===a||null===(o=a[0])||void 0===o?void 0:o.value)||"",H_js_error_id:s?x.hash(JSON.stringify(s)):"",H_js_error_content:s?JSON.stringify(s):"",H_js_sourcemap_version:n};e.ctx.sendRequest("track","H_performance_js",c),t.onSuccess()}))}},{key:"vueInit",value:function(e,t){var n=this,r=e.config.errorHandler;e.config.errorHandler=function(i,a,o){n.captureVueError(i,a,o),"function"==typeof r&&r.call(e,i,a,o),t.showVueError&&console.error(i)}}},{key:"ReactInit",value:function(e,t){var n=this;e.errorHandler=function(e,r,i){n.captureReactError(e,r,i),t.showError&&console.error(e)}}},{key:"captureVueError",value:function(e,t,n){var r={componentName:this.formatComponentName(t),lifecycleHook:n};t&&(t.$options&&t.$options.propsData?r.propsData=t.$options.propsData:t.$props&&(r.propsData=t.$props)),setTimeout((function(){Zn.captureException(e,{extra:r})})),D.log(e,n)}},{key:"captureReactError",value:function(e,t,n){formatComponentName(t),t&&t.props&&t.props,setTimeout((function(){Zn.captureException(e,{extra:info})}))}},{key:"formatComponentName",value:function(e){if(!e)return"<Anonymous>";if(e.$root===e)return"<Root>";if(!e.$options)return"<Anonymous>";var t=e.$options,n=t.name||t._componentTag,r=t.__file;if(!n&&r){var i=r.match(/([^/\\]+)\.vue$/);i&&(n=i[1])}return(n?"<".concat(n.replace(/(?:^|[-_])(\w)/g,(function(e){return e.toUpperCase()})).replace(/[-_]/g,""),">"):"<Anonymous>")+(r?" at ".concat(r):"")}},{key:"captureError",value:function(){return Zn.captureException.apply(Zn,arguments)}}])}(),er=window.location,tr=function(){return i((function e(t,r){n(this,e),e.instance=this,this.config=t,this.ctx=r,this.initialized=!0,this.load(t)}),[{key:"load",value:function(e){this.initialized||(this.config=e,this.initialized=!0)}},{key:"autoTrack",value:function(e,t){this.ctx.track("H_pageview",x.extend(c(c({},e||{}),{},{H_referrer:x.getReferrer(null,!0),H_url:er.href,H_url_path:er.pathname,H_url_hash:er.hash,H_title:document.title})),t)}},{key:"autoTrackSinglePage",value:function(e,t){var n;n=this.autoTrackIsUsed?er.href:x.getReferrer(),this.ctx.track("H_pageview",x.extend(c(c({},e||{}),{},{H_referrer:n,url:er.href,H_url_path:er.pathname,H_url_hash:er.hash,H_title:document.title})),t)}},{key:"listenSinglePage",value:function(){var e=this,t=this.ctx.getConfig("isSinglePage");t&&x.mitt.on("hasInitEpm",(function(){e.onUrlChange((function(n){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(n!==er.href){Q.pageProp.H_referrer=n;var r=x.extend({H_url:er.href,H_referrer:n},t);e.autoTrack(r)}};if(x.check.isBoolean(t))r();else if(x.check.isFunction(t)){var i=t();x.check.isObject(i)?r(i):!0===i&&r()}}))}))}},{key:"onUrlChange",value:function(e){x.check.isFunction(e)&&(e(),x.mitt.on("urlChange",e))}}])}(),nr={errorCapture:!0,performance:!0,presetProperties:{latest_utm:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,url:!0,title:!0},autoTrack:{},isSinglePage:!1},rr=function(){return i((function e(){return n(this,e),e.instance||(e.instance=this,this.config={},this.initialized=!1,this._=x),e.instance}),[{key:"setConfig",value:function(e){x.check.isObject(e)&&x.extend(this.config,e)}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"sendRequest",value:function(e,t,n){var r={properties:c(c({},x.info.epmProperties()),n),type:e,event:t,time:x.now(),_track_id:Number(String(x.getRandom()).slice(2,5)+String(x.getRandom()).slice(2,4)+String(x.now()).slice(-4))};if(J.getAnonymousId()&&(r.anonymous_id=J.getAnonymousId()),J.getSessionId())if(x.now()-J.getSessionIdUpdateTime()>18e5){var i=x.getRandom();r.properties.H_session_id=i,J.setSessionId(i)}else r.properties.H_session_id=J.getSessionId();else r.properties.H_session_id=x.getRandom(),J.setSessionId(r.properties.H_session_id);J.getAccountId()!==J.getAnonymousId()&&J.getAccountId()&&(r.account_id=J.getAccountId()),r.send_time=x.now(),D.log(r),x.check.isString(r)||(r=JSON.stringify(r));var a=x.base64Encode(r),o="crc="+x.hashCode(a),s="data="+x.encodeURIComponent(a)+"&ext="+x.encodeURIComponent(o),l={callback:this.getConfig("globalCallback"),data:s,serverUrl:this.getConfig("serverUrl"),dataSendTimeout:this.getConfig("dataSendTimeout")};new j(l).run()}},{key:"track",value:function(e,t,n){var r=x.check.isFunction(n)?n:function(){};x.check.isString(e)&&(x.check.isObject(t)||x.check.isUndefined(t))?this.sendRequest("track",e,t,r):D.log("eventName must be a sting and properties must be an object")}},{key:"quick",value:function(e){for(var t={autoTrack:this.autoTrack.autoTrack,autoTrackSinglePage:this.autoTrack.autoTrackSinglePage},n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t[e].call(this.autoTrack,r)}},{key:"init",value:function(e){if(x.check.isEmptyObject(this.config)){if(this.setConfig(x.extend(nr,e)),D.showLog=this.getConfig("showLog"),O.serverUrl=this.getConfig("serverUrl"),this.isSinglePage=this.getConfig("isSinglePage"),!O.checkServerUrl(this.getConfig("serverUrl")))return;if(J.load(this.config),Y(this.config),this.store=J,this.config.performance)new dt(this.config,this).init();if(this.config.errorCapture&&new $n(this.config,this).init(),this.initialized=!0,!this.config.autoTrackConfig){x.initUrlChange();var t=new tr(this.config,this);this.autoTrack=t,t.listenSinglePage(),x.mitt.emit("hasInitEpm")}}else D.log("EmpMonitor has been initialized")}}])}(),ir=new Proxy(new rr,{get:function(e,t){return x.check.isFunction(e[t])?function(){if(e.initialized||"init"===t){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e[t].apply(e,r)}console.log("performanceErrorSdk not yet initialized!")}:e[t]}});window.hinaEpmStatistic=ir;var ar=["serverUrl"],or={init:function(e){var t=e.name,n=e.serverUrl,r=e.showLog,i=e.autoTrackConfig,a=e.performanceErrorConfig,o=e.globalCallback,s=e.dataSendTimeout,l=e.isSinglePage;if(i&&He.init(e),a){var u=a.serverUrl,h=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(a,ar);ir.init(c({name:t,isSinglePage:l,serverUrl:u||n,showLog:r,autoTrackConfig:i,globalCallback:o,dataSendTimeout:s},h))}}};new Proxy(or,{get:function(e,t){return"init"!==t?He.initialized?He[t]:ir.initialized&&ir[t]?ir[t]:(console.log("sdk not yet initialized!"),function(){}):e[t]}})}();
