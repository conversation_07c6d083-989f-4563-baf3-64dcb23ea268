import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// 判断是否为掌心宝贝平台，服务端渲染页面需要传 hostname
export const isPalmBaby = (hostname = '') => {
  const key = 'baby-mobile';
  if (hostname) {
    return hostname.includes(key);
  }
  if (typeof window !== 'undefined') {
    return window.location.host.includes(key);
  }
  return false;
};
