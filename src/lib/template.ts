interface Template {
  [key: string]: any
}

export const templates: Template = {
  photoGallery: {
    attrs: {
      width: 2480,
      height: 3508,
      tmpType: 11
    },
    className: 'Stage',
    children: [
      {
        attrs: {},
        className: 'Layer',
        children: [
          {
            attrs: {
              source:
                'https://edu-media.ancda.com/prod/archives/theme/photo-gallery.png'
            },
            className: 'Image'
          },
          {
            attrs: {
              width: 2090,
              height: 2628,
              x: 275,
              y: 300,
              type: 'content'
            },
            className: 'Group',
            children: [
              {
                attrs: {
                  width: 1460,
                  align: 'center',
                  x: 245,
                  y: 60,
                  fontFamily: 'liZi,Apple-Color-Emoji',
                  text: '班级照片墙',
                  type: 'title',
                  fontSize: 146,
                  fill: '#aade47',
                  editable: true,
                  maxLength: 10
                },
                className: 'Text'
              }
            ]
          },
          {
            attrs: {
              width: 130,
              height: 130,
              x: 2308,
              y: 3338,
              type: 'pageNum',
              name: 'page<PERSON>um'
            },
            className: 'Group',
            children: [
              {
                attrs: {
                  radius: 65,
                  fill: '#fff'
                },
                className: 'Circle'
              },
              {
                attrs: {
                  text: '1',
                  fontSize: 55,
                  width: 130,
                  fill: '#333',
                  align: 'center',
                  x: -65,
                  y: -25
                },
                className: 'Text'
              }
            ]
          }
        ]
      }
    ]
  },
  groupPhoto: {
    attrs: {
      width: 2480,
      height: 3508,
      tmpType: 12
    },
    className: 'Stage',
    children: [
      {
        attrs: {},
        className: 'Layer',
        children: [
          {
            attrs: {
              source:
                'https://edu-media.ancda.com/prod/archives/theme/group-photo.png'
            },
            className: 'Image'
          },
          {
            attrs: {
              width: 2090,
              height: 2628,
              x: 275,
              y: 300,
              type: 'content'
            },
            className: 'Group',
            children: [
              {
                attrs: {
                  width: 1460,
                  align: 'center',
                  x: 245,
                  y: 200,
                  fontFamily: 'liZi,Apple-Color-Emoji',
                  text: '班级集体照',
                  type: 'title',
                  fontSize: 146,
                  fill: '#83A4ED',
                  editable: true,
                  maxLength: 10
                },
                className: 'Text'
              },
              {
                attrs: {
                  source:
                    'https://edu-media.ancda.com/prod/archives/album/common/group_photo.png',
                  width: 1800,
                  height: 1200,
                  y: 446,
                  x: 75,
                  editable: true
                },
                className: 'Image'
              },
              {
                attrs: {
                  width: 1800,
                  height: 1000,
                  fontSize: 64,
                  fill: '#333333',
                  fontFamily: 'Alibaba-PuHuiTi,Apple-Color-Emoji',
                  x: 75,
                  y: 1750,
                  lineHeight: 1.8,
                  text: '说点什么吧',
                  editable: true,
                  multiline: true,
                  maxLength: 250
                },
                className: 'Text'
              }
            ]
          },
          {
            attrs: {
              width: 130,
              height: 130,
              x: 2308,
              y: 3338,
              type: 'pageNum',
              name: 'pageNum'
            },
            className: 'Group',
            children: [
              {
                attrs: {
                  radius: 65,
                  fill: '#fff'
                },
                className: 'Circle'
              },
              {
                attrs: {
                  text: '',
                  fontSize: 55,
                  width: 130,
                  fill: '#333',
                  align: 'center',
                  x: -65,
                  y: -25
                },
                className: 'Text'
              }
            ]
          }
        ]
      }
    ]
  },
  teacherWish: {
    attrs: {
      width: 2480,
      height: 3508,
      tmpType: 13
    },
    className: 'Stage',
    children: [
      {
        attrs: {},
        className: 'Layer',
        children: [
          {
            attrs: {
              source:
                'https://edu-media.ancda.com/prod/archives/theme/teacher-wish.png'
            },
            className: 'Image'
          },
          {
            attrs: {
              width: 2090,
              height: 2628,
              x: 275,
              y: 300,
              type: 'content'
            },
            className: 'Group',
            children: [
              {
                attrs: {
                  width: 1460,
                  align: 'center',
                  x: 245,
                  y: 200,
                  fontFamily: 'liZi,Apple-Color-Emoji',
                  text: '老师祝福视频',
                  type: 'title',
                  fontSize: 146,
                  fill: '#fb974b',
                  editable: true,
                  maxLength: 10
                },
                className: 'Text'
              },
              {
                attrs: {
                  source:
                    'https://edu-media.ancda.com/prod/archives/album/common/teacher_img_square.png',
                  width: 1600,
                  height: 900,
                  y: 446,
                  x: 180,
                  editable: true,
                  url: '',
                  mediaType: 'video'
                },
                className: 'Image'
              },
              {
                attrs: {
                  width: 1600,
                  height: 1000,
                  fontSize: 64,
                  fill: '#333333',
                  fontFamily: 'Alibaba-PuHuiTi',
                  x: 160,
                  y: 1500,
                  lineHeight: 1.8,
                  text: '老师祝福语',
                  editable: true,
                  multiline: true,
                  maxLength: 250
                },
                className: 'Text'
              },
              {
                attrs: { x: 1700, y: -150, width: 360, height: 420 },
                className: 'Group',
                children: [
                  {
                    attrs: {
                      width: 360,
                      height: 420,
                      fill: '#FFF',
                      shadowColor: 'black',
                      shadowBlur: 20,
                      shadowOffsetX: 10,
                      shadowOffsetY: 10,
                      shadowOpacity: 0.1
                    },
                    className: 'Rect'
                  },
                  {
                    attrs: {
                      x: 30,
                      y: 30,
                      width: 300,
                      height: 300,
                      mediaType: 'qrcode',
                      source:
                        'https://file.ancda.com/public/file/app/qrcode.png'
                    },
                    className: 'Image'
                  },
                  {
                    attrs: {
                      height: 60,
                      fontSize: 50,
                      fill: '#666',
                      x: 30,
                      y: 330,
                      align: 'center',
                      lineHeight: 1.5,
                      text: '扫码观看视频'
                    },
                    className: 'Text'
                  }
                ]
              }
            ]
          },
          {
            attrs: {
              width: 130,
              height: 130,
              x: 2308,
              y: 3338,
              type: 'pageNum',
              name: 'pageNum'
            },
            className: 'Group',
            children: [
              {
                attrs: {
                  radius: 65,
                  fill: '#fff'
                },
                className: 'Circle'
              },
              {
                attrs: {
                  text: '1',
                  fontSize: 55,
                  width: 130,
                  fill: '#333',
                  align: 'center',
                  x: -65,
                  y: -25
                },
                className: 'Text'
              }
            ]
          }
        ]
      }
    ]
  }
}
