import api from '@/lib/api';

export const apiGet = async (url: string, data: any) => {
  return api.get(url, { params: data });
};

export const apiDel = async (url: string, data: any) => {
  return api.delete(url, { params: data });
};

export const apiPut = async (url: string, data: any) => {
  return api.put(url, data);
};

export const apiPost = async (url: string, data: any) => {
  return api.post(url, data);
};

export const apiPat = async (url: string, data: any) => {
  return api.patch(url, data);
};
