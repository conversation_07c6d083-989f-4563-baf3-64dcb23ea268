import bluebird from 'bluebird';

export const generateCatalogue = async (stageList: Array<any>) => {
  if (stageList.length <= 0) return;
  // 页数
  const curPage = 0;
  // 获取目录
  const arr: Array<any> = [];
  // 对分页进行重新计算并返回
  for (let i = 0; i < stageList.length; i++) {
    const layer = Array.isArray(stageList[i].children)
      ? stageList[i].children[0]
      : [];
    layer.children?.map(async (node: any) => {
      const isContent = node.attrs.type && node.attrs.type === 'content';

      if (isContent) {
        node.children.forEach((val: any) => {
          // 通用页标题
          if (val.attrs.type === 'title') {
            // 评价报告单独处理
            const isEvaluate = stageList[i].attrs.tmpType === 7;
            arr.push({
              page: isEvaluate ? i - 1 : i,
              title: isEvaluate ? '评价报告' : val.attrs.text,
              type: 'theme',
              media: '',
            });
          }
          // }
        });
      } else if (node.attrs.type === 'title') {
        arr.push({
          page: i,
          title: node.attrs.text,
          type: 'theme',
          media: '',
        });
      }
    });
  }
  // 由于最后一个成长评价标题用的是sub的标题，所以这里简单处理将最后一title改掉
  // if (Array.isArray(arr)) {
  //   const lastItem = arr[arr.length - 1]
  //   lastItem.title = '综合素质评价报告'
  //   lastItem.page -= 1
  // }
  return bluebird.resolve(arr);
};
