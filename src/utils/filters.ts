export function relationsFilter(type: number) {
  if (type === undefined) {
    return '';
  }
  const relations = {
    1: '爸爸',
    2: '妈妈',
    3: '爷爷',
    4: '奶奶',
    5: '外公',
    6: '外婆',
    7: '伯父',
    8: '伯母',
    9: '叔叔',
    10: '婶婶',
    11: '姑父',
    12: '姑母',
    13: '舅舅',
    14: '舅妈',
    15: '姐姐',
    16: '哥哥',
    17: '嫂嫂',
    18: '姨妈',
    19: '姑丈',
    20: '其他',
  }[type];
  return relations;
}
// 微信群发，朋友圈，小红书，抖音，视频号，快手，知乎、闲鱼，脉脉
// 对应1，2，3，4，5，6，7，8，9
export function openTypeFilter(type: number | string | undefined) {
  const typeNum = Number(type);
  if (!typeNum) {
    return '';
  }
  const openType = {
    1: '微信群发',
    2: '朋友圈',
    3: '小红书',
    4: '抖音',
    5: '视频号',
    6: '快手',
    7: '知乎',
    8: '闲鱼',
    9: '脉脉',
    10: '微信',
  }[typeNum];
  return openType || '';
}
export const relations = [
  {
    value: 1,
    label: '爸爸',
  },
  {
    value: 2,
    label: '妈妈',
  },
  {
    value: 3,
    label: '爷爷',
  },
  {
    value: 4,
    label: '奶奶',
  },
  {
    value: 5,
    label: '外公',
  },
  {
    value: 6,
    label: '外婆',
  },
  {
    value: 7,
    label: '伯父',
  },
  {
    value: 8,
    label: '伯母',
  },
  {
    value: 9,
    label: '叔叔',
  },
  {
    value: 10,
    label: '婶婶',
  },
  {
    value: 11,
    label: '姑父',
  },
  {
    value: 12,
    label: '姑母',
  },
  {
    value: 13,
    label: '舅舅',
  },
  {
    value: 14,
    label: '舅妈',
  },
  {
    value: 15,
    label: '姐姐',
  },
  {
    value: 16,
    label: '哥哥',
  },
  {
    value: 17,
    label: '嫂嫂',
  },
  {
    value: 18,
    label: '姨妈',
  },
  {
    value: 19,
    label: '姑丈',
  },
  {
    value: 20,
    label: '其他',
  },
];
