import Compressor from 'compressorjs';
import format from 'date-fns/format';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import ObsClient from 'esdk-obs-browserjs';

import { isProd } from '@/constant/env';
import { useObsStore } from '@/store/useObsStore';
// 系统环境，非正式环境都传到 test 文件夹
export function getEnv() {
  return isProd ? 'prod' : 'test';
}

// obs 上传接口
export async function uploadObs(
  file: any,
  key: string,
  isBase64 = false,
  progressCallback?: (progressData: any) => void
): Promise<string> {
  const { getToken } = useObsStore.getState();
  const credentials = await getToken();
  console.log('🚀 ~ credentials:', credentials);
  let blobData: any = null;
  if (isBase64) {
    const base64Response = await fetch(file);
    blobData = await base64Response.blob();
  }
  if (file instanceof Blob) {
    blobData = file;
  }
  const { ak, sk, bucket, domain, endpoint, securityToken } = credentials;
  return new Promise((resolve, reject) => {
    const obsClient = new ObsClient({
      access_key_id: ak,
      secret_access_key: sk,
      security_token: securityToken,
      server: endpoint
    });
    // const obsClient: any = null;
    const data: any = {
      Bucket: bucket, // 桶名
      Key: `${key}`, // 路径 + 文件名
      SourceFile: file,
      ProgressCallback: (transferredAmount: number, totalAmount: number) => {
        // 百分比取整数
        const progress = Math.floor((transferredAmount * 100.0) / totalAmount);
        progressCallback && progressCallback(progress);
      }
    };
    if (blobData) {
      delete data.SourceFile;
      data.Body = blobData;
    }

    obsClient.putObject(data, (err: any, result: any) => {
      if (err) {
        console.error(`Error-->${err}`);
        reject(err);
      } else {
        console.log(`Status-->${result.CommonMsg.Status}`);
        if (result.CommonMsg.Status === 200) {
          const url = `${domain}/${key}`;
          resolve(url);
        } else {
          reject(result.CommonMsg.Message);
        }
      }
    });
  });
}

// 断点续传上传接口
export async function uploadObsWithResume(
  file: any,
  key: string,
  progressCallback?: (progressData: any) => void,
  uploadCheckpoint?: any,
  checkpointCallback?: (checkpoint: any) => void
): Promise<string> {
  // 文件大小检查，断点续传上传接口传入的文件总大小至少要 100K 以上
  if (file.size < 100 * 1024) {
    // 如果文件小于100K，使用普通上传
    return uploadObs(file, key, progressCallback);
  }

  const { getToken } = useObsStore.getState();
  const credentials = await getToken();
  console.log('🚀 ~ credentials:', credentials);

  let blobData: any = null;

  const sourceFile = blobData || file;
  const { ak, sk, bucket, domain, endpoint, securityToken } = credentials;

  return new Promise((resolve, reject) => {
    const obsClient = new ObsClient({
      access_key_id: ak,
      secret_access_key: sk,
      security_token: securityToken,
      server: endpoint
    });

    // 保存断点续传记录对象
    let checkpointObj: any = uploadCheckpoint;
    // 保存取消上传控制参数
    let resumeHook: any;

    // 断点续传上传参数
    const uploadParams: any = {
      Bucket: bucket, // 桶名
      Key: `${key}`, // 路径 + 文件名
      SourceFile: sourceFile, // 上传的文件对象
      PartSize: 20 * 1024 * 1024, // 分段大小
      TaskNum: 3, // 分段上传的并发数，默认为 3

      // 如果有断点记录，使用它继续上传
      UploadCheckpoint: checkpointObj,

      // 进度回调
      ProgressCallback: (transferredAmount: number, totalAmount: number) => {
        // 百分比取整数
        const progress = Math.floor((transferredAmount * 100.0) / totalAmount);
        progressCallback?.(progress);
      },

      // 事件回调
      EventCallback: (eventType: string, eventParam: any, eventResult: any) => {
        console.log(`Event: ${eventType}`, eventParam, eventResult);
      },

      // 获取断点续传控制参数的回调函数
      ResumeCallback: (hook: any, checkpoint: any) => {
        // 保存取消断点续传上传任务控制参数
        resumeHook = hook;
        // 保存断点记录
        checkpointObj = checkpoint;

        // 如果断点记录中的 sourceFile 丢失（例如浏览器刷新后），重新设置
        if (checkpointObj && !checkpointObj.sourceFile) {
          checkpointObj.sourceFile = sourceFile;
        }

        // 调用外部回调函数，传递断点续传记录
        if (checkpointCallback) {
          checkpointCallback(checkpointObj);
        }
      }
    };

    // 执行上传
    const handleUpload = () => {
      obsClient.uploadFile(uploadParams, (err: any, result: any) => {
        if (err) {
          console.error(`Error-->${err}`);

          // 如果有断点记录，可以尝试继续上传
          if (checkpointObj) {
            console.log('尝试从断点继续上传...');

            // 更新上传参数，使用最新的断点记录
            uploadParams.UploadCheckpoint = checkpointObj;

            // 重新上传
            handleUpload();
          } else {
            reject(err);
          }
        } else {
          console.log(`Status-->${result.CommonMsg.Status}`);
          if (result.CommonMsg.Status < 300) {
            const url = `${domain}/${key}`;
            resolve(url);
          } else {
            reject(result.CommonMsg.Message);
          }
        }
      });
    };

    // 开始上传
    handleUpload();
  });
}

// 图片压缩
export function compress(
  image: any,
  width = 2000,
  height = 2000,
  quality = 0.9
): Promise<File> {
  return new Promise((resolve, reject) => {
    new Compressor(image, {
      maxWidth: width, // 压缩后图片最大宽度
      maxHeight: height, // 压缩后图片最大高度
      quality, // 压缩后图片的清晰度，取值 0-1，值越小图像越模糊
      success: (result: File) => resolve(result),
      error: reject
    });
  });
}

// 随机字符串
export function generateString(length: number) {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

// 生成上传路径 key
export function generateKey(uri: string, folderName: string) {
  const date = format(new Date(), 'yyyy-MM-dd');
  const fileName = uri.substring(uri.lastIndexOf('/') + 1);
  const fileExtension = fileName.split('.').pop()?.toLowerCase();
  const randomName = generateString(8);
  const key = `${getEnv()}/${folderName}/${date}/${randomName}${
    fileExtension ? `.${fileExtension}` : ''
  }`;
  return key;
}
