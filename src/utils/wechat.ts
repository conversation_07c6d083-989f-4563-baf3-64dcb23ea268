// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { getSign } from '@/api/wx';
import { getBrowser } from '@/utils';
interface WxSdk {
  config: (config: any) => void;
  ready: (callback: () => void) => void;
  error: (callback: (res: any) => void) => void;
  chooseImage: (options: any) => void;
  hideMenuItems: (options: { menuList: string[] }) => void;
  hideOptionMenu: () => void;
  showOptionMenu: () => void;
  updateAppMessageShareData: (options: any) => void;
  updateTimelineShareData: (options: any) => void;
}

let wxSdk: WxSdk;
function initWechat() {
  console.log('初始化微信jssdk');
  return new Promise<void>((resolve, reject) => {
    getSign({ url: encodeURIComponent(window.location.href) })
      .then(async (data) => {
        const url = new URL(window.location.href);
        const isDebug =
          url.searchParams.get('debug') === 'true' ||
          process.env.NODE_ENV === 'development';
        wxSdk = (await import('weixin-js-sdk')).default;
        console.log('wxSdk ', await import('weixin-js-sdk'));
        console.log('wxSdk:11 ', wxSdk);
        wxSdk.config({
          debug: isDebug,
          appId: data.appId,
          timestamp: data.timestamp,
          nonceStr: data.nonceStr,
          signature: data.signature,
          jsApiList: [
            'checkJsApi',
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'chooseWXPay',
            'hideMenuItems',
            'hideOptionMenu',
            'chooseImage',
          ],
          openTagList: ['wx-open-launch-app'],
        });
        resolve(1);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

function checkIsReady() {
  return new Promise((resolve, reject) => {
    initWechat().then(() => {
      wxSdk.ready(() => {
        resolve(true);
      });
      wxSdk.error((err: any) => {
        reject(err);
      });
    });
  });
}

function wechatShare(wxData: {
  title: string;
  desc: string;
  link: string;
  imgUrl?: string;
}) {
  const logoUrl = 'https://file.ancda.com/public/file/app/logo.png';
  const shareData = {
    title: wxData.title,
    desc: wxData.desc,
    link: wxData.link.replace(window.location.hash, ''),
    imgUrl: wxData.imgUrl || logoUrl,
  };
  if (getBrowser() === 'wechat') {
    checkIsReady()
      .then(() => {
        console.log('weixin ready share', shareData);
        wxSdk.updateAppMessageShareData({
          title: shareData.title,
          desc: shareData.desc,
          link: shareData.link,
          imgUrl: shareData.imgUrl,
          success: () => {
            console.log('shareData.link', shareData.link);
          },
        });
        wxSdk.updateTimelineShareData({
          title: shareData.title,
          link: shareData.link,
          imgUrl: shareData.imgUrl,
          success: () => {
            console.log('share success');
          },
        });
      })
      .catch((err) => {
        console.log('分享失败', err);
        checkIsReady();
      });
  }
}

function chooseImage() {
  return new Promise<string[]>((resolve, reject) => {
    checkIsReady()
      .then(() => {
        console.log('打开相册');
        wxSdk.chooseImage({
          count: 1, // 默认9
          sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
          sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
          success(res: any) {
            const localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
            console.log('localIds', localIds);
            resolve(localIds);
          },
        });
      })
      .catch((err) => {
        console.log('打开失败', err);
        reject(err);
      });
  });
}

function weChatInit() {
  checkIsReady()
    .then(() => {
      console.log('微信初始化');
    })
    .catch((err) => {
      console.log('微信初始化失败', err);
      checkIsReady();
    });
}

// 禁用微信复制链接和分享功能
function disableWechatCopyAndShare(options?: {
  hideAll?: boolean;
  menuList?: string[];
}) {
  const defaultMenuList = [
    'menuItem:copyUrl', // 复制链接
    'menuItem:share:appMessage', // 发送给朋友
    'menuItem:share:timeline', // 分享到朋友圈
    'menuItem:share:qq', // 分享到QQ
    'menuItem:share:QZone', // 分享到QQ空间
    'menuItem:openWithQQBrowser', // 在QQ浏览器中打开
    'menuItem:openWithSafari' // 在Safari中打开
  ];

  if (getBrowser() === 'wechat') {
    checkIsReady()
      .then(() => {
        if (options?.hideAll) {
          // 完全隐藏右上角菜单
          wxSdk.hideOptionMenu();
          console.log('已隐藏微信右上角菜单');
        } else {
          // 隐藏指定的菜单项
          const menuList = options?.menuList || defaultMenuList;
          wxSdk.hideMenuItems({
            menuList: menuList
          });
          console.log('已隐藏微信菜单项:', menuList);
        }
      })
      .catch((err) => {
        console.log('禁用微信功能失败', err);
        // 降级方案：使用WeixinJSBridge
        interface WeixinJSBridge {
          call: (method: string) => void;
        }
        
        const hideWithBridge = () => {
          const bridge = (window as unknown as { WeixinJSBridge?: WeixinJSBridge }).WeixinJSBridge;
          if (typeof bridge !== 'undefined') {
            bridge.call('hideOptionMenu');
            console.log('使用WeixinJSBridge隐藏菜单');
          }
        };
        
        const bridge = (window as unknown as { WeixinJSBridge?: WeixinJSBridge }).WeixinJSBridge;
        if (typeof bridge !== 'undefined') {
          hideWithBridge();
        } else {
          document.addEventListener('WeixinJSBridgeReady', hideWithBridge);
        }
      });
  }
}

// 显示微信菜单（恢复功能）
function enableWechatMenu() {
  if (getBrowser() === 'wechat') {
    checkIsReady()
      .then(() => {
        wxSdk.showOptionMenu();
        console.log('已显示微信右上角菜单');
      })
      .catch((err) => {
        console.log('显示微信菜单失败', err);
      });
  }
}

export { chooseImage, weChatInit, wechatShare, disableWechatCopyAndShare, enableWechatMenu };
