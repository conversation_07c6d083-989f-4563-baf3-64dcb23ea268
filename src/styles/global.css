@tailwind base;
@layer base {
  @import "antd-mobile/es/global/global.css";
}
@tailwind components;
@tailwind utilities;
:root:root {
  --adm-font-size-main: var(--adm-font-size-8);
}
:root {
  /* #region  /**=========== Primary Color =========== */
  --tw-color-primary-50: 240 249 255;
  --tw-color-primary-100: 224 242 254;
  --tw-color-primary-200: 186 230 253;
  --tw-color-primary-300: 125 211 252;
  --tw-color-primary-400: 56 189 248;
  --tw-color-primary-500: 14 165 233;
  --tw-color-primary-600: 2 132 199;
  --tw-color-primary-700: 3 105 161;
  --tw-color-primary-800: 7 89 133;
  --tw-color-primary-900: 12 74 110;
  --color-primary-50: rgb(var(--tw-color-primary-50)); /* #f0f9ff */
  --color-primary-100: rgb(var(--tw-color-primary-100)); /* #e0f2fe */
  --color-primary-200: rgb(var(--tw-color-primary-200)); /* #bae6fd */
  --color-primary-300: rgb(var(--tw-color-primary-300)); /* #7dd3fc */
  --color-primary-400: rgb(var(--tw-color-primary-400)); /* #38bdf8 */
  --color-primary-500: rgb(var(--tw-color-primary-500)); /* #0ea5e9 */
  --color-primary-600: rgb(var(--tw-color-primary-600)); /* #0284c7 */
  --color-primary-700: rgb(var(--tw-color-primary-700)); /* #0369a1 */
  --color-primary-800: rgb(var(--tw-color-primary-800)); /* #075985 */
  --color-primary-900: rgb(var(--tw-color-primary-900)); /* #0c4a6e */
  /* #endregion  /**======== Primary Color =========== */
}

@layer base {

  @font-face {
    font-family: 'Alibaba-PuHuiTi';
    src: url(https://edu-media.ancda.com/prod/archives/album/fonts/AlibabaPuHuiTi-2-55-Regular.woff)
        format('woff'),
      url(https://edu-media.ancda.com/prod/archives/album/fonts/AlibabaPuHuiTi-2-55-Regular.ttf)
        format('truetype');
    font-display: swap;
  }
  @font-face {
    font-family: 'liZi';
    src: url(https://edu-media.ancda.com/prod/archives/album/fonts/LiZiXianYuLangLangJain.woff)
        format('woff'),
      url(https://edu-media.ancda.com/prod/archives/album/fonts/LiZiXianYuLangLangJain.ttf)
        format('truetype');
    font-display: swap;
  }

  html {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* #region  /**=========== Typography =========== */
  .h0 {
    @apply text-3xl font-bold md:text-5xl;
  }

  h1,
  .h1 {
    @apply text-2xl font-bold md:text-4xl;
  }

  h2,
  .h2 {
    @apply text-xl font-bold md:text-3xl;
  }

  h3,
  .h3 {
    @apply text-lg font-bold md:text-2xl;
  }

  h4,
  .h4 {
    @apply text-base font-bold md:text-lg;
  }

  body,
  .p {
    @apply text-sm md:text-base leading-normal;
  }

  .primary-gradient {
    @apply bg-gradient-to-r from-[#31C3FF] to-[#4E78FF];
  }
  /* #endregion  /**======== Typography =========== */

  .layout {
    /* 1100px */
    max-width: 68.75rem;
    @apply mx-auto w-11/12;
  }

  .bg-dark a.custom-link {
    @apply border-gray-200 hover:border-gray-200/0;
  }

  /* Class to adjust with sticky footer */
  .min-h-main {
    @apply min-h-[calc(100vh-56px)];
  }
  svg {
    display: inline-block;
  }
  [type='search']:focus,
  textarea:focus {
    --tw-ring-shadow: none;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer utilities {
  .animated-underline {
    background-image: linear-gradient(#33333300, #33333300),
      linear-gradient(
        to right,
        var(--color-primary-400),
        var(--color-primary-500)
      );
    background-size: 100% 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
  }
  @media (prefers-reduced-motion: no-preference) {
    .animated-underline {
      transition: 0.3s ease;
      transition-property: background-size, color, background-color,
        border-color;
    }
  }
  .animated-underline:hover,
  .animated-underline:focus-visible {
    background-size: 0 2px, 100% 2px;
  }
}
/* 去掉输入框的 focus 边框 */
input {
  --tw-ring-shadow: 0 0 #000 !important;
}
/* tinymce 样式 */
.tox-tinymce {
  border: 1px solid #eee !important;
}
.tox-tinymce:not(.tox-tinymce-inline)
  .tox-editor-header:not(:first-child)
  .tox-toolbar-overlord:first-child
  .tox-toolbar__primary,
.tox-tinymce:not(.tox-tinymce-inline)
  .tox-editor-header:not(:first-child)
  .tox-toolbar:first-child {
  border-top: 1px solid #eee !important;
}

.tox:not([dir='rtl']) .tox-toolbar__group:not(:last-of-type) {
  border-right: 1px solid #eee !important;
}

.tox[dir='rtl'] .tox-toolbar__group:not(:last-of-type) {
  border-left: 1px solid #eee !important;
}

.tox.tox-tinymce-aux .tox-toolbar__overflow {
  border: 1px solid #eee !important;
}

.rv-typography__text > a {
  color: #4e78ff;
  cursor: pointer;
}

/* 自定义表单富文本格式 */
.html-content * {
  max-width: 100% !important;
  cursor: pointer;
  margin: 0;
  padding: 0;
  box-sizing: border-box !important;
  -webkit-box-sizing: border-box !important;
  word-wrap: break-word !important;
  line-height: normal !important;
}

.center {
  @apply flex justify-center items-center;
}

.primary-bg {
  background-color: var(--adm-color-primary);
}

.primary-color {
  color: var(--adm-color-primary);
}

/* antd-mobile 样式覆盖 */
.adm-list-item {
  padding-left: var(--padding-left);
  padding-right: var(--padding-left);
  background-color: var(--adm-color-transparent) !important;

}
.adm-list-item-content {
  padding-right: 0 !important;
}
.adm-form .adm-list-card {
  --border-inner: 1px solid #f2f2f2;
}
.adm-image-viewer-slides-inner > * {
  margin-right: 0 !important;
}
/*  表单禁用透明度修改 */
.adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > * {
  opacity: 0.8 !important;
}

.adm-image-uploader-grid, .adm-image-uploader-space {
  --gap: 10px;
}

.adm-space-horizontal > .adm-space-item {
  margin-right: 12px !important;
}

.adm-capsule-tabs-header {
  border-bottom: 0 !important;
}
.adm-capsule-tabs-tab-active {
  background-color: #f60 !important;
}
.adm-tabs {
  color: #999;
}
.adm-tabs-tab-active {
  color: #333 !important;
}
.adm-tabs-tab-line {
  height: 4px !important;
  background: linear-gradient(90deg, #31C3FF 0%, var(--adm-color-primary) 100%) !important;
  border-radius: 4px !important;
}

.adm-calendar-cell.adm-calendar-cell-selected.adm-calendar-cell.adm-calendar-cell-selected.adm-calendar-cell-top {
  border-radius: 99px !important;
}

/* form-render 表单分组标题 */
.frm-widget-group-title {
  color: #333 !important;
  border-top: 0 !important;
  border-bottom: 0 !important;
  display: none;
  padding: 0 !important;
}
/* 美化 markdown 渲染 */
.prose h3 {
  @apply inline-block bg-gradient-to-r from-cyan-100 to-cyan-200 bg-[length:100%_20px] lg:bg-[length:100%_10px] bg-no-repeat bg-bottom
}

/* 心跳动画 */
.pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    /* opacity: 1; */
    transform: translate(-50%, -50%) scale(1.05);
  }
  50% {
    /* opacity: .9; */
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes pulse-no-offset {
  0%, 100% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(1);
  }
}
.pulse-no-offset {
  animation: pulse-no-offset 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* @layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
} */
