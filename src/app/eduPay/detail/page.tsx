'use client';
import { useSearchParams, useRouter } from 'next/navigation';

import { List, Modal, Button } from 'antd-mobile';
import React, { useEffect, useState } from 'react';
import { getStudentUnPayDetail, getStudentPayDetail } from '@/api/eduPay';
import { useEduPayStore } from '@/store/useEduPayStore';

if (typeof document !== 'undefined') {
  document.title = '账单详情';
}

function chgMethodFilter(status) {
  const statusMap = {
    1: '现金',
    2: 'POS',
    3: '银行',
    4: '微信',
    5: '支付宝',
    6: '支付宝教育缴费',
    7: '微信教育缴费',
  };
  return statusMap[status];
}

export default () => {
  const searchParams = useSearchParams();
  const billId = searchParams?.get('billId') || null;
  const recordId = searchParams?.get('recordId') || null;
  const isHistory = searchParams?.get('isHistory') || false;
  const instName = useEduPayStore((state) => state.instName);
  const studentId = useEduPayStore((state) => state.studentId);
  const studentName = useEduPayStore((state) => state.studentName);
  const className = useEduPayStore((state) => state.className);

  const [items, setItems] = useState<any[]>([]);
  const [billInfo, setBillInfo] = React.useState<any>({
    name: '',
    amount: '0.00',
    createTime: '',
    chgRecordNo: '',
    chgMethod: 0,
  });

  useEffect(() => {
    if (studentId) {
      if (billId) {
        getStudentUnPayDetail(studentId, billId).then((res: any) => {
          setBillInfo({
            name: res.billName || '',
            amount: res.amount || '',
            createTime: res.createTime || '',
            chgRecordNo: res.orderNo || '',
          });
          if (Array.isArray(res.itemList)) {
            setItems(res.itemList);
          }
        });
      }
      if (recordId) {
        getStudentPayDetail(studentId, recordId).then((res: any) => {
          setBillInfo({
            name: res.recordName || '',
            amount: res.amount || '',
            createTime: res.createTime || '',
            chgRecordNo: res.chgRecordNo || '',
            chgMethod: res.chgMethod || 0,
          });
          if (Array.isArray(res.recordList)) {
            setItems(res.recordList);
          }
        });
      }
    }
  }, [billId, studentId]);

  const showModal = () => {
    Modal.alert({
      title: '缴费指引',
      content: (
        <>
          <div className="flex pt-3">
            <span className="float-left pr-2">1.</span>
            <p>打开支付宝APP，搜索 “校园缴费”</p>
          </div>
          <div className="flex pt-3">
            <span className="float-left pr-2">2. </span>
            <p>点击 “支付宝校园缴费”</p>
          </div>
          <div className="flex pt-3 pb-8">
            <span className="float-left pr-2">3. </span>
            <p>找到该笔账单并缴费</p>
          </div>
        </>
      ),
    });
  };

  return (
    <div className="bg-slate-50 h-screen">
      <div className="p-8 text-center">
        <p className="text-center text-lg mb-2">{billInfo.name} </p>
        <h1 className="text-center text-4xl mb-2">{`¥ ${billInfo.amount}`} </h1>
        {isHistory && <p className="text-xl">支付成功</p>}
      </div>
      <List mode="card" header="费用明细">
        {items.map((item) => (
          <List.Item
            key={item.billItemId}
            extra={`¥ ${item.itemAmount}`}
            arrowIcon={false}
          >
            {item.itemName}
          </List.Item>
        ))}
      </List>
      <List mode="card" header="账单信息">
        <List.Item extra={studentName} arrowIcon={false}>
          学生姓名
        </List.Item>
        <List.Item extra={instName} arrowIcon={false}>
          所在学校
        </List.Item>
        <List.Item arrowIcon={false} extra={className}>
          所在班级
        </List.Item>
        <List.Item arrowIcon={false} extra={billInfo.chgRecordNo}>
          {isHistory ? '收费记录' : '账单'}单号
        </List.Item>
        <List.Item arrowIcon={false} extra={billInfo.createTime}>
          记录生成日期
        </List.Item>
        {isHistory && (
          <List.Item
            arrowIcon={false}
            extra={chgMethodFilter(billInfo.chgMethod)}
          >
            收费方式
          </List.Item>
        )}
      </List>
      {!isHistory && (
        <div className="flex justify-center items-center mt-5 px-10">
          <Button
            block
            color="primary"
            size="large"
            fill="outline"
            shape="rounded"
            onClick={() => showModal()}
          >
            缴费指引
          </Button>
        </div>
      )}
    </div>
  );
};
