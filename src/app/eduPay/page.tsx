'use client';

import React, { useEffect } from 'react';
import { Form, Input, Button, Toast } from 'antd-mobile';
import { getSchoolEduPayInfo } from '@/api/eduPay';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEduPayStore } from '@/store/useEduPayStore';
import type { FormInstance } from 'antd-mobile/es/components/form';

if (typeof document !== 'undefined') {
  document.title = '缴费账单查询';
}

export default function () {
  const router = useRouter();
  const searchParams = useSearchParams();
  const instId = searchParams?.get('instId') || '';
  const instName = searchParams?.get('instName') || '-';
  const setInstName = useEduPayStore((state) => state.setInstName);
  const setStudentId = useEduPayStore((state) => state.setStudentId);
  const setStudentName = useEduPayStore((state) => state.setStudentName);
  const setClassName = useEduPayStore((state) => state.setClassName);
  const setAvatar = useEduPayStore((state) => state.setAvatar);

  const [form] = Form.useForm();
  const formRef = React.useRef<FormInstance>(null);

  useEffect(() => {
    if (instName) {
      setInstName(instName);
    }
  }, [instName]);

  const onFinish = (values: any) => {
    console.log('🚀 ~ values:', values);
    getSchoolEduPayInfo({
      instId,
      mobile: values.mobile,
      studentName: values.studentName,
    }).then((res: any) => {
      if (Array.isArray(res) && res.length > 0) {
        setStudentId(res[0].studentId || '');
        setStudentName(res[0].name || '');
        setClassName(res[0].className || '');
        setAvatar(res[0].avatar || '');
        Toast.show({ content: '登录成功' });
        router.push('/eduPay/list');
      }
    });
  };

  return (
    <div className="flex flex-col justify-center items-center h-screen bg-slate-50 px-8">
      <h1 className="text-center text-lg mb-6">缴费账单查询</h1>
      <Form
        ref={formRef}
        form={form}
        layout="horizontal"
        className="bg-white rounded-xl w-full"
        mode="card"
        onFinish={onFinish}
        footer={
          <div className="px-4">
            <Button block type="submit" color="primary" shape="rounded">
              查询账单
            </Button>
          </div>
        }
      >
        <Form.Item label="幼儿园:">
          <Input placeholder="请输入" value={instName} readOnly />
        </Form.Item>
        <Form.Item
          label="学生姓名:"
          name="studentName"
          rules={[{ required: true, message: '姓名不能为空' }]}
        >
          <Input placeholder="请输入" />
        </Form.Item>
        <Form.Item
          label="家长手机号:"
          name="mobile"
          rules={[{ required: true, message: '手机号不能为空' }]}
        >
          <Input placeholder="请输入" />
        </Form.Item>
      </Form>
    </div>
  );
}
