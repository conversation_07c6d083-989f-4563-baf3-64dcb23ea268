'use client';

import { useRouter } from 'next/navigation';
import Script from 'next/script';
import Cookies from 'js-cookie';
import { PiTimer } from '@/components/Icons';
import { List, Image, DotLoading } from 'antd-mobile';
import Empty from '@/components/Empty';

import { getStudentUnPayList, getStudentInfo } from '@/api/eduPay';
import { useEffect, useState } from 'react';
import { useEduPayStore } from '@/store/useEduPayStore';

type BillItem = {
  billId: string;
  billName: string;
  amount: string;
  createTime: string;
};

if (typeof document !== 'undefined') {
  document.title = '账单记录';
}

export default function Page() {
  const router = useRouter();
  const token = Cookies.get('Authorization');

  const studentId = useEduPayStore((state) => state.studentId);
  const studentName = useEduPayStore((state) => state.studentName);
  const className = useEduPayStore((state) => state.className);
  const avatar = useEduPayStore((state) => state.avatar);
  const setInstName = useEduPayStore((state) => state.setInstName);
  const setStudentId = useEduPayStore((state) => state.setStudentId);
  const setStudentName = useEduPayStore((state) => state.setStudentName);
  const setClassName = useEduPayStore((state) => state.setClassName);
  const setAvatar = useEduPayStore((state) => state.setAvatar);
  const [billList, setBillList] = useState<BillItem[]>([]);

  useEffect(() => {
    if (token) {
      getStudentInfo().then((res: any) => {
        console.log('🚀 ~ res:', res);
        if (res.isOpenPayment === 0) {
          router.replace('/eduPay/notEnabled');
        }
        setInstName(res.instName || '');
        setStudentId(res.studentId || '');
        setStudentName(res.studentName || '');
        setClassName(res.className || '');
        setAvatar(res.avatar || '');
      });
    }
    if (studentId) {
      getStudentUnPayList(studentId).then((res) => {
        console.log('🚀 ~ res:', res);
        if (Array.isArray(res)) {
          setBillList(res);
        }
      });
    }
  }, [studentId, token]);

  const handleToDetail = (billId: string) => {
    router.push(`/eduPay/detail?billId=${billId}`);
  };

  const handleToHistory = () => {
    router.push('/eduPay/history');
  };
  if (studentId === '') {
    return (
      <div className="h-screen flex justify-center items-center">
        <DotLoading />
      </div>
    );
  }

  return (
    <main>
      <div className="max-w-md mx-auto bg-slate-50 h-screen">
        <div className="px-4 py-6 bg-white">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Image
                className="h-10 w-6 rounded-full"
                src={avatar}
                alt="用户头像"
                width={60}
                height={60}
              />
            </div>
            <div className="ml-3">
              <h2 className="text-lg font-semibold text-gray-800">
                {studentName}
              </h2>
              <p className="text-sm text-gray-600">{className}</p>
            </div>
            <div className="ml-auto">
              <div
                className=" text-teal-500 flex items-center"
                onClick={handleToHistory}
              >
                <PiTimer fontSize={20} color="#4299e1" />
                <span className="text-blue-500 text-md ml-1">缴费记录</span>
              </div>
            </div>
          </div>
        </div>

        {billList.length === 0 ? (
          <div className="h-full">
            <Empty title="暂无待缴费账单" />
          </div>
        ) : (
          <List mode="card" header="待缴费账单列表">
            {billList.map((item: BillItem) => (
              <List.Item
                key={item.billId}
                extra={`¥ ${item.amount}`}
                description={item.createTime}
                arrowIcon
                onClick={() => handleToDetail(item.billId)}
              >
                {item.billName || ''}
              </List.Item>
            ))}
          </List>
        )}
      </div>
    </main>
  );
}
