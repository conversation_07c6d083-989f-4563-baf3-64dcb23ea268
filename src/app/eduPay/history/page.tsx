'use client';

import { List } from 'antd-mobile';
import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getStudentPayList } from '@/api/eduPay';

import { useEduPayStore } from '@/store/useEduPayStore';
import Empty from '@/components/Empty';
import { format } from 'date-fns';

// 按日期分组的函数
function groupByDate(data) {
  const grouped = data.reduce((acc, record) => {
    const date = format(new Date(record.createTime), 'yyyy年M月'); // 提取日期部分
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(record);
    return acc;
  }, {});

  // 将对象转换为数组格式
  return Object.entries(grouped).map(([date, records]) => ({
    date,
    records,
  }));
}

export default () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const studentId = useEduPayStore((state) => state.studentId);

  const [billList, setBillList] = useState([]);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '缴费记录';
    }
  }, []);

  useEffect(() => {
    if (studentId) {
      getStudentPayList(studentId).then((res) => {
        console.log('🚀 ~ res:', res);
        if (Array.isArray(res)) {
          // 按日期重新分组数据
          setBillList(groupByDate(res));
        }
      });
    }
  }, [studentId]);

  function handleToDetail(recordId: string) {
    router.push(`/eduPay/detail?recordId=${recordId}&isHistory=true`);
  }

  return (
    <div className="bg-slate-50 h-screen overflow-y-scroll">
      {/* <div className="p-4 text-lg bg-white">缴费记录</div> */}
      {billList.length === 0 ? (
        <div className="h-full">
          <Empty title="暂无缴费记录" />
        </div>
      ) : (
        <>
          {billList.map((item: any) => (
            <List mode="card" key={item.date} header={item.date}>
              {Array.isArray(item.records) &&
                item.records.map((record: any) => (
                  <List.Item
                    key={record.recordId}
                    extra={`¥ ${record.amount}`}
                    description={record.createTime}
                    arrowIcon
                    onClick={() => handleToDetail(record.recordId)}
                  >
                    {record.recordName || ''}
                  </List.Item>
                ))}
            </List>
          ))}
        </>
      )}
    </div>
  );
};
