'use client';

import { Bell, Info } from 'lucide-react';
import Head from 'next/head';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function Home() {
  const searchParams = useSearchParams();
  const [message, setMessage] = useState({
    title: searchParams.get('title') || '系统消息',
    content: searchParams.get('content') || '暂无内容'
  });
  useEffect(() => {
    // 保存原来的标题
    const originalTitle = document.title;
    // 设置新标题
    document.title = '消息详情';

    // 组件卸载时恢复原标题
    return () => {
      document.title = originalTitle;
    };
  }, []);
  useEffect(() => {
    const content = searchParams.get('content');
    if (content) {
      const decodedContent = decodeURIComponent(content)
        .replace(/\\n/g, '\n') // Convert \n string to actual line breaks
        .replace(/\\r/g, '\r') // Handle carriage returns
        .replace(/\+/g, ' '); //
      setMessage((prev) => ({
        ...prev,
        content: decodedContent
      }));
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Head>
        <title>消息详情</title>
      </Head>

      {/* Main Content */}
      <main className="pb-6 pt-1">
        {/* Message Card */}
        <div className="mx-4 mt-4 overflow-hidden rounded-xl bg-white shadow-sm">
          {/* Message Header */}
          <div className="border-b border-gray-100 p-4">
            <div className="mb-2 flex items-center gap-3">
              <div className="rounded-lg bg-blue-100 p-2">
                <Bell className="size-5 text-blue-600" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900">系统通知</h2>
            </div>

            {/* Message Meta  */}
            <div className="mt-3 flex flex-wrap gap-x-4 gap-y-2">
              <div className="flex items-center text-sm text-gray-500">
                <Info className="mr-1 size-4" />
                <span>{message.title}</span>
              </div>
            </div>
          </div>

          {/* Message Content */}
          <div className="p-4">
            <div className="whitespace-pre-line leading-relaxed text-gray-700">
              {message.content}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
