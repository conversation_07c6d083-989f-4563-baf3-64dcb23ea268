'use client';

import Cookies from 'js-cookie';
import {
  BadgeCheck,
  CreditCard,
  Edit3,
  FileText,
  MessageCircle,
  Phone,
  PlaySquare,
  RefreshCcw,
  Repeat,
  Trash2,
  Users,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { aiWebUrlHost } from '@/constant/env';
import { isPalmBaby } from '@/lib/utils';
import { hinaTrack, navigationToNativePage } from '@/utils/index';

import { faqList, tabs } from './components/content';

const ServiceCenter = () => {
  const selfServices = [
    {
      icon: <CreditCard size={24} />,
      label: '绑定考勤卡',
      route: 'rn://FaceStack?initialRoute=BindCardScreen',
    },
    {
      icon: <PlaySquare size={24} />,
      label: '购买视频服务',
      route: 'app://app/babyOnline/videoPlay',
    },
    {
      icon: <Trash2 size={24} />,
      label: '清除缓存',
      route: 'app://app/my/clearCache',
    },
    {
      icon: <FileText size={24} />,
      label: '查看缴费记录',
      route: 'rn://SunshineStack?initialRoute=ParentOrderListScreen',
    },
    {
      icon: <RefreshCcw size={24} />,
      label: '换绑手机号',
      route: 'app://app/my/changePhone',
    },

    {
      icon: <Repeat size={24} />,
      label: '管理自动续费',
      route: 'rn://MemberStack?initialRoute=AutoRenewalScreen',
    },
    {
      icon: <Users size={24} />,
      label: '邀请亲友团',
      route: 'rn://FriendsStack?initialRoute=IndexScreen&friendsOpen=1',
    },
    {
      icon: <BadgeCheck size={24} />,
      label: '购买会员服务',
      route: 'rn://MemberStack?initialRoute=ParentsIndexScreen',
    },
  ];
  const router = useRouter();
  const searchParams = useSearchParams();
  const parentName = Cookies.get('parentName');
  const tabFromUrl = searchParams.get('activeTab');
  const [activeTab, setActiveTab] = useState(tabFromUrl || 'account');
  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('activeTab', tabKey);
    window.history.replaceState({}, '', newUrl);
  };
  useEffect(() => {
    hinaTrack('CSR_homepage');
    if (typeof document !== 'undefined') {
      document.title = '我的客服';
    }
    const handlePopState = () => {
      const tab = searchParams.get('activeTab');
      if (tab) {
        setActiveTab(tab);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);
  const handleSelfServiceClick = (item: any) => {
    navigationToNativePage(item.route);
    hinaTrack('CSR_selfservice_click', {
      selfservice_type: item.label,
    });
  };
  const handleClick = (type, name) => {
    hinaTrack('CSR_bottom_button_click', {
      CSR_bottom_button: name,
    });
    if (type === 1) {
      navigationToNativePage('app://app/my/feedback');
    } else if (type === 2) {
      navigationToNativePage(`${aiWebUrlHost}/?appId=66&hideTitleBar=1`);
    } else {
      window.open('tel:4008879121');
    }
  };
  return (
    <div className="relative min-h-screen">
      <div
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: isPalmBaby()
            ? 'url(https://unicorn-media.ancda.com/production/app/member/servicebg.jpg)'
            : 'url(https://unicorn-media.ancda.com/production/app/member/serviceBg.jpg)',
        }}
      />
      <div className="relative">
        <div className="relative mt-2 flex items-center gap-4 rounded-b-2xl p-4">
          <div className="flex-1">
            <div className="text-xl font-bold">Hi，{parentName}</div>
            <div className="mt-1 text-sm text-gray-600">
              欢迎来到我的客服，
              <br />
              您的满意是我们不懈的追求！
            </div>
          </div>
          <div className="absolute -bottom-1 right-4 flex items-center justify-center rounded-full">
            <img
              src="https://unicorn-media.ancda.com/production/app/member/serviceCenterAvatar.png"
              alt="avatar"
              className="size-24"
            />
          </div>
        </div>
        <div className="mx-2 mt-1 rounded-2xl bg-white py-4">
          <div className="mb-2 px-4 text-lg font-bold">自助服务</div>
          <div className="grid grid-cols-4">
            {selfServices.map((item) => (
              <div
                key={item.label}
                className="mb-2 flex cursor-pointer flex-col items-center justify-center rounded-lg p-2 px-1 hover:bg-[#F0F9FF]"
                onClick={() => handleSelfServiceClick(item)}
              >
                {item.icon}
                <span className="mt-2 text-center text-xs leading-tight">
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="mx-2 mb-[170px] mt-8 flex flex-1 flex-col rounded-2xl bg-white px-2 py-4 shadow">
          <div className="mb-2 px-2 text-lg font-bold">猜你想问</div>
          <div className="relative">
            <div className="scrollbar-hide mb-2 flex gap-4 overflow-x-auto border-b pr-2">
              {tabs.map((tab) => (
                <button
                  key={tab.key}
                  type="button"
                  className={`whitespace-nowrap pb-1 text-base font-medium ${activeTab === tab.key ? '' : 'text-gray-500'}`}
                  onClick={() => handleTabChange(tab.key)}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
          <div className="flex-1 overflow-y-auto">
            {faqList[activeTab as keyof typeof faqList].map((q, index) => (
              <div
                key={q}
                className="mb-2 cursor-pointer rounded-lg  px-3 py-2 text-sm text-gray-700 hover:bg-[#E6F7FF]"
                onClick={() => {
                  router.push(
                    `/serviceCenter/qaContent?activeTab=${activeTab}&questionIndex=${index}`,
                  );
                }}
              >
                {q}
              </div>
            ))}
          </div>
        </div>

        <div className="fixed bottom-0 left-0 z-10 flex w-full items-center justify-around border-t bg-white/80 py-4 backdrop-blur-sm">
          <div
            className="flex cursor-pointer flex-col items-center text-xs text-gray-600 transition-colors "
            onClick={() => handleClick(1, '我要反馈')}
          >
            <Edit3 size={18} />
            <span className="mt-1.5">我要反馈</span>
          </div>
          <div
            className="flex cursor-pointer flex-col items-center text-xs text-gray-600 transition-colors "
            onClick={() => handleClick(2, '在线客服')}
          >
            <MessageCircle size={18} />
            <span className="mt-1.5">在线客服</span>
          </div>
          <div
            className="flex cursor-pointer flex-col items-center text-xs text-gray-600 transition-colors "
            onClick={() => handleClick(3, '电话咨询')}
          >
            <Phone size={18} />
            <span className="mt-1.5">电话咨询</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceCenter;
