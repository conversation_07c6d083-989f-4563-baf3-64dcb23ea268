'use client';

import { Button, Dialog, Input, Swiper, Toast } from 'antd-mobile';
import Cookies from 'js-cookie';
import Head from 'next/head';
import React, { useEffect, useState } from 'react';

import { getInsurance, submitApply } from '@/api/insurance';
import OverLayLoad from '@/components/OverLayLoad/Index';
import { validateChineseMobileNumber } from '@/utils';

import styles from './teacher.module.css';

const Index = () => {
  const [status, setStatus] = useState(4);
  const [container, setContainer] = useState(styles.container);
  const [schoolInfo, setSchoolInfo] = useState<any>({
    name: '',
    schoolName: '',
    tel: '',
  });
  const [principalNameTitle, setPrincipalNameTitle] = useState('');
  const [butLoading, setButLoading] = useState(false);
  const [insuranceId, setInsuranceId] = useState(0);
  const [loadingOverlay, setLoadingOverlay] = useState(true);
  const mockData = [
    { name: '李**园长已申请开通', id: 1 },
    { name: '张**园长已申请开通', id: 2 },
    { name: '梁**园长已申请开通', id: 3 },
    { name: '刘*园长已申请开通', id: 4 },
    { name: '杨*园长已申请开通', id: 5 },
    { name: '欧阳**园长已申请开通', id: 6 },
    { name: '王**园长已申请开通', id: 7 },
    { name: '李*园长已申请开通', id: 8 },
    { name: '吴**园长已申请开通', id: 9 },
    { name: '张*园长已申请开通', id: 10 },
    { name: '李*园长已申请开通', id: 11 },
    { name: '黄*园长已申请开通', id: 12 },
    { name: '黄**园长已申请开通', id: 13 },
    { name: '袁**园长已申请开通', id: 14 },
  ];
  useEffect(() => {
    const teacherName = Cookies.get('teacherName');
    const mobile = Cookies.get('mobile');
    const instName = Cookies.get('instName');
    console.log('🚀 ~ file: api.ts:19 ~ token:', {
      teacherName,
      mobile,
      instName,
    });
    setSchoolInfo({
      name: teacherName,
      schoolName: instName,
      tel: mobile,
    });
    initData();
  }, []);
  const initData = async () => {
    insuranceInfo();
  };
  const insuranceInfo = () => {
    getInsurance({})
      .then((response: any) => {
        if (Number(response.status) === 1) {
          window.location.href = 'https://www.xiebao18.com/';
          window.history.replaceState(null, '', window.location.href);
          return false;
        }
        if (response.id) {
          setInsuranceId(response.id);
          setStatus(Number(response.status));
          setContainer(styles.containerReviewbg);
          setPrincipalNameTitle(response.name);
        }
        setLoadingOverlay(false);
      })
      .catch(() => {
        setLoadingOverlay(false);
      });
  };
  const submitInfo = () => {
    if (!schoolInfo.name) {
      Toast.show('请填写您的称呼');
      return false;
    }
    if (!validateChineseMobileNumber(schoolInfo.tel)) {
      Toast.show('请填写正确的手机号码！');
      return false;
    }
    if (!schoolInfo.schoolName) {
      Toast.show('请填写学校名称！');
      return false;
    }
    setButLoading(true);
    submitApply({
      id: insuranceId,
      ...schoolInfo,
    })
      .then((res: any) => {
        Dialog.alert({
          title: '提交成功',
          content: '审核时间1-2个工作日',
        }).then(() => {
          // on close
          setSchoolInfo({
            name: '',
            schoolName: '',
            tel: '',
          });
          insuranceInfo();
        });
      })
      .finally(() => {
        setButLoading(false);
      });
  };
  const submitInfoAlign = () => {
    setStatus(4);
    setContainer(styles.container);
  };
  const renderHtml = () => {
    switch (status) {
      case 0:
        return (
          <>
            <div className={styles.reviewing}>
              <img src="/images/insurance/reviewing.png" alt="" />
            </div>
            <div className={styles.qrcodeitem}>
              <p>
                了解审核进度,可添加<span>@保险小助手小贝</span>
              </p>
              <img
                src="https://file.ancda.com/public/file/app/qrcode/qrcode-insurance.png"
                alt=""
              />
              <p>长按下载图片-打开微信扫一扫添加</p>
            </div>
          </>
        );
      case 1:
        return (
          <>
            <div className={styles.reviewSuccess}>
              <p>开通保险合作系统</p>
              <p>恭喜{principalNameTitle}园长</p>
            </div>
            <div className={styles.qrcodeitem}>
              <p>
                添加<span>@保险小助手小贝,</span>获取账号密码
              </p>
              <img
                src="https://file.ancda.com/public/file/app/qrcode/qrcode-insurance.png"
                alt=""
              />
              <p>长按下载图片-打开微信扫一扫添加</p>
            </div>
          </>
        );
      case 2:
        return (
          <>
            <div className={styles.reviewfliad}>
              <img src="/images/insurance/reviewfliad.png" alt="" />
            </div>
            <div className={styles.qrcodeitem}>
              <p>
                添加<span>@保险小助手小贝,</span>咨询失败原因
              </p>
              <img
                src="https://file.ancda.com/public/file/app/qrcode/qrcode-insurance.png"
                alt=""
              />
              <p>长按下载图片-打开微信扫一扫添加</p>
            </div>
          </>
        );
      default:
        return (
          <>
            <div className={styles.reviewSuccess}>
              <p>保险合作系统已下架</p>
              <p>详情请扫二维码咨询</p>
            </div>
            <div className={styles.qrcodeitem}>
              <p>
                添加<span>@保险小助手小贝,</span>咨询详情
              </p>
              <img
                src="https://file.ancda.com/public/file/app/qrcode/qrcode-insurance.png"
                alt=""
              />
              <p>长按下载图片-打开微信扫一扫添加</p>
            </div>
          </>
        );
    }
  };
  return (
    <div className={container}>
      <Head>
        <title>开通保险系统</title>
      </Head>
      <OverLayLoad
        visible={loadingOverlay}
        style={{
          backgroundColor: '#fff',
        }}
      />
      {status === 4 ? (
        <>
          <div className={styles.headbanner}>
            <Swiper
              style={{ height: '40px' }}
              autoplay={3000}
              vertical
              indicator={false}
            >
              {mockData.map((item) => {
                return (
                  <Swiper.Item key={item.id}>
                    <div className={styles.horseman}>
                      <div>{item.name}</div>
                    </div>
                  </Swiper.Item>
                );
              })}
            </Swiper>
          </div>
          <div className={styles.formBox}>
            <div className={styles.costmformItem}>
              <p className={styles.costmlable}>姓名</p>
              <div className={styles.border}>
                <Input
                  value={schoolInfo.name}
                  onChange={(name) => setSchoolInfo({ ...schoolInfo, name })}
                />
              </div>
            </div>
            <div className={styles.costmformItem}>
              <p className={styles.costmlable}>手机号码</p>
              <div className={styles.border}>
                <Input
                  value={schoolInfo.tel}
                  type="tel"
                  onChange={(tel) => setSchoolInfo({ ...schoolInfo, tel })}
                />
              </div>
            </div>
            <div className={styles.costmformItem}>
              <p className={styles.costmlable}>幼儿园名称</p>
              <div className={styles.border}>
                <Input value={schoolInfo.schoolName} disabled />
              </div>
            </div>
          </div>
        </>
      ) : (
        <div>{renderHtml()}</div>
      )}
      <div className={styles.footerbanner}>
        <Swiper>
          <Swiper.Item>
            <img
              className={styles.bannerImg}
              src="/images/insurance/banner1.png"
              alt=""
            />
          </Swiper.Item>
          <Swiper.Item>
            <img
              className={styles.bannerImg}
              src="/images/insurance/banner2.png"
              alt=""
            />
          </Swiper.Item>
        </Swiper>
      </div>
      {status === 4 && (
        <div className={styles.butfixed}>
          <Button
            shape="rounded"
            block
            color="primary"
            loading={butLoading}
            onClick={submitInfo}
          >
            提交
          </Button>
        </div>
      )}
      {status === 2 && (
        <div className={styles.butfixed}>
          <Button
            shape="rounded"
            block
            color="primary"
            loading={butLoading}
            onClick={submitInfoAlign}
          >
            再次申请
          </Button>
        </div>
      )}
    </div>
  );
};
export default Index;
