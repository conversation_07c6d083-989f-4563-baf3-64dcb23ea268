.container {
  background: url('/images/insurance/homebg.png') no-repeat 0 center;
  background-size: 100% 100%;
  position: relative;
  min-height: 100vh;
  padding-bottom: 150px;
}
.containerReviewbg {
  background: url('/images/insurance/reviewbg.png') no-repeat 0 center;
  background-size: 100% 100%;
  position: relative;
  min-height: 100vh;
}
.reviewing {
  padding-top: 80px;
}
.reviewing img {
  height: 191px;
  margin: 0 auto;
}
.reviewfliad {
  padding-top: 50px;
}
.reviewfliad img {
  height: 165px;
  margin: 0 auto;
}
.reviewSuccess {
  padding-top: 50px;
}
.reviewSuccess p {
  font-size: 68px;
  text-align: center;
  font-weight: 600;
  color: #fff;
  text-shadow: 1px 1px 50px rgba(0, 0, 0, 0.2), 0 0;
  background-image: -webkit-linear-gradient(bottom, #fdeecc, #fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.reviewSuccess p + p {
  margin-top: 40px;
}
.headbanner {
  padding-top: 18vh;
}
.horseman {
  text-align: center;
}
.horseman div {
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  padding: 8px 50px;
  border-radius: 50px;
}
.qrcodeitem {
  padding: 50px 0;
  width: 90vw;
  background-color: #fff;
  margin: 80px auto 20px;
  text-align: center;
  border-radius: 20px;
}
.qrcodeitem p {
  color: #333;
}
.qrcodeitem p span {
  color: rgba(236, 99, 65, 1);
}
.qrcodeitem img {
  margin: 40px auto 20px;
  width: 350px;
  height: 350px;
}
.formBox {
  width: 90vw;
  padding: 30px 10px;
  margin: 16vh auto 0;
  border-radius: 20px;
  background-color: #fff;
}
.costmformItem {
  padding: 0px 16px;
  margin-bottom: 10px;
}
.costmlable {
  font-size: 24px;
  margin-bottom: 10px;
}
.van-cell {
  padding: 0 10px !important;
}
.border {
  border: 1px solid #eee;
  padding: 10px;
  border-radius: 8px;
}
.footerbanner {
  width: 90vw;
  margin: 0 auto;
  padding-top: 35px;
}
.footerbanner .bannerImg {
  height: 242px;
}
.butfixed {
  width: 100vw;
  position: fixed;
  bottom: 30px;
  padding: 0 30px;
}
