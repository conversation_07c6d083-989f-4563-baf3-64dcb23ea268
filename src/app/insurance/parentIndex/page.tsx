'use client';

import Script from 'next/script';
import React, { useEffect, useState } from 'react';

import { finishTask } from '@/api/common';
import { getInsuranceUrl } from '@/api/insurance';
import OverLayLoad from '@/components/OverLayLoad/Index';
import { showNativeRewardModal } from '@/utils';

const Index = () => {
  const [isOpen, setInOpen] = useState(false);
  const [visible, setVisible] = useState(true);

  useEffect(() => {

    getInsuranceUrl()
      .then((res: any) => {
        if (res.url) {
          setInOpen(true);
          setTimeout(() => {
            window.location.href = res.url;
            window.history.replaceState(null, '', window.location.href);
          }, 500);
        }
      })
      .finally(() => {
        setVisible(false);
      });
    const taskId = 'view_insure';
    finishTask({ taskId }).then((res: any) => {
      if (res.score) {
        showNativeRewardModal(taskId, res.score || 0, 0);
      }
    });

  }, []);

  return (
    <div className="h-screen">
      <OverLayLoad
        visible={visible}
        style={{
          backgroundColor: '#fff',
        }}
      />
      {!isOpen && (
        <img className="h-screen" src="/images/insurance/noOpen.png" alt="" />
      )}
      <Script
        src="//cdn.jsdelivr.net/npm/eruda"
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
      />
    </div>
  );
};
export default Index;
