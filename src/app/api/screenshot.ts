// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import type { NextApiRequest, NextApiResponse } from 'next';
import puppeteer from 'puppeteer';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const { url } = req.query;
  console.log('🚀 ~ file: screenshot.ts:13 ~ url:', typeof url);
  if (typeof url !== 'string') {
    return res.status(400);
  }
  const browser = await puppeteer.launch({
    // executablePath: '/usr/lib64/chromium-browser/chromium-browser',
    headless: 'new',
    devtools: false,
    // userDataDir: './tmp',
    args: [
      '--disable-features=IsolateOrigins',
      '--disable-site-isolation-trials',
      '--autoplay-policy=user-gesture-required',
      '--disable-background-networking',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-breakpad',
      '--disable-client-side-phishing-detection',
      '--disable-component-update',
      '--disable-default-apps',
      '--disable-dev-shm-usage',
      '--disable-domain-reliability',
      '--disable-extensions',
      '--disable-features=AudioServiceOutOfProcess',
      '--disable-hang-monitor',
      '--disable-ipc-flooding-protection',
      '--disable-notifications',
      '--disable-offer-store-unmasked-wallet-cards',
      '--disable-popup-blocking',
      '--disable-print-preview',
      '--disable-prompt-on-repost',
      '--disable-renderer-backgrounding',
      '--disable-setuid-sandbox',
      '--disable-speech-api',
      '--disable-sync',
      '--hide-scrollbars',
      '--ignore-gpu-blacklist',
      '--metrics-recording-only',
      '--mute-audio',
      '--no-default-browser-check',
      '--no-first-run',
      '--no-pings',
      '--no-sandbox',
      '--no-zygote',
      '--password-store=basic',
      '--use-gl=swiftshader',
      '--use-mock-keychain',
      '--font-render-hinting=none',
    ],
  });
  const page = await browser.newPage();
  // await page.goto(url, { waitUntil: 'networkidle0' });

  const html = `<!DOCTYPE html>
  <html>
  <head>
      <meta charset="utf-8"/>
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
      <meta name="renderer" content="webkit"/>
      <meta
              name="viewport"
              content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
      />
      <style type="text/css">
          html,
          body {
              margin: 0;
              padding: 0;
              font: 16px arial, sans-serif;
              overflow-x: hidden;
              width: 100%;
              background-color: #fff;
          }
  
          h1, h2, h3, h4, h5, p {
              margin: 0
          }
  
          .pagecontent {
              position: relative;
              overflow: hidden;
          }
  
          .headerFont {
              font-size: 16px;
              padding: 0 20px
          }
  
          .rowTitle {
              font-size: 18px;
              text-align: left;
              padding: 0 20px 5px;
          }
  
          .rowSubTitle {
              font-size: 14px;
              text-align: left;
              padding: 0 20px 5px;
              text-indent: 2em;
              line-height: 2em;
          }
  
          table, th, td {
              border: 1px solid #333;
              border-collapse: collapse;
              font-size: 16px;
              padding: 10px 0;
          }
  
          .underline {
              text-decoration: underline;
              padding: 0px 3px;
          }
  
          .rowSubChileTitle {
              text-indent: 4em;
          }
  
          .footer {
              padding: 20px 0;
              overflow: hidden;
          }
  
          .footer .footerRepresent {
              float: left;
              width: 50%;
              padding-left: 40px;
              padding-right: 20px;
              box-sizing: border-box;
          }
  
          .footer .footerParty {
              position: relative;
              float: right;
              width: 50%;
              padding-right: 40px;
              box-sizing: border-box;
          }
  
          .stamp {
              position: absolute;
              top: 50px;
              left: 50px;
              width: 150px;
              height: 150px;
          }
  
          .stamp1 {
              position: absolute;
              top: 100px;
              right: 0px;
              width: 75px;
              height: 150px;
          }
  
          .stamp2 {
              position: absolute;
              top: 100px;
              right: 0px;
              width: 75px;
              height: 150px;
          }
  
          .party {
              font-size: 16px;
              text-align: left;
              margin: 0 0 10px;
              font-weight: normal;
          }
  
          .represent {
              font-weight: normal;
              font-size: 16px;
              text-align: left;
              margin: 0 0 10px;
          }
  
          .signname {
              width: 100px;
              height: auto;
              vertical-align: middle;
          }
      </style>
  </head>
  <body>
  <div class="pagecontent">
      <img src="https://unicorn-media.ancda.com/production/app/contract/seal-left.png" class="stamp1" alt="">
      <h1 style="font-size: 26px;text-align: center;margin:20px auto">“掌心宝贝”系统平台服务合同</h1>
      <h3 class="headerFont" style="text-align: right;">合同编号:<span class="underline">X202309044444444440010</span></h3>
      <h3 class="headerFont" style="text-align: left;margin-bottom:8px;">甲方:
          <span class="underline">
                  六安市裕安区京狮·锦绣华府幼儿园
          </span>
      </h3>
      <h3 class="headerFont" style="text-align: left;margin-bottom:10px;">乙方:深圳市云领天下科技有限公司(以下简称“乙方”)</h3>
      <p class="headerFont" style="text-indent: 2em;line-height: 2em;">
          乙方系深圳市云领天下科技有限公司“掌心宝贝”家园互动系统产品之平台服务商；根据《中华人民共和国民典法》，甲、乙双方本着平等互利的原则，双方就甲方安装使用“掌心宝贝”事宜，双方经协商一致，达成下述合同条款，共同遵守执行。具体内容如下：</p>
      <h2 class="rowTitle">第一条 总则</h2>
      <p class="rowSubTitle">1、合同生效:本合同经甲乙双方签字盖章之日起生效。</p>
      <p class="rowSubTitle">2、合同修改:除非由双方授权代表签字并经单位加盖公章予以确认的书面修改文件，否则合同不得修改。符合这一程序的修改将构成合同的一部分，并将与合同具有同等的法律效力。</p>
      <p class="rowSubTitle">3、合同的含义和解释，以及各方之间的关系均受中华人民共和国法律约束。</p>
      <h2 class="rowTitle">第二条 服务项目、价格和施工</h2>
      <p class="rowSubTitle">1、下表中第<span class="underline">A</span>项平台使用费，结算时按实际使用摄像头节点计，由甲方支付至乙方指定账户。</p>
      <div style="padding:0 2vw 10px">
          <table style="width: 100%">
              <tr>
                  <th width="10%">序号</th>
                  <th width="18%">服务项目</th>
                  <th width="18%">说明</th>
                  <th width="18%">价格</th>
                  <th width="18%">使用时长</th>
                  <th width="18%">合计金额</th>
              </tr>
              <tbody>
              <tr style="text-align:center;">
                  <td width="10%">A</td>
                  <td width="18%">阳光宝贝</td>
                  <td width="18%">在线视频监控服务</td>
                  <td width="18%">1,000元/8路NVR</td>
                  <td width="18%">3年</td>
                  <td width="18%">3000.00元</td>
              </tr>
              </tbody>
          </table>
      </div>
      <p class="rowSubTitle">“阳光宝贝”监控接入说明：甲方后续新签订一家幼儿园开通“阳光宝贝”在线视频业务，即需支付
              <span class="underline">3,000</span>元/3年/8路NVR、
              <span class="underline">2,000</span>元/1年/16路NVR、
              <span class="underline">3,000</span>元/1年/32路NVR，
              <span class="underline">6,000</span>元/1年/32路NVR，
          设备接入开启后一年内使用，若要新增额外摄像头节点，需支付升级补差价费用，升级的套餐年限根据现有套餐的剩余年限计算，年限不足一年则按一年计算补差费用。次年同期支付年服务费后可继续使用该功能。阳光宝贝年服务费订单完成支付即视为该视频服务功能已开通，不受理退款。年服务到期后未及时续费，“阳光宝贝”视频功能暂停，续费后即可恢复使用。
      </p>
      <p class="rowSubTitle">3、考勤卡价格，结算时按实际使用幼儿人数计，按照选择的付费方式支付至乙方指定账户。</p>
      <p class="rowSubTitle">A类型和单价</p>
      <p class="rowSubTitle rowSubChileTitle">标准卡<span class="underline">10</span>元/张、心形卡<span class="underline">20</span>元/张
      </p>
      <p class="rowSubTitle">4、本合同期限为<span class="underline">3</span>年，自本合同签订之日起计算。</p>
      <p class="rowSubTitle">5、当甲方支付了<span class="underline">3,000</span>元费用后，乙方开始提供服务。
      </p>
      <p class="rowSubTitle">6、甲方设备接入订单生效，即视频包年业务开始生效。</p>
  </div>
  <div class="pagecontent">
      <div style="position: relative;overflow: hidden;padding: 20px 0">
          <img src="https://unicorn-media.ancda.com/production/app/contract/seal-right.png" class="stamp2" alt="">
          <h2 class="rowTitle">第三条 甲方的权利和义务</h2>
          <p class="rowSubTitle">1、甲方必须指定相关负责人配合乙方售后技术支持与跟踪服务工作的开展。</p>
          <h2 class="rowTitle">第四条 乙方的权利和义务</h2>
          <p class="rowSubTitle">1、乙方同意按本合同约定提供相关设备并进行安装及调试，并做好维护工作。</p>
          <p class="rowSubTitle">2、甲方若对乙方的服务，或对使用“掌心宝贝”有任何疑问和意见，均可以向乙方进行反馈或投诉，乙方有义务解决甲方的疑问和意见。</p>
          <h2 class="rowTitle">第五条 合同变更和解除</h2>
          <p class="rowSubTitle">1、保障甲方在正常产品使用过程中，甲方不能提前解约，如提前解约，乙方有权不受理阳光宝贝服务费订单退款。</p>
          <p class="rowSubTitle">2、甲方如因自身经营情况希望提前解除合作协议，须向乙方提出书面申请，经协商同意后，并妥当处理了相关费用后，协议方可解除。</p>
          <h2 class="rowTitle">第六条 争议解决</h2>
          <p class="rowSubTitle">
              甲、乙双方对本合同书的条款在理解上发生争议时，应本着友好协商的态度修改、补充有关条款；双方在履行合同时如发生争议，亦应友好协商解决，如协商不成，将在乙方所在地法院提起诉讼。</p>
          <h2 class="rowTitle">第七条 其他</h2>
          <p class="rowSubTitle">1、本协议书一式二份，甲方执一份，乙方执一份；本协议自双方签字盖章后生效。</p>
          <p class="rowSubTitle">2、需在合同期满后继续合作的，甲方应于本合同届满前与乙方重新签订合同。</p>
      </div>
      <div class="footer" style="position:relative;overflow: hidden;">
          <div class="footerRepresent">
              <h3 class="represent">甲方:
                  <span>
                          六安市裕安区京狮·锦绣华府幼儿园
                  </span>
              </h3>
              <h3 class="represent">地址:
                  <span>
                          六安市裕安区解放南路华邦·锦绣华府小区
                  </span>
              </h3>
              <h3 class="represent">代表:
                      <img src="https://mediatx.ancda.com/prod/signature/2023-09-19/XKYL0DKN.png" class="signname" alt="">
              </h3>
              <h3 class="represent">电话:
                  <span>
                          15256456362
                  </span>
              </h3>
              <h3 class="represent">日期:<span>2023年09月19日</span></h3>
          </div>
          <div class="footerParty">
              <img src="https://unicorn-media.ancda.com/production/app/contract/seal-full.png" class="stamp" alt="">
              <h3 class="party"><span>乙方</span>：深圳市云领天下科技有限公司(盖章)<span></span></h3>
              <h3 class="party"><span>地址</span>：广东省深圳市南山区高新中区科技中二路软件园二期13栋302室掌心宝贝<span></span></h3>
              <h3 class="party"><span>代表</span>：戴振光 <span></span></h3>
              <h3 class="party">收款银行：中国建设银行深圳金沙支行<span></span></h3>
              <h3 class="party"><span>帐号</span>：44201596300052518522<span></span></h3>
              <h3 class="party"><span>电话</span>：4008879121<span></span></h3>
              <h3 class="party"><span>日期</span>：<span>2023年09月19日</span></h3>
          </div>
      </div>
  </div>
  </body>
  </html>`;
  await page.setContent(html, { waitUntil: 'networkidle0' });

  await page.emulateMediaType('screen');
  // await timeout(5000);
  const screenshot = await page.screenshot({
    path: 'screenshot.jpg', // TODO delete local image
    fullPage: true,
  });
  // console.log('🚀 ~ file: screenshot.ts:25 ~ screenshot:', screenshot);
  await browser.close();
  return res
    .status(200)
    .setHeader('content-type', 'image/png')
    .send(screenshot);
}
