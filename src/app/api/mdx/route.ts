import fs from 'node:fs';
import path from 'node:path';
import { NextResponse } from 'next/server';

import matter from 'gray-matter';
import remarkHtml from 'remark-html';
import remarkParse from 'remark-parse';
import { unified } from 'unified';

const postsDirectory = path.join(process.cwd(), 'posts');

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const filename = searchParams.get('filename');
  console.log('🚀 ~ filename:', filename);
  if (!filename) {
    return NextResponse.json(
      { error: 'Filename is required' },
      { status: 400 },
    );
  }

  const filePath = path.join(postsDirectory, `${filename}.md`);

  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    // Parse the frontmatter metadata
    const { data, content } = matter(fileContent);

    // Convert Markdown to HTML
    const processedContent = await unified()
      .use(remarkParse)
      .use(remarkHtml, { sanitize: false })
      .process(content);

    const contentHtml = processedContent.toString();
    return NextResponse.json({ contentHtml, filename, data });
  } catch (error) {
    return NextResponse.json({ error: 'File not found' }, { status: 404 });
  }
}