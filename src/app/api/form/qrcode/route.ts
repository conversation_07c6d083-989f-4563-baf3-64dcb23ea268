import path from 'node:path';

import Canvas from 'canvas';
import Kon<PERSON> from 'konva';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import QRCode from 'qrcode';

import { isPalmBaby } from '@/lib/utils';

export async function GET(request: NextRequest) {
  const loadImg = (url: string) =>
    new Promise((resolve, _) => {
      const img: any = new Canvas.Image();
      img.onload = () => {
        resolve(img);
      };
      img.onerror = () => {
        console.log('图片出现异常');
        resolve({});
      };
      // img.crossOrigin = 'Anonymous'
      img.src = url;
    });

  const width = 600;
  const height = 600;
  const { searchParams, hostname } = new URL(request.url);
  const instanceId = searchParams.get('instanceId');
  const instanceName = searchParams.get('instanceName');

  // 加载字体
  const fontFile = (name: string) => {
    return path.join(__dirname, '../../../../../../src/assets/fonts/', name);
  };
  Canvas.registerFont(fontFile('DingTalkJinBuTi-Regular.ttf'), {
    family: 'DingTalk JinBuTi',
  });
  if (!instanceId || !instanceName) {
    return new NextResponse('参数错误', {
      status: 400,
    });
  }

  const qrCodeJson = {
    message: `请使用${isPalmBaby(hostname) ? '掌心宝贝' : '掌心智校'}APP扫码`,
    fid: '20002',
    router: `rn://FormStack?initialRoute=SubmitScreen&instanceId=${instanceId}`,
  };

  // 生成二维码
  try {
    const opts = {
      errorCorrectionLevel: 'H',
      type: 'image/png',
      quality: 1,
      margin: 2,
      color: {
        dark: '#26A259',
        light: '#FFFFFF',
      },
    };
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const result = await QRCode.toDataURL(JSON.stringify(qrCodeJson), opts);
    if (!result) {
      return;
    }
    // 初始化画布
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const stage = new Konva.Stage({ width, height });
    // add canvas element
    const layer = new Konva.Layer();
    stage.add(layer);

    // 背景图 start
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const bgImage = new Konva.Image({
      width,
      height,
    });
    layer.add(bgImage);
    const imagePath = path.join(
      __dirname,
      '../../../../../../src/assets/images/invite/qrcode-bg.png',
    );

    const bgImageObj = await loadImg(imagePath);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    bgImage.image(bgImageObj);

    // 背景图 end

    // 二维码 start
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const qrCodeGroup = new Konva.Group({
      x: 150,
      y: 130,
      width: 300,
      height: 300,
    });
    const qrCodeRect = new Konva.Rect({
      x: 0,
      y: 0,
      width: 300,
      height: 300,
      fill: '#ffe59a',
      shadowColor: 'black',
      shadowBlur: 50,
      shadowOffset: { x: 0, y: 0 },
      shadowOpacity: 0.1,
    });
    qrCodeGroup.add(qrCodeRect);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const qrcodeImg = new Konva.Image({
      x: 0,
      y: 0,
      width: 300,
      height: 300,
    });
    qrCodeGroup.add(qrcodeImg);
    layer.add(qrCodeGroup);

    const imageObj = await loadImg(result);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    qrcodeImg.image(imageObj);
    // 二维码 end

    // 标题 start
    const titleGroup = new Konva.Group({
      x: 140,
      y: 40,
      width: 320,
      height: 100,
    });
    layer.add(titleGroup);

    const line = new Konva.Rect({
      x: 0,
      y: 30,
      width: 320,
      height: 20,
      fill: '#ffe59a',
      cornerRadius: 10,
    });
    titleGroup.add(line);

    const simpleText = new Konva.Text({
      x: titleGroup.width() / 2,
      y: 0,
      text: `邀请您填写`,
      fontSize: 44,
      fontFamily: 'DingTalk JinBuTi',
      fill: '#cb6f08',
    });

    simpleText.offsetX(simpleText.width() / 2);
    titleGroup.add(simpleText);
    // 标题 end

    // 班级名称 start
    const classText = new Konva.Text({
      x: stage.width() / 2,
      y: 460,
      width: instanceName.length > 18 ? 500 : undefined,
      height: 64,
      text: instanceName,
      fontSize: 32,
      fontFamily: 'DingTalk JinBuTi',
      fill: '#333333',
    });

    classText.offsetX(classText.width() / 2);
    layer.add(classText);

    // 班级名称 start
    const tipText = new Konva.Text({
      x: stage.width() / 2,
      y: 550,
      text: `请使用${
        isPalmBaby(hostname) ? '掌心宝贝' : '掌心智校'
      }APP扫码填写`,
      fontSize: 24,
      // fontFamily: 'DingTalk JinBuTi',
      fill: '#999',
    });

    tipText.offsetX(tipText.width() / 2);
    layer.add(tipText);
    // 班级名称 end

    // 生成最终图片
    const img = stage.toDataURL({
      mimeType: 'image/png',
      quality: 1,
      pixelRatio: 2,
    });

    return NextResponse.json({ data: img });
    // 下面的返回方式直接显示图片
    // return new Response(Buffer.from(img.split(',')[1], 'base64'), {
    //   status: 200,
    //   headers: {
    //     'Content-Type': 'image/png',
    //     'Content-Disposition': 'inline',
    //   },
    // });
  } catch (err) {
    console.error(err);
  }
}
