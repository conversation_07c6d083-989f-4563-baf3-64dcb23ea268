import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import path from 'path';
import puppeteer from 'puppeteer';

import { updatedFile } from '@/utils/updatedFile';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get('url');
  if (typeof url !== 'string') {
    return new NextResponse('参数错误', {
      status: 400,
    });
  }
  const browser = await puppeteer.launch({
    // executablePath: '/usr/lib64/chromium-browser/chromium-browser',
    executablePath: '/usr/bin/google-chrome-stable',
    headless: 'new',
    devtools: false,
    // userDataDir: './tmp',
    args: [
      '--disable-features=IsolateOrigins',
      '--disable-site-isolation-trials',
      '--autoplay-policy=user-gesture-required',
      '--disable-background-networking',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-breakpad',
      '--disable-client-side-phishing-detection',
      '--disable-component-update',
      '--disable-default-apps',
      '--disable-dev-shm-usage',
      '--disable-domain-reliability',
      '--disable-extensions',
      '--disable-features=AudioServiceOutOfProcess',
      '--disable-hang-monitor',
      '--disable-ipc-flooding-protection',
      '--disable-notifications',
      '--disable-offer-store-unmasked-wallet-cards',
      '--disable-popup-blocking',
      '--disable-print-preview',
      '--disable-prompt-on-repost',
      '--disable-renderer-backgrounding',
      '--disable-setuid-sandbox',
      '--disable-speech-api',
      '--disable-sync',
      '--hide-scrollbars',
      '--ignore-gpu-blacklist',
      '--metrics-recording-only',
      '--mute-audio',
      '--no-default-browser-check',
      '--no-first-run',
      '--no-pings',
      '--no-sandbox',
      '--no-zygote',
      '--password-store=basic',
      '--use-gl=swiftshader',
      '--use-mock-keychain',
      '--font-render-hinting=none',
    ],
  });
  const page = await browser.newPage();
  await page.goto(url, { waitUntil: 'networkidle0' });

  await page.emulateMediaType('screen');

  const pdfPath = path.join(__dirname, 'result.pdf');

  await page.pdf({
    path: pdfPath,
    margin: { top: '50px', right: '50px', bottom: '50px', left: '50px' },
    printBackground: true,
    format: 'A4',
    width: 2480,
    height: 3508,
  });
  await browser.close();
  const date = new Date();
  const fileKey = `prod/form/pdf/${`${date.getFullYear()}-${
    date.getMonth() + 1
  }-${date.getDate()}`}/`;

  const fileName = `${`${date.getFullYear()}-${
    date.getMonth() + 1
  }-${date.getDate()}`}-${new Date().getTime()}.pdf`;
  const pdfUrl = await updatedFile(fileKey + fileName, pdfPath);
  return NextResponse.json({
    url: pdfUrl,
  });

  // const stat = fs.statSync(pdfPath);
  // res.setHeader('Content-Length', stat.size);

  // return new Response(pdf, {
  //   status: 200,
  //   headers: {
  //     'Content-Type': 'application/pdf',
  //     'Content-Disposition': 'inline',
  //   },
  // });
}
