import path from 'node:path';

import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

import { updatedFile } from '@/utils/updatedFile';

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get('url');
  const reportKey = searchParams.get('reportKey');
  if (typeof url !== 'string' || typeof reportKey !== 'string') {
    return new NextResponse('参数错误', {
      status: 400,
    });
  }
  const browser = await puppeteer.launch({
    executablePath: '/usr/bin/google-chrome-stable',
    headless: 'new',
    devtools: false,
    args: [
      '--disable-features=IsolateOrigins',
      '--disable-site-isolation-trials',
      '--autoplay-policy=user-gesture-required',
      '--disable-background-networking',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-breakpad',
      '--disable-client-side-phishing-detection',
      '--disable-component-update',
      '--disable-default-apps',
      '--disable-dev-shm-usage',
      '--disable-domain-reliability',
      '--disable-extensions',
      '--disable-features=AudioServiceOutOfProcess',
      '--disable-hang-monitor',
      '--disable-ipc-flooding-protection',
      '--disable-notifications',
      '--disable-offer-store-unmasked-wallet-cards',
      '--disable-popup-blocking',
      '--disable-print-preview',
      '--disable-prompt-on-repost',
      '--disable-renderer-backgrounding',
      '--disable-setuid-sandbox',
      '--disable-speech-api',
      '--disable-sync',
      '--hide-scrollbars',
      '--ignore-gpu-blacklist',
      '--metrics-recording-only',
      '--mute-audio',
      '--no-default-browser-check',
      '--no-first-run',
      '--no-pings',
      '--no-sandbox',
      '--no-zygote',
      '--password-store=basic',
      '--use-gl=swiftshader',
      '--use-mock-keychain',
      '--font-render-hinting=medium',
      '--enable-font-antialiasing',
      '--force-color-profile=srgb',
      '--disable-font-subpixel-positioning',
      '--lang=zh-CN',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
    ],
  });
  const page = await browser.newPage();
  await page.evaluateOnNewDocument(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      * {
        font-family: 
          'Alibaba PuHuiTi 3.0',
          'Noto Sans CJK SC',
          'WenQuanYi Micro Hei',
          'WenQuanYi Zen Hei',
          'PingFang SC',
          'STHeiti',
          'Heiti SC', 
          'Songti SC',
          'Microsoft YaHei', 
          'SimHei',
          'Arial Unicode MS',
          sans-serif !important;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }
      
      body, html {
        font-family: 
          'Alibaba PuHuiTi 3.0',
          'Noto Sans CJK SC',
          'WenQuanYi Micro Hei',
          'WenQuanYi Zen Hei',
          'PingFang SC',
          'STHeiti',
          'Heiti SC', 
          'Songti SC',
          'Microsoft YaHei', 
          'SimHei',
          'Arial Unicode MS',
          sans-serif !important;
      }
      
      /* 确保所有文本元素都应用字体 */
      p, div, span, h1, h2, h3, h4, h5, h6, td, th, li, a, label, input, textarea, select, strong, em, b, i {
        font-family: inherit !important;
      }
      
      /* 强制覆盖任何自定义字体样式 */
      [style*="font-family"],
      .font-serif,
      .serif,
      .font-sans,
      .sans-serif {
        font-family: 'Alibaba PuHuiTi 3.0', '阿里巴巴普惠体 3.0', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif !important;
      }
    `;
    document.head.appendChild(style);
  });

  await page.setExtraHTTPHeaders({
    Authorization: reportKey,
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Charset': 'utf-8',
  });
  page.setDefaultNavigationTimeout(30000);
  await Promise.all([
    page.goto(url, { waitUntil: ['load', 'networkidle0'] }),
    delay(2000),
    page.waitForSelector('[data-loaded="false"]'),
  ]);

  await page.evaluate(() => {
    const allElements = document.querySelectorAll('*');
    for (const element of allElements) {
      if (element.textContent && /[\u4e00-\u9fff]/.test(element.textContent)) {
        (element as HTMLElement).style.fontFamily =
          "'Alibaba PuHuiTi 3.0', '阿里巴巴普惠体 3.0', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif";
      }
    }
    document.body.offsetHeight;
  });

  await delay(2000);

  await page.emulateMediaType('screen');
  const pdfPath = path.join(__dirname, 'record.pdf');
  await page.pdf({
    path: pdfPath,
    margin: { top: '25px', right: '50px', bottom: '50px', left: '50px' },
    printBackground: true,
    format: 'A4',
    width: 2480,
    height: 3508,
  });
  await browser.close();
  const date = new Date();
  const fileKey = `prod/record/pdf/${`${date.getFullYear()}-${
    date.getMonth() + 1
  }-${date.getDate()}`}/`;

  const fileName = `${`${date.getFullYear()}-${
    date.getMonth() + 1
  }-${date.getDate()}`}-${new Date().getTime()}.pdf`;
  const pdfUrl = await updatedFile(fileKey + fileName, pdfPath);
  return NextResponse.json({
    url: pdfUrl,
  });
}
