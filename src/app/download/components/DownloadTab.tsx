'use client';

import { Button, CapsuleTabs } from 'antd-mobile';
import Image from 'next/image';

interface DownloadTabsProps {
  data: any[];
  isIOS: boolean;
}

export default function DownloadTabs({ data, isIOS }: DownloadTabsProps) {
  return (
    <CapsuleTabs defaultActiveKey={isIOS ? 'iOS' : 'Android'} className="mt-4">
      {data.map((category) => (
        <CapsuleTabs.Tab key={category.platform} title={category.platform}>
          {category.data.map((item, idx) => (
            <div key={`type${idx}`} className="w-[540px]">
              <h2 className="mb-2 mt-2 text-base text-gray-900">{item.name}</h2>
              <div className="mb-3 flex justify-center">
                <Image src={item.qrcode} alt="" width={120} height={120} />
              </div>
              <div className="mb-5 flex justify-center">
                <Button
                  shape="rounded"
                  size="small"
                  color="primary"
                  onClick={() => {
                    window.open(item.downloadUrl, '_blank');
                  }}
                  className="mx-auto"
                >
                  直接下载安装
                </Button>
                {category.platform === 'Android' && !!item.downloadUrl32 && (
                  <Button
                    shape="rounded"
                    size="small"
                    onClick={() => {
                      window.open(item.downloadUrl32, '_blank');
                    }}
                    className="mx-auto !ml-3"
                  >
                    32位APP下载
                  </Button>
                )}
              </div>
            </div>
          ))}
        </CapsuleTabs.Tab>
      ))}
    </CapsuleTabs>
  );
}