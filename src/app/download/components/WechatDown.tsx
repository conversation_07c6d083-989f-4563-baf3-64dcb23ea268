'use client';

import { Button, Radio, Space } from 'antd-mobile';
import type { RadioValue } from 'antd-mobile/es/components/radio';
import { useState } from 'react';

interface AppData {
  name: string;
  downloadUrl: string;
  qrcode: string;
  downloadUrl32?: string;
}

interface PlatformData {
  platform: string;
  data: AppData[];
}

interface DownloadTabsProps {
  data: PlatformData[];
  babyData: PlatformData[];
  isIOS: boolean;
}

export default function DownloadTabs({
  data,
  babyData,
  isIOS,
}: DownloadTabsProps) {
  const [userType, setUserType] = useState<string>(''); // 'parent' | 'teacher'
  const [appType, setAppType] = useState<string>(''); // 'baby' | 'smart'

  const handleUserTypeChange = (val: RadioValue) => {
    setUserType(val as string);
  };

  const handleAppTypeChange = (val: RadioValue) => {
    setAppType(val as string);
  };

  const handleDownload = () => {
    if (!userType || !appType) return;

    let downloadUrl = '';

    if (appType === 'baby') {
      if (userType === 'parent') {
        downloadUrl = isIOS
          ? babyData[1]?.data[0]?.downloadUrl || ''
          : babyData[0]?.data[0]?.downloadUrl || '';
      } else {
        downloadUrl = isIOS
          ? babyData[1]?.data[1]?.downloadUrl || ''
          : babyData[0]?.data[1]?.downloadUrl || '';
      }
    } else if (userType === 'parent') {
      downloadUrl = isIOS
        ? data[1]?.data[0]?.downloadUrl || ''
        : data[0]?.data[0]?.downloadUrl || '';
    } else {
      downloadUrl = isIOS
        ? data[1]?.data[1]?.downloadUrl || ''
        : data[0]?.data[1]?.downloadUrl || '';
    }

    // 打印选择信息和下载地址
    console.log('用户选择信息：', {
      userType: userType === 'parent' ? '家长' : '老师',
      appType: appType === 'baby' ? '掌心宝贝' : '掌心智校',
      platform: isIOS ? 'iOS' : 'Android',
      downloadUrl,
    });

    if (downloadUrl) {
      console.log('即将打开下载链接：', downloadUrl);
      window.open(downloadUrl, '_blank');
    } else {
      console.error('下载链接为空，无法下载');
    }
  };

  return (
    <div className="layout flex min-h-screen flex-col items-center justify-center bg-gradient-to-b  text-center text-black">
      <div className="mx-4 flex w-full max-w-sm flex-col items-center rounded-3xl bg-gradient-to-b p-6">
        <div className="mb-8 text-left text-base font-medium leading-relaxed text-black">
          您好，为更助您更准确地定制下载的APP，请先完成以下2个问答：
        </div>

        <div className="mb-6 w-full">
          <div className="mb-4 text-left text-sm font-medium text-black">
            *1. 您是家长还是老师？
          </div>
          <div className="flex w-full justify-start">
            <Radio.Group value={userType} onChange={handleUserTypeChange}>
              <Space
                direction="vertical"
                className="w-full"
                style={{ alignItems: 'flex-start' }}
              >
                <div className="flex w-full items-start justify-start">
                  <Radio
                    value="parent"
                    className="[&_.adm-radio-icon-checked]:!border-black [&_.adm-radio-icon-checked]:!bg-black [&_.adm-radio-icon-checked_svg]:!fill-cyan-400 [&_.adm-radio-icon]:!border-black"
                  />
                  <span
                    className="ml-3 cursor-pointer text-sm text-black"
                    onClick={() => setUserType('parent')}
                  >
                    我是家长
                  </span>
                </div>
                <div className="flex w-full items-start justify-start">
                  <Radio
                    value="teacher"
                    className="[&_.adm-radio-icon-checked]:!border-black [&_.adm-radio-icon-checked]:!bg-black [&_.adm-radio-icon-checked_svg]:!fill-cyan-400 [&_.adm-radio-icon]:!border-black"
                  />
                  <span
                    className="ml-3 cursor-pointer text-sm text-black"
                    onClick={() => setUserType('teacher')}
                  >
                    我是老师
                  </span>
                </div>
              </Space>
            </Radio.Group>
          </div>
        </div>

        <div className="mb-8 w-full">
          <div className="mb-2 text-left text-sm font-medium text-black">
            *2. 您孩子所在学校使用的APP叫什么？
          </div>
          <div className="mb-4 text-left text-xs leading-relaxed text-black/80">
            (若您不确定，可以向学校老师询问)
          </div>
          <div className="flex w-full justify-start">
            <Radio.Group value={appType} onChange={handleAppTypeChange}>
              <Space
                direction="vertical"
                className="w-full"
                style={{ alignItems: 'flex-start' }}
              >
                <div className="flex w-full items-start justify-start">
                  <Radio
                    value="baby"
                    className="[&_.adm-radio-icon-checked]:!border-white [&_.adm-radio-icon-checked]:!bg-white [&_.adm-radio-icon-checked_svg]:!fill-cyan-400 [&_.adm-radio-icon]:!border-black"
                  />
                  <span
                    className="ml-3 cursor-pointer text-sm text-black"
                    onClick={() => setAppType('baby')}
                  >
                    掌心宝贝
                  </span>
                </div>
                <div className="flex w-full items-start justify-start">
                  <Radio
                    value="smart"
                    className="[&_.adm-radio-icon-checked]:!border-white [&_.adm-radio-icon-checked]:!bg-white [&_.adm-radio-icon-checked_svg]:!fill-cyan-400 [&_.adm-radio-icon]:!border-black"
                  />
                  <span
                    className="ml-3 cursor-pointer text-sm text-black"
                    onClick={() => setAppType('smart')}
                  >
                    掌心智校
                  </span>
                </div>
              </Space>
            </Radio.Group>
          </div>
        </div>

        <Button
          shape="rounded"
          size="large"
          onClick={handleDownload}
          disabled={!userType || !appType}
          className={`w-full ${
            userType && appType
              ? '!border-none !bg-[#4E78FF] !text-white'
              : '!border-none !bg-[#4E78FF] !text-white'
          }`}
        >
          去下载
        </Button>
      </div>
    </div>
  );
}
