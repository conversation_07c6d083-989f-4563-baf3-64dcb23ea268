'use client';

import { getProjectDetail } from '@/api/pbl';
import { Tabs } from 'antd-mobile';
import { atom } from 'jotai';
import { useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';
import { FaRegClock } from 'react-icons/fa';
import { Editor } from './components/Editor';
import { useAtom } from 'jotai';
import Material from './components/Material';
import RecordByDay from './components/RecordByDay';
import { projectDataAtom } from '@/store/pbl';
import { useCommonStore } from '@/store/useCommonStore';

export const markdownAtom = atom<string>('');

export default function App() {
  const setAuthorization = useCommonStore((state) => state.setAuthorization);

  const searchParams = useSearchParams();
  const projectId = searchParams?.get('ProjectId') || '';
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    setAuthorization(authorization);
  }
  const [projectDetail, setProjectDetail] = useState<any>(null);
  const [activeIndex, setActiveIndex] = useQueryState('activeIndex');
  const [isLoading, setIsLoading] = useState(true);
  const [_, setProjectData] = useAtom(projectDataAtom);

  useEffect(() => {
    if (projectId) {
      setIsLoading(true);
      getProjectDetail(projectId)
        .then((res: any) => {
          setProjectDetail(res);
          setProjectData(res);
          if (typeof document !== 'undefined') {
            document.title = res.projectName;
          }
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [projectId]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin" />
        <p className="mt-4">加载中...</p>
      </div>
    );
  }

  return (
    <div className=" relative h-full">
      <div className="">
        <div className="bg-white p-4 pb-2">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h1 className="text-xl font-medium text-gray-900">
                {projectDetail?.projectName}
              </h1>
              <div className="flex items-center mt-2 text-sm text-gray-500">
                <FaRegClock className="text-gray-500 mr-2" />
                <span>{projectDetail?.startDate}</span>
                <span className="mx-2">~</span>
                <span>{projectDetail?.endDate}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="sticky top-0 z-10 w-full bg-white">
        <Tabs
          style={{
            '--content-padding': '0',
            backgroundColor: '#FFF'
          }}
          activeLineMode="fixed"
          onChange={(key) => {
            setActiveIndex(key);
          }}
          activeKey={activeIndex}
        >
          <Tabs.Tab title="报告" key="1" />
          <Tabs.Tab title="每日记录" key="2" />
          <Tabs.Tab title="相关素材" key="3" />
        </Tabs>
      </div>
      <div className="flex flex-1 flex-col overflow-scroll ">
        {activeIndex === '1' && (
          <div className="p-4">
            <Editor projectId={projectId} />
          </div>
        )}
        {activeIndex === '2' && <RecordByDay projectId={projectId} />}
        {activeIndex === '3' && <Material projectId={projectId} />}
      </div>
    </div>
  );
}
