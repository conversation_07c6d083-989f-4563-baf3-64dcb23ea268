import React, { useEffect, useState } from 'react';
import { getDailyObservationDetail } from '@/api/pbl';

import Media from '@/app/pbl/record/detail/components/Media';

export default function Record(props: { value: string; readOnly?: boolean }) {
  const { value, ...rest } = props;

  const readOnly = rest.readOnly || false;
  const [data, setData] = useState({
    summary: '',
    mediaList: []
  });

  useEffect(() => {
    if (value) {
      getDailyObservationDetail(value).then((res) => {
        setData(res || {});
      });
    }
  }, [value]);

  return (
    <div className="w-full">
      <div className="text-text-primary mb-4 leading-relaxed">
        {data.summary || ''}
      </div>
      <Media media={data.mediaList || []} />
    </div>
  );
}
