import { createReactBlockSpec } from '@blocknote/react';
import Record from './Record';
import { useAtom } from 'jotai';
import { isEditableAtom } from '@/store/pbl';

export const RecordBlock = createReactBlockSpec(
  {
    type: 'record',
    propSchema: {
      projectId: {
        default: ''
      }
    },
    content: 'none',
    isSelectable: false
    // isFileBlock: false
  },
  {
    render: ({ block, editor }) => {
      const [isEditable] = useAtom(isEditableAtom);
      return (
        <div className="">
          <Record value={block.props.projectId} />
        </div>
      );
    }
  }
);
