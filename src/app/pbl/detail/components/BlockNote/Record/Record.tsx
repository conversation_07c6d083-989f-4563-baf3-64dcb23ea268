import React, { useEffect, useState } from 'react';

import { getDailyObservationList } from '@/api/pbl';
import Media from '@/app/pbl/record/detail/components/Media';

export default function Record(props: { value: string; readOnly?: boolean }) {
  const { value, ...rest } = props;

  const readOnly = rest.readOnly || false;
  const [data, setData] = useState([]);

  useEffect(() => {
    if (value) {
      getDailyObservationList(value).then((res) => {
        setData(res.list || []);
      });
    }
  }, [value]);

  return data.map((item) => (
    <div key={item.summaryId} className="w-full">
      <div className="text-text-primary mb-4 leading-relaxed">
        {item.summary}
      </div>
      <Media media={item.mediaList || []} />
    </div>
  ));
}
