import { defaultProps } from '@blocknote/core';
import { createReactBlockSpec } from '@blocknote/react';

// The Alert block.
export const Custom = createReactBlockSpec(
  {
    type: 'custom',
    propSchema: {},
    content: 'none',
    deletable: false,
    draggable: false, // 禁止拖动
    isSelectable: false,
    readonly: true,
  },
  {
    render: (props) => {
      return (
        <div style={{ padding: '10px', backgroundColor: 'yellow' }}>
          Pretend this is a Custom block, and it contains a link
          <a
            href="https://google.com"
            target="_blank"
            rel="nofollow noreferrer noopener"
          >
            Google
          </a>
        </div>
      );
    },
  },
);
