'use client';

import { Image, Button, Space } from 'antd-mobile';
import { createReactBlockSpec } from '@blocknote/react';
import { useAtom } from 'jotai';
import { isEditableAtom } from '@/store/pbl';
import { useState, useRef, useEffect } from 'react';
import {
  PiTrash,
  PiPlusCircle,
  PiImage,
  PiMicrophone,
  PiTextAa,
  PiPlayCircle
} from '@/components/Icons';
import { Popup, Popover, TextArea } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/popover';
import { useImmer } from 'use-immer';
import { nanoid } from '@/utils';
import { Mic, Square } from 'lucide-react';
import { uploadFile } from '../upload';
import { defaultAvatar } from '@/constant/config';

type MessageType = 'text' | 'audio' | 'video' | 'image';

interface Message {
  id: string;
  type: MessageType;
  name: string;
  avatar: string;
  content: string;
  url?: string;
}

type NoteProps = Array<Message>;

const TranscribeAudio = ({
  content,
  url
}: {
  content: string;
  url?: string;
}) => {
  const [transcribeContent, setTranscribeContent] = useState(content);
  const [isLoading, setIsLoading] = useState(false);

  const transcribeAudio = async () => {
    setIsLoading(true);
    // const data = await transcribeAudio(url);
    setTimeout(() => {
      setTranscribeContent(
        '音频转文字音频转文字音频转文字音频转文字音频转文字音频转文字音频转文字音频转文字音频转文字音频转文字音频转文字'
      );
      setIsLoading(false);
    }, 1000);
  };

  return transcribeContent ? (
    <div className="mt-2">{transcribeContent}</div>
  ) : (
    <div className="mt-2 flex justify-end">
      <Button size="mini" onClick={transcribeAudio} loading={isLoading}>
        <Space>
          <PiTextAa />
          <span>音频转文字</span>
        </Space>
      </Button>
    </div>
  );
};

const ChatItem: React.FC<{
  message: Message;
  onDelete: (id: string) => void;
}> = ({ message, onDelete }) => {
  const [isEditable] = useAtom(isEditableAtom);

  const renderContent = () => {
    switch (message.type) {
      case 'text':
        return (
          <div className="bg-white p-3 rounded-lg shadow-sm w-full text-gray-700">
            {message.content}
          </div>
        );
      case 'audio':
        return (
          <>
            <audio controls className="w-full max-w-xs">
              <source src={message.url} type="audio/mpeg" />
              <track kind="captions" />
            </audio>
            <TranscribeAudio content={message.content} url={message.url} />
          </>
        );
      case 'video':
        return (
          <video controls className="w-full max-w-sm rounded-lg shadow-lg">
            <source src={message.url} type="video/mp4" />
            <track kind="captions" />
          </video>
        );
      case 'image':
        return (
          <Image
            src={message.url}
            alt="Shared image"
            width={'100%'}
            className=""
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex items-start space-x-3 mb-4">
      <Image
        src={message.avatar || ''}
        alt={message.name}
        width={40}
        height={40}
        className="rounded-full"
      />
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <p className="font-semibold text-gray-800">{message.name}</p>
          {isEditable && (
            <button
              onClick={() => onDelete(message.id)}
              className=""
              type="button"
            >
              <PiTrash fontSize={20} />
            </button>
          )}
        </div>
        <div className="mt-1">{renderContent()}</div>
      </div>
    </div>
  );
};

const actions: Action[] = [
  { key: 'text', icon: <PiTextAa size={16} />, text: '文字' },
  { key: 'image', icon: <PiImage size={16} />, text: '图片' },
  { key: 'audio', icon: <PiMicrophone size={16} />, text: '录音' },
  { key: 'video', icon: <PiPlayCircle size={16} />, text: '视频' }
];

const AudioRecorder: React.FC<{
  onRecordingComplete: (blob: Blob) => void;
}> = ({ onRecordingComplete }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<Blob[]>([]);
  const [amplitudeValues, setAmplitudeValues] = useState(Array(20).fill(2));

  useEffect(() => {
    let animationInterval: NodeJS.Timeout;
    if (isRecording) {
      animationInterval = setInterval(() => {
        setAmplitudeValues((prev) => prev.map(() => Math.random() * 40 + 2));
      }, 100);
    }
    return () => clearInterval(animationInterval);
  }, [isRecording]);

  const handleStartRecording = async () => {
    if (navigator.mediaDevices) {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);

      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunks.current.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunks.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setAudioUrl(audioUrl);
        onRecordingComplete(audioBlob);
        audioChunks.current = [];
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
    }
  };

  const handleStopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  return (
    <div className="flex flex-col items-center gap-6 p-6">
      <div className="flex items-center gap-4">
        {!isRecording ? (
          <button
            onClick={handleStartRecording}
            type="submit"
            className="flex items-center gap-2 px-4 py-2 text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Mic className="w-5 h-5" />
            开始录音
          </button>
        ) : (
          <button
            onClick={handleStopRecording}
            type="submit"
            className="flex items-center gap-2 px-4 py-2 text-white bg-red-500 rounded-lg hover:bg-red-600 transition-colors"
          >
            <Square className="w-5 h-5" />
            停止录音
          </button>
        )}
      </div>
      {isRecording && (
        <div className="flex items-center gap-1 h-20">
          {amplitudeValues.map((height, index) => (
            <div
              key={index}
              className="w-1 bg-blue-500 rounded-full transition-all duration-100"
              style={{
                height: `${height}px`,
                opacity: isRecording ? '1' : '0.3'
              }}
            />
          ))}
        </div>
      )}

      {audioUrl && !isRecording && (
        <audio src={audioUrl} controls className="w-full">
          <source src={audioUrl} type="audio/wav" />
          <track kind="captions" srcLang="zh" label="" />
        </audio>
      )}
    </div>
  );
};

const AddNote = ({ onSave }: { onSave: (content: Message) => void }) => {
  const [visible, setVisible] = useState(false);
  const [content, setContent] = useImmer<Message>({
    id: '',
    name: '李若涵',
    avatar:
      'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
    content: '',
    type: 'text'
  });
  const [audioBlob, setAudioBlob] = useState(null);
  const handleSave = async () => {
    if (content.type === 'audio') {
      const data = await uploadFile(audioBlob);
      onSave({
        ...content,
        url: data
      });
    } else {
      onSave(content);
    }
    setVisible(false);
  };
  const uploadMedia = async (e) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        const url = await uploadFile(file);
        console.log('🚀 ~ url:', url);
        onSave({
          ...content,
          url
        });
      } catch (error) {
        console.error('Upload failed:', error);
      }
    }
  };
  return (
    <div className="flex items-center justify-center">
      <input
        type="file"
        accept="video/*"
        className="hidden" // 隐藏原始input
        id="video-upload"
        onChange={uploadMedia}
      />

      <input
        type="file"
        accept="image/*"
        className="hidden" // 隐藏原始input
        id="image-upload"
        onChange={uploadMedia}
      />

      <Popover.Menu
        actions={actions}
        onAction={(node) => {
          console.log('🚀 ~ node:', node);
          setContent((draft) => {
            draft.type = node.key;
          });
          // 图片视频直接上传
          if (node.key === 'image') {
            document.getElementById('image-upload')?.click();
          } else if (node.key === 'video') {
            document.getElementById('video-upload')?.click();
          } else {
            setVisible(true);
          }
        }}
        placement="bottom-start"
        trigger="click"
      >
        <button
          className="px-3 py-1 bg-blue-600 text-white rounded mt-4"
          type="button"
        >
          <PiPlusCircle fontSize={20} /> 添加内容
        </button>
      </Popover.Menu>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        bodyStyle={{ height: 'mr-30vh' }}
      >
        <div className="p-4">
          <div className="mb-5 flex justify-between text-base">
            <div className="text-stone-400" onClick={() => setVisible(false)}>
              取消
            </div>
            <div className="">添加内容</div>
            <div className="text-blue-500" onClick={() => handleSave()}>
              保存
            </div>
          </div>
          <div className="flex-1">
            {content.type === 'text' && (
              <TextArea
                className="border p-2 rounded-lg"
                defaultValue={''}
                showCount
                rows={5}
                placeholder="请输入内容"
                maxLength={200}
                onChange={(value) =>
                  setContent((draft) => {
                    draft.content = value;
                  })
                }
              />
            )}
            {content.type === 'audio' && (
              <AudioRecorder
                onRecordingComplete={(blob: Blob) => {
                  setAudioBlob(blob);
                  console.log('🚀 ~ blob:', blob);
                  // setContent((draft) => {
                  //   draft.content = blob;
                  // })
                }}
              />
            )}
          </div>
        </div>
      </Popup>
    </div>
  );
};

export const UserNotes = createReactBlockSpec(
  {
    type: 'userNotes',
    propSchema: {
      notes: {
        default: []
      }
    },
    content: 'none',
    isSelectable: false,
    isFileBlock: false
  },
  {
    containsInlineContent: false,
    render: ({ block, editor }) => {
      const [isEditable] = useAtom(isEditableAtom);
      const notes: NoteProps = block.props.notes || [];

      const handleDeleteNote = (id: string) => {
        editor.updateBlock(block, {
          props: {
            ...block.props,
            notes: notes.filter((item) => item.id !== id)
          }
        });
      };

      const handleSave = (content: Message) => {
        editor.updateBlock(block, {
          props: {
            ...block.props,
            notes: [...notes, { ...content, id: nanoid(16) }]
          }
        });
      };

      return (
        <div className="w-full mx-auto mb-8 p-4 bg-gray-100 rounded-xl shadow-md">
          <div className="space-y-6">
            {notes.map((message) => (
              <ChatItem
                key={message.id}
                message={message}
                onDelete={handleDeleteNote}
              />
            ))}
          </div>
          {isEditable && <AddNote onSave={handleSave} />}
        </div>
      );
    }
  }
);
