import { compress, generateKey, uploadObs } from '@/utils/obs';

// 供 antd-mobile ImageUploader 使用
export async function uploadFile(file: File): Promise<string> {
  console.log('🚀 ~ file: index.tsx:17 ~ file:', file);
  try {
    // 图片压缩
    if (file.type.indexOf('image') > -1) {
      const key = generateKey(file.name, 'pbl');
      const tempFile: File = await compress(file);
      const url = await uploadObs(tempFile, key);
      if (!url) {
        throw new Error('Fail to upload');
      }
      return Promise.resolve(url);
    }
    if (file.type.indexOf('video') > -1) {
      const key = generateKey(file.name, 'pbl');

      const url = await uploadObs(file, key);
      if (!url) {
        throw new Error('Fail to upload');
      }
      return Promise.resolve(url);
    }
    if (file.type.indexOf('audio') > -1 && file instanceof Blob) {
      const key = generateKey(`audio-${Date.now()}.wav`, 'pbl');
      const url = await uploadObs(file, key);
      if (!url) {
        throw new Error('Fail to upload');
      }
      return Promise.resolve(url);
    }
    // Add default case for unsupported file types
    throw new Error('Unsupported file type');
  } catch (err: unknown) {
    if (err instanceof Error) {
      throw new Error(err.message);
    }
    throw new Error('Upload failed');
  }
}
