'use client';

import { createReactBlockSpec } from '@blocknote/react';
import { useState } from 'react';
import { useAtom } from 'jotai';
import { markdownAtom, projectDataAtom } from '@/store/pbl';
import { Button, DotLoading, Modal, TextArea } from 'antd-mobile';
import { aiGenerateContent } from '@/api/pbl';
import { PiRobot } from 'react-icons/pi';

// 定义 AI 响应的接口
interface AIResponse {
  answer: string;
  [key: string]: unknown;
}

// 生成唯一 ID 的函数
const generateUniqueId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 创建一个用于显示 AI 内容输入框的 Modal 组件
interface AIPlaceholderModalProps {
  // @ts-ignore - 使用通用类型
  editor: unknown;
  onClose: () => void;
}

const AIPlaceholderModal = ({ editor, onClose }: AIPlaceholderModalProps) => {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [markdownText] = useAtom(markdownAtom);
  const [projectData] = useAtom(projectDataAtom);

  const generateContent = async () => {
    setIsLoading(true);
    try {
      console.log('🚀 ~ markdownText:', markdownText);

      const promptRequirement = prompt
        ? `要求：${prompt}`
        : '生成不超过 300 字的相关内容';

      const query = `
      下面是一份针对学前儿童的 PBL 项目学习报告：

      ---

      项目名称：${projectData.projectName}

      ${markdownText}

      ---

      你需要生成 <AI_COMPLETION_HERE> 标记处的内容，只需要生成这个标记处的内容，不需要生成其他内容，${promptRequirement}，不要追问我问题，这是个一次性任务，直接输出纯文字内容，不要带 markdown 格式
      `;

      // @ts-ignore
      const response: AIResponse = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        query,
        response_mode: 'blocking'
      });

      if (response) {
        // 创建一个新块
        const newBlock = {
          id: generateUniqueId(),
          type: 'paragraph',
          props: {
            textColor: 'default',
            backgroundColor: 'default',
            textAlignment: 'left'
          },
          content: [
            {
              type: 'text',
              text: response.answer || '无法生成内容，请重试',
              styles: {}
            }
          ],
          children: []
        };

        // 在光标位置插入一个 paragraph 类型的内容
        // @ts-ignore - 忽略类型错误
        editor.insertBlocks(
          [newBlock],
          // @ts-ignore - 忽略类型错误
          editor.getTextCursorPosition().block,
          'after'
        );

        // 设置文本光标位置到新插入的块的末尾
        // @ts-ignore - 忽略类型错误
        editor.setTextCursorPosition(newBlock.id, 'end');

        setIsLoading(false);
        onClose();
      }
    } catch (error) {
      console.error('生成内容失败：', error);
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={true}
      content={
        <div className="p-2">
          <div className="flex items-center mb-4">
            <PiRobot className="text-blue-500 mr-2" size={20} />
            <span className="font-medium">AI 内容生成</span>
          </div>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-4">
              <div className="text-blue-500">
                <DotLoading />
              </div>
              <p className="mt-2 text-gray-500">正在生成内容，请稍候...</p>
            </div>
          ) : (
            <div>
              <TextArea
                placeholder="请输入您对 AI 的额外要求（可不填）"
                value={prompt}
                onChange={setPrompt}
                disabled={isLoading}
                rows={3}
                className="mb-4"
              />
              <div className="flex justify-end gap-2">
                <Button color="default" onClick={onClose}>
                  取消
                </Button>
                <Button color="primary" onClick={generateContent}>
                  开始生成
                </Button>
              </div>
            </div>
          )}
        </div>
      }
      closeOnMaskClick={!isLoading}
      showCloseButton={!isLoading}
      onClose={onClose}
    />
  );
};

// 创建一个自定义的 Block 规范，用于表示 AI 补全位置
export const AIPlaceholder = createReactBlockSpec(
  {
    type: 'aiPlaceholder',
    propSchema: {},
    content: 'none',
    isSelectable: true,
    isFileBlock: false
  },
  {
    // @ts-ignore - 忽略类型错误
    render: ({ editor }) => {
      const [showModal, setShowModal] = useState(true);

      const handleClick = () => {
        setShowModal(true);
      };

      const handleClose = () => {
        setShowModal(false);
        setTimeout(() => {
          // @ts-ignore - 忽略类型错误
          const blocks = editor.document;
          for (const block of blocks) {
            if (block.type === 'aiPlaceholder') {
              // @ts-ignore - 忽略类型错误
              editor.removeBlocks([block]);
              break;
            }
          }
        }, 100);
      };

      return (
        <>
          <div
            className="flex items-center justify-center p-2 my-2 border-2 border-dashed border-blue-400 rounded-md cursor-pointer hover:bg-blue-50"
            onClick={handleClick}
          >
            <PiRobot className="text-blue-500 mr-2" size={20} />
            <span className="text-blue-500">AI 待生成内容...</span>
          </div>
          {showModal && (
            <AIPlaceholderModal editor={editor} onClose={handleClose} />
          )}
        </>
      );
    },
    toExternalHTML: () => {
      return <div>{'<AI_COMPLETION_HERE>'}</div>;
    }
  }
);
