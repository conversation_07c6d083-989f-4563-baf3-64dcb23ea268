'use client';

import { useRouter } from 'next/navigation';
import {
  Clock,
  Users,
  Share,
  SquareChartGantt,
  ChevronRight,
  Mic,
  PlayCircle
} from 'lucide-react';
import Media from '@/app/pbl/record/detail/components/Media';

interface MediaItem {
  type: 'image' | 'video' | 'audio';
  url: string;
  caption: string;
  thumbnail?: string;
  duration?: string;
}

interface RecordItemProps {
  record: {
    summaryId: string;
    summaryDate: string;
    observationIds: number[];
    suggest: string;
    summary: string;
    createTime: string;
    updateTime: string;
    projectId: string;
    observationCnt: number;
    mediaList: MediaItem[];
    students: {
      id: string;
      name: string;
      avatar: string;
    }[];
  };
  index: number;
}

// 格式化日期显示
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const weekdays = [
    '星期日',
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六'
  ];
  const weekday = weekdays[date.getDay()];
  return { month, day, weekday };
};

export default function RecordByDayItem({ record, index }: RecordItemProps) {
  console.log('🚀 ~ record:', record);
  const router = useRouter();
  const { month, day, weekday } = formatDate(record.summaryDate);
  const delay = index * 0.1;
  // 确保日期非空
  const date = record?.summaryDate?.split(' ')[0] || '';
  // 时间默认为当天的创建时间
  const time = record?.createTime?.split(' ')?.[1]?.substring(0, 5) || '';
  return (
    <div
      className="bg-white rounded-xl shadow-sm overflow-hidden card-animate"
      style={{ animationDelay: `${delay}s` }}
      onClick={() => {
        router.push(`/pbl/record/detail/day?id=${record.summaryId}`);
      }}
    >
      {/* 日期头部 */}
      <div className="flex items-center p-4 border-b border-gray-200">
        <div className="flex flex-col items-center justify-center bg-slate-400 bg-opacity-10 rounded-lg p-2 w-14 h-14">
          <span className="text-indigo-500 text-xl font-bold">{day}</span>
          <span className="text-indigo-500 text-xs">{month}月</span>
        </div>
        <div className="ml-3">
          <h3 className="font-medium ">{weekday}</h3>
          <p className="text-sm">{date}</p>
        </div>
        <div className="ml-auto flex items-center text-gray-600">
          <Clock className="w-3 h-3 " />
          <span className="text-sm  ml-1">{time}</span>
        </div>
      </div>

      {/* 卡片内容 */}
      <div className="p-4">
        <p className="text-text-primary mb-4 leading-relaxed line-clamp-5">
          {record.summary}
        </p>

        <Media media={record.mediaList} />

        {/* 参与儿童 */}
        <div className="flex mt-3 items-start">
          <span className="text-xs mr-2 flex items-center mt-1">
            <Users className="w-3 h-3 mr-1" />
            参与儿童：
          </span>
          <div className="flex-1 flex flex-wrap">
            {record.students.map((child, i) => (
              <span
                key={`${child.id}`}
                className="text-xs bg-slate-100 rounded-full px-2 py-1 mr-1 mb-1"
              >
                {child.name || ''}
              </span>
            ))}
          </div>
        </div>
      </div>
      {/* 卡片底部操作区 */}
      <div className="flex items-center justify-between px-4 py-3 bg-neutral">
        <div className="flex items-center text-sm">
          <button
            className="flex items-center"
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              const date = record.summaryDate.split(' ')[0];
              router.push(
                `/pbl/record/list?date=${date}&weekday=${weekday}&observationIds=${record.observationIds}`
              );
            }}
          >
            <SquareChartGantt className="w-4 h-4 mr-1" />
            <span>关联 {record.observationCnt} 条记录</span>
            <ChevronRight className="w-4 h-4 ml-1 text-gray-500" />
          </button>
        </div>
        <div>
          <button
            type="button"
            className="text-primary text-sm flex items-center"
          >
            <Share className="w-4 h-4 mr-1" />
            <span>分享</span>
          </button>
        </div>
      </div>
    </div>
  );
}
