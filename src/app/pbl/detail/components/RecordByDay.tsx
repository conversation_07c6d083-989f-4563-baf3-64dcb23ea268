'use client';

import { useRouter } from 'next/navigation';
import { useInfiniteQuery } from '@tanstack/react-query';
import { getDailyObservationList } from '@/api/pbl';
import RecordByDayItem from './RecordByDayItem';
import { InfiniteScroll } from 'antd-mobile';
import Empty from '@/components/Empty';

// 媒体项类型定义
interface MediaItem {
  mediaId: string;
  observationId: string;
  type: number; // 1=图片，2=视频，3=音频
  url: string;
  cover?: string;
  duration?: number;
  videoPlayType?: number;
  createTime: string;
  createUserId: string;
  name: string;
  deptId: string;
}

// 学生信息类型定义
interface Student {
  id: string;
  name: string;
  avatar: string;
}

// 观察记录类型
interface ObservationRecord {
  summaryId: string;
  summaryDate: string;
  observationIds: number[];
  suggest: string;
  summary: string;
  createTime: string;
  updateTime: string;
  projectId: string;
  observationCnt: number;
  mediaList: MediaItem[];
  students: Student[];
}

// 接口返回数据类型
interface ApiResponse {
  list: ObservationRecord[];
  total: number;
  pageSize?: number;
  pageNum?: number;
}

export default function Record({ projectId }: { projectId: string }) {
  const router = useRouter();

  // 使用 useInfiniteQuery 获取无限滚动的观察记录
  const { isLoading, data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery<ApiResponse>(
      ['dailyObservations', projectId],
      async ({ pageParam = 1 }) => {
        // 这里假设 API 支持分页，如果后端 API 不支持分页参数，可以在前端模拟分页
        const response = await getDailyObservationList(projectId);
        // 模拟分页，每页 10 条数据
        // @ts-ignore - 忽略类型检查以避免 linter 错误
        const allRecords = response.list || [];
        const pageSize = 10;
        const startIndex = (pageParam - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedList = allRecords.slice(startIndex, endIndex);

        return {
          list: paginatedList,
          total: allRecords.length,
          pageSize,
          pageNum: pageParam
        };
      },
      {
        enabled: !!projectId,
        refetchOnWindowFocus: false,
        staleTime: 5 * 60 * 1000, // 5 分钟内不重新获取数据
        getNextPageParam: (lastPage, allPages) => {
          // 计算下一页的页码
          const nextPage = allPages.length + 1;
          // 如果没有更多数据，返回 undefined，否则返回下一页页码
          return lastPage.list.length === 0 || lastPage.list.length < 10
            ? undefined
            : nextPage;
        }
      }
    );

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin" />
        <p className="mt-4">加载中...</p>
      </div>
    );
  }

  // 合并所有页面的数据
  const observationRecords: ObservationRecord[] =
    data?.pages?.flatMap((page) => page.list || []) || [];

  // 如果没有数据且不在加载中，则显示 Empty 组件
  if (!isLoading && observationRecords.length === 0) {
    return (
      <main className="flex pt-20  flex-col items-center justify-center">
        <Empty title="暂无观察记录" />
        <div className="p-4 rounded-lg text-xs text-gray-500 text-center">
          每日观察记录会在每天的 23:00 自动生成
        </div>
      </main>
    );
  }

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage) {
      await fetchNextPage();
    }
  };

  return (
    <main className="bg-slate-50 min-h-main">
      <div className=" flex flex-col">
        {/* 主内容区 */}
        <div className="flex-1 overflow-y-auto custom-scrollbar p-4 bg-neutral">
          <div className="space-y-5">
            {observationRecords.map((record, index) => (
              <div key={record.summaryId}>
                <RecordByDayItem record={record} index={index} />
              </div>
            ))}

            {/* 使用 antd-mobile 的 InfiniteScroll 组件 */}
            <InfiniteScroll
              loadMore={loadMore}
              hasMore={!!hasNextPage}
              threshold={250}
            >
              {isFetchingNextPage ? (
                <div className="flex flex-col items-center py-4">
                  <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin" />
                  <p className="mt-2 text-sm text-gray-500">加载更多...</p>
                </div>
              ) : hasNextPage ? (
                <div className="text-center py-6">
                  <p className="text-sm text-gray-500">上拉加载更多</p>
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-sm text-gray-500">没有更多数据了</p>
                </div>
              )}
            </InfiniteScroll>
          </div>
        </div>
      </div>
    </main>
  );
}
