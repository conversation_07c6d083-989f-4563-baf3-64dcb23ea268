import type { Block } from '@blocknote/core';

import { memo, useState, useEffect } from 'react';

import { getPblContent, updatePblContent } from '@/api/pbl';
import { BlockNote } from './BlockNote/index';
import { defaultPblData } from '../../create/data';

export const Editor = memo(function Editor({
  projectId
}: {
  projectId: string;
}) {
  const [content, setContent] = useState<Block[]>([]);

  useEffect(() => {
    if (projectId) {
      initData();
    }
  }, [projectId]);

  const initData = () => {
    getPblContent(projectId).then((res: any) => {
      try {
        const pblContent = res.content ? JSON.parse(res.content) : [];
        const contentToSet =
          pblContent.length > 0 ? pblContent : defaultPblData;

        setContent(contentToSet);
      } catch (error) {
        console.error('🚀 ~ error:', error);
      }
    });
  };

  const onChange = (blocks: Block[]) => {
    console.log('🚀 ~ blocks:', blocks);
    updatePblContent(projectId, {
      content: JSON.stringify(blocks)
    }).then(() => {
      console.log('🚀 ~ 更新完成');
    });
  };

  return (
    <div className="">
      {content.length > 0 && (
        <BlockNote content={content} onChange={onChange} projectId={projectId} />
      )}
    </div>
  );
});
