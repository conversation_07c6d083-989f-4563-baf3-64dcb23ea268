'use client';

import { Popover } from 'antd-mobile';
import React, { ReactNode } from 'react';

export interface PopoverAction {
  text: string;
  key: string;
  icon?: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  danger?: boolean;
}

interface PopoverMenuProps {
  actions: PopoverAction[];
  placement?: 'top' | 'top-start' | 'top-end' | 'right' | 'right-start' | 'right-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end';
  trigger?: 'click' | 'hover';
  children: ReactNode;
  onAction?: (action: PopoverAction) => void;
  className?: string;
}

const PopoverMenu: React.FC<PopoverMenuProps> = ({
  actions,
  placement = 'bottom-start',
  trigger = 'click',
  children,
  onAction,
  className
}) => {
  const handleAction = (action: PopoverAction) => {
    if (action.onClick) {
      action.onClick();
    }
    if (onAction) {
      onAction(action);
    }
  };

  return (
    <Popover.Menu
      actions={actions.map(action => ({
        ...action,
        onClick: () => handleAction(action)
      }))}
      placement={placement}
      trigger={trigger}
      className={className}
    >
      <div onClick={(e) => e.stopPropagation()}>
        {children}
      </div>
    </Popover.Menu>
  );
};

export default PopoverMenu;
