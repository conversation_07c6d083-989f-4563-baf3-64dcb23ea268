import type React from 'react';
import { forwardRef, useImperativeHandle, useEffect, useState } from 'react';
import { Plus, Trash2, Folder } from 'lucide-react';
import { SortableList } from './SortableList';
import { deleteMenu, updateMenu, addMenu, updateMenuNode } from '@/api/pbl';
import { Dialog, Input, Toast } from 'antd-mobile';

export interface MenuItem {
  id: string;
  title: string;
  parentId: string;
  children?: MenuItem[];
  depth: number;
}

interface SortableItemProps {
  id: string;
  item: MenuItem;
  onEdit: (item: MenuItem) => void;
  onDelete: (id: string) => void;
  onAddSub: (id: string) => void;
  isEditing: boolean;
  editingTitle: string;
  onEditingTitleChange: (title: string) => void;
  onEditComplete: (item: MenuItem, title: string) => void;
}

const MenuItemContent: React.FC<SortableItemProps> = ({
  item,
  onEdit,
  onDelete,
  onAddSub,
  isEditing,
  editingTitle,
  onEditingTitleChange,
  onEditComplete,
}) => {
  return (
    <div className="flex justify-between items-center gap-2 py-1 w-full">
      <div className="flex items-center gap-2">
        <SortableList.DragHandle />

        {isEditing ? (
          <input
            type="text"
            className="border rounded px-2 py-1"
            value={editingTitle}
            onChange={(e) => onEditingTitleChange(e.target.value)}
            onBlur={() => onEditComplete(item, editingTitle)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                onEditComplete(item, editingTitle);
              }
            }}
          />
        ) : (
          <span className="cursor-pointe" onClick={() => onEdit(item)}>
            {item.title}
          </span>
        )}
      </div>
      <div>
        {item.depth < 3 && (
          <button
            onClick={() => onAddSub(item.id)}
            className="p-1 hover:bg-gray-100 rounded"
            aria-label="添加子菜单"
            type="button"
          >
            <Plus size={16} className="text-green-600" />
          </button>
        )}
        <button
          onClick={() => onDelete(item.id)}
          className="p-1 hover:bg-gray-100 rounded"
          aria-label="删除菜单"
          type="button"
        >
          <Trash2 size={16} className="text-red-600" />
        </button>
      </div>
    </div>
  );
};

// 定义ref的类型
export interface MenuTreeRef {
  getMenuData: () => MenuItem[];
}

const MenuTree = forwardRef<
  MenuTreeRef,
  { data: MenuItem[]; projectId: string | undefined | null; rootId: string }
>((props, ref) => {
  const { data, projectId, rootId } = props;
  const [menuData, setMenuData] = useState<MenuItem[]>(data);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState<string>('');

  const [value, setValue] = useState<string>('');
  const [currentParentId, setCurrentParentId] = useState<string>(''); // 当前新增菜单的父菜单
  const [dialogVisible, setDialogVisible] = useState(false);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getMenuData: () => menuData,
  }));

  useEffect(() => {
    if (Array.isArray(data) && data.length > 0) {
      setMenuData(data);
    }
  }, [data]);

  const findMenuItemById = (items: MenuItem[], id: string): MenuItem | null => {
    for (const item of items) {
      if (item.id === id) return item;
      if (item.children) {
        const found = findMenuItemById(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const addNewMenu = (parentId?: string) => {
    setCurrentParentId(parentId || rootId);
    setValue('');
    setDialogVisible(true);
  };

  const deleteMenuItem = (id: string) => {
    Dialog.confirm({
      content: '确定要删除吗？',
      onConfirm: async () => {
        deleteMenu(id).then(() => {
          const deleteFromItems = (items: MenuItem[]): MenuItem[] => {
            return items.filter((item) => {
              if (item.id === id) return false;
              if (item.children) {
                item.children = deleteFromItems(item.children);
              }
              return true;
            });
          };
          setMenuData(deleteFromItems(menuData));
          Toast.show({
            icon: 'success',
            content: '删除成功',
          });
        });
      },
    });
  };

  const startEditing = (item: MenuItem) => {
    setEditingId(item.id);
    setEditingTitle(item.title);
  };

  const onAddNewMenu = () => {
    const title = value.trim();
    if (title === '') {
      Toast.show({
        content: '菜单标题不能为空',
      });
      return;
    }
    const data = {
      projectId: projectId || '',
      parentId: currentParentId,
      title,
      description: '',
    };
    addMenu(data).then((res: any) => {
      setDialogVisible(false);
      if (currentParentId === rootId) {
        setMenuData((prev) => [
          ...prev,
          {
            id: res.id,
            parentId: currentParentId,
            title,
            depth: 1,
            children: [],
          },
        ]);
      } else {
        const updateMenuItems = (items: MenuItem[]): MenuItem[] => {
          return items.map((item) => {
            if (item.id === currentParentId) {
              const newItem: MenuItem = {
                id: res.id,
                parentId: currentParentId,
                title,
                depth: item.depth + 1,
                children: item.depth < 2 ? [] : undefined,
              };
              return {
                ...item,
                children: [...(item.children || []), newItem],
              };
            }
            if (item.children) {
              return {
                ...item,
                children: updateMenuItems(item.children),
              };
            }
            return item;
          });
        };
        setMenuData(updateMenuItems(menuData));
      }
    });
  };

  const onEditComplete = (item: MenuItem, newTitle: string) => {
    console.log('🚀 ~ onEditComplete:', item, newTitle);
    updateMenuNode(item.id, {
      title: newTitle,
    }).then(() => {
      const updateTitle = (menuItems: MenuItem[]): MenuItem[] => {
        return menuItems.map((menuItem) => {
          if (menuItem.id === item.id) {
            return { ...menuItem, title: newTitle };
          }
          if (menuItem.children) {
            return { ...menuItem, children: updateTitle(menuItem.children) };
          }
          return menuItem;
        });
      };

      setMenuData(updateTitle(menuData));
      setEditingId(null);
      setEditingTitle('');
    });
  };

  const renderSortableMenuItems = (items: MenuItem[]) => {
    return (
      <SortableList
        items={items}
        onChange={(newItems) => {
          // 更新对应层级的菜单项
          const updateMenuItems = (menuItems: MenuItem[]): MenuItem[] => {
            return menuItems.map((item) => {
              if (item.children && item.children === items) {
                return { ...item, children: newItems };
              }
              if (item.children) {
                return {
                  ...item,
                  children: updateMenuItems(item.children),
                };
              }
              return item;
            });
          };
          setMenuData(updateMenuItems(menuData));
        }}
        renderItem={(item) => (
          <SortableList.Item id={item.id}>
            <MenuItemContent
              id={item.id}
              item={item}
              onEdit={startEditing}
              onDelete={deleteMenuItem}
              onAddSub={addNewMenu}
              isEditing={editingId === item.id}
              editingTitle={editingTitle}
              onEditingTitleChange={setEditingTitle}
              onEditComplete={onEditComplete}
            />
          </SortableList.Item>
        )}
      />
    );
  };

  const renderMenuItem = (item: MenuItem) => {
    return (
      <div key={item.id}>
        {item.depth === 3 ? (
          <MenuItemContent
            id={item.id}
            item={item}
            onEdit={startEditing}
            onDelete={deleteMenuItem}
            onAddSub={addNewMenu}
            isEditing={editingId === item.id}
            editingTitle={editingTitle}
            onEditingTitleChange={setEditingTitle}
            onEditComplete={onEditComplete}
          />
        ) : (
          <div className="">
            <div className="flex justify-between items-center gap-2 py-1">
              <div className="flex items-center">
                <Folder className="text-gray-500" size={16} />
                {editingId === item.id ? (
                  <input
                    type="text"
                    placeholder="请输入标题"
                    className="border rounded px-2 py-1 border-red-400"
                    value={editingTitle}
                    onChange={(e) => setEditingTitle(e.target.value)}
                    onBlur={() => onEditComplete(item, editingTitle)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        onEditComplete(item, editingTitle);
                      }
                    }}
                  />
                ) : (
                  <span
                    className="cursor-pointer ml-2 text-base"
                    onClick={() => startEditing(item)}
                  >
                    {item.title}
                  </span>
                )}
              </div>
              <div>
                {item.depth < 3 && (
                  <button
                    type="button"
                    onClick={() => addNewMenu(item.id)}
                    className="p-1 hover:bg-gray-100 rounded"
                    aria-label="添加子菜单"
                  >
                    <Plus size={16} className="text-green-600" />
                  </button>
                )}
                <button
                  onClick={() => deleteMenuItem(item.id)}
                  className="p-1 hover:bg-gray-100 rounded"
                  aria-label="删除菜单"
                  type="button"
                >
                  <Trash2 size={16} className="text-red-600" />
                </button>
              </div>
            </div>
            {item.children && item.children.length > 0 && (
              <div className="pl-4">
                {item.depth === 2
                  ? renderSortableMenuItems(item.children)
                  : item.children.map((child) => renderMenuItem(child))}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="">
      <div className="flex justify-between items-center p-2 border-b">
        <h2 className="text-sm font-medium text-gray-700">目录大纲</h2>
        <button
          onClick={() => addNewMenu()}
          className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          title="添加新项目"
          type="button"
        >
          <Plus className="w-5 h-5 text-gray-500" />
        </button>
      </div>
      <div className="mx-auto p-4">
        {menuData.map((item) => renderMenuItem(item))}
      </div>
      <Dialog
        visible={dialogVisible}
        content={
          <div>
            <Input
              value={value}
              placeholder="请输入菜单标题"
              onChange={(value) => {
                setValue(value);
              }}
              className="w-full border rounded px-3 py-2"
            />
          </div>
        }
        closeOnMaskClick
        onClose={() => {
          setDialogVisible(false);
        }}
        actions={[
          {
            key: 'confirm',
            text: '确定',
            onClick: onAddNewMenu,
          },
        ]}
      />
    </div>
  );
});

export default MenuTree;
