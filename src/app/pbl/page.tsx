'use client';

import { <PERSON>, Dialog, Empty, SwipeAction, Toast } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { format } from 'date-fns';
import { Calendar } from 'lucide-react';

import { deleteProject, getProjectList } from '@/api/pbl';
import { PiPlusCircle } from '@/components/Icons';
import {
  PiPencil,
  PiDotsThreeOutlineFill,
  PiTrash,
  PiList
} from 'react-icons/pi';
import PopoverMenu from './components/PopoverMenu';

import './page.css';

interface Project {
  instId?: string;
  deptId?: string;
  projectId: string;
  projectName: string;
  description: string;
  theme?: string;
  drivingQuestion?: string;
  learningGoals?: string;
  keyword?: string;
  startDate: string;
  endDate: string;
  status?: number;
  delFlag?: number;
  createUserId?: string;
  createTime: string;
  updateTime: string;
}

export default function PBLPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [projectProgress, setProjectProgress] = useState(0);
  const router = useRouter();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'PBL 项目';
    }
  }, []);

  const fetchProjects = useCallback(async () => {
    try {
      const res = await getProjectList({});
      const projectList = res?.list || [];
      setProjects(projectList);

      // 获取当前日期
      const currentDate = new Date();

      // 筛选出时间范围在当前日期内的项目
      const current = projectList.find((project: Project) => {
        const startDate = new Date(project.startDate);
        const endDate = new Date(project.endDate);

        // 检查日期是否有效
        if (
          startDate.toString() === 'Invalid Date' ||
          endDate.toString() === 'Invalid Date'
        ) {
          return false;
        }

        // 检查当前日期是否在项目的开始日期和结束日期之间
        return currentDate >= startDate && currentDate <= endDate;
      });

      if (current) {
        setProjects(
          projectList.filter(
            (project: Project) => project.projectId !== current.projectId
          )
        );

        setCurrentProject(current);

        // 计算项目进度
        const startDate = new Date(current.startDate);
        const endDate = new Date(current.endDate);

        // 计算总天数
        const totalDays = Math.max(
          1,
          (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        // 计算已经过去的天数
        const passedDays = Math.max(
          0,
          (currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        // 计算进度百分比
        const progress = Math.min(
          100,
          Math.max(0, Math.round((passedDays / totalDays) * 100))
        );
        setProjectProgress(progress);
      }
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: '获取项目列表失败'
      });
    }
  }, []);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  const handleDelete = async (projectId: string) => {
    const result = await Dialog.confirm({
      content: '确定要删除该项目吗？'
    });

    if (result) {
      try {
        await deleteProject(projectId);
        Toast.show({
          icon: 'success',
          content: '删除成功'
        });
        fetchProjects();
      } catch (error) {
        Toast.show({
          icon: 'fail',
          content: '删除失败'
        });
      }
    }
  };

  const rightActions = [
    {
      key: 'delete',
      text: '删除',
      color: 'danger'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="p-4">
        {currentProject ? (
          <div
            className="card bg-white rounded-2xl p-6 mb-6 overflow-hidden fluid-gradient animate-fade-in"
            onClick={() =>
              router.push(`/pbl/detail?ProjectId=${currentProject.projectId}&activeIndex=1`)
            }
          >
            <div className="relative z-10">
              <div className="flex justify-between items-start">
                <h2 className="text-lg mb-3 text-slate-500">当前项目</h2>
                <div className="relative">
                  <PopoverMenu
                    actions={[
                      {
                        text: '编辑',
                        key: 'edit',
                        icon: <PiPencil fontSize={16} color="#333" />,
                        onClick: () => {
                          router.push(
                            `/pbl/create?ProjectId=${currentProject.projectId}`
                          );
                        }
                      },
                      {
                        text: '删除',
                        key: 'delete',
                        icon: (
                          <PiTrash fontSize={16} className="text-red-500" />
                        ),
                        onClick: () => handleDelete(currentProject.projectId)
                      }
                    ]}
                    placement="bottom-end"
                    trigger="click"
                  >
                    <PiDotsThreeOutlineFill fontSize={20} color="#666" />
                  </PopoverMenu>
                </div>
              </div>
              <h3 className="text-xl font-bold mb-2">
                {currentProject.projectName}
              </h3>
              <p className="text-gray-600 mb-4">{currentProject.description}</p>
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-1">
                  <span>项目进度</span>
                  <span>{projectProgress}%</span>
                </div>
                <div className="w-full bg-gray-100 rounded-full h-2.5">
                  <div
                    className="progress-bar h-2.5 rounded-full"
                    style={{ width: `${projectProgress}%` }}
                  />
                </div>
              </div>

              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-600">开始日期</p>
                  <p className="font-medium">{currentProject.startDate}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">预计结束</p>
                  <p className="font-medium">{currentProject.endDate}</p>
                </div>
              </div>
            </div>
          </div>
        ) : null}
        {projects.length === 0 ? (
          <Empty description="暂无项目" />
        ) : (
          <div className="space-y-4 mb-[100px]">
            {projects.map((project) => (
              <SwipeAction
                key={project.projectId}
                rightActions={rightActions}
                onAction={() => handleDelete(project.projectId)}
              >
                <Card
                  onClick={() =>
                    router.push(`/pbl/detail?ProjectId=${project.projectId}&activeIndex=1`)
                  }
                  className="bg-white rounded-xl shadow overflow-hidden border border-gray-200"
                >
                  <div className="relative">
                    <div className="text-xl font-medium">
                      {project.projectName}
                    </div>
                    <div className="text-sm text-stone-600 py-2">
                      {project.description}
                    </div>
                    <div className="text-sm text-stone-500 flex items-center">
                      <Calendar className="inline-block w-4 h-4 mr-1" />
                      {format(new Date(project.createTime), 'yyyy-MM-dd')}
                    </div>
                    <div className="absolute right-0 top-0">
                      <PopoverMenu
                        actions={[
                          {
                            text: '项目大纲',
                            key: 'outline',
                            icon: <PiList fontSize={16} color="#333" />,
                            onClick: () => {
                              router.push(
                                `/pbl/outline?ProjectId=${project.projectId}`
                              );
                            }
                          },
                          {
                            text: '编辑',
                            key: 'edit',
                            icon: <PiPencil fontSize={16} color="#333" />,
                            onClick: () => {
                              router.push(
                                `/pbl/create?ProjectId=${project.projectId}`
                              );
                            }
                          },
                          {
                            text: '删除',
                            key: 'delete',
                            icon: (
                              <PiTrash fontSize={16} className="text-red-500" />
                            ),
                            onClick: () => handleDelete(project.projectId)
                          }
                        ]}
                        placement="bottom-start"
                        trigger="click"
                      >
                        <PiDotsThreeOutlineFill fontSize={20} color="#666" />
                      </PopoverMenu>
                    </div>
                  </div>
                </Card>
              </SwipeAction>
            ))}
          </div>
        )}
        <div className="fixed bottom-0 left-0 right-0 flex items-center justify-center pb-3">
          <button
            type="button"
            className="bg-gradient-to-r from-indigo-500 to-purple-400 text-white shadow-none focus:ring-4 text-base focus:ring-blue-300 font-medium rounded-3xl px-6 py-2 focus:outline-none dark:focus:ring-blue-800"
            onClick={() => router.push('/pbl/create')}
          >
            <div className="flex items-center justify-center">
              <PiPlusCircle fontSize={24} />
              <span className="ml-1">新建项目</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
}
