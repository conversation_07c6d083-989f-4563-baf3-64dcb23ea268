'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import MaterialList from '../detail/components/Material';
import { useEffect } from 'react';

export default function App() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || undefined;
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '素材管理';
    }
  }, []);

  return (
    <main className="bg-slate-50 min-h-screen">
      <MaterialList projectId={projectId} />
    </main>
  );
}
