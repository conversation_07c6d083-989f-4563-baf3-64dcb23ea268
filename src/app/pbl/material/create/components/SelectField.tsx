'use client';

import { ChevronDown } from 'lucide-react';

interface SelectFieldProps {
  label: string;
  value: string;
  placeholder: string;
  onClick: () => void;
  className?: string;
}

const SelectField: React.FC<SelectFieldProps> = ({
  label,
  value,
  placeholder,
  onClick,
  className
}) => {
  return (
    <div className={className}>
      <div className="block text-sm font-medium text-gray-700 mb-1.5">
        {label}
      </div>
      <div onClick={onClick} className="relative cursor-pointer">
        <div className="w-full bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          {value ? (
            <span>{value}</span>
          ) : (
            <span className="text-gray-400">{placeholder}</span>
          )}
        </div>
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <ChevronDown className="w-5 h-5 text-gray-400" />
        </div>
      </div>
    </div>
  );
};

export default SelectField;
