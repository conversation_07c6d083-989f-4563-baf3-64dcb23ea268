'use client';

import {
  useCallback,
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle
} from 'react';
import { Toast } from 'antd-mobile';
import format from 'date-fns/format';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';

import { generateKey, uploadObsWithResume } from '@/utils/obs';
import FileUploadCard from './FileUploadCard';
import FileItem, { FILE_STATUS, type FileItemType } from './FileItem';
import { getMessage, uploadVideo } from '@/utils';

// 定义文件类型
const ALLOWED_FILE_TYPES = [
  'video/mp4',
  'video/quicktime',
  'audio/mp4',
  'audio/x-m4a'
];
// 定义文件大小限制 (1000MB)
const MAX_FILE_SIZE = 1000 * 1024 * 1024;

// 组件属性定义
interface FileUploadProps {
  onUploadComplete?: (completedFiles: FileItemType[]) => void;
  onUploadStatusChange?: (isUploading: boolean) => void;
}

const FileUpload = forwardRef<
  { getFiles: () => FileItemType[] },
  FileUploadProps
>(({ onUploadComplete, onUploadStatusChange }, ref) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [files, setFiles] = useState<FileItemType[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    // 获取当前文件列表
    getFiles: () => files
  }));

  // Dnd-kit sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(1))} ${sizes[i]}`;
  };

  useEffect(() => {
    getMessage(onMessage);
  }, []);

  // 获取到原生通知
  const onMessage = useCallback((event: { data: string }) => {
    console.log('获取到原生通知 data: ', event);
    try {
      const data = JSON.parse(event.data);
      console.log('🚀 ~ data:', data.selectVideoAndUpload);
      if (data.selectVideoAndUpload) {
        const res = data.selectVideoAndUpload;
        if (Array.isArray(res)) {
          const newFiles = res.map((item) => ({
            id: `${new Date().getTime()}-${Math.random()}`,
            name: item.fileName || '未命名文件',
            size: item.fileSize || 0,
            type: 'video/mp4',
            status: 'completed',
            progress: 100,
            url: item.fileUrl || '',
            duration: item.duration || 0,
            cover: '',
            error: ''
          }));

          // 使用函数式更新确保获取最新的 files 状态
          setFiles((prevFiles) => {
            const updatedFiles = [...prevFiles, ...newFiles];
            onUploadComplete?.(updatedFiles);
            return updatedFiles;
          });
        }
      }
    } catch (error) {
      console.log('onMessage', error);
    }
  }, []);

  // 处理文件选择
  const handleFileSelect = () => {
    if (
      window.android?.selectVideoAndUploadV2 ||
      // window.android?.selectVideoAndUpload ||
      window.webkit?.messageHandlers?.selectVideoAndUpload
    ) {
      const date = format(new Date(), 'yyyy-MM-dd');
      uploadVideo(5, 360, true, `pbl/material/${date}`, 2, 6000000);
    } else {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }
  };

  // 获取视频/音频时长
  const getMediaDuration = (file: File): Promise<number> => {
    return new Promise((resolve) => {
      // 创建一个媒体元素（视频或音频）
      const media = file.type.includes('video')
        ? document.createElement('video')
        : document.createElement('audio');

      media.preload = 'metadata';

      // 当元数据加载完成后获取时长
      media.onloadedmetadata = () => {
        // 释放对象 URL
        console.log('media', media);
        window.URL.revokeObjectURL(media.src);
        resolve(media.duration);
      };

      // 如果加载失败，返回 0 作为时长
      media.onerror = () => {
        console.error('无法获取媒体时长');
        window.URL.revokeObjectURL(media.src);
        resolve(0);
      };

      // 创建对象 URL
      media.src = URL.createObjectURL(file);
    });
  };

  // 处理文件变更 - 支持多文件选择
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    // 创建新文件队列
    const newFileItems: FileItemType[] = [];
    const invalidFiles: string[] = [];

    // 处理所有选择的文件
    for (let i = 0; i < selectedFiles.length; i++) {
      const newFile = selectedFiles[i];
      if (!newFile) continue;

      // 验证文件类型
      if (!ALLOWED_FILE_TYPES.includes(newFile.type)) {
        invalidFiles.push(`${newFile.name} (不支持的文件类型)`);
        continue;
      }

      // 验证文件大小
      if (newFile.size > MAX_FILE_SIZE) {
        invalidFiles.push(`${newFile.name} (超过 1000MB 限制)`);
        continue;
      }

      // 获取媒体时长
      const duration = await getMediaDuration(newFile);

      // 创建文件对象
      const fileItem: FileItemType = {
        id: `${Date.now()}-${i}`,
        file: newFile,
        name: newFile.name,
        size: newFile.size,
        type: newFile.type,
        status: FILE_STATUS.WAITING,
        progress: 0,
        duration: duration // 添加前端获取的时长
      };

      newFileItems.push(fileItem);
    }

    // 显示无效文件的提示
    if (invalidFiles.length > 0) {
      Toast.show({
        content: `${invalidFiles.length}个文件无法上传：\n${invalidFiles.slice(0, 3).join('\n')}${invalidFiles.length > 3 ? '\n...' : ''}`,
        position: 'top',
        duration: 3000
      });
    }

    // 如果没有有效文件，直接返回
    if (newFileItems.length === 0) {
      return;
    }

    // 添加到文件列表
    const updatedFiles = [...files, ...newFileItems];
    setFiles(updatedFiles);

    // 重置文件输入，以便可以再次选择同一文件
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // 开始队列上传
    processUploadQueue(newFileItems);
  };

  // 处理拖拽结束事件
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active && over && active.id !== over.id) {
      setFiles((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        const newItems = arrayMove(items, oldIndex, newIndex);

        // 如果拖动影响了已上传完成的文件顺序，通知父组件
        if (onUploadComplete) {
          const completedFiles = newItems.filter(
            (f) => f.status === FILE_STATUS.COMPLETED && f.url
          );
          onUploadComplete(completedFiles);
        }
        return newItems;
      });
    }
  };

  // 处理上传队列
  const processUploadQueue = async (fileQueue: FileItemType[]) => {
    if (fileQueue.length === 0) return;

    setIsUploading(true);
    if (onUploadStatusChange) {
      onUploadStatusChange(true);
    }

    try {
      // 一个接一个地上传文件
      for (const fileItem of fileQueue) {
        // 跳过已经上传或正在上传的文件
        if (
          fileItem.status === FILE_STATUS.COMPLETED ||
          fileItem.status === FILE_STATUS.UPLOADING
        ) {
          continue;
        }

        // 更新状态为正在上传
        setFiles((prev) =>
          prev.map((f) =>
            f.id === fileItem.id ? { ...f, status: FILE_STATUS.UPLOADING } : f
          )
        );

        // 上传文件
        try {
          const updatedFile = await uploadFile(fileItem);

          // 更新文件状态
          setFiles((prev) => {
            const newFiles = prev.map((f) =>
              f.id === fileItem.id ? updatedFile : f
            );
            return newFiles;
          });

          // 通知父组件已完成上传的文件
          if (onUploadComplete) {
            // 获取所有已完成上传的文件
            setFiles((prev) => {
              const completedFiles = prev.filter(
                (f) => f.status === FILE_STATUS.COMPLETED && f.url
              );
              onUploadComplete(completedFiles);
              return prev;
            });
          }
        } catch (error) {
          console.error(`文件 ${fileItem.name} 上传失败：`, error);
          // 单个文件失败不影响队列继续处理
        }
      }
    } finally {
      setIsUploading(false);
      if (onUploadStatusChange) {
        onUploadStatusChange(false);
      }
    }
  };

  // 处理文件删除
  const handleFileDelete = (id: string) => {
    setFiles((prev) => {
      const newFiles = prev.filter((file) => file.id !== id);

      // 如果删除的是已上传完成的文件，通知父组件已完成上传的文件列表变化
      if (onUploadComplete) {
        const completedFiles = newFiles.filter(
          (f) => f.status === FILE_STATUS.COMPLETED && f.url
        );
        onUploadComplete(completedFiles);
      }

      return newFiles;
    });
  };

  // 保存断点续传记录
  const [uploadCheckpoints, setUploadCheckpoints] = useState<
    Record<string, unknown>
  >({});

  // 上传文件
  const uploadFile = async (fileItem: FileItemType): Promise<FileItemType> => {
    // Define progress callback within the function scope
    const progressCallback = (progress: number) => {
      setFiles((prev) => {
        const newFiles = prev.map((f) =>
          f.id === fileItem.id
            ? { ...f, progress, status: FILE_STATUS.UPLOADING }
            : f
        );
        return newFiles;
      });
    };

    try {
      // 1. Generate OBS key
      const key = generateKey(fileItem.file?.name || '', 'pbl/material');

      // 2. 获取断点续传记录（如果有）
      const checkpoint = uploadCheckpoints[fileItem.id];

      // 3. Call OBS upload with progress callback, checkpoint and checkpoint callback
      const fileUrl = await uploadObsWithResume(
        fileItem.file,
        key,
        progressCallback,
        checkpoint,
        (newCheckpoint) => {
          // 保存断点续传记录
          setUploadCheckpoints((prev) => ({
            ...prev,
            [fileItem.id]: newCheckpoint
          }));
        }
      );

      // 3. Check if URL was returned
      if (!fileUrl) {
        throw new Error('上传失败，未获取到文件 URL');
      }

      // 5. Update file state to completed
      const updatedFile = {
        ...fileItem,
        status: FILE_STATUS.COMPLETED,
        progress: 100,
        url: fileUrl,
        // 优先使用前端获取的时长，如果没有则使用后端返回的
        duration: fileItem.duration,
        cover: ''
      };

      setFiles((prev) => {
        const newFiles = prev.map((f) =>
          f.id === fileItem.id ? updatedFile : f
        );

        // 通知父组件已完成上传的文件
        if (onUploadComplete) {
          const completedFiles = newFiles.filter(
            (f) => f.status === FILE_STATUS.COMPLETED && f.url
          );
          onUploadComplete(completedFiles);
        }

        return newFiles;
      });

      return updatedFile;
    } catch (error: unknown) {
      console.error('文件上传失败：', error);

      // Update file state to failed
      const failedFile = {
        ...fileItem,
        status: FILE_STATUS.FAILED,
        // Check if error is an instance of Error before accessing message
        error: error instanceof Error ? error.message : '上传失败，请重试'
      };

      setFiles((prev) => {
        const newFiles = prev.map((f) =>
          f.id === fileItem.id ? failedFile : f
        );
        return newFiles;
      });

      throw error; // Re-throw the error for handleSubmit to catch
    }
  };

  return (
    <div>
      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="video/*"
        multiple
        onChange={handleFileChange}
      />

      {/* 文件上传区域 */}
      <div className="mb-10">
        <FileUploadCard
          isUploading={isUploading}
          onFileSelect={handleFileSelect}
          multiple={true}
        />
      </div>
      {/* 文件列表 */}
      {files.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">
              已选文件 ({files.length})
            </h2>
          </div>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={files.map((f) => f.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-3">
                {files.map((file) => (
                  <FileItem
                    key={file.id}
                    file={file}
                    onDelete={handleFileDelete}
                    isUploading={isUploading}
                    formatFileSize={formatFileSize}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>
      )}
    </div>
  );
});

export default FileUpload;
