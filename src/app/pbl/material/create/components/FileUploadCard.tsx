'use client';

import { CloudUpload, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileUploadCardProps {
  isUploading: boolean;
  onFileSelect: () => void;
  className?: string;
  multiple?: boolean;
}

const FileUploadCard: React.FC<FileUploadCardProps> = ({
  isUploading,
  onFileSelect,
  className,
  multiple = false
}) => {
  return (
    <div
      className={cn(
        'w-full max-w-md mx-auto flex flex-col items-center',
        className
      )}
    >
      <button
        type="button"
        onClick={onFileSelect}
        disabled={isUploading}
        className="group relative w-56 aspect-square bg-white border-2 border-dashed border-blue-300 hover:border-blue-500 rounded-2xl overflow-hidden transition-all duration-300 flex flex-col items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        <div className="relative z-10 flex flex-col items-center justify-center p-6 text-center">
          <div className="w-16 h-16 mb-6 rounded-full bg-blue-50 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <CloudUpload
              className="w-8 h-8 text-blue-500 group-hover:text-blue-600 transition-colors"
              strokeWidth={1.5}
            />
          </div>

          <h3 className="text-xl font-medium text-gray-800 mb-2 group-hover:text-blue-700 transition-colors">
            添加素材文件
          </h3>

          <p className="text-gray-500 text-sm group-hover:text-gray-700 transition-colors">
            点击选择{multiple ? '一个或多个' : ''}视频或录音文件
          </p>
        </div>
      </button>

      <div className="mt-4 flex items-center justify-center text-sm text-gray-500">
        <Info className="w-4 h-4 mr-2" />
        <span className="text-xs">
          支持 MP4, MOV, M4A 格式，最大 1000MB{multiple ? '，可多选' : ''}
        </span>
      </div>
    </div>
  );
};

export default FileUploadCard;
