"use client";

import {
  Video,
  Mic,
  Check,
  CheckCircle2,
  Trash,
  GripVertical,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// 定义文件状态
export const FILE_STATUS = {
  WAITING: "waiting",
  UPLOADING: "uploading",
  COMPLETED: "completed",
  FAILED: "failed",
};

// 定义文件类型
export interface FileItemType {
  id: string;
  file?: File;
  name: string;
  size: number;
  type: string;
  status: string;
  progress: number;
  url?: string;
  duration?: number;
  cover?: string;
  error?: string;
}

interface FileItemProps {
  file: FileItemType;
  onDelete: (id: string) => void;
  isUploading: boolean;
  formatFileSize: (bytes: number) => string;
}

const FileItem: React.FC<FileItemProps> = ({
  file,
  onDelete,
  isUploading,
  formatFileSize,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: file.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : "auto",
  };

  const isVideo = file.type.includes("video");
  const isCompleted = file.status === FILE_STATUS.COMPLETED;
  const isFailed = file.status === FILE_STATUS.FAILED;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative overflow-hidden rounded-xl shadow-sm transition-all duration-300 flex items-center",
        isCompleted
          ? "bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-100"
          : "bg-white border border-gray-100",
        "hover:shadow-md",
      )}
    >
      <div
        {...attributes}
        {...listeners}
        className="ml-4 cursor-grab touch-none flex items-center self-stretch"
        aria-label="拖动排序"
      >
        <GripVertical className="w-5 h-5 text-gray-400" />
      </div>

      <div className="flex-1 p-4 flex items-center gap-3 min-w-0">
        <div
          className={cn(
            "w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0",
            isVideo
              ? "bg-blue-100 text-blue-600"
              : "bg-purple-100 text-purple-600",
            isCompleted && "bg-emerald-100 text-emerald-600",
          )}
        >
          {isCompleted ? (
            <CheckCircle2 className="w-6 h-6" />
          ) : isVideo ? (
            <Video className="w-6 h-6" />
          ) : (
            <Mic className="w-6 h-6" />
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-center mb-1">
            <h4 className="text-sm font-medium text-gray-800 truncate pr-2">
              {file.name}
            </h4>
            <span className="text-xs text-gray-500 whitespace-nowrap">
              {formatFileSize(file.size)}
            </span>
          </div>

          {isCompleted ? (
            <div className="flex items-center justify-between">
              <div className="text-xs text-emerald-600 flex items-center">
                <Check className="w-3.5 h-3.5 mr-1" />
                <span>上传完成</span>
              </div>
              {file.duration ? (
                <div className="text-xs text-gray-500">
                  {Math.floor(file.duration / 60)}:
                  {String(Math.floor(file.duration % 60)).padStart(2, "0")}
                </div>
              ) : null}
            </div>
          ) : isFailed ? (
            <div className="text-xs text-red-500">
              {file.error || "上传失败"}
            </div>
          ) : file.status === FILE_STATUS.UPLOADING ? (
            <div className="w-full">
              <div className="flex justify-between text-xs text-gray-500 mb-1">
                <span>正在上传...</span>
                <span>{file.progress}%</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-1.5 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-400 to-indigo-500 transition-all duration-300"
                  style={{ width: `${file.progress}%` }}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="text-xs text-amber-600 flex items-center">
                <span>等待上传</span>
              </div>
              {file.duration ? (
                <div className="text-xs text-gray-500">
                  {Math.floor(file.duration / 60)}:
                  {String(Math.floor(file.duration % 60)).padStart(2, "0")}
                </div>
              ) : null}
            </div>
          )}
        </div>

        <button
          type="button"
          onClick={() => onDelete(file.id)}
          disabled={isUploading && !isCompleted}
          className={cn(
            "ml-1 p-1.5 rounded-full transition-colors",
            isCompleted
              ? "text-gray-400 hover:text-emerald-600 hover:bg-emerald-50"
              : "text-gray-400 hover:text-red-500 hover:bg-red-50",
            "disabled:opacity-50 disabled:cursor-not-allowed",
          )}
          aria-label="删除文件"
        >
          <Trash className="w-4 h-4 text-red-400" />
        </button>
      </div>
    </div>
  );
};

export default FileItem;
