'use client';

import { useEffect, useState, useRef } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Toast, Picker, Switch, Input, Popup } from 'antd-mobile';
import { Check, ChevronDown, Loader2, X, Search } from 'lucide-react';

import { uploadMaterial, getProjectList } from '@/api/pbl';
import { useImmer } from 'use-immer';
import { cn } from '@/lib/utils';
import ClassPicker from '@/components/ClassPicker';

// 组件导入
import FileUpload from './components/FileUpload';
import type { FileItemType } from './components/FileItem';
import SelectField from './components/SelectField';
// import Script from 'next/script';

interface MediaItem {
  type: number; // 1 图片 2 视频 3 音频
  url: string;
  videoPlayType: number;
  fileSize: number;
  duration: number;
  cover: string;
  name: string;
  source: number;
}

interface SubmitData {
  observationId?: string;
  projectId: string;
  projectName?: string;
  deptId: string;
  deptName?: string;
  medias: MediaItem[];
  isMerge: number;
  contentTheme?: string; // 内容主题
  regionId?: string; // 区域ID
  regionName?: string; // 区域名称
  tagIds?: string[]; // 标签ID数组
  tagNames?: string[]; // 标签名称数组
}

export const options = [
  {
    label: '开始阶段',
    value: '1'
  },
  {
    label: '发展阶段',
    value: '2'
  },
  {
    label: '高潮阶段',
    value: '3'
  }
];

// 模拟区域数据 - 根据图片内容
export const regionData = {
  室内: [
    { label: '活动室', value: 'indoor_1' },
    { label: '教室', value: 'indoor_2' },
    { label: '洗漱区', value: 'indoor_3' },
    { label: '走廊', value: 'indoor_4' },
    { label: '楼梯', value: 'indoor_5' },
    { label: '游戏室', value: 'indoor_6' },
    { label: '666', value: 'indoor_7' },
    { label: '小山坡', value: 'indoor_8' }
  ],
  区角: [
    { label: '科学区', value: 'corner_1' },
    { label: '美工区', value: 'corner_2' },
    { label: '阅读区', value: 'corner_3' },
    { label: '建构区', value: 'corner_4' },
    { label: '益智区', value: 'corner_5' },
    { label: '生活区', value: 'corner_6' },
    { label: '语言区', value: 'corner_7' }
  ],
  户外: [
    { label: '公共区域', value: 'outdoor_1' },
    { label: '操场', value: 'outdoor_2' },
    { label: '攀爬区', value: 'outdoor_3' }
  ]
};

// 模拟标签数据 - 根据图片内容
export const tagOptions = [
  { label: '生活', value: 'tag_1' },
  { label: '学习', value: 'tag_2' },
  { label: '游戏', value: 'tag_3' },
  { label: '运动', value: 'tag_4' },
  { label: '欢食', value: 'tag_5' }
];

export default function MaterialCreatePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId = searchParams?.get('classId') || '';
  const fileUploadRef = useRef<{ getFiles: () => FileItemType[] }>(null);
  // 原始项目列表，项目列表会根据班级 id 过滤
  const [originalProjectList, setOriginalProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);

  const [projectList, setProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);
  const [isUploading, setIsUploading] = useState(false);
  const [projectPickerVisible, setProjectPickerVisible] = useState(false);
  const [regionPopupVisible, setRegionPopupVisible] = useState(false);
  const [tagPopupVisible, setTagPopupVisible] = useState(false);
  const [tagSearchText, setTagSearchText] = useState('');
  const [submitData, setSubmitData] = useImmer<SubmitData>({
    observationId: '',
    projectId: '',
    projectName: '',
    deptId: classId,
    deptName: '',
    medias: [],
    isMerge: 0,
    contentTheme: '', // 内容主题
    regionId: '',
    regionName: '',
    tagIds: [],
    tagNames: []
  });

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '添加素材';
    }
    fetchProjectList();
  }, []);

  useEffect(() => {
    if (submitData.deptId && originalProjectList.length) {
      setSubmitData((draft) => {
        draft.projectId = '';
        draft.projectName = '';
      });
      setProjectList(
        originalProjectList.filter((item) => item.deptId === submitData.deptId)
      );
    }
  }, [submitData.deptId, originalProjectList]);

  const fetchProjectList = async () => {
    const response = await getProjectList({});
    // @ts-ignore
    const data = response.list;

    setOriginalProjectList(data || []);
  };

  // 处理文件上传完成
  const handleUploadComplete = (completedFiles: FileItemType[]) => {
    console.log('🚀 ~ completedFiles:', completedFiles);
    setSubmitData((draft) => {
      draft.medias = completedFiles.map((file) => ({
        type: file.type.includes('video')
          ? 2
          : file.type.includes('audio')
            ? 3
            : file.type.includes('image')
              ? 1
              : 0,
        duration: Math.floor(file.duration || 0),
        videoPlayType: 1,
        fileSize: file.size || 0,
        source: 1,
        url: file.url || '',
        cover: file.cover || '',
        name: file.name
      }));
    });
  };

  // 处理上传状态变化
  const handleUploadStatusChange = (uploading: boolean) => {
    setIsUploading(uploading);
  };

  // 处理保存
  const handleSave = async () => {
    // 表单验证
    if (!submitData.deptId) {
      Toast.show({
        content: '请选择班级',
        position: 'top'
      });
      return;
    }

    // 获取当前文件列表
    const currentFiles = fileUploadRef.current?.getFiles() || [];
    console.log('🚀 ~ currentFiles:', currentFiles);
    // 准备最终的提交数据
    const finalSubmitData =
      submitData.medias.length === 0 && currentFiles.length > 0
        ? {
            ...submitData,
            medias: currentFiles.map((file) => ({
              type: file.type.includes('video')
                ? 2
                : file.type.includes('audio')
                  ? 3
                  : file.type.includes('image')
                    ? 1
                    : 0,
              duration: Math.floor(file.duration || 0),
              videoPlayType: 1,
              fileSize: file.size || 0,
              source: 1,
              url: file.url || '',
              cover: file.cover || '',
              name: file.name
            }))
          }
        : { ...submitData };

    // 验证是否有文件
    if (finalSubmitData.medias.length === 0) {
      Toast.show({
        content: '请选择至少一个文件',
        position: 'top'
      });
      return;
    }

    try {
      setIsUploading(true);

      // 同步更新状态（可选，用于 UI 显示）
      setSubmitData((draft) => {
        draft.medias = finalSubmitData.medias;
      });

      // 使用最新的数据进行上传
      await uploadMaterial(finalSubmitData);

      Toast.show({
        icon: 'success',
        content: '素材保存成功',
        position: 'top'
      });
      router.back();
    } catch (error) {
      console.error('素材保存失败：', error);
      Toast.show({
        icon: 'fail',
        content: '素材保存失败，请重试',
        position: 'top'
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-slate-50 to-white px-4 py-8">
      {/* 页面标题 */}
      <div className="max-w-3xl mx-auto mb-8">
        <p className="text-gray-500">
          上传视频或音频素材，系统将自动分析生成观察记录
        </p>
      </div>

      {/* 文件上传组件 */}
      <div className="max-w-3xl mx-auto">
        <FileUpload
          ref={fileUploadRef}
          onUploadComplete={handleUploadComplete}
          onUploadStatusChange={handleUploadStatusChange}
        />
      </div>

      {/* 素材信息 */}
      <div className="max-w-3xl mx-auto mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6 transition-all duration-300 hover:shadow-md">
          <h2 className="text-lg font-semibold text-gray-800 mb-5">素材设置</h2>
          <div className="grid gap-5 md:grid-cols-2">
            <div>
              <div className="block text-sm font-medium text-gray-700 mb-1.5">
                关联班级
              </div>
              <div className="relative cursor-pointer">
                <div className="w-full bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <ClassPicker
                    value={classId}
                    onChange={(classId, className) => {
                      if (classId) {
                        setSubmitData((draft) => {
                          draft.deptId = classId;
                          draft.deptName = className;
                        });
                      }
                    }}
                    placeholder="请选择班级"
                  />
                </div>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            </div>
            {!!submitData.deptId && projectList.length > 0 && (
              <SelectField
                label="关联 PBL 项目"
                value={submitData.projectName || ''}
                placeholder="请选择 PBL 项目"
                onClick={() => setProjectPickerVisible(true)}
              />
            )}

            {/* 内容主题输入框 */}
            <div className="md:col-span-2">
              <div className="block text-sm font-medium text-gray-700 mb-1.5">
                内容主题
              </div>
              <Input
                value={submitData.contentTheme}
                onChange={(value) => {
                  setSubmitData((draft) => {
                    draft.contentTheme = value;
                  });
                }}
                placeholder="描述关键信息，提升AI理解准确度"
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                style={{
                  '--color': '#374151',
                  '--font-size': '14px',
                  '--placeholder-color': '#9ca3af'
                }}
              />
            </div>

            {/* 区域选择 */}
            <SelectField
              label="观察地点"
              value={submitData.regionName || ''}
              placeholder="请选择"
              onClick={() => setRegionPopupVisible(true)}
            />

            {/* 标签选择 */}
            <SelectField
              label="观察对象"
              value={submitData.tagNames?.join(', ') || ''}
              placeholder="请选择标签"
              onClick={() => setTagPopupVisible(true)}
            />
            {submitData.medias.length > 1 && (
              <div className="flex justify-between items-center gap-2 mt-4">
                <span className="text-sm text-gray-700">
                  本次上传素材合并为一条观察记录
                </span>
                <Switch
                  checked={submitData.isMerge === 1}
                  onChange={(checked) => {
                    setSubmitData((draft) => {
                      draft.isMerge = checked ? 1 : 0;
                    });
                  }}
                />
              </div>
            )}
          </div>
        </div>

        {/* 保存按钮 */}
        <button
          type="button"
          onClick={handleSave}
          disabled={isUploading}
          className={cn(
            'w-full py-3 px-6 rounded-full font-medium text-white transition-all duration-300',
            'flex items-center justify-center space-x-2',
            'shadow-md hover:shadow-lg disabled:opacity-60 disabled:cursor-not-allowed',
            'bg-gradient-to-r from-violet-400 to-violet-500 text-white'
          )}
        >
          {isUploading ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              <span>处理中...</span>
            </>
          ) : (
            <>
              <Check className="w-5 h-5 mr-2" />
              <span>保存素材</span>
            </>
          )}
        </button>
      </div>
      {/* PBL 项目选择 */}
      <Picker
        columns={[
          projectList.map((project) => ({
            label: project.projectName,
            value: project.projectId
          }))
        ]}
        visible={projectPickerVisible}
        onClose={() => {
          setProjectPickerVisible(false);
        }}
        onConfirm={(v, c) => {
          console.log(v, c);
          if (v[0]) {
            setSubmitData((draft) => {
              draft.projectId = (c.items[0]?.value as string) || '';
              draft.projectName = (c.items[0]?.label as string) || '';
            });
          }
        }}
      />

      {/* 观察地点选择 Popup */}
      <Popup
        visible={regionPopupVisible}
        onMaskClick={() => setRegionPopupVisible(false)}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '60vh'
        }}
      >
        <div className="p-4">
          {/* 标题栏 */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-800">选择观察地点</h3>
            <button
              type="button"
              onClick={() => setRegionPopupVisible(false)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* 区域分类 */}
          {Object.entries(regionData).map(([category, items]) => (
            <div key={category} className="mb-6">
              <div className="flex items-center mb-3">
                <div className="w-1 h-4 bg-green-500 rounded mr-2" />
                <h4 className="text-base font-medium text-gray-700">
                  {category}
                </h4>
              </div>
              <div className="grid grid-cols-3 gap-3">
                {items.map((item) => (
                  <button
                    type="button"
                    key={item.value}
                    onClick={() => {
                      setSubmitData((draft) => {
                        draft.regionId = item.value;
                        draft.regionName = item.label;
                      });
                      setRegionPopupVisible(false);
                    }}
                    className={cn(
                      'px-4 py-3 rounded-lg text-sm font-medium transition-colors',
                      'border border-gray-200 hover:border-gray-300',
                      submitData.regionId === item.value
                        ? 'bg-green-50 border-green-300 text-green-700'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </Popup>

      {/* 标签选择 Popup */}
      <Popup
        visible={tagPopupVisible}
        onMaskClick={() => setTagPopupVisible(false)}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '60vh'
        }}
      >
        <div className="p-4">
          {/* 标题栏 */}
          <div className="flex items-center justify-center mb-6 relative">
            <h3 className="text-lg font-medium text-gray-800">选择标签</h3>
            <button
              type="button"
              onClick={() => setTagPopupVisible(false)}
              className="absolute right-0 p-1 rounded-full hover:bg-gray-100"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* 搜索框 */}
          <div className="relative mb-6">
            <div className="flex items-center bg-gray-50 rounded-lg px-3 py-2">
              <Search className="w-4 h-4 text-gray-400 mr-2" />
              <input
                type="text"
                placeholder="输入关键词搜索或添加自定义标签"
                value={tagSearchText}
                onChange={(e) => setTagSearchText(e.target.value)}
                className="flex-1 bg-transparent text-sm text-gray-700 placeholder-gray-400 outline-none"
              />
            </div>
          </div>

          {/* 标签列表 */}
          <div className="space-y-3 mb-6">
            {tagOptions
              .filter(
                (tag) =>
                  tagSearchText === '' ||
                  tag.label.toLowerCase().includes(tagSearchText.toLowerCase())
              )
              .map((tag) => (
                <div
                  key={tag.value}
                  className="flex items-center justify-between py-3 border-b border-gray-100"
                >
                  <span className="text-gray-700">#{tag.label}</span>
                  <button
                    type="button"
                    onClick={() => {
                      setSubmitData((draft) => {
                        const isSelected = draft.tagIds?.includes(tag.value);
                        if (isSelected) {
                          draft.tagIds = draft.tagIds?.filter(
                            (id) => id !== tag.value
                          );
                          draft.tagNames = draft.tagNames?.filter(
                            (name) => name !== tag.label
                          );
                        } else {
                          draft.tagIds = [...(draft.tagIds || []), tag.value];
                          draft.tagNames = [
                            ...(draft.tagNames || []),
                            tag.label
                          ];
                        }
                      });
                    }}
                    className={cn(
                      'w-5 h-5 rounded-full border-2 flex items-center justify-center',
                      submitData.tagIds?.includes(tag.value)
                        ? 'border-green-500 bg-green-500'
                        : 'border-gray-300'
                    )}
                  >
                    {submitData.tagIds?.includes(tag.value) && (
                      <Check className="w-3 h-3 text-white" />
                    )}
                  </button>
                </div>
              ))}
          </div>

          {/* 已添加标签 */}
          <div className="mb-6">
            <div className="text-sm text-gray-500 mb-2">
              已添加标签 ({submitData.tagIds?.length || 0})
            </div>
            {!submitData.tagIds || submitData.tagIds.length === 0 ? (
              <div className="text-center text-gray-400 py-4">暂无添加标签</div>
            ) : (
              <div className="flex flex-wrap gap-2">
                {submitData.tagNames?.map((tagName, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm"
                  >
                    #{tagName}
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={() => {
                setTagPopupVisible(false);
                setTagSearchText('');
              }}
              className="flex-1 py-3 border border-gray-300 rounded-full text-gray-700 font-medium"
            >
              取消
            </button>
            <button
              type="button"
              onClick={() => {
                setTagPopupVisible(false);
                setTagSearchText('');
              }}
              className="flex-1 py-3 bg-green-500 text-white rounded-full font-medium"
            >
              确定
            </button>
          </div>
        </div>
      </Popup>
      {/* <Script
        src="//cdn.jsdelivr.net/npm/eruda"
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
      /> */}
    </main>
  );
}
