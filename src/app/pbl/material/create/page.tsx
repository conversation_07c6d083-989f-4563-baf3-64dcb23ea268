'use client';

import { useEffect, useState, useRef } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Toast, Picker, Switch, Input } from 'antd-mobile';
import { Check, ChevronDown, Loader2 } from 'lucide-react';

import { uploadMaterial, getProjectList } from '@/api/pbl';
import { useImmer } from 'use-immer';
import { cn } from '@/lib/utils';
import ClassPicker from '@/components/ClassPicker';

// 组件导入
import FileUpload from './components/FileUpload';
import type { FileItemType } from './components/FileItem';
import SelectField from './components/SelectField';
// import Script from 'next/script';

interface MediaItem {
  type: number; // 1 图片 2 视频 3 音频
  url: string;
  videoPlayType: number;
  fileSize: number;
  duration: number;
  cover: string;
  name: string;
  source: number;
}

interface SubmitData {
  observationId?: string;
  projectId: string;
  projectName?: string;
  deptId: string;
  deptName?: string;
  medias: MediaItem[];
  isMerge: number;
  contentTheme?: string; // 内容主题
}

export const options = [
  {
    label: '开始阶段',
    value: '1'
  },
  {
    label: '发展阶段',
    value: '2'
  },
  {
    label: '高潮阶段',
    value: '3'
  }
];

export default function MaterialCreatePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId = searchParams?.get('classId') || '';
  const fileUploadRef = useRef<{ getFiles: () => FileItemType[] }>(null);
  // 原始项目列表，项目列表会根据班级 id 过滤
  const [originalProjectList, setOriginalProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);

  const [projectList, setProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);
  const [isUploading, setIsUploading] = useState(false);
  const [projectPickerVisible, setProjectPickerVisible] = useState(false);
  const [submitData, setSubmitData] = useImmer<SubmitData>({
    observationId: '',
    projectId: '',
    projectName: '',
    deptId: classId,
    deptName: '',
    medias: [],
    isMerge: 0,
    contentTheme: '' // 内容主题
  });

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '添加素材';
    }
    fetchProjectList();
  }, []);

  useEffect(() => {
    if (submitData.deptId && originalProjectList.length) {
      setSubmitData((draft) => {
        draft.projectId = '';
        draft.projectName = '';
      });
      setProjectList(
        originalProjectList.filter((item) => item.deptId === submitData.deptId)
      );
    }
  }, [submitData.deptId, originalProjectList]);

  const fetchProjectList = async () => {
    const response = await getProjectList({});
    // @ts-ignore
    const data = response.list;

    setOriginalProjectList(data || []);
  };

  // 处理文件上传完成
  const handleUploadComplete = (completedFiles: FileItemType[]) => {
    console.log('🚀 ~ completedFiles:', completedFiles);
    setSubmitData((draft) => {
      draft.medias = completedFiles.map((file) => ({
        type: file.type.includes('video')
          ? 2
          : file.type.includes('audio')
            ? 3
            : file.type.includes('image')
              ? 1
              : 0,
        duration: Math.floor(file.duration || 0),
        videoPlayType: 1,
        fileSize: file.size || 0,
        source: 1,
        url: file.url || '',
        cover: file.cover || '',
        name: file.name
      }));
    });
  };

  // 处理上传状态变化
  const handleUploadStatusChange = (uploading: boolean) => {
    setIsUploading(uploading);
  };

  // 处理保存
  const handleSave = async () => {
    // 表单验证
    if (!submitData.deptId) {
      Toast.show({
        content: '请选择班级',
        position: 'top'
      });
      return;
    }

    // 获取当前文件列表
    const currentFiles = fileUploadRef.current?.getFiles() || [];
    console.log('🚀 ~ currentFiles:', currentFiles);
    // 准备最终的提交数据
    const finalSubmitData =
      submitData.medias.length === 0 && currentFiles.length > 0
        ? {
            ...submitData,
            medias: currentFiles.map((file) => ({
              type: file.type.includes('video')
                ? 2
                : file.type.includes('audio')
                  ? 3
                  : file.type.includes('image')
                    ? 1
                    : 0,
              duration: Math.floor(file.duration || 0),
              videoPlayType: 1,
              fileSize: file.size || 0,
              source: 1,
              url: file.url || '',
              cover: file.cover || '',
              name: file.name
            }))
          }
        : { ...submitData };

    // 验证是否有文件
    if (finalSubmitData.medias.length === 0) {
      Toast.show({
        content: '请选择至少一个文件',
        position: 'top'
      });
      return;
    }

    try {
      setIsUploading(true);

      // 同步更新状态（可选，用于 UI 显示）
      setSubmitData((draft) => {
        draft.medias = finalSubmitData.medias;
      });

      // 使用最新的数据进行上传
      await uploadMaterial(finalSubmitData);

      Toast.show({
        icon: 'success',
        content: '素材保存成功',
        position: 'top'
      });
      router.back();
    } catch (error) {
      console.error('素材保存失败：', error);
      Toast.show({
        icon: 'fail',
        content: '素材保存失败，请重试',
        position: 'top'
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-slate-50 to-white px-4 py-8">
      {/* 页面标题 */}
      <div className="max-w-3xl mx-auto mb-8">
        <p className="text-gray-500">
          上传视频或音频素材，系统将自动分析生成观察记录
        </p>
      </div>

      {/* 文件上传组件 */}
      <div className="max-w-3xl mx-auto">
        <FileUpload
          ref={fileUploadRef}
          onUploadComplete={handleUploadComplete}
          onUploadStatusChange={handleUploadStatusChange}
        />
      </div>

      {/* 素材信息 */}
      <div className="max-w-3xl mx-auto mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6 transition-all duration-300 hover:shadow-md">
          <h2 className="text-lg font-semibold text-gray-800 mb-5">素材设置</h2>
          <div className="grid gap-5 md:grid-cols-2">
            <div>
              <div className="block text-sm font-medium text-gray-700 mb-1.5">
                关联班级
              </div>
              <div className="relative cursor-pointer">
                <div className="w-full bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <ClassPicker
                    value={classId}
                    onChange={(classId, className) => {
                      if (classId) {
                        setSubmitData((draft) => {
                          draft.deptId = classId;
                          draft.deptName = className;
                        });
                      }
                    }}
                    placeholder="请选择班级"
                  />
                </div>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            </div>
            {!!submitData.deptId && projectList.length > 0 && (
              <SelectField
                label="关联 PBL 项目"
                value={submitData.projectName || ''}
                placeholder="请选择 PBL 项目"
                onClick={() => setProjectPickerVisible(true)}
              />
            )}
            {submitData.medias.length > 1 && (
              <div className="flex justify-between items-center gap-2 mt-4">
                <span className="text-sm text-gray-700">
                  本次上传素材合并为一条观察记录
                </span>
                <Switch
                  checked={submitData.isMerge === 1}
                  onChange={(checked) => {
                    setSubmitData((draft) => {
                      draft.isMerge = checked ? 1 : 0;
                    });
                  }}
                />
              </div>
            )}
          </div>
        </div>

        {/* 保存按钮 */}
        <button
          type="button"
          onClick={handleSave}
          disabled={isUploading}
          className={cn(
            'w-full py-3 px-6 rounded-full font-medium text-white transition-all duration-300',
            'flex items-center justify-center space-x-2',
            'shadow-md hover:shadow-lg disabled:opacity-60 disabled:cursor-not-allowed',
            'bg-gradient-to-r from-violet-400 to-violet-500 text-white'
          )}
        >
          {isUploading ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              <span>处理中...</span>
            </>
          ) : (
            <>
              <Check className="w-5 h-5 mr-2" />
              <span>保存素材</span>
            </>
          )}
        </button>
      </div>
      {/* PBL 项目选择 */}
      <Picker
        columns={[
          projectList.map((project) => ({
            label: project.projectName,
            value: project.projectId
          }))
        ]}
        visible={projectPickerVisible}
        onClose={() => {
          setProjectPickerVisible(false);
        }}
        onConfirm={(v, c) => {
          console.log(v, c);
          if (v[0]) {
            setSubmitData((draft) => {
              draft.projectId = (c.items[0]?.value as string) || '';
              draft.projectName = (c.items[0]?.label as string) || '';
            });
          }
        }}
      />
      {/* <Script
        src="//cdn.jsdelivr.net/npm/eruda"
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
      /> */}
    </main>
  );
}
