'use client';

import React, { useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { Tabs } from 'antd-mobile';
import { getMaterialDetail, getObservationList } from '@/api/pbl';

import MediaPlayer from './components/MediaPlayer';
import MediaDetailCard from './components/MediaDetailCard';
import RecordItem from '../../record/list/components/RecordItem';

export default function MediaDetailPage() {
  const searchParams = useSearchParams();
  const mediaId = searchParams?.get('id') || '';

  const [currentTab, setCurrentTab] = useState('details');

  // 获取媒体详情
  const { data: mediaData, isLoading } = useQuery({
    queryKey: ['materialDetail', mediaId],
    queryFn: () => getMaterialDetail(mediaId),
    enabled: !!mediaId
  });

  // 获取相关记录
  const { data: relatedRecords = [], refetch } = useQuery({
    queryKey: ['relatedRecords', mediaId],
    queryFn: async () => {
      const res = await getObservationList({
        mediaId,
        page: 1,
        perPage: 10
      });
      return res.list || [];
    },
    enabled: !!mediaId
  });

  if (isLoading || !mediaData) {
    return (
      <div className="flex items-center justify-center h-screen bg-slate-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto" />
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-8">
      <main className="max-w-4xl mx-auto px-4 py-6">
        <div className="mb-8">
          <MediaPlayer
            url={mediaData.url}
            cover={mediaData.cover}
            type={mediaData.type}
            duration={mediaData.duration}
            name={mediaData.name}
          />
        </div>

        <div className="mb-6">
          <Tabs activeKey={currentTab} onChange={(key) => setCurrentTab(key)}>
            <Tabs.Tab title="详细信息" key="details" />
            <Tabs.Tab title="相关记录" key="records" />
          </Tabs>
        </div>

        {currentTab === 'details' && <MediaDetailCard mediaData={mediaData} />}

        {currentTab === 'records' && (
          <div className="space-y-4">
            {relatedRecords.length > 0 ? (
              relatedRecords.map((record) => (
                <RecordItem key={record.id} record={record} />
              ))
            ) : (
              <div className="bg-white rounded-xl p-8 text-center">
                <p className="text-gray-500">暂无相关记录</p>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
}
