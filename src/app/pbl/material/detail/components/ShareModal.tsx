'use client';

import React from 'react';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  mediaName: string;
  mediaUrl: string;
}

const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  mediaName,
  mediaUrl
}) => {
  if (!isOpen) return null;

  const shareOptions = [
    {
      id: 'wechat',
      name: '微信',
      icon: (
        <svg
          className="w-5 h-5 text-blue-400"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22.5 12c0-5.799-4.701-10.5-10.5-10.5S1.5 6.201 1.5 12s4.701 10.5 10.5 10.5 10.5-4.701 10.5-10.5z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M12 8.25v7.5M8.25 12h7.5"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      bgColor: 'bg-blue-100'
    },
    {
      id: 'dingtalk',
      name: '钉钉',
      icon: (
        <svg
          className="w-5 h-5 text-green-400"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.5 12h9M16.5 7.5v9"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M12 22.5c5.799 0 10.5-4.701 10.5-10.5S17.799 1.5 12 1.5 1.5 6.201 1.5 12s4.701 10.5 10.5 10.5z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      bgColor: 'bg-green-100'
    },
    {
      id: 'email',
      name: '邮件',
      icon: (
        <svg
          className="w-5 h-5 text-purple-400"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M21 8.25c0-2.485-2.015-4.5-4.5-4.5h-9c-2.485 0-4.5 2.015-4.5 4.5v7.5c0 2.485 2.015 4.5 4.5 4.5h9c2.485 0 4.5-2.015 4.5-4.5v-7.5z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M3.75 9.75l4.083 3.334c1.599 1.3 3.866 1.3 5.464 0L17.25 9.75"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      bgColor: 'bg-purple-100'
    },
    {
      id: 'copyLink',
      name: '复制链接',
      icon: (
        <svg
          className="w-5 h-5 text-amber-400"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.5 7.5h.75c1.495 0 2.242 0 2.825.322.583.323 1.04.916 1.955 2.102l.375.487c.186.242.279.363.381.363.102 0 .195-.121.381-.363l.375-.487c.915-1.186 1.372-1.779 1.955-2.102.583-.322 1.33-.322 2.825-.322h.75M7.5 7.5h-.75c-1.495 0-2.242 0-2.825.322-.583.323-1.04.916-1.955 2.102l-1.72 2.23c-.372.483-.557.724-.557 1.001 0 .277.186.518.557 1.001l1.72 2.23c.915 1.186 1.372 1.779 1.955 2.102.583.322 1.33.322 2.825.322h9c1.495 0 2.242 0 2.825-.322.583-.323 1.04-.916 1.955-2.102l1.72-2.23c.372-.483.557-.724.557-1.001 0-.277-.186-.518-.557-1.001l-1.72-2.23c-.915-1.186-1.372-1.779-1.955-2.102-.583-.322-1.33-.322-2.825-.322h-.75"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      bgColor: 'bg-amber-100'
    }
  ];

  const handleCopyLink = () => {
    navigator.clipboard
      .writeText(mediaUrl)
      .then(() => {
        alert('链接已复制到剪贴板');
        onClose();
      })
      .catch((err) => {
        console.error('无法复制链接：', err);
      });
  };

  const handleShare = (option: string) => {
    if (option === 'copyLink') {
      handleCopyLink();
    } else {
      // 实际应用中这里会有不同平台的分享逻辑
      console.log(`分享到 ${option}`, mediaUrl);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full p-6 animate-fade-in">
        <h3 className="text-xl font-bold mb-4 text-gray-800">分享媒体</h3>

        <div className="space-y-4 mb-6">
          {shareOptions.map((option) => (
            <div
              key={option.id}
              className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => handleShare(option.id)}
            >
              <div
                className={`w-10 h-10 rounded-full ${option.bgColor} flex items-center justify-center`}
              >
                {option.icon}
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-800">{option.name}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={onClose}
            className="px-5 py-2 bg-gray-200 hover:bg-gray-300 rounded-lg font-medium transition-colors text-gray-700"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShareModal;
