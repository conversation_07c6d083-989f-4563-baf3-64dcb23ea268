'use client';

import { FileText, Clock, Calendar, Building, Trash2 } from 'lucide-react';

import StatusBadge from '@/app/pbl/detail/components/StatusBadge';
import { Toast, Dialog } from 'antd-mobile';
import ShareModal from './ShareModal';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { deleteMaterial } from '@/api/pbl';

interface MediaDetailCardProps {
  mediaData: any;
}

const MediaDetailCard: React.FC<MediaDetailCardProps> = ({ mediaData }) => {
  const router = useRouter();
  const {
    handleStatus,
    mediaId,
    name,
    fileSize,
    duration,
    createTime,
    dept,
    createUser
  } = mediaData;
  const [showShareModal, setShowShareModal] = useState(false);

  const formatFileSize = (size: string): string => {
    const sizeInMB = Number.parseInt(size) / (1024 * 1024);
    return `${sizeInMB.toFixed(2)} MB`;
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}分${secs}秒`;
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleDownload = () => {
    if (!mediaData) return;

    // 创建一个临时链接并触发下载
    const link = document.createElement('a');
    link.href = mediaData.url;
    link.download = mediaData.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    Toast.show({
      content: '开始下载文件',
      position: 'bottom'
    });
  };

  const handleShare = () => {
    setShowShareModal(true);
  };

  const handleBookmark = () => {
    Toast.show({
      content: '已添加到收藏',
      position: 'bottom'
    });
  };

  const handleDelete = () => {
    Dialog.confirm({
      content: '删除后记录将无法恢复',
      cancelText: '取消',
      confirmText: '确认删除',
      onConfirm: async () => {
        await deleteMaterial(mediaId);
        Toast.show({
          icon: 'success',
          content: '删除成功',
          position: 'bottom'
        });
        router.back();
      }
    });
  };

  return (
    <div className="bg-white rounded-xl overflow-hidden shadow-sm">
      <div className="p-6">
        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-6">
          {/* 左侧信息 */}

          <div className="flex-1">
            <div className="mb-2 flex items-center justify-between gap-2">
              <StatusBadge status={handleStatus} />
            </div>
            <div className="flex flex-col gap-3 mb-6">
              <h1 className="text-xl font-bold break-all	 text-gray-800">
                {name || '未命名文件'}
              </h1>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                  <FileText className="w-5 h-5 text-purple-500" />
                </div>
                <div>
                  <p className="text-gray-500 text-sm">文件大小</p>
                  <p className="font-medium text-gray-800">
                    {formatFileSize(fileSize)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                  <Clock className="w-5 h-5 text-blue-500" />
                </div>
                <div>
                  <p className="text-gray-500 text-sm">时长</p>
                  <p className="font-medium text-gray-800">
                    {formatDuration(duration)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-emerald-100 flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-emerald-500" />
                </div>
                <div>
                  <p className="text-gray-500 text-sm">创建时间</p>
                  <p className="font-medium text-gray-800">
                    {formatDate(createTime)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <Building className="w-5 h-5 text-amber-500" />
                </div>
                <div>
                  <p className="text-gray-500 text-sm">班级</p>
                  <p className="font-medium text-gray-800">
                    {dept?.name || '-'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧信息 */}
          <div className="">
            <h3 className="text-lg font-medium mb-4 text-gray-800">创建者</h3>
            <div className="flex items-center gap-4 mb-6">
              <img
                src={createUser.avatar}
                alt={createUser.name}
                className="w-14 h-14 rounded-full object-cover border-2 border-purple-100"
              />
              <div>
                <p className="font-medium text-gray-800">{createUser.name}</p>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center ">
            {handleStatus === 0 && (
              <div className="flex items-center justify-center border border-red-400 w-12 h-12 rounded-full">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete();
                  }}
                >
                  <Trash2 className="h-5 w-5 text-red-400" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 分享模态框 */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        mediaName={mediaData.name}
        mediaUrl={mediaData.url}
      />
    </div>
  );
};

export default MediaDetailCard;
