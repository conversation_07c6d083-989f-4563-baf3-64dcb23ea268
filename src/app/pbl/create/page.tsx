'use client';

import { But<PERSON>, DatePicker, Form, Input, TextArea, Toast } from 'antd-mobile';
import type { FormInstance } from 'antd-mobile/es/components/form';
import { format } from 'date-fns';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';

import {
  createProject,
  getProjectDetail,
  updatePblContent,
  updateProject,
} from '@/api/pbl';
import ClassPicker from '@/components/ClassPicker';

import { defaultPblData } from './data';

interface ProjectResponse {
  deptId: string;
  projectId: string;
  projectName: string;
  description: string;
  learningGoals: string;
  drivingQuestion: string;
  startDate: string;
  endDate: string;
}

interface FormValues {
  projectName: string;
  description: string;
  learningGoals?: string;
  drivingQuestion?: string;
  startDate: Date;
  endDate: Date;
}

export default function CreateProject() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('ProjectId');
  const form = useRef<FormInstance>(null);
  const [projectData, setProjectData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [classError, setClassError] = useState(false);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '创建项目';
    }

    if (projectId) {
      getProjectDetail(projectId).then((response) => {
        // @ts-ignore
        const res = response as ProjectResponse;
        form.current?.setFieldsValue({
          projectName: res.projectName,
          description: res.description || '',
          learningGoals: res.learningGoals || '',
          drivingQuestion: res.drivingQuestion || '',
          startDate: new Date(res.startDate),
          endDate: new Date(res.endDate),
          deptId: res.deptId,
        });
        setProjectData({
          deptId: res.deptId,
          projectId: res.projectId,
          projectName: res.projectName,
          description: res.description || '',
          learningGoals: res.learningGoals || '',
          drivingQuestion: res.drivingQuestion || '',
          startDate: new Date(res.startDate),
          endDate: new Date(res.endDate),
          ageRange: '',
          evaluationCriteria: '',
          resources: '',
        });
      });
    }
  }, [projectId, setProjectData]);

  const onClassChange = useCallback((classId: string, className: string) => {
    form.current?.setFieldsValue({
      deptId: classId,
    });
    setClassError(false);
  }, []);

  const onFinish = async (values: FormValues) => {
    console.log('🚀 ~ values:111', values);
    console.log('🚀 ~ form:', form);
    // 如果开始时间大于结束时间，则提示错误
    if (values.startDate > values.endDate) {
      Toast.show({
        content: '开始时间不能大于结束时间',
      });
      return;
    }

    const data = {
      ...values,
      startDate: format(values.startDate, 'yyyy-MM-dd'),
      endDate: format(values.endDate, 'yyyy-MM-dd'),
    };
    setLoading(true);
    try {
      if (projectId) {
        await updateProject(projectId, {
          ...data,
          nodes: [],
        }).finally(() => {
          setLoading(false);
        });
        router.replace(`/pbl/detail?ProjectId=${projectId}`);
      } else {
        const response = await createProject({
          project: {
            ...data,
          },
        }).finally(() => {
          setLoading(false);
        });
        // @ts-ignore
        const res = response.project as ProjectResponse;
        setProjectData({
          deptId: res.deptId,
          projectId: res.projectId,
          projectName: values.projectName,
          description: values.description,
          learningGoals: values.learningGoals || '',
          drivingQuestion: values.drivingQuestion || '',
          startDate: values.startDate,
          endDate: values.endDate,
          ageRange: '',
          evaluationCriteria: '',
          resources: '',
        });
        await updatePblContent(res.projectId, {
          content: JSON.stringify(defaultPblData),
        });
        router.replace(`/pbl/detail?ProjectId=${res.projectId}`);
      }
    } catch (error) {
      Toast.show({
        content: '保存失败',
      });
    }
  };

  const handleSubmit = async () => {
    try {
      await form.current?.validateFields();
      setClassError(false);
      form.current?.submit();
    } catch (e) {
      if (!form.current?.getFieldValue('deptId')) {
        setClassError(true);
      }
    }
  };

  return (
    <Form
      ref={form}
      onFinish={onFinish}
      footer={
        <Button block color="primary" loading={loading} onClick={handleSubmit}>
          保存
        </Button>
      }
    >
      <Form.Item
        name="projectName"
        label="项目名称"
        rules={[{ required: true }]}
      >
        <Input placeholder="请输入项目名称" />
      </Form.Item>

      <Form.Item
        name="description"
        label="项目描述"
        rules={[{ required: true }]}
      >
        <TextArea placeholder="请输入项目描述" />
      </Form.Item>

      {/* <Form.Item
        name="learningGoals"
        label="学习目标"
        rules={[{ required: true }]}
      >
        <TextArea placeholder="请输入学习目标" />
      </Form.Item>

      <Form.Item
        name="drivingQuestion"
        label="驱动性问题"
        rules={[{ required: true }]}
      >
        <TextArea placeholder="请输入驱动性问题" />
      </Form.Item> */}
      <div className="hidden">
        <Form.Item
          name="deptId"
          label="驱动性问题"
          rules={[{ required: true }]}
        />
      </div>
      <div
        className="adm-list-item"
        onClick={() => {
          const action = { current: { open: () => {} } };
          action.current.open();
        }}
      >
        <div className="adm-form-item-label -ml-2">
          <span className="text-red-500">*</span>关联班级
        </div>
        <div
          className={`adm-list-item-content w-full border-t !border-t-white pb-2 pt-1 ${classError ? 'text-red-500' : ''}`}
        >
          <ClassPicker
            onChange={onClassChange}
            placeholder="请选择班级"
            value={projectData.deptId}
          />
          <div className="adm-list-item-content-arrow">
            <svg
              viewBox="64 64 896 896"
              focusable="false"
              data-icon="right"
              width="1em"
              height="1em"
              fill="currentColor"
              aria-hidden="true"
            >
              <path d="M765.7 486.8L314.9 134.7A8 8 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z" />
            </svg>
          </div>
        </div>
      </div>

      <Form.Item
        name="startDate"
        label="开始时间"
        trigger="onConfirm"
        rules={[{ required: true }]}
        onClick={(_, action) => {
          action?.current?.open();
        }}
      >
        <DatePicker
          onConfirm={(val) => {
            form.current?.setFieldsValue({
              startDate: val,
            });
          }}
        >
          {(value) => (value ? format(value, 'yyyy-MM-dd') : '请选择开始时间')}
        </DatePicker>
      </Form.Item>

      <Form.Item
        name="endDate"
        label="结束时间"
        trigger="onConfirm"
        rules={[{ required: true }]}
        onClick={(_, action) => {
          action?.current?.open();
        }}
      >
        <DatePicker
          onConfirm={(val) => {
            form.current?.setFieldsValue({
              endDate: val,
            });
          }}
        >
          {(value) => (value ? format(value, 'yyyy-MM-dd') : '请选择结束时间')}
        </DatePicker>
      </Form.Item>
    </Form>
  );
}
