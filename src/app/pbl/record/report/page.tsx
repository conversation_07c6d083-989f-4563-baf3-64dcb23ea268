'use client';

import { motion } from 'framer-motion';
// 导入新的图标
import { ClipboardList, UserCheck } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Card } from '@/components/ui/card';

const ReportEntryPage = () => {
  if (typeof window !== 'undefined') {
    document.title = '报告统计';
  }
  const router = useRouter();
  const reportModules = [
    {
      title: '班级报告统计',
      description: '对各个班级整体情况分析',
      // 更新图标为 ClipboardList
      icon: ClipboardList,
      path: '/pbl/record/report/classList',
      color: 'text-blue-500', // 可以考虑为不同模块设置不同颜色
    },
    {
      title: '幼儿报告统计',
      description: '对单个幼儿情况分析',
      // 更新图标为 UserCheck
      icon: UserCheck,
      path: '/pbl/record/report/studentList',
      color: 'text-purple-500', // 可以考虑为不同模块设置不同颜色
    },
  ];
  const handleClick = (item: any) => {
    router.push(item.path);
  };
  return (
    // 添加 flex justify-center 使内容在页面水平居中
    <div className="container mx-auto flex min-h-[calc(100vh-theme(spacing.16))]  px-4 py-8">
      <div className=" w-full  lg:max-w-4xl">
        {reportModules.map((item, index) => (
          <motion.div
            key={item.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }} // 调整动画时长
          >
            <Card
              // 调整内边距、背景、阴影和 hover 效果
              className="group mb-4 cursor-pointer overflow-hidden rounded-lg bg-white p-4 shadow-md transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl"
              onClick={() => handleClick(item)}
            >
              {/* 调整图标和标题的布局与样式 */}
              <div className="mb-4 flex items-center">
                {/* 给图标添加背景，使用模块自身的颜色 */}
                <div
                  className={`bg- mr-4 rounded-full p-3${item.color.split('-')[1]}-100`}
                >
                  {/* 动态背景色 */}
                  <item.icon className={`size-6 ${item.color}`} />
                </div>
                {/* 调整标题字体大小和粗细 */}
                <h2 className="text-base font-bold text-gray-800">
                  {item.title}
                </h2>
              </div>
              {/* 调整描述文字颜色和大小 */}
              <p className="text-sm text-gray-600">{item.description}</p>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default ReportEntryPage;
