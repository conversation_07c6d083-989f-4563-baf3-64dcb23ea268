'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Di<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>rror<PERSON>lock,
  FloatingB<PERSON>ble,
  InfiniteScroll,
  PullToRefresh,
  Space,
  SwipeAction,
  Toast,
} from 'antd-mobile';
import { CheckCircle, Filter, Plus, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import { deleteClassReport, getClassReportList } from '@/api/pbl';

import type { FilterValues } from '../studentList/components/FilterDrawer';
import FilterDrawer from '../studentList/components/FilterDrawer';

interface ClassReport {
  id: string;
  reportId: string;
  deptName: string;
  studentCnt: number;
  observationCnt: number;
}

interface ApiResponse {
  list: ClassReport[];
}

const Page = () => {
  const router = useRouter();
  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const [selectedClassName, setSelectedClassName] = useState<string | null>(
    null,
  );
  const [revealedIds, setRevealedIds] = useState<Set<number | string>>(
    new Set(),
  );
  const [filterVisible, setFilterVisible] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [page, setPage] = useState(1);
  const pageRef = useRef(1);
  const [pageSize] = useState(20);
  const [data, setData] = useState<ClassReport[]>([]);
  const isFirstRender = useRef(true);

  const setPageRef = (val: number) => {
    pageRef.current = val;
    setPage(val);
  };

  useEffect(() => {
    setPageRef(1);
    loadData();
  }, [selectedClass, startDate, endDate]);

  async function loadData() {
    try {
      const result = await getClassReportList({
        page: pageRef.current,
        pageSize,
        deptId: selectedClass,
        startDate,
        endDate,
      });
      if (pageRef.current === 1) {
        setData(result.list);
      } else {
        setData((prevData) => [...prevData, ...result.list]);
      }
      setPageRef(pageRef.current + 1);
      setHasMore(result.list.length >= pageSize);
    } catch (error) {
      Toast.show({ content: '加载数据失败', icon: 'fail' });
    }
  }

  const handleRefresh = async () => {
    setPageRef(1);
    await loadData();
  };

  const handleFilterConfirm = (filters: FilterValues) => {
    setSelectedClass(filters.classId);
    setSelectedClassName(filters.className);
    setStartDate(filters.startDate);
    setEndDate(filters.endDate);
    setFilterVisible(false);
  };

  const handleFilterReset = () => {
    // 重置父组件中的筛选状态
    setSelectedClass(null);
    setSelectedClassName(null);
    setStartDate(null);
    setEndDate(null);
  };
  const handleAddReport = () => {
    router.push('/pbl/record/report/generate?type=2');
  };
  const InfiniteScrollContent = ({
    hasMore,
    empty,
  }: {
    hasMore?: boolean;
    empty?: boolean;
  }) => {
    return (
      <>
        {hasMore ? (
          <>
            <span>Loading</span>
            <DotLoading />
          </>
        ) : empty ? (
          <ErrorBlock status="empty" title="暂无报告内容" description="" />
        ) : (
          <span>～数据已经见底～</span>
        )}
      </>
    );
  };

  const handleDelete = async (reportId: string) => {
    try {
      await deleteClassReport(reportId);
      Toast.show({ content: '删除成功', icon: 'success' });
      handleRefresh(); // Refresh the list after deletion
    } catch (error) {
      Toast.show({ content: '删除失败', icon: 'fail' });
    }
  };

  const showDeleteConfirm = (reportId: string) => {
    Dialog.confirm({
      content: '确定要删除这份报告吗？',
      onConfirm: () => handleDelete(reportId),
    });
  };
  const handleReveal = (id: number | string) => {
    setRevealedIds((prev) => new Set(prev).add(id));
  };

  const handleClose = (id: number | string) => {
    setRevealedIds((prev) => {
      const next = new Set(prev);
      next.delete(id);
      return next;
    });
  };
  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {/* 筛选区域 */}
      <div
        style={{
          padding: '10px 16px',
          borderBottom: '1px solid #eee',
        }}
      >
        <div className="flex flex-row items-center justify-between">
          <div className="text-base">班级报告列表</div>
          <Button onClick={() => setFilterVisible(true)} size="small">
            {/* 点击按钮打开抽屉 */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                color: '#333',
              }}
            >
              <Filter size={14} />
              <span className="text-sm">筛选</span>
            </div>
          </Button>
        </div>
        {/* 显示当前筛选条件 */}
        <div
          style={{
            fontSize: '12px',
            color: '#999',
            marginTop: '5px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}
        >
          {selectedClassName && (
            <span className="mr-2">班级: {selectedClassName} </span>
          )}
          {endDate && startDate && (
            <span className="mr-2">
              时间: {startDate.toLocaleDateString()}至:
              {endDate.toLocaleDateString()}
            </span>
          )}
        </div>
      </div>
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          background: '#f2f2f2',
          padding: '10px 16px 0',
        }}
      >
        {/* 列表区域 */}
        <PullToRefresh onRefresh={handleRefresh}>
          {data.map((item) => (
            <SwipeAction
              key={item.id}
              style={{
                background: '#f2f2f2',
                borderRadius: '8px',
                overflow: 'hidden',
                marginBottom: '8px',
              }}
              closeOnAction={false}
              rightActions={[
                {
                  key: 'delete',
                  text: '删除',
                  color: 'danger',
                  onClick: () => showDeleteConfirm(item.reportId),
                },
              ]}
              onActionsReveal={() => handleReveal(item.id!)}
              onClose={() => handleClose(item.id!)}
            >
              <Card
                className="mb-3 !py-2"
                style={{
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                  marginBottom: '0',
                  borderTopLeftRadius: 8,
                  borderBottomLeftRadius: 8,
                  borderTopRightRadius: revealedIds.has(item.id!) ? 0 : 8,
                  borderBottomRightRadius: revealedIds.has(item.id!) ? 0 : 8,
                  transition: 'border-radius 0.2s',
                }}
                onClick={() => {
                  router.push(`classDetail?reportId=${item.reportId}`);
                }}
              >
                <div className="flex items-center">
                  <div
                    style={{
                      backgroundColor: '#1677ff',
                      color: '#fff',
                      borderRadius: '50%',
                      width: '55px',
                      height: '55px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    {item.deptName?.slice(0, 3)}
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="mb-4 text-lg  font-medium">
                      {item.deptName}
                    </div>
                    <div className="mt-1 text-sm text-gray-500">
                      <Space>
                        <span className="mb-2">
                          <User size={14} className="mr-1 inline" />
                          幼儿总数：{item.studentCnt}
                        </span>
                        <span>
                          <CheckCircle size={14} className="mr-1 inline" />
                          观察记录数：{item.observationCnt}
                        </span>
                      </Space>
                    </div>
                  </div>
                </div>
              </Card>
            </SwipeAction>
          ))}
          <InfiniteScroll loadMore={() => loadData()} hasMore={hasMore}>
            <InfiniteScrollContent
              hasMore={hasMore}
              empty={data.length === 0}
            />
          </InfiniteScroll>
        </PullToRefresh>
        <FloatingBubble
          style={{
            '--initial-position-bottom': '80px', // 调整底部距离
            '--initial-position-right': '24px', // 调整右侧距离
            '--size': '48px', // 调整按钮大小
          }}
          onClick={handleAddReport} // 设置点击事件
        >
          <Plus size={24} />
        </FloatingBubble>
        <FilterDrawer
          visible={filterVisible}
          onClose={() => setFilterVisible(false)}
          onConfirm={handleFilterConfirm} // 传递确认回调
          onReset={handleFilterReset} // 传递重置回调
          type={2} // 1:学生,2:班级
          initialFilters={{
            // 传递当前的筛选状态作为初始值
            classId: selectedClass,
            className: selectedClassName,
            startDate,
            endDate,
            student: '',
          }}
        />
      </div>
    </div>
  );
};

export default Page;
