'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CascadePicker,
  DatePicker,
  Form,
  Radio,
  Steps,
  Toast,
} from 'antd-mobile';
import { format, subDays } from 'date-fns';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getMyClassList } from '@/api/invite';
import { createClassReport, createStudentReport } from '@/api/pbl';
import StudentSelector from '@/components/ChooseStudent';

type CascadePickerOption = {
  label: string;
  value: string;
  children?: CascadePickerOption[];
};
const { Step } = Steps;

export default function ReportPage() {
  if (typeof window !== 'undefined') {
    document.title = '幼儿评价报告生成';
  }
  const searchParams = useSearchParams();
  const type = Number(searchParams.get('type'));
  const studentId = searchParams.get('studentId');
  const studentName = searchParams.get('studentName');
  const classId = searchParams.get('classId');
  const className = searchParams.get('className');
  const [form] = Form.useForm();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedClassName, setSelectedClassName] = useState(className);
  const [classListData, setClassListData] = useState([]);

  const [classPickerVisible, setClassPickerVisible] = useState(false);

  const [startDateVisible, setStartDateVisible] = useState(false);
  const [endDateVisible, setEndDateVisible] = useState(false);
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date>(new Date());

  const [reportType, setReportType] = useState<'student' | 'class'>(
    type === 1 ? 'student' : 'class',
  );
  useEffect(() => {
    form.setFieldsValue({
      student: studentId,
      class: classId,
      reportType: 'student',
    });
  }, [studentId, classId]);
  useEffect(() => {
    initClassPickerData();
  }, []);
  function convertToCascadePickerOptions(treeArray) {
    return treeArray.map((item) => {
      const option: CascadePickerOption = {
        label: item.name,
        value: item.id.toString(),
      };
      if (item.children && item.children.length > 0) {
        option.children = convertToCascadePickerOptions(item.children);
      }
      return option;
    });
  }

  const initClassPickerData = () => {
    getMyClassList().then((res: any) => {
      if (Array.isArray(res.children)) {
        setClassListData(convertToCascadePickerOptions(res.children));
      }
    });
  };
  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      const { student, class: selectedClassId } = values; // 获取表单值

      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');
      console.log('endDateStr: ', endDateStr);
      console.log('startDateStr: ', startDateStr);
      if (reportType === 'student') {
        await createStudentReport({
          studentId: student,
          startDate: startDateStr,
          endDate: endDateStr,
        });
        Toast.show('报告已经创建');
      } else {
        await createClassReport({
          deptId: selectedClassId,
          startDate: startDateStr,
          endDate: endDateStr,
        });
        Toast.show('报告已经创建');
      }
      router.back();
    } catch (error) {
      Toast.show(
        `生成报告失败:${error.response?.data?.message || error.message}`,
      );
    } finally {
      setLoading(false);
    }
  };

  const handleStudentSelect = (value: any[]) => {
    if (value.length === 0) {
      setSelectedClass('');
      setSelectedClassName('');
      form.setFieldsValue({ student: '' });
      return;
    }
    const student = value[0];
    setSelectedClass(student.classId);
    setSelectedClassName(student.className);
    form.setFieldsValue({ student: student?.studentId });
    Toast.show({
      content: `已选择学生：${student.className} - ${student?.studentName || ''}`,
      position: 'bottom',
    });
  };

  const handleClassConfirm = (val: string[], extend: { items: any[] }) => {
    if (extend.items.length > 0) {
      const selected = extend.items[extend.items.length - 1];
      setSelectedClass(selected.value);
      setSelectedClassName(selected.label);
      form.setFieldsValue({ class: selected.value, student: undefined }); // 设置班级值，清空学生值
      setClassPickerVisible(false);
      Toast.show({
        content: `已选择班级：${selected.label}`,
        position: 'bottom',
      });
    } else {
      Toast.show('请选择一个具体的班级');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="mx-auto max-w-3xl">
        <Steps current={0} className="mb-8">
          <Step
            title={
              reportType === 'student'
                ? '选择学生和时间范围'
                : '选择班级和时间范围'
            }
            description="设置报告基本信息"
          />
          <Step title="生成评价报告" description="系统自动生成报告" />
        </Steps>

        {/* 表单卡片样式优化 */}
        <Card
          className="overflow-hidden rounded-xl shadow-sm"
          headerStyle={{
            background: '#f8fafc',
            padding: '0 26px',
          }}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            className="[&.adm-form]:!border-0 [&.adm-form]:!border-none"
            style={{
              '--border-bottom': 'none',
              '--border-top': 'none',
              '--border-inner': 'none',
            }}
            onValuesChange={(_, allValues) => {}}
          >
            <Form.Item label="报告类型" className="mb-2">
              <Radio.Group
                value={reportType}
                onChange={(value) => {
                  setReportType(value as 'student' | 'class');
                  setSelectedClass('');
                  setSelectedClassName('');
                  form.resetFields(['student', 'class']);
                }}
              >
                <Radio
                  value="student"
                  style={{
                    '--icon-size': '20px',
                    '--font-size': '14px',
                    '--gap': '8px',
                    marginRight: '16px',
                  }}
                >
                  学生报告
                </Radio>
                <Radio
                  value="class"
                  style={{
                    '--icon-size': '20px',
                    '--font-size': '14px',
                    '--gap': '8px',
                  }}
                >
                  班级报告
                </Radio>
              </Radio.Group>
            </Form.Item>

            {reportType === 'student' ? (
              <Form.Item
                name="student"
                label={<span className="text-gray-700">选择学生</span>}
                rules={[{ required: true, message: '请选择学生' }]}
              >
                <StudentSelector
                  onMultiSelect={handleStudentSelect}
                  hideTitle
                  multiple
                />
              </Form.Item>
            ) : (
              <Form.Item
                name="class" // 表单字段名改为 'class'
                label={<span className="text-gray-700 ">选择班级</span>}
                rules={[{ required: true, message: '请选择班级' }]}
              >
                <Button
                  onClick={() => setClassPickerVisible(true)}
                  className="w-full rounded-lg border border-gray-200 bg-white px-4 py-2.5 text-left !text-sm text-gray-700 transition "
                  style={{
                    height: '44px',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {selectedClassName || '请选择班级'}
                </Button>
              </Form.Item>
            )}

            <Form.Item
              label={<span className="text-gray-700">报告时间</span>}
              rules={[{ required: true, message: '请选择时间范围' }]}
            >
              <div className="flex items-center space-x-3">
                <div
                  className="flex-1 cursor-pointer rounded-lg border border-gray-200 bg-white px-4 py-2.5 text-gray-700 transition hover:border-blue-500"
                  onClick={() => setStartDateVisible(true)}
                >
                  {startDate.toLocaleDateString()}
                </div>
                <span className="text-gray-500">至</span>
                <div
                  className="flex-1 cursor-pointer rounded-lg border border-gray-200 bg-white px-4 py-2.5 text-gray-700 transition hover:border-blue-500"
                  onClick={() => setEndDateVisible(true)}
                >
                  {endDate.toLocaleDateString()}
                </div>
              </div>
            </Form.Item>

            <Form.Item className="mb-0">
              <Button
                type="submit" // 明确 type="submit"
                block // 让按钮宽度充满父容器
                color="primary"
                loading={loading}
                className="h-11 rounded-lg text-lg font-medium"
              >
                开始生成报告
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>

      {/* 日期选择器 DatePicker */}
      <DatePicker
        visible={startDateVisible}
        value={startDate}
        title="选择开始日期"
        min={new Date('2023-01-01')}
        max={endDate}
        onClose={() => setStartDateVisible(false)}
        onConfirm={(val) => {
          setStartDate(val);
          setStartDateVisible(false);
        }}
      />

      <DatePicker
        visible={endDateVisible}
        value={endDate}
        title="选择结束日期"
        min={startDate}
        max={new Date()}
        onClose={() => setEndDateVisible(false)}
        onConfirm={(val) => {
          setEndDate(val);
          setEndDateVisible(false);
        }}
      />
      <CascadePicker
        title="选择班级"
        options={classListData}
        visible={classPickerVisible} // 使用新的 state
        onClose={() => {
          setClassPickerVisible(false);
        }}
        onConfirm={handleClassConfirm} // 使用新的确认处理函数
      />
    </div>
  );
}
