'use client';

import { Grid } from 'antd-mobile';

// Define types for our data structure
interface DistributionItem {
  category: string;
  students: string[];
}

interface AbilityItem {
  name: string;
  distribution: DistributionItem[];
}

interface AbilityDistributionGridProps {
  abilityDistribution: AbilityItem[];
}

const AbilityDistributionGrid: React.FC<AbilityDistributionGridProps> = ({
  abilityDistribution,
}) => {
  const columns = ['能力', '未发现', '0-5次', '5次及以上'];
  const columnCount = 4;

  return (
    <div className="overflow-hidden rounded-lg border border-border">
      <Grid columns={columnCount} className="bg-muted">
        {columns.map((column, index) => (
          <Grid.Item
            key={`header-${index}`}
            className="border-b border-r border-border p-1 text-sm font-medium last:border-r-0"
          >
            <div className="flex min-h-[24px] items-center justify-center">
              {column}
            </div>
          </Grid.Item>
        ))}
      </Grid>

      {abilityDistribution.map((ability, rowIndex) => (
        <Grid
          key={`row-${rowIndex}`}
          columns={columnCount}
          className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-background'}
        >
          <Grid.Item className="border-b border-r border-border p-1 text-sm">
            <div className="flex min-h-[24px] items-center justify-center">
              {ability.name}
            </div>
          </Grid.Item>

          {ability.distribution.map((item, cellIndex) => (
            <Grid.Item
              key={`cell-${rowIndex}-${cellIndex}`}
              className="border-b border-r border-border p-1 text-sm last:border-r-0"
            >
              <div className="flex min-h-[24px] items-center justify-center text-xs">
                {item.students.join(', ')}
              </div>
            </Grid.Item>
          ))}
        </Grid>
      ))}
    </div>
  );
};

export default AbilityDistributionGrid;
