'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r,
  DotLoading,
  Grid,
  List,
  Modal,
  Space,
  Tag,
  Toast
} from 'antd-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import {
  Legend,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer
} from 'recharts';

import {
  getClassReportDetail,
  getObservationDimensions,
  updateClassReport
} from '@/api/pbl';
import { share } from '@/utils';

interface Student {
  studentId: string;
  studentName: string;
  happinessAvg: number;
  engagementAvg: number;
  observationCnt: number;
}

interface AbilityKey {
  communication: number;
  collaboration: number;
  creativity: number;
  criticalThinking: number;
  citizenshipCharacterEducation: number;
}

interface FieldKey {
  healthScore: number;
  languageScore: number;
  societyScore: number;
  scienceScore: number;
  artsScore: number;
}

interface DomainOverview {
  subDomain: string;
  abilityName: string;
  level1: { pct: number };
  level2: { pct: number };
  level3: { pct: number };
  none: { pct: number };
  [key: string]: { pct: number } | string;
}

interface DomainReport {
  id: string;
  domain: string;
  overview: DomainOverview[];
  highlights: string;
  issues: string;
  supportCases?: string;
}

interface ClassReport {
  deptName: string;
  startDate: string;
  endDate: string;
  evaluator: string;
  observationCnt: number;
  studentCnt: number;
  radarChart: FieldKey;
  deepLearningCounters: AbilityKey;
  happinessEngagement: {
    happinessAvg: number;
    engagementAvg: number;
  };
}

interface DeepLearningStatistics {
  label: string;
  students: Array<{ id: string; name: string }>;
}

interface ReportData {
  classReport: ClassReport;
  classReportStudentList: Student[];
  classReportDomainList: DomainReport[];
  deepLearningStatistics: Record<keyof AbilityKey, DeepLearningStatistics[]>;
  observationStats: {
    domainDistributionText: string;
    timeTrendText: string;
  };
}

interface BarChartDataItem {
  name: string;
  name1: string;
  name2: string;
  ability: number;
  field: number;
  abilityKey: keyof AbilityKey;
  fieldKey: keyof FieldKey;
}

interface RadarChartItem {
  subject: string;
  score: number;
  fullMark: number;
  field: keyof ClassReport['radarChart'];
}

export default function ClassReportPage() {
  const searchParams = useSearchParams();
  const reportId = searchParams.get('reportId');
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportModal, setExportModal] = useState(false);
  const [pdfUrl, setPdfUrl] = useState('');
  const [barChartData, setBarChartData] = useState<BarChartDataItem[]>([]);
  const [assessmentItems] = useState<string[]>([
    '3-6岁指南',
    '幸福感与参与度',
    '深度学习能力'
  ]);
  const [dimensions, setDimensions] = useState<string[]>([]);
  const [dimensionsList] = useState([
    {
      dimensions: '2',
      label: '1. 《3-6岁儿童学习与发展指南》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。'
    },
    {
      dimensions: '3',
      label: '2. 《幸福感和参与度指标》',
      value: '评估指标包括：2个领域，10个子领域，共10项评估指标。'
    },
    {
      dimensions: '1',
      label: '3. 《深度学习能力指标》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。'
    }
  ]);
  const [abilityCounts] = useState([
    {
      label: '沟通能力',
      value: 'communication'
    },
    {
      label: '协作能力',
      value: 'collaboration'
    },
    {
      label: '创造力',
      value: 'creativity'
    },
    {
      label: '批判性思维',
      value: 'criticalThinking'
    },
    {
      label: '公民意识与品格教育',
      value: 'citizenshipCharacterEducation'
    }
  ]);
  const [distribution] = useState([
    {
      label: '水平1',
      value: 'level1',
      color: 'warning'
    },
    {
      label: '水平2',
      value: 'level2',
      color: 'success'
    },
    {
      label: '水平3',
      value: 'level3',
      color: 'success'
    },
    {
      label: '未观察到',
      value: 'none',
      color: 'danger'
    }
  ]);
  const [happinessAvgObj, setHappinessAvgObj] = useState<
    Record<number, string[]>
  >({
    1: [],
    2: [],
    3: [],
    4: [],
    5: []
  });
  const [engagementAvgObj, setEngagementAvgObj] = useState<
    Record<number, string[]>
  >({
    1: [],
    2: [],
    3: [],
    4: [],
    5: []
  });
  const [fieldRadarChart, setFieldRadarChart] = useState<RadarChartItem[]>([
    { subject: '健康', score: 0, fullMark: 100, field: 'healthScore' },
    { subject: '语言', score: 0, fullMark: 100, field: 'languageScore' },
    { subject: '社会', score: 0, fullMark: 100, field: 'societyScore' },
    { subject: '科学', score: 0, fullMark: 100, field: 'scienceScore' },
    { subject: '艺术', score: 0, fullMark: 100, field: 'artsScore' }
  ]);
  const [editingDomain, setEditingDomain] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<{
    highlights: string;
    issues: string;
    supportCases: string;
  }>({
    highlights: '',
    issues: '',
    supportCases: ''
  });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!reportId) {
        setError('报告ID不能为空');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const resReportData = await getClassReportDetail({ reportId });

        if (!resReportData) {
          throw new Error('获取报告数据失败');
        }
        // Update radar chart data
        const fieldRadarChartTemp = fieldRadarChart.map((item) => ({
          ...item,
          score: resReportData.classReport?.radarChart[item.field] ?? 0
        }));
        setFieldRadarChart(fieldRadarChartTemp);

        // Update happiness and engagement data
        const happinessAvgObjTemp: Record<number, string[]> = {
          1: [],
          2: [],
          3: [],
          4: [],
          5: []
        };
        const engagementAvgObjTemp: Record<number, string[]> = {
          1: [],
          2: [],
          3: [],
          4: [],
          5: []
        };

        for (const item of resReportData.classReportStudentList ?? []) {
          if (item.happinessAvg && happinessAvgObjTemp?.[item.happinessAvg]) {
            happinessAvgObjTemp[item.happinessAvg].push(item.studentName);
          }
          if (
            item.engagementAvg &&
            engagementAvgObjTemp?.[item.engagementAvg]
          ) {
            engagementAvgObjTemp[item.engagementAvg].push(item.studentName);
          }
        }

        // Update bar chart data
        const dataBar: BarChartDataItem[] = [
          {
            name: '创造力/科学',
            name1: '创造力',
            name2: '科学',
            ability: 0,
            field: 0,
            abilityKey: 'creativity',
            fieldKey: 'scienceScore'
          },
          {
            name: '协作能力/社会',
            name1: '协作能力',
            name2: '社会',
            ability: 0,
            field: 0,
            abilityKey: 'collaboration',
            fieldKey: 'societyScore'
          },
          {
            name: '批判性思维/艺术',
            name1: '批判性思维',
            name2: '艺术',
            ability: 0,
            field: 0,
            abilityKey: 'criticalThinking',
            fieldKey: 'artsScore'
          },
          {
            name: '沟通能力/语言',
            name1: '沟通能力',
            name2: '语言',
            ability: 0,
            field: 0,
            abilityKey: 'communication',
            fieldKey: 'languageScore'
          },
          {
            name: '公民意识品格教育/健康',
            name1: '公民意识品格教育',
            name2: '健康',
            ability: 0,
            field: 0,
            abilityKey: 'citizenshipCharacterEducation',
            fieldKey: 'healthScore'
          }
        ];

        for (const item of dataBar) {
          const abilityValue =
            resReportData.classReport?.deepLearningCounters[item.abilityKey];
          const fieldValue =
            resReportData.classReport?.radarChart[item.fieldKey];
          item.ability = abilityValue ?? 0;
          item.field = fieldValue ?? 0;
        }

        setBarChartData(dataBar);
        setHappinessAvgObj(happinessAvgObjTemp);
        setEngagementAvgObj(engagementAvgObjTemp);
        setReportData(resReportData);
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取报告数据失败');
        console.error('Error fetching report data:', err);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    getObservationDimensions().then((res: any) => {
      setDimensions(res.dimensions);
    });
  }, []);

  const handleExport = async () => {
    if (!reportId) {
      Toast.show({
        content: '报告ID不能为空',
        position: 'center'
      });
      return;
    }

    try {
      setExportLoading(true);
      const token = Cookies.get('Authorization');
      if (!token) {
        throw new Error('未登录或登录已过期');
      }

      const currentUrl = `${window.location.origin}/pbl/record/report/classDetail/export?reportId=${reportId}`;
      const response = await fetch(
        `/api/record/export?url=${encodeURIComponent(currentUrl)}&reportKey=${token}`
      );

      if (!response.ok) {
        throw new Error('PDF生成失败');
      }

      const data = await response.json();
      if (!data.url) {
        throw new Error('未获取到PDF链接');
      }

      setPdfUrl(data.url);
      setExportModal(true);
    } catch (error) {
      Toast.show({
        content:
          error instanceof Error ? error.message : '导出报告失败，请稍后重试',
        position: 'center'
      });
    } finally {
      setExportLoading(false);
    }
  };

  const handleShare = () => {
    share(pdfUrl);
  };

  const handleEdit = (
    domain: string,
    data: { highlights: string; issues: string; supportCases?: string }
  ) => {
    setEditingDomain(domain);
    setEditForm({
      highlights: data.highlights,
      issues: data.issues,
      supportCases: data.supportCases || ''
    });
  };

  const handleSave = async (domain: string) => {
    if (!reportId) return;

    try {
      setSaving(true);
      await updateClassReport({
        reportId,
        title: reportData?.classReport?.deptName || '',
        evaluator: reportData?.classReport?.evaluator || '',
        classReportDomainList:
          reportData?.classReportDomainList.map((item) =>
            item.domain === domain ? { ...item, ...editForm } : item
          ) || []
      });

      // Refresh data
      const resReportData = await getClassReportDetail({ reportId });
      setReportData(resReportData);
      setEditingDomain(null);
      Toast.show({
        content: '保存成功',
        position: 'center'
      });
    } catch (err) {
      Toast.show({
        content: err instanceof Error ? err.message : '保存失败',
        position: 'center'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingDomain(null);
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
        <DotLoading color="primary" />
        <span className="ml-2 mt-2 text-gray-500">正在打开评价报告...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
        <div className="mb-4 text-red-500">{error}</div>
        <Button color="primary" onClick={() => window.location.reload()}>
          重试
        </Button>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
        <div className="mb-4 text-gray-500">未找到报告数据</div>
        <Button color="primary" onClick={() => window.location.reload()}>
          重试
        </Button>
      </div>
    );
  }

  return (
    <div
      style={{
        padding: '12px',
        backgroundColor: '#f0f2f5',
        minHeight: '100vh'
      }}
    >
      <div className="mb-4 flex items-center justify-between">
        <h1
          style={{
            fontSize: '1.6em',
            color: '#1677ff'
          }}
        >
          {reportData.classReport?.deptName} 幼儿评价报告
        </h1>
        <Button
          color="primary"
          fill="solid"
          className="min-w-[100px] rounded-full px-8 shadow-md"
          loading={exportLoading}
          onClick={handleExport}
        >
          导出报告
        </Button>
      </div>

      <Card
        title="报告基本信息"
        style={{ marginBottom: '15px', borderRadius: '8px' }}
        headerStyle={{ borderBottom: 'none', color: '#333' }}
        bodyStyle={{ paddingTop: '5px' }}
      >
        <List
          style={{
            '--border-inner': 'none',
            '--border-top': 'none',
            '--border-bottom': 'none'
          }}
        >
          <List.Item
            extra={
              <Tag color="default" fill="outline">
                {reportData.classReport?.startDate}-
                {reportData.classReport?.endDate}
              </Tag>
            }
          >
            评估时间范围
          </List.Item>
          <List.Item extra={reportData.classReport?.evaluator}>
            评估人
          </List.Item>
          <List.Item extra={reportData.classReport?.deptName}>班级</List.Item>
          <List.Item
            extra={
              <Tag color="success" fill="outline">
                {reportData.classReport?.observationCnt}
              </Tag>
            }
          >
            观察记录条数
          </List.Item>
          <List.Item
            extra={
              <Tag color="primary" fill="outline">
                {reportData.classReport?.studentCnt}
              </Tag>
            }
          >
            评估幼儿人数
          </List.Item>
          <List.Item>评估内容项</List.Item>
          <List.Item>
            <Space wrap style={{ '--gap': '8px' }}>
              {assessmentItems.map((item) => (
                <Tag key={item} color="primary" fill="solid">
                  {item}
                </Tag>
              ))}
            </Space>
          </List.Item>
        </List>
      </Card>

      <Card
        title="本报告依据以下标准进行评估"
        style={{ marginBottom: '15px', borderRadius: '8px' }}
        headerStyle={{ borderBottom: 'none', color: '#333' }}
        bodyStyle={{ paddingTop: '5px' }}
      >
        <p
          style={{ color: '#666', fontSize: '0.9em', margin: '0 0 10px 16px' }}
        />
        <List
          style={{
            '--border-inner': 'none',
            '--border-top': 'none',
            '--border-bottom': 'none'
          }}
        >
          {dimensionsList.map((item) => {
            if (dimensions.includes(item.dimensions)) {
              return (
                <List.Item
                  key={item.dimensions}
                  description={
                    <span style={{ color: '#888', fontSize: '0.85em' }}>
                      评估指标包括：5个领域，12个子领域，共36项评估指标。
                    </span>
                  }
                >
                  {item.label}
                </List.Item>
              );
            }
          })}
        </List>
      </Card>

      <Card
        title="班级发展情况"
        style={{ marginBottom: '15px', borderRadius: '8px' }}
        headerStyle={{ borderBottom: 'none', color: '#333' }}
        bodyStyle={{ paddingTop: '5px' }}
      >
        <Collapse
          defaultActiveKey=""
          accordion
          style={
            {
              '--border-top': 'none',
              '--border-bottom': 'none'
            } as unknown as React.CSSProperties
          }
        >
          {dimensions.includes('2') && (
            <Collapse.Panel
              key="guide"
              title={
                <span style={{ fontWeight: '500' }}>
                  一、3-6岁儿童学习与发展指南
                </span>
              }
            >
              <div style={{ height: '300px', width: '100%' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart
                    cx="50%"
                    cy="50%"
                    data={fieldRadarChart}
                    outerRadius="80%"
                  >
                    <PolarGrid stroke="#e5e7eb" />
                    <PolarAngleAxis
                      dataKey="subject"
                      tick={{ fill: '#666', fontSize: 14 }}
                    />
                    <PolarRadiusAxis
                      angle={18}
                      domain={[0, 100]}
                      tick={{ fill: '#999', fontSize: 10 }}
                    />
                    <Radar
                      name="当前表现"
                      dataKey="score"
                      stroke="#3b82f6"
                      fill="#3b82f6"
                      fillOpacity={0.4}
                    />
                    <Legend />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
              <Divider style={{ margin: '20px 0', color: '#999' }}>
                各领域发展评估详细说明
              </Divider>
              {reportData.classReportDomainList.map((item, index) => (
                <Card
                  key={item.id}
                  title={`${item.domain}领域`}
                  style={{
                    marginBottom: '12px',
                    borderRadius: '6px',
                    backgroundColor: '#fafafa'
                  }}
                  headerStyle={{
                    fontSize: '1em',
                    color: '#1677ff',
                    borderBottom: '1px solid #eee'
                  }}
                >
                  <Collapse
                    defaultActiveKey=""
                    accordion
                    style={
                      {
                        '--adm-color-border': 'transparent',
                        '--adm-border-color': 'transparent',
                        '--adm-color-background': '#fafafa',
                        '--padding-left': '0px',
                        '--padding-right': '10px'
                      } as unknown as React.CSSProperties
                    }
                  >
                    <Collapse.Panel
                      key={item.domain}
                      title={<span className="text-sm">各目标项评估</span>}
                      style={
                        {
                          '--border-top': 'none',
                          '--border-bottom': 'none',
                          '--border-inner': 'none',
                          '--padding-left': '0'
                        } as unknown as React.CSSProperties
                      }
                    >
                      {item.overview.map((domain, i) => {
                        return (
                          <div className="mb-4 space-y-4" key={i.toString()}>
                            <p className="text-sm font-semibold">
                              {i + 1}、{domain.subDomain}|{domain.abilityName}
                            </p>
                            <ul>
                              <li>
                                <span className="text-sm">
                                  班级整体发展水平分布
                                </span>
                                <Space wrap style={{ '--gap': '6px' }}>
                                  {distribution.map((dist, j) => {
                                    const val = domain[dist.value];
                                    if (
                                      val &&
                                      typeof val === 'object' &&
                                      'pct' in val
                                    ) {
                                      return (
                                        <Tag
                                          key={j.toString()}
                                          color={dist.color}
                                        >
                                          {` ${(val as { pct: number }).pct}%${dist.label}`}
                                        </Tag>
                                      );
                                    }
                                    return null;
                                  })}
                                </Space>
                              </li>
                            </ul>
                          </div>
                        );
                      })}
                    </Collapse.Panel>
                  </Collapse>
                  <ul>
                    <li style={{ margin: '5px 0' }}>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">发展亮点：</span>
                        {editingDomain === item.domain ? (
                          <div className="flex gap-2">
                            <Button
                              size="mini"
                              onClick={handleCancel}
                              style={{ fontSize: '12px' }}
                            >
                              取消
                            </Button>
                            <Button
                              size="mini"
                              color="primary"
                              fill="outline"
                              loading={saving}
                              onClick={() => handleSave(item.domain)}
                              style={{ fontSize: '12px' }}
                            >
                              保存
                            </Button>
                          </div>
                        ) : (
                          <div
                            onClick={() =>
                              handleEdit(item.domain, {
                                highlights: item.highlights,
                                issues: item.issues,
                                supportCases: item.supportCases
                              })
                            }
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                            </svg>
                          </div>
                        )}
                      </div>
                      {editingDomain === item.domain ? (
                        <textarea
                          className="mt-1 w-full rounded border border-gray-300 p-2 text-sm"
                          value={editForm.highlights}
                          onChange={(e) =>
                            setEditForm((prev) => ({
                              ...prev,
                              highlights: e.target.value
                            }))
                          }
                          rows={7}
                        />
                      ) : (
                        <span className="mt-1 block cursor-pointer rounded p-2 text-sm text-[#555] hover:bg-gray-50">
                          {item.highlights}
                        </span>
                      )}
                    </li>
                    <li style={{ margin: '5px 0' }}>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">突出问题：</span>
                      </div>
                      {editingDomain === item.domain ? (
                        <textarea
                          className="mt-1 w-full rounded border border-gray-300 p-2 text-sm"
                          value={editForm.issues}
                          onChange={(e) =>
                            setEditForm((prev) => ({
                              ...prev,
                              issues: e.target.value
                            }))
                          }
                          rows={7}
                        />
                      ) : (
                        <span className="mt-1 block cursor-pointer rounded p-2 text-sm text-[#ff4d4f] hover:bg-gray-50">
                          {item.issues}
                        </span>
                      )}
                    </li>
                    {item.supportCases && (
                      <li style={{ margin: '5px 0' }}>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">支持建议：</span>
                        </div>
                        {editingDomain === item.domain ? (
                          <textarea
                            className="mt-1 w-full rounded border border-gray-300 p-2 text-sm"
                            value={editForm.supportCases}
                            onChange={(e) =>
                              setEditForm((prev) => ({
                                ...prev,
                                supportCases: e.target.value
                              }))
                            }
                            rows={7}
                          />
                        ) : (
                          <span className="mt-1 block cursor-pointer rounded p-2 text-sm text-[#555] hover:bg-gray-50">
                            {item.supportCases}
                          </span>
                        )}
                      </li>
                    )}
                  </ul>
                </Card>
              ))}
            </Collapse.Panel>
          )}
          {dimensions.includes('3') && (
            <Collapse.Panel
              key="wellbeing"
              title={
                <span style={{ fontWeight: '500' }}>
                  二、幸福感和参与度指标
                </span>
              }
            >
              <List
                style={{
                  '--border-inner': 'none',
                  '--border-top': 'none',
                  '--border-bottom': 'none',
                  '--font-size': '0.95em'
                }}
              >
                <List.Item
                  extra={
                    <Tag color="warning" fill="solid">
                      {
                        reportData.classReport?.happinessEngagement
                          ?.engagementAvg
                      }
                    </Tag>
                  }
                >
                  幸福感平均值 (1-5分)
                </List.Item>
                <List.Item
                  extra={
                    <Tag color="success" fill="solid">
                      {
                        reportData.classReport?.happinessEngagement
                          ?.happinessAvg
                      }
                    </Tag>
                  }
                >
                  参与度平均值 (1-5分)
                </List.Item>
              </List>
              <Divider style={{ margin: '15px 0', color: '#999' }}>
                幸福感指标详情
              </Divider>
              {Object.entries(happinessAvgObj).map(([key, students]) => (
                <List
                  key={`wb-${key}`}
                  header={
                    <span
                      style={{ color: '#555' }}
                    >{`${key} - 等级 ${key}`}</span>
                  }
                  style={
                    {
                      '--border-inner': 'none',
                      '--border-top': 'none',
                      '--border-bottom': 'none',
                      '--font-size': '0.9em',
                      marginBottom: '8px'
                    } as unknown as React.CSSProperties
                  }
                >
                  <List.Item>
                    {students.length > 0 ? (
                      <Space
                        wrap
                        style={
                          { '--gap': '6px' } as unknown as React.CSSProperties
                        }
                      >
                        {students.map((student) => (
                          <Tag key={student} color="warning" fill="outline">
                            {student}
                          </Tag>
                        ))}
                      </Space>
                    ) : (
                      <Tag color="weak" fill="outline">
                        无
                      </Tag>
                    )}
                  </List.Item>
                </List>
              ))}
              <Divider style={{ margin: '15px 0', color: '#999' }}>
                参与度指标详情
              </Divider>
              {Object.entries(engagementAvgObj).map(([key, students]) => (
                <List
                  key={`en-${key}`}
                  header={
                    <span
                      style={{ color: '#555' }}
                    >{`${key} - 等级 ${key}`}</span>
                  }
                  style={
                    {
                      '--border-inner': 'none',
                      '--border-top': 'none',
                      '--border-bottom': 'none',
                      '--font-size': '0.9em',
                      marginBottom: '8px'
                    } as unknown as React.CSSProperties
                  }
                >
                  <List.Item>
                    {students.length > 0 ? (
                      <Space
                        wrap
                        style={
                          { '--gap': '6px' } as unknown as React.CSSProperties
                        }
                      >
                        {students.map((student) => (
                          <Tag key={student} color="success" fill="outline">
                            {student}
                          </Tag>
                        ))}
                      </Space>
                    ) : (
                      <Tag color="weak" fill="outline">
                        无
                      </Tag>
                    )}
                  </List.Item>
                </List>
              ))}
            </Collapse.Panel>
          )}
          {dimensions.includes('1') && (
            <Collapse.Panel
              key="deeplearning"
              title={
                <span style={{ fontWeight: '500' }}>
                  三、项目式学习中幼儿深度学习能力表
                </span>
              }
            >
              <Divider style={{ margin: '15px 0', color: '#999' }}>
                各项能力总次数
              </Divider>
              <List
                header=""
                style={{
                  '--border-inner': 'none',
                  '--border-top': 'none',
                  '--border-bottom': 'none',
                  '--font-size': '0.95em'
                }}
              >
                {abilityCounts.map((ability, index) => (
                  <List.Item
                    key={ability.value}
                    extra={
                      <Tag color="primary" fill="outline">
                        {
                          reportData.classReport?.deepLearningCounters?.[
                            ability.value
                          ]
                        }
                      </Tag>
                    }
                  >
                    {ability.label}
                  </List.Item>
                ))}
              </List>
              <Divider style={{ margin: '15px 0', color: '#999' }}>
                各能力发现次数分布
              </Divider>
              {abilityCounts.map((abilityData) => (
                <Card
                  key={abilityData.value}
                  title={abilityData.label}
                  style={{
                    marginBottom: '12px',
                    borderRadius: '6px',
                    backgroundColor: '#fafafa'
                  }}
                  headerStyle={{
                    fontSize: '1em',
                    color: '#1677ff',
                    borderBottom: '1px solid #eee'
                  }}
                  bodyStyle={{ padding: '10px 12px' }}
                >
                  {(
                    reportData.deepLearningStatistics as Record<
                      string,
                      DeepLearningStatistics[]
                    >
                  )[abilityData.value as keyof AbilityKey]?.map((dist) => (
                    <List
                      key={dist.label}
                      header={
                        <span style={{ color: '#555' }}>{dist.label}</span>
                      }
                      style={
                        {
                          '--border-inner': 'none',
                          '--border-top': 'none',
                          '--border-bottom': 'none',
                          '--font-size': '0.9em',
                          marginBottom: '5px'
                        } as unknown as React.CSSProperties
                      }
                    >
                      <List.Item>
                        <Space
                          wrap
                          style={
                            { '--gap': '6px' } as unknown as React.CSSProperties
                          }
                        >
                          {dist.students.length > 0 ? (
                            dist.students.map((student) => (
                              <Tag
                                key={student.id}
                                color="default"
                                fill="outline"
                              >
                                {student.name}
                              </Tag>
                            ))
                          ) : (
                            <Tag color="weak" fill="outline">
                              无
                            </Tag>
                          )}
                        </Space>
                      </List.Item>
                    </List>
                  ))}
                </Card>
              ))}
            </Collapse.Panel>
          )}
        </Collapse>
      </Card>
      <Card
        title="班级幼儿观察记录统计"
        style={{ marginBottom: '15px', borderRadius: '8px' }}
        headerStyle={{ borderBottom: 'none', color: '#333' }}
        bodyStyle={{ paddingTop: '5px' }}
      >
        {/*  

        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={barChartData}
            layout="vertical"
            margin={{
              top: 20,
              right: 10,
              left: 15,
              bottom: 20
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis
              dataKey="name"
              type="category"
              width={0}
              tick={false}
              axisLine={false}
            />
            <Tooltip />
            <Legend verticalAlign="top" height={36} iconType="circle" />
            {dimensions.includes('1') && (
              <Bar
                name="能力"
                dataKey="ability"
                fill="#4F8AFA"
                radius={[0, 10, 10, 0]}
                maxBarSize={40}
                activeBar={
                  <Rectangle
                    fill="#4F8AFA"
                    stroke="#2B5DB9"
                    radius={[10, 10, 10, 10]}
                  />
                }
                label={({ x, y, width, height, value, index }) => {
                  const data = barChartData[index];
                  if (!data || data.name1 == null || value == null)
                    return <g />;
                  const labelText = `${data.name1}: ${value}`;
                  const labelWidth: number = labelText.length * 7;
                  const padding: number = 8;
                  let labelX: number;
                  let fillColor: string;
                  if (width > labelWidth + padding * 2) {
                    // 显示在柱子内部右侧
                    labelX = x + width - padding;
                    fillColor = '#fff';
                    return (
                      <g>
                        <text
                          x={labelX}
                          y={y + height / 2}
                          alignmentBaseline="middle"
                          textAnchor="end"
                          fill={fillColor}
                          fontSize={12}
                        >
                          {labelText}
                        </text>
                      </g>
                    );
                  }
                  // 显示在柱子外部
                  labelX = x + width + padding;
                  fillColor = '#333';
                  return (
                    <g>
                      <text
                        x={labelX}
                        y={y + height / 2}
                        alignmentBaseline="middle"
                        textAnchor="start"
                        fill={fillColor}
                        fontSize={12}
                      >
                        {labelText}
                      </text>
                    </g>
                  );
                }}
              />
            )}
            {dimensions.includes('2') && (
              <Bar
                name="领域"
                dataKey="field"
                fill="#FFB74D"
                radius={[0, 10, 10, 0]}
                maxBarSize={40}
                activeBar={
                  <Rectangle
                    fill="#FFB74D"
                    stroke="#FF9800"
                    radius={[10, 10, 10, 10]}
                  />
                }
                label={({ x, y, width, height, value, index }) => {
                  const data = barChartData[index];
                  if (!data || data.name2 == null || value == null)
                    return <g />;
                  const labelText = `${data.name2}: ${value}`;
                  const labelWidth: number = labelText.length * 7;
                  const padding: number = 8;
                  let labelX: number;
                  let fillColor: string;
                  if (width > labelWidth + padding * 2) {
                    // 显示在柱子内部右侧
                    labelX = x + width - padding;
                    fillColor = '#fff';
                    return (
                      <g>
                        <text
                          x={labelX}
                          y={y + height / 2}
                          alignmentBaseline="middle"
                          textAnchor="end"
                          fill={fillColor}
                          fontSize={12}
                        >
                          {labelText}
                        </text>
                      </g>
                    );
                  }
                  // 显示在柱子外部
                  labelX = x + width + padding;
                  fillColor = '#333';
                  return (
                    <g>
                      <text
                        x={labelX}
                        y={y + height / 2}
                        alignmentBaseline="middle"
                        textAnchor="start"
                        fill={fillColor}
                        fontSize={12}
                      >
                        {labelText}
                      </text>
                    </g>
                  );
                }}
              />
            )}
          </BarChart>
        </ResponsiveContainer>
        <p
          style={{
            color: '#666',
            fontSize: '0.9em',
            margin: '0 16px 8px 16px',
            lineHeight: 1.5
          }}
        >
          {reportData.observationStats?.domainDistributionText}
        </p>
        <p
          style={{
            color: '#666',
            fontSize: '0.9em',
            margin: '0 16px 15px 16px',
            lineHeight: 1.5
          }}
        >
          {reportData.observationStats?.timeTrendText}
        </p>
 */}
        <Divider style={{ margin: '15px 0', color: '#999' }}>
          幼儿观察记录次数
        </Divider>
        <Grid columns={3} gap={10} style={{ padding: '0 5px' }}>
          {reportData.classReportStudentList
            ?.sort((a, b) => b.observationCnt - a.observationCnt)
            .map((student) => (
              <Grid.Item key={student.studentId}>
                <Card
                  bodyStyle={{
                    padding: '10px 5px',
                    textAlign: 'center',
                    borderRadius: '4px',
                    backgroundColor: '#fff',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
                  }}
                >
                  <div
                    style={{
                      fontSize: '0.9em',
                      color: '#333',
                      marginBottom: '5px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}
                  >
                    {student.studentName}
                  </div>
                  <Tag
                    color="processing"
                    fill="outline"
                    style={{ fontSize: '0.8em' }}
                  >
                    {student.observationCnt}次
                  </Tag>
                </Card>
              </Grid.Item>
            ))}
        </Grid>
      </Card>
      <Modal
        visible={exportModal}
        closeOnAction
        closeOnMaskClick
        content={
          <div className="relative py-4">
            <div
              className="absolute right-0 top-0 cursor-pointer p-2"
              onClick={() => setExportModal(false)}
              aria-label="Close modal"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <title>Close</title>
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </div>
            <div className="mb-4 text-center text-base font-medium">
              报告导出成功
            </div>
            <div className="mb-6 text-center text-sm text-gray-500">
              您可以复制链接或直接分享报告
            </div>

            <div className="flex gap-3 px-2">
              <CopyToClipboard
                text={pdfUrl}
                onCopy={(text: string, result: boolean) => {
                  Toast.show({ content: result ? '复制成功' : '复制失败' });
                }}
              >
                <Button
                  color="primary"
                  className="flex-1 rounded-lg border-blue-600 bg-blue-50 text-blue-600"
                  fill="outline"
                >
                  复制链接
                </Button>
              </CopyToClipboard>
              <Button
                color="primary"
                className="flex-1 rounded-lg bg-blue-600"
                onClick={handleShare}
              >
                分享报告
              </Button>
            </div>
          </div>
        }
        actions={[]}
      />
    </div>
  );
}
