'use client';
import { Button, Dialog, Form, Input, List, Toast } from 'antd-mobile';
import { Plus, Edit, Trash2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';

const TagManagePage = () => {
  const [tags, setTags] = useState<{ id: number; name: string }[]>([]);
  const [form] = Form.useForm();

  useEffect(() => {
    const fetchTags = async () => {
      // Mock data
      const mockTags = [
        { id: 1, name: '自由游戏' },
        { id: 2, name: '集体教学' },
        { id: 3, name: '小组合作' },
        { id: 4, name: '一对一辅导' },
        { id: 5, name: '角色扮演' },
        { id: 6, name: '建构操作' },
        { id: 7, name: '科学探究' },
        { id: 8, name: '艺术创作' },
        { id: 9, name: '运动挑战' },
        { id: 10, name: '主题探究' }
      ];
      setTags(mockTags);
    };
    fetchTags();
  }, []);

  const onAdd = () => {
    form.resetFields();
    Dialog.show({
      title: '添加标签',
      content: (
        <Form form={form} layout="horizontal">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      ),
      closeOnAction: true,
      actions: [
        [
          {
            key: 'cancel',
            text: '取消'
          },
          {
            key: 'submit',
            text: '提交',
            bold: true,
            onClick: async () => {
              try {
                const values = await form.validateFields();
                const newTag = { id: Date.now(), name: values.name };
                setTags([...tags, newTag]);
                Toast.show({
                  icon: 'success',
                  content: '添加成功'
                });
              } catch (error) {
                console.log(error);
              }
            }
          }
        ]
      ]
    });
  };

  const onEdit = (tag: { id: number; name: string }) => {
    form.resetFields();
    form.setFieldsValue({ name: tag.name });
    Dialog.show({
      title: '编辑标签',
      content: (
        <Form form={form} layout="horizontal">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      ),
      closeOnAction: true,
      actions: [
        [
          {
            key: 'cancel',
            text: '取消'
          },
          {
            key: 'submit',
            text: '提交',
            bold: true,
            onClick: async () => {
              try {
                const values = await form.validateFields();
                const updatedTags = tags.map((t) =>
                  t.id === tag.id ? { ...t, name: values.name } : t
                );
                setTags(updatedTags);
                Toast.show({
                  icon: 'success',
                  content: '更新成功'
                });
              } catch (error) {
                console.log(error);
              }
            }
          }
        ]
      ]
    });
  };

  const onDelete = (tag: { id: number; name: string }) => {
    Dialog.confirm({
      content: `确定要删除标签“${tag.name}”吗？`,
      onConfirm: async () => {
        const updatedTags = tags.filter((t) => t.id !== tag.id);
        setTags(updatedTags);
        Toast.show({
          icon: 'success',
          content: '删除成功'
        });
      }
    });
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-lg font-medium">标签管理</h1>
        <Button size="small" color="primary" onClick={onAdd}>
          <Plus className="w-4 h-4" />
        </Button>
      </div>
      <List>
        {tags.map((tag) => (
          <List.Item
            key={tag.id}
            extra={
              <div className="flex gap-2">
                <Button
                  size="small"
                  color="primary"
                  fill="none"
                  onClick={() => onEdit(tag)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  size="small"
                  color="danger"
                  fill="none"
                  onClick={() => onDelete(tag)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            }
          >
            {tag.name}
          </List.Item>
        ))}
      </List>
    </div>
  );
};

export default TagManagePage;
