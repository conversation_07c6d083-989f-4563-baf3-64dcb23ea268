"use client";

import { List, Switch, Toast } from "antd-mobile";
import { Book<PERSON><PERSON>, Heart, Brain } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

interface ScaleConfig {
	id: string;
	title: string;
	description: string;
	icon: React.ReactNode;
	enabled: boolean;
}

// 默认配置数据
const DEFAULT_SCALE_CONFIGS: ScaleConfig[] = [
	{
		id: "guide",
		title: "3-6 岁儿童学习与发展指南",
		description: "基于国家教育部发布的儿童发展指南进行评估",
		icon: <BookOpen className="w-5 h-5 text-blue-500" />,
		enabled: true,
	},
	{
		id: "luwen",
		title: "幸福感与参与度",
		description: "评估儿童在活动中的幸福感和参与程度",
		icon: <Heart className="w-5 h-5 text-red-500" />,
		enabled: true,
	},
	{
		id: "deeperLearning",
		title: "深度学习",
		description: "评估儿童的深度学习能力和思维发展",
		icon: <Brain className="w-5 h-5 text-purple-500" />,
		enabled: false,
	},
];

export default function ScalePage() {
	const [scaleConfigs, setScaleConfigs] = useState<ScaleConfig[]>(
		DEFAULT_SCALE_CONFIGS,
	);

	useEffect(() => {
		if (typeof document !== "undefined") {
			document.title = "观察指标配置";
		}
	}, []);

	const handleToggle = useCallback(
		(id: string, checked: boolean) => {
			// 如果要禁用，检查是否至少保留一个启用的指标
			if (!checked) {
				const enabledConfigs = scaleConfigs.filter(
					(config) => config.enabled && config.id !== id,
				);
				if (enabledConfigs.length === 0) {
					Toast.show({
						content: "至少需要启用一个指标",
						duration: 2000,
					});
					return;
				}
			}

			setScaleConfigs((prev) =>
				prev.map((config) =>
					config.id === id ? { ...config, enabled: checked } : config,
				),
			);

			// 显示操作反馈
			Toast.show({
				content: checked ? "已启用该指标" : "已禁用该指标",
				duration: 1500,
			});
		},
		[scaleConfigs],
	);

	const enabledCount = scaleConfigs.filter((config) => config.enabled).length;

	return (
		<main className="min-h-screen bg-slate-50">
			<div className="">
				<div className="p-4">
					<h1 className="text-xl font-semibold text-gray-800 mb-2">
						观察指标配置
					</h1>
					<p className="text-sm text-gray-500 mb-4">
						配置观察记录中使用的评估指标，已启用 {enabledCount} 个指标
					</p>
				</div>

				<List mode="card" style={{ "--border-inner": "none" }}>
					{scaleConfigs.map((config) => (
						<List.Item
							key={config.id}
							prefix={config.icon}
							description={config.description}
							extra={
								<Switch
									checked={config.enabled}
									onChange={(checked) => handleToggle(config.id, checked)}
								/>
							}
							className="!py-3"
						>
							<div className="font-medium text-gray-800">{config.title}</div>
						</List.Item>
					))}
				</List>

				{/* 使用说明 */}
				<div className="p-4 mt-4">
					<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
						<h3 className="text-sm font-medium text-blue-800 mb-2">使用说明</h3>
						<ul className="text-xs text-blue-700 space-y-1">
							<li>• 启用的指标将在创建观察记录时可选择使用</li>
							<li>• 建议至少启用一个指标以确保评估的完整性</li>
							<li>• 配置更改会立即生效，无需额外保存操作</li>
						</ul>
					</div>
				</div>

				{/* 统计信息 */}
				<div className="p-4">
					<div className="bg-white rounded-lg shadow-sm p-4">
						<h3 className="text-sm font-medium text-gray-800 mb-3">配置统计</h3>
						<div className="grid grid-cols-2 gap-4">
							<div className="text-center">
								<div className="text-2xl font-bold text-green-600 mb-1">
									{enabledCount}
								</div>
								<div className="text-xs text-gray-500">已启用指标</div>
							</div>
							<div className="text-center">
								<div className="text-2xl font-bold text-gray-400 mb-1">
									{scaleConfigs.length - enabledCount}
								</div>
								<div className="text-xs text-gray-500">已禁用指标</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}
