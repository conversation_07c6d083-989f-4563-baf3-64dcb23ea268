'use client';

import { List } from 'antd-mobile';
import { BarChart3, MapPin, Tag } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';

interface SettingItem {
  key: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
}

export default function SettingsPage() {
  const router = useRouter();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '系统设置';
    }
  }, []);

  const settingsItems: SettingItem[] = [
    {
      key: 'area',
      title: '区域设置',
      description: '选择观察记录中可用的区域',
      icon: <MapPin className="w-5 h-5 text-blue-500" />,
      path: '/pbl/record/settings/area'
    },
    {
      key: 'area-manage',
      title: '区域管理',
      description: '管理区域分类和子区域',
      icon: <MapPin className="w-5 h-5 text-purple-500" />,
      path: '/pbl/record/settings/area/manage'
    },
    {
      key: 'tags',
      title: '标签设置',
      description: '管理观察标签配置',
      icon: <Tag className="w-5 h-5 text-green-500" />,
      path: '/pbl/record/settings/tags'
    },
    {
      key: 'scale',
      title: '观察指标配置',
      description: '配置观察记录的评估指标',
      icon: <BarChart3 className="w-5 h-5 text-orange-500" />,
      path: '/pbl/record/settings/scale'
    }
  ];

  const handleItemClick = useCallback(
    (path: string) => {
      router.push(path);
    },
    [router]
  );

  return (
    <main className="min-h-screen bg-slate-50">
      <div className="">
        <div className="p-4">
          <h1 className="text-xl font-semibold text-gray-800 mb-2">系统设置</h1>
          <p className="text-sm text-gray-500">配置观察记录相关设置</p>
        </div>
        <List mode="card" style={{ '--border-inner': 'none' }}>
          {settingsItems.map((item) => (
            <List.Item
              key={item.key}
              prefix={item.icon}
              description={item.description}
              onClick={() => handleItemClick(item.path)}
              className="!py-2"
            >
              <div className="font-medium text-gray-800">{item.title}</div>
            </List.Item>
          ))}
        </List>
      </div>
    </main>
  );
}
