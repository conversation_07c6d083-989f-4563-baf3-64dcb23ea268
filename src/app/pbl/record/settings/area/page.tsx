"use client";

import { But<PERSON>, NavBar, Toast } from "antd-mobile";
import { Save, RotateCcw } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";

// 区域数据类型定义
interface AreaItem {
	id: string;
	name: string;
	selected: boolean;
}

interface AreaCategory {
	id: string;
	title: string;
	items: AreaItem[];
}

const AreaSettingsPage = () => {
	const [categories, setCategories] = useState<AreaCategory[]>([]);
	const [hasChanges, setHasChanges] = useState(false);

	// 初始化模拟数据
	const initializeData = (): AreaCategory[] => {
		return [
			{
				id: "indoor",
				title: "室内",
				items: [
					{ id: "activity-room", name: "活动室", selected: false },
					{ id: "classroom", name: "教室", selected: false },
					{ id: "washroom", name: "洗漱区", selected: false },
					{ id: "corridor", name: "走廊", selected: false },
					{ id: "stairs", name: "楼梯", selected: false },
					{ id: "game-room", name: "游戏室", selected: false },
					{ id: "room-666", name: "666", selected: false },
					{ id: "xiaoshan", name: "小山坡", selected: false },
				],
			},
			{
				id: "corner",
				title: "区角",
				items: [
					{ id: "science-area", name: "科学区", selected: false },
					{ id: "art-area", name: "美术区", selected: false },
					{ id: "reading-area", name: "阅读区", selected: false },
					{ id: "construction-area", name: "建构区", selected: false },
					{ id: "puzzle-area", name: "益智区", selected: false },
					{ id: "living-area", name: "生活区", selected: false },
					{ id: "language-area", name: "语言区", selected: false },
				],
			},
			{
				id: "outdoor",
				title: "户外",
				items: [
					{ id: "public-area", name: "公共区域", selected: false },
					{ id: "playground", name: "操场", selected: false },
					{ id: "climbing-area", name: "攀爬区", selected: false },
				],
			},
		];
	};

	// 从本地存储加载数据
	const loadData = () => {
		try {
			const savedData = localStorage.getItem("pbl-area-settings");
			if (savedData) {
				const parsedData = JSON.parse(savedData);
				setCategories(parsedData);
			} else {
				const initialData = initializeData();
				setCategories(initialData);
			}
		} catch (error) {
			console.error("加载区域设置失败:", error);
			const initialData = initializeData();
			setCategories(initialData);
		}
	};

	useEffect(() => {
		loadData();
		if (typeof document !== "undefined") {
			document.title = "区域设置";
		}
	}, []);

	// 选择区域 - 单选模式，支持取消选中
	const selectArea = useCallback((categoryId: string, itemId: string) => {
		setCategories((prevCategories) =>
			prevCategories.map((category) =>
				category.id === categoryId
					? {
							...category,
							items: category.items.map((item) => {
								if (item.id === itemId) {
									// 如果点击的是已选中的项，则取消选中
									return { ...item, selected: !item.selected };
								} else {
									// 其他项取消选中
									return { ...item, selected: false };
								}
							}),
						}
					: category,
			),
		);
		setHasChanges(true);
	}, []);

	// 保存设置
	const saveSettings = () => {
		try {
			localStorage.setItem("pbl-area-settings", JSON.stringify(categories));
			setHasChanges(false);
			Toast.show({
				icon: "success",
				content: "保存成功",
			});
		} catch (error) {
			console.error("保存失败:", error);
			Toast.show({
				icon: "fail",
				content: "保存失败",
			});
		}
	};

	// 重置设置
	const resetSettings = useCallback(() => {
		const initialData = initializeData();
		setCategories(initialData);
		setHasChanges(true);
		Toast.show({
			content: "已重置为默认设置",
		});
	}, []);

	// 统计信息 - 使用 useMemo 优化性能
	const statistics = useMemo(() => {
		const selectedCount = categories.reduce(
			(total, category) =>
				total + category.items.filter((item) => item.selected).length,
			0,
		);
		const totalCount = categories.reduce(
			(total, category) => total + category.items.length,
			0,
		);
		return { selectedCount, totalCount };
	}, [categories]);

	// 渲染分类标题
	const renderCategoryTitle = (category: AreaCategory) => (
		<div className="flex items-center mb-4 mt-6 first:mt-0">
			<div className="w-1 h-6 bg-green-500 rounded-full mr-3"></div>
			<h2 className="text-lg font-semibold text-gray-800">{category.title}</h2>
		</div>
	);

	// 渲染区域标签 - 优化响应式布局
	const renderAreaTags = (category: AreaCategory) => (
		<div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-6">
			{category.items.map((item) => (
				<button
					key={item.id}
					type="button"
					className={`
	           px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 active:scale-95
	           ${
								item.selected
									? "bg-blue-500 text-white shadow-md hover:bg-blue-600"
									: "bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300"
							}
	         `}
					onClick={() => selectArea(category.id, item.id)}
				>
					{item.name}
				</button>
			))}
		</div>
	);

	return (
		<div className="min-h-screen bg-slate-50">
			<NavBar
				right={
					<div className="flex gap-2">
						<Button
							size="small"
							color="default"
							fill="none"
							onClick={resetSettings}
						>
							<RotateCcw className="w-4 h-4" />
							重置
						</Button>
						<Button
							size="small"
							color="primary"
							fill="solid"
							onClick={saveSettings}
							disabled={!hasChanges}
						>
							<Save className="w-4 h-4" />
							保存
						</Button>
					</div>
				}
				onBack={() => history.back()}
			>
				区域设置
			</NavBar>

			<div className="p-4">
				<div className="mb-4">
					<p className="text-sm text-gray-500">选择观察记录中可用的区域标签</p>
				</div>

				{categories.map((category) => (
					<div key={category.id}>
						{renderCategoryTitle(category)}
						{renderAreaTags(category)}
					</div>
				))}

				{/* 统计信息 */}
				<div className="mt-8 p-4 bg-white rounded-lg shadow-sm">
					<div className="text-sm text-gray-600">
						<div className="flex justify-between mb-2">
							<span>已选择区域：</span>
							<span className="font-medium text-blue-600">
								{statistics.selectedCount}
							</span>
						</div>
						<div className="flex justify-between">
							<span>总区域数：</span>
							<span className="font-medium">{statistics.totalCount}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AreaSettingsPage;
