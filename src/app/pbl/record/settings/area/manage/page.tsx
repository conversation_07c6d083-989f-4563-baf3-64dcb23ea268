"use client";

import { Button, Dialog, Form, Input, List, NavBar, Toast } from "antd-mobile";
import { Plus, Edit3, Trash2, ChevronDown, ChevronRight } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";

// 数据类型定义
interface AreaItem {
	id: string;
	name: string;
	selected: boolean;
}

interface AreaCategory {
	id: string;
	title: string;
	items: AreaItem[];
}

interface EditForm {
	type: "category" | "item";
	mode: "add" | "edit";
	categoryId?: string;
	itemId?: string;
	currentName?: string;
}

const AreaManagePage = () => {
	const [categories, setCategories] = useState<AreaCategory[]>([]);
	const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
	const [editForm, setEditForm] = useState<EditForm | null>(null);
	const [form] = Form.useForm();

	// 初始化数据
	const initializeData = (): AreaCategory[] => {
		return [
			{
				id: "indoor",
				title: "室内",
				items: [
					{ id: "activity-room", name: "活动室", selected: false },
					{ id: "classroom", name: "教室", selected: false },
					{ id: "washroom", name: "洗漱区", selected: false },
					{ id: "corridor", name: "走廊", selected: false },
					{ id: "stairs", name: "楼梯", selected: false },
					{ id: "game-room", name: "游戏室", selected: false },
					{ id: "room-666", name: "666", selected: false },
					{ id: "xiaoshan", name: "小山坡", selected: false },
				],
			},
			{
				id: "corner",
				title: "区角",
				items: [
					{ id: "science-area", name: "科学区", selected: false },
					{ id: "art-area", name: "美术区", selected: false },
					{ id: "reading-area", name: "阅读区", selected: false },
					{ id: "construction-area", name: "建构区", selected: false },
					{ id: "puzzle-area", name: "益智区", selected: false },
					{ id: "living-area", name: "生活区", selected: false },
					{ id: "language-area", name: "语言区", selected: false },
				],
			},
			{
				id: "outdoor",
				title: "户外",
				items: [
					{ id: "public-area", name: "公共区域", selected: false },
					{ id: "playground", name: "操场", selected: false },
					{ id: "climbing-area", name: "攀爬区", selected: false },
				],
			},
		];
	};

	// 加载数据
	const loadData = useCallback(() => {
		try {
			const savedData = localStorage.getItem("pbl-area-settings");
			if (savedData) {
				const parsedData = JSON.parse(savedData);
				setCategories(parsedData);
				setExpandedKeys(parsedData.map((cat: AreaCategory) => cat.id));
			} else {
				const initialData = initializeData();
				setCategories(initialData);
				setExpandedKeys(initialData.map((cat) => cat.id));
			}
		} catch (error) {
			console.error("加载数据失败:", error);
			const initialData = initializeData();
			setCategories(initialData);
			setExpandedKeys(initialData.map((cat) => cat.id));
		}
	}, []);

	useEffect(() => {
		loadData();
		if (typeof document !== "undefined") {
			document.title = "区域管理";
		}
	}, [loadData]);

	// 保存数据
	const saveData = useCallback((newCategories: AreaCategory[]) => {
		try {
			localStorage.setItem("pbl-area-settings", JSON.stringify(newCategories));
			setCategories(newCategories);
		} catch (error) {
			console.error("保存数据失败:", error);
			Toast.show({
				icon: "fail",
				content: "保存失败",
			});
		}
	}, []);

	// 切换展开状态
	const toggleExpanded = useCallback((categoryId: string) => {
		setExpandedKeys((prev) =>
			prev.includes(categoryId)
				? prev.filter((id) => id !== categoryId)
				: [...prev, categoryId],
		);
	}, []);

	// 打开编辑弹窗
	const openEditDialog = useCallback(
		(editConfig: EditForm) => {
			setEditForm(editConfig);
			form.setFieldsValue({ name: editConfig.currentName || "" });
		},
		[form],
	);

	// 关闭编辑弹窗
	const closeEditDialog = useCallback(() => {
		setEditForm(null);
		form.resetFields();
	}, [form]);

	// 提交编辑
	const handleSubmit = useCallback(async () => {
		if (!editForm) return;

		try {
			const values = await form.validateFields();
			const newName = values.name.trim();

			if (!newName) {
				Toast.show({ content: "名称不能为空" });
				return;
			}

			let newCategories = [...categories];

			if (editForm.type === "category") {
				if (editForm.mode === "add") {
					// 添加新分类
					const newId = `category-${Date.now()}`;
					newCategories.push({
						id: newId,
						title: newName,
						items: [],
					});
				} else {
					// 编辑分类
					newCategories = newCategories.map((cat) =>
						cat.id === editForm.categoryId ? { ...cat, title: newName } : cat,
					);
				}
			} else {
				// 处理子区域
				if (editForm.mode === "add") {
					// 添加新子区域
					const newId = `item-${Date.now()}`;
					newCategories = newCategories.map((cat) =>
						cat.id === editForm.categoryId
							? {
									...cat,
									items: [
										...cat.items,
										{ id: newId, name: newName, selected: false },
									],
								}
							: cat,
					);
				} else {
					// 编辑子区域
					newCategories = newCategories.map((cat) =>
						cat.id === editForm.categoryId
							? {
									...cat,
									items: cat.items.map((item) =>
										item.id === editForm.itemId
											? { ...item, name: newName }
											: item,
									),
								}
							: cat,
					);
				}
			}

			saveData(newCategories);
			closeEditDialog();
			Toast.show({
				icon: "success",
				content: editForm.mode === "add" ? "添加成功" : "修改成功",
			});
		} catch (error) {
			console.error("提交失败:", error);
		}
	}, [editForm, form, categories, saveData, closeEditDialog]);

	// 删除分类
	const deleteCategory = useCallback(
		(categoryId: string) => {
			const category = categories.find((cat) => cat.id === categoryId);
			if (!category) return;

			if (category.items.length > 0) {
				Toast.show({
					content: "请先删除该分类下的所有子区域",
				});
				return;
			}

			Dialog.confirm({
				content: `确定要删除分类"${category.title}"吗？`,
				onConfirm: () => {
					const newCategories = categories.filter(
						(cat) => cat.id !== categoryId,
					);
					saveData(newCategories);
					Toast.show({
						icon: "success",
						content: "删除成功",
					});
				},
			});
		},
		[categories, saveData],
	);

	// 删除子区域
	const deleteItem = useCallback(
		(categoryId: string, itemId: string) => {
			const category = categories.find((cat) => cat.id === categoryId);
			const item = category?.items.find((item) => item.id === itemId);
			if (!category || !item) return;

			Dialog.confirm({
				content: `确定要删除区域"${item.name}"吗？`,
				onConfirm: () => {
					const newCategories = categories.map((cat) =>
						cat.id === categoryId
							? {
									...cat,
									items: cat.items.filter((item) => item.id !== itemId),
								}
							: cat,
					);
					saveData(newCategories);
					Toast.show({
						icon: "success",
						content: "删除成功",
					});
				},
			});
		},
		[categories, saveData],
	);

	// 渲染分类标题
	const renderCategoryHeader = (category: AreaCategory) => {
		const isExpanded = expandedKeys.includes(category.id);

		return (
			<div className="flex items-center justify-between py-3 px-4 bg-gray-50">
				<div
					className="flex items-center flex-1 cursor-pointer"
					onClick={() => toggleExpanded(category.id)}
				>
					{isExpanded ? (
						<ChevronDown className="w-4 h-4 text-gray-500 mr-2" />
					) : (
						<ChevronRight className="w-4 h-4 text-gray-500 mr-2" />
					)}
					<span className="font-medium text-gray-800">{category.title}</span>
					<span className="ml-2 text-sm text-gray-500">
						({category.items.length})
					</span>
				</div>
				<div className="flex gap-2">
					<Button
						size="small"
						fill="none"
						onClick={() =>
							openEditDialog({
								type: "item",
								mode: "add",
								categoryId: category.id,
							})
						}
					>
						<Plus className="w-4 h-4" />
					</Button>
					<Button
						size="small"
						fill="none"
						onClick={() =>
							openEditDialog({
								type: "category",
								mode: "edit",
								categoryId: category.id,
								currentName: category.title,
							})
						}
					>
						<Edit3 className="w-4 h-4" />
					</Button>
					<Button
						size="small"
						color="danger"
						fill="none"
						onClick={() => deleteCategory(category.id)}
					>
						<Trash2 className="w-4 h-4" />
					</Button>
				</div>
			</div>
		);
	};

	// 渲染子区域列表
	const renderItems = (category: AreaCategory) => {
		if (!expandedKeys.includes(category.id)) return null;

		return (
			<div className="bg-white">
				{category.items.map((item) => (
					<List.Item
						key={item.id}
						extra={
							<div className="flex gap-2">
								<Button
									size="small"
									fill="none"
									onClick={() =>
										openEditDialog({
											type: "item",
											mode: "edit",
											categoryId: category.id,
											itemId: item.id,
											currentName: item.name,
										})
									}
								>
									<Edit3 className="w-4 h-4" />
								</Button>
								<Button
									size="small"
									color="danger"
									fill="none"
									onClick={() => deleteItem(category.id, item.id)}
								>
									<Trash2 className="w-4 h-4" />
								</Button>
							</div>
						}
						className="!pl-12"
					>
						{item.name}
					</List.Item>
				))}
				{category.items.length === 0 && (
					<div className="py-8 text-center text-gray-500 text-sm">
						暂无子区域
					</div>
				)}
			</div>
		);
	};

	return (
		<div className="min-h-screen bg-slate-50">
			<NavBar
				right={
					<Button
						size="small"
						color="primary"
						fill="none"
						onClick={() =>
							openEditDialog({
								type: "category",
								mode: "add",
							})
						}
					>
						<Plus className="w-4 h-4" />
						添加分类
					</Button>
				}
				onBack={() => history.back()}
			>
				区域管理
			</NavBar>

			<div className="p-4">
				<div className="mb-4">
					<p className="text-sm text-gray-500">
						管理观察记录的区域分类和子区域
					</p>
				</div>
				<div className="bg-white rounded-lg overflow-hidden shadow-sm">
					{categories.map((category) => (
						<div
							key={category.id}
							className="border-b border-gray-100 last:border-b-0"
						>
							{renderCategoryHeader(category)}
							{renderItems(category)}
						</div>
					))}
				</div>

				{categories.length === 0 && (
					<div className="py-16 text-center text-gray-500">
						<p className="mb-4">暂无分类</p>
						<Button
							color="primary"
							onClick={() =>
								openEditDialog({
									type: "category",
									mode: "add",
								})
							}
						>
							添加第一个分类
						</Button>
					</div>
				)}
			</div>

			{/* 编辑弹窗 */}
			{editForm && (
				<Dialog
					visible={true}
					title={
						editForm.type === "category"
							? editForm.mode === "add"
								? "添加分类"
								: "编辑分类"
							: editForm.mode === "add"
								? "添加子区域"
								: "编辑子区域"
					}
					content={
						<Form form={form} layout="horizontal">
							<Form.Item
								name="name"
								label="名称"
								rules={[{ required: true, message: "请输入名称" }]}
							>
								<Input placeholder="请输入名称" />
							</Form.Item>
						</Form>
					}
					closeOnAction={true}
					actions={[
						[
							{
								key: "cancel",
								text: "取消",
								onClick: closeEditDialog,
							},
							{
								key: "submit",
								text: "确定",
								bold: true,
								onClick: handleSubmit,
							},
						],
					]}
				/>
			)}
		</div>
	);
};

export default AreaManagePage;
