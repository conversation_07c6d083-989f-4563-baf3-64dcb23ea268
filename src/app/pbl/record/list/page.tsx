'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { InfiniteScroll } from 'antd-mobile';
import { format } from 'date-fns';
import { Calendar } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getObservationList } from '@/api/pbl';

import RecordItem from './components/RecordItem';

const pageSize = 10;

/*
 根据date和 studentID 判断两种显示方式
*/
export default function App() {
  const searchParams = useSearchParams();
  const studentId = searchParams?.get('studentId');
  const studentName = searchParams?.get('studentName');
  const date = searchParams?.get('date');
  const observationIds = searchParams?.get('observationIds') || '';
  const weekday = searchParams?.get('weekday');
  const [total, setTotal] = useState(0);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录';
    }
  }, []);

  const {
    data: infiniteObservationData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingObservations
  } = useInfiniteQuery({
    queryKey: ['observationList', studentId, date],
    queryFn: async ({ pageParam = 1 }) => {
      const params: {
        studentId?: string;
        date?: string[];
        page: number;
        perPage: number;
        observationIds?: string[];
      } = {
        page: pageParam,
        perPage: pageSize
      };
      if (studentId) {
        params.studentId = studentId;
      }
      if (date) {
        params.observationIds = JSON.parse(observationIds);
        params.date = [date];
      }
      const response = await getObservationList(params);

      // 确保返回完整的分页信息，包括数据和分页状态
      // 以安全的方式访问返回数据
      const responseData = response as unknown;
      const records = Array.isArray(responseData)
        ? responseData
        : Array.isArray((responseData as any).list)
          ? (responseData as any).list
          : [];
      setTotal((responseData as any).total);
      return {
        data: records,
        page: pageParam,
        hasMore: records.length >= pageSize
      };
    },
    getNextPageParam: (lastPage, allPages) => {
      // 如果没有更多数据，返回 undefined 表示不需要加载下一页
      if (
        lastPage.hasMore === false ||
        !lastPage.data ||
        lastPage.data.length < pageSize
      ) {
        return undefined;
      }
      return lastPage.page + 1;
    },
    enabled: !!studentId || !!date
  });

  // 从 infiniteObservationData 中提取记录数据
  const recordsList = infiniteObservationData?.pages
    ? infiniteObservationData.pages.flatMap((page) => {
        if (page) {
          return page.data || [];
        }
        return [];
      })
    : [];

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  if (isLoadingObservations) {
    return (
      <div className="py-10 text-center">
        <div className="text-sm text-gray-500">加载中...</div>
      </div>
    );
  }

  return (
    <main className=" min-h-screen bg-slate-50">
      <div className="mb-4 px-4 pt-6 text-base font-semibold">
        {!!studentId && (
          <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center">
              {studentName}的观察记录
            </div>
            <div className="flex items-center justify-end text-sm font-normal text-gray-500">
              <span className="">{total} 条记录</span>
            </div>
          </div>
        )}
        {!!date && (
          <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center">
              <Calendar className="mr-2 size-4 text-gray-500" />
              {format(new Date(date), 'yyyy-MM-dd')} 观察记录
            </div>
            <div className="flex items-center justify-end text-sm font-normal text-gray-600">
              <span>{weekday}</span>
              <span className="mx-2">•</span>
              <span className="">{total} 条记录</span>
            </div>
          </div>
        )}
      </div>
      <div className="flex flex-1 flex-col overflow-y-auto px-4 py-2">
        {recordsList.length === 0 ? (
          <div className="flex flex-1 items-center justify-center py-10 text-center">
            暂无观察记录
          </div>
        ) : (
          <div className="mb-12">
            {/* 这里传递实际的记录数据给 Records 组件 */}
            {recordsList.map((record) => (
              <div key={record.observationId}>
                <RecordItem record={record} studentId={studentId || undefined}/>
              </div>
            ))}
            {/* 使用 InfiniteScroll 组件 */}
            <InfiniteScroll
              loadMore={loadMore}
              hasMore={!!hasNextPage}
              threshold={250}
              className="!py-2"
            >
              {isFetchingNextPage ? (
                <div className="py-3 text-center">
                  <span className="text-sm text-gray-500">加载更多数据...</span>
                </div>
              ) : hasNextPage ? (
                <div className="py-3 text-center">
                  <span className="text-sm text-gray-500">上拉加载更多</span>
                </div>
              ) : (
                <div className="py-3 text-center">
                  <span className="text-sm text-gray-500">没有更多数据了</span>
                </div>
              )}
            </InfiniteScroll>
          </div>
        )}
      </div>
    </main>
  );
}
