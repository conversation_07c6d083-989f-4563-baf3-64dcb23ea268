'use client';

import { ChevronRight, X } from 'lucide-react';
import type { RecordData } from '../mock/recordData';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface StudentsDisplayProps {
  record: RecordData;
}

const StudentsDisplay = ({ record }: StudentsDisplayProps) => {
  const router = useRouter();
  const [showParticipants, setShowParticipants] = useState(false);

  const handleParticipantsClick = (record: RecordData) => {
    setShowParticipants(true);
  };

  if (!record.students?.length) return null;

  return (
    <div className="flex items-center space-x-2 mb-4">
      <div
        className="flex items-center cursor-pointer"
        onClick={(e) => {
          e.stopPropagation();
          handleParticipantsClick?.(record);
        }}
      >
        <div className="flex -space-x-2">
          {record.students.slice(0, 3).map((participant) => (
            <img
              key={participant.id}
              className="w-8 h-8 rounded-full border-2 border-white"
              src={participant.avatar}
              alt={participant.name}
            />
          ))}
          {record.students.length > 3 && (
            <div className="w-8 h-8 rounded-full bg-violet-100 border-2 border-white flex items-center justify-center">
              <span className="text-violet-600 text-xs">
                +{record.students.length - 3}
              </span>
            </div>
          )}
        </div>
        <span className="text-sm text-gray-500 ml-2">
          {record.students.length}位学生参与
        </span>
        <ChevronRight className="w-4 h-4 text-gray-400 ml-1" />
      </div>
      {showParticipants && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-80 max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b border-gray-100 flex justify-between items-center">
              <h3 className="font-medium">参与学生</h3>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowParticipants(false);
                }}
                type="button"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>
            <div className="p-4 overflow-y-auto">
              {record?.students?.map((participant) => (
                <div
                  key={participant.id}
                  className="flex justify-between items-center py-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(
                      `/pbl/record/detail/student?observationId=${record.observationId}&studentId=${participant.id}`
                    );

                    setShowParticipants(false);
                  }}
                >
                  <div className="flex items-center">
                    <img
                      src={participant.avatar}
                      alt={participant.name}
                      className="w-10 h-10 rounded-full"
                    />
                    <span className="ml-3">{participant.name}</span>
                  </div>
                  <ChevronRight className="w-6 h-6 text-gray-400 ml-1" />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentsDisplay;
