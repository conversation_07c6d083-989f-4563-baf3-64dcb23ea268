'use client';

import { useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { useRouter } from 'next/navigation';
import { ActionSheet } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import { navigationToNativePage } from '@/utils';
import { useCommonStore } from '@/store/useCommonStore';
import { compare } from 'compare-versions';

interface AddRecordButtonProps {
  extra?: string;
  cancelText?: string;
  buttonText?: string;
  icon?: React.ReactNode;
  className?: string;
  classId?: string;
}

export default function AddRecordButton({
  extra = '请选择记录模式',
  cancelText = '取消',
  buttonText = '新增观察记录',
  icon = <FaPlus />,
  className = '',
  classId = '',
}: AddRecordButtonProps) {
  const version = useCommonStore((state) => state.version);
  const brand = useCommonStore((state) => state.brand);
  const router = useRouter();
  const [visible, setVisible] = useState(false);

  // 在组件内部定义固定的 actions
  const actions: Action[] = [
    {
      text: 'AI 自动生成观察记录',
      description: (
        <div className="text-xs text-gray-500">
          上传素材，AI 会自动生成观察记录
        </div>
      ),
      key: 'copy',
      onClick: async () => {
        if (
          (brand === '1' && compare(version, '1.33.0', '>')) ||
          (brand === '2' && compare(version, '6.21.2', '>='))
        ) {
          navigationToNativePage(
            `app://app/pbl/addMaterials?deptId=${classId}&videoCompressBitrate=10000000`,
          );
          setVisible(false);
        } else {
          router.push(`/pbl/material/create?classId=${classId}`);
        }
      },
    },
    {
      text: '手动创建观察记录',
      key: 'edit',
      onClick: async () => {
        router.push(`/pbl/record/create?classId=${classId}`);
      },
    },
  ];

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 px-6 pb-2">
        <button
          type="button"
          className={`w-full bg-gradient-to-r from-violet-400 to-violet-500 text-white py-3 px-6 rounded-full shadow-md hover:shadow-lg transition-all flex items-center justify-center space-x-2 ${className}`}
          onClick={() => setVisible(true)}
        >
          {icon}
          <span>{buttonText}</span>
        </button>
      </div>
      <ActionSheet
        extra={extra}
        cancelText={cancelText}
        visible={visible}
        actions={actions}
        onClose={() => setVisible(false)}
      />
    </>
  );
}
