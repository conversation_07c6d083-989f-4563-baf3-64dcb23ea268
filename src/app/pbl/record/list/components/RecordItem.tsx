'use client';

import { useRouter } from 'next/navigation';
import { FaRegClock, FaPlay } from 'react-icons/fa';
import type { RecordData } from '../mock/recordData';
import Media from '../../detail/components/Media';
import StudentsDisplay from './StudentsDisplay';

interface RecordItemProps {
  record: RecordData;
  studentId?: string;
}

export default function RecordItem({ record,studentId }: RecordItemProps) {
  const router = useRouter();

  const handleRecordClick = () => {
    if (record.isGenerating === 1) {
      return;
    }
    if(studentId) {
      router.push(`/pbl/record/detail/student?observationId=${record.observationId}&studentId=${studentId}`);
    } else {
      router.push(`/pbl/record/detail?observationId=${record.observationId}`);
    }
  };

  const getTagColor = (color: string) => {
    const colorMap: Record<string, string> = {
      violet: 'bg-violet-100 text-violet-600',
      blue: 'bg-blue-100 text-blue-600',
      purple: 'bg-purple-100 text-purple-600',
      yellow: 'bg-yellow-100 text-yellow-600',
      green: 'bg-green-100 text-green-600',
      red: 'bg-red-100 text-red-600',
      indigo: 'bg-indigo-100 text-indigo-600',
      teal: 'bg-teal-100 text-teal-600'
    };
    return colorMap[color] || 'bg-gray-100 text-gray-600';
  };

  return (
    <div
      className="card-hover bg-white rounded-2xl shadow-sm overflow-hidden mb-4"
      onClick={handleRecordClick}
    >
      <div className="p-4">
        <Media media={record.medias || []} />

        {(record.type === 'text' || record.type === 'audio') && (
          <h3 className="font-semibold text-lg mb-1">{record.title}</h3>
        )}

        <p className="text-gray-700 mb-3 line-clamp-5">{record.content}</p>

        {record.type === 'audio' && record.audio && (
          <div className="bg-yellow-50 rounded-lg p-3 mb-3 flex items-center">
            <button
              type="button"
              className="w-10 h-10 rounded-full bg-violet-500 text-white flex items-center justify-center shadow-md hover:bg-violet-600 transition-colors"
            >
              <FaPlay />
            </button>
            <div className="ml-3 flex-1">
              <div className="flex justify-between text-sm text-gray-500 mb-1">
                <span>故事录音</span>
                <span>{record.audio.duration}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-violet-500 h-1.5 rounded-full"
                  style={{ width: `${record.audio.progress * 100}%` }}
                />
              </div>
            </div>
          </div>
        )}

        <StudentsDisplay record={record} />

        <div className="flex space-x-2 mb-4">
          {record.tags?.map((tag, index) => (
            <span
              key={`${tag.name}-${index}`}
              className={`${getTagColor(tag.color)} px-2 py-1 rounded-full text-xs`}
            >
              #{tag.name}
            </span>
          ))}
        </div>

        <div className="flex items-center text-sm text-gray-600">
          <div className="flex items-center">
            <img
              src={record.createUser?.avatar}
              alt={record.createUser?.name}
              className="w-6 h-6 rounded-full border-2 border-white mr-1"
            />
            <span>{record.createUser?.name}</span>
          </div>
          <div className="mx-2">•</div>
          <div className="flex items-center">
            <FaRegClock className="text-gray-500 mr-1" />
            <span>{record.createTime}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
