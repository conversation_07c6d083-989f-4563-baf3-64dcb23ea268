import { PlayCircle, Mic } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import MediaPreview from '@/components/UploadFile/components/MediaPreview';
import type { FileType } from '@/components/UploadFile/types';
import { formatDuration } from '@/utils';

interface MediaItem {
  mediaId?: number;
  url: string;
  name?: string;
  duration?: string | number;
  type: number | string;
  cover?: string;
}

function Media({ media }: { media: MediaItem[] }) {
  console.log('🚀 ~ media:', media);

  const mediaList: FileType[] = media.map((item) => ({
    ...item,
    id: String(item.mediaId || ''),
    mediaId: item.mediaId !== undefined ? String(item.mediaId) : undefined,
    type: item.type === 1 ? 'image' : item.type === 2 ? 'video' : 'audio',
    duration:
      typeof item.duration === 'string'
        ? Number.parseInt(item.duration, 10)
        : item.duration
  }));

  const [previewFile, setPreviewFile] = useState<FileType | null>(null);

  // 打开预览
  const handlePreview = (e: React.MouseEvent, file: FileType) => {
    e.stopPropagation();
    setPreviewFile({
      status: 'success',
      progress: 100,
      url: file.url,
      type: file.type,
      id: file.id,
      name: file.name || '',
      duration: file.duration
    });
  };

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewFile(null);
  };

  // 渲染媒体预览
  const renderMediaPreview = (media: FileType) => {
    if (media.type === 'image') {
      return (
        <div
          className="relative overflow-hidden rounded-lg bg-slate-100"
          onClick={(e) => handlePreview(e, media)}
        >
          <img
            src={media.url || ''}
            alt=""
            className="w-full aspect-[4/3] object-cover"
          />
        </div>
      );
    }

    return (
      <div
        className="relative overflow-hidden rounded-lg bg-slate-100 flex items-center justify-center aspect-[4/3] "
        onClick={(e) => handlePreview(e, media)}
      >
        {media.type === 'video' && (
          <>
            <img
              src={
                media.cover ||
                `${media.url}?x-workflow-graph-name=video-thumbnail`
              }
              alt=""
              className="w-full aspect-[4/3] object-cover absolute"
            />
            <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
              <PlayCircle className="text-white w-8 h-8 opacity-80" />
            </div>
          </>
        )}
        {media.type === 'audio' && (
          <div
            className="flex flex-col items-center justify-center"
            onClick={(e) => handlePreview(e, media)}
          >
            <Mic className="text-primary w-8 h-8 mb-1" />
            <span className="text-xs">{formatDuration(media.duration)}</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div onClick={(e) => e.stopPropagation()}>
      {/* 媒体内容 */}
      {mediaList && mediaList.length > 0 && (
        <div className="mb-4">
          <div className="grid grid-cols-3 gap-2">
            {mediaList.map((item) => (
              <div key={item.mediaId} className="media-item">
                {renderMediaPreview(item)}
                <p className="text-xs mt-1 truncate">{item.name || ''}</p>
              </div>
            ))}
          </div>
        </div>
      )}
      {/* 媒体预览模态框 */}
      {previewFile && (
        <MediaPreview file={previewFile} onClose={handleClosePreview} />
      )}
    </div>
  );
}

export default Media;
