import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import { CircleCheck, Circle, ChevronDown } from 'lucide-react';

import { formatEvaluationData } from '../../create/components/DevelopmentGuide';
import Icon, { type IconName } from '../../create/components/Icon';
import { getObservationEvaluation } from '@/api/pbl';

// 单个目标项组件
function GoalItem({
  goal,
  isAchieved
}: {
  goal: {
    id: string;
    ability_name: string;
  };
  isAchieved: boolean;
}) {
  return (
    <li
      className={clsx(
        'flex items-center space-x-2 py-2.5',
        !isAchieved && 'opacity-50' // 未达成的目标降低透明度
      )}
    >
      {isAchieved ? (
        <CircleCheck className="w-4 h-4  text-green-500" />
      ) : (
        <Circle className="w-4 h-4 text-gray-400" />
      )}

      <span
        className={clsx(
          'text-sm',
          isAchieved ? 'text-gray-800' : 'text-gray-600'
        )}
      >
        {goal.ability_name}
      </span>
    </li>
  );
}

function SubCategorySection({ subCategory, achievedGoals }) {
  return (
    <div className="mb-5 last:mb-0">
      <h4 className="text-base font-semibold text-gray-700 mb-2">
        {subCategory.label}
      </h4>
      <ul className="list-none p-0 m-0">
        {subCategory.options?.map((goal) => (
          <GoalItem
            key={goal.id}
            goal={goal}
            isAchieved={new Set(achievedGoals).has(Number(goal.id))}
          />
        ))}
      </ul>
    </div>
  );
}

function CategoryAccordion({ category, achievedGoals, isOpen, onToggle }) {
  // 计算该主分类下的总达成数和总目标数
  const allGoals = category.subDomains.flatMap(
    (sc) => sc.options?.flatMap((o) => o.id) || []
  );
  const achievedCount = allGoals.filter((goal) =>
    new Set(achievedGoals).has(Number(goal))
  ).length;
  const totalCount = allGoals.length;

  return (
    <div className="mb-4 overflow-hidden rounded-lg  bg-white transition-all duration-300 ease-in-out">
      <button
        type="button"
        onClick={onToggle}
        className={clsx(
          'flex items-center justify-between w-full p-4 text-left focus:outline-none transition-colors duration-200',
          category.bgColor // 使用分类的背景色
        )}
      >
        <div className="flex items-center space-x-3">
          <Icon
            name={category.icon}
            className={clsx('w-4 h-4', category.color)}
          />
          <span className={clsx('text-base', category.color)}>
            {category.label}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <span className={clsx('text-sm font-medium', category.color)}>
            {`已达成 ${achievedCount}/${totalCount}`}
          </span>
          <ChevronDown
            className={clsx(
              'w-5 h-5 text-gray-500 transform transition-transform duration-300',
              isOpen ? 'rotate-180' : 'rotate-0'
            )}
          />
        </div>
      </button>

      <div
        className={clsx(
          'overflow-hidden transition-all duration-500 ease-in-out border  border-gray-100 border-t-0',
          isOpen ? 'max-h-[1200px] opacity-100' : 'max-h-0 opacity-0' // 使用 max-h 和 opacity 实现平滑过渡
        )}
      >
        <div className="p-4">
          {category.subDomains.map((subCategory) => (
            <SubCategorySection
              key={subCategory.label}
              subCategory={subCategory}
              achievedGoals={achievedGoals}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

function DevelopmentGuide({ evaluations }: { evaluations: string[] }) {
  const [evaluationData, setEvaluationData] = useState([]);

  useEffect(() => {
    getObservationEvaluation({
      dimensionType: 2
    }).then((res) => {
      //@ts-ignore
      const data = res?.dimensions?.[0]?.domains || [];
      setEvaluationData(formatEvaluationData(data));
    });
  }, []);
  const [openCategoryId, setOpenCategoryId] = useState<number | null>(null); // 默认展开第一个

  const handleToggle = (categoryId: number | null) => {
    setOpenCategoryId((prevId: number | null) =>
      prevId === categoryId ? null : categoryId
    );
  };

  return (
    <div className="flex-grow overflow-y-auto custom-scrollbar">
      {evaluationData.map((category) => (
        <CategoryAccordion
          key={category.label}
          category={category}
          achievedGoals={evaluations}
          isOpen={openCategoryId === category.label}
          onToggle={() => handleToggle(category.label)}
        />
      ))}
    </div>
  );
}

export default DevelopmentGuide;
