import clsx from 'clsx';
import { useState } from 'react';
import { ChevronDown, ChevronUp, Check } from 'lucide-react';
import { bloomEvaluationData } from '../../create/components/data';
// 定义类型
interface Example {
  id: string;
  title: string;
  description: string;
  examples: string[];
  isSelected: boolean;
}

interface Category {
  level: string;
  color: 'blue' | 'green' | 'yellow' | 'purple' | 'pink' | 'red' | 'gray';
  items: Example[];
}

// 评价项目组件 - 详情页版本（只读）
function EvaluationItem({ item }: { item: Example }) {
  return (
    <div className={clsx('bg-white mb-4 relative')}>
      <div className="flex items-start">
        <div
          className={clsx(
            'w-5 h-5 flex items-center justify-center rounded-full p-1 shadow-sm mr-3 mt-0.5',
            item.isSelected ? 'bg-indigo-500' : 'bg-gray-300'
          )}
        >
          <Check className="w-5 h-5 text-white" />
        </div>
        <div className={clsx(item.isSelected ? 'opacity-100' : 'opacity-60')}>
          <h4 className="font-semibold  mb-1.5">{item.title}</h4>
          <p className="text-sm ">{item.description}</p>
        </div>
      </div>
    </div>
  );
}

// 评价分类组件 - 详情页版本（只读）
function EvaluationCategory({
  category,
  items
}: {
  category: Category;
  items: Example[];
}) {
  const [isExpanded, setIsExpanded] = useState(true); // 分类默认展开

  // 切换分类展开/折叠
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Tailwind color classes mapping
  const colorClasses = {
    blue: 'text-blue-600 border-blue-200 hover:bg-blue-50',
    green: 'text-green-600 border-green-200 hover:bg-green-50',
    yellow: 'text-yellow-600 border-yellow-200 hover:bg-yellow-50',
    purple: 'text-purple-600 border-purple-200 hover:bg-purple-50',
    pink: 'text-pink-600 border-pink-200 hover:bg-pink-50',
    red: 'text-red-600 border-red-200 hover:bg-red-50',
    gray: 'text-gray-600 border-gray-200 hover:bg-gray-50'
  };
  const categoryColorClass = colorClasses[category.color] || colorClasses.gray;
  const headerTextColor = categoryColorClass.split(' ')[0]; // 获取文本颜色类

  return (
    <section className="mb-4">
      {/* 可点击的分类标题 */}
      <div
        className={clsx(
          'flex justify-between items-center p-3 rounded-t-lg border-b-2 cursor-pointer transition-colors duration-200',
          categoryColorClass,
          !isExpanded && 'rounded-b-lg' // 折叠时底部也加圆角
        )}
        onClick={toggleExpand}
      >
        <h3
          className={clsx('text-lg font-serif font-semibold', headerTextColor)}
        >
          {category.level}
        </h3>
        {isExpanded ? (
          <ChevronUp
            className={clsx(
              'w-5 h-5 transition-transform duration-300',
              headerTextColor
            )}
          />
        ) : (
          <ChevronDown
            className={clsx(
              'w-5 h-5 transition-transform duration-300',
              headerTextColor
            )}
          />
        )}
      </div>

      {/* 可折叠的内容区域 */}
      <div
        className={clsx(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isExpanded ? 'opacity-100' : 'max-h-0 opacity-0' // 使用足够大的 max-height
        )}
      >
        <div className="pt-4 px-1">
          {items.map((item) => (
            <EvaluationItem key={item.id} item={item} />
          ))}
        </div>
      </div>
    </section>
  );
}

function Bloom() {
  // 模拟从 API 获取的数据
  const [selectedData] = useState<string[]>(['r1', 'u2']);
  const [evaluationData] = useState<Category[]>(
    bloomEvaluationData.map((cat) => ({
      ...cat,
      items: cat.items.map((item) => ({
        ...item,
        isSelected: selectedData.includes(item.id)
      }))
    }))
  );

  return (
    <div className="w-full h-full bg-brand-background ">
      {/* 主要内容区域 */}
      <main className="">
        {evaluationData.map((category) => (
          <EvaluationCategory
            key={category.level}
            category={category}
            items={category.items}
          />
        ))}
      </main>
    </div>
  );
}

export default Bloom;
