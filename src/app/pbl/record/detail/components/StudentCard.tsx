'use client';

import { <PERSON><PERSON><PERSON>D<PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, ClipboardList } from 'lucide-react';
import clsx from 'clsx';
import DevelopmentGuide from './DevelopmentGuide';
import LeuvenScales from './LeuvenScales';
import Bloom from './Bloom';
import { memo, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAtom, useSetAtom } from 'jotai';
import { evaluationAtom, studentAtom } from '@/store/pbl';
import DeeperLearning from './DeeperLearning';
import { getSchoolScales } from '@/api/pbl';

interface AbilityTarget {
  id: number;
  name: string;
  completed: boolean;
}

interface AbilityCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  targets: AbilityTarget[];
  subCategories?: {
    name: string;
    targets: AbilityTarget[];
  }[];
}

interface Student {
  id: number;
  name: string;
  avatar: string;
  abilities?: string[];
  evaluation?: {
    observationText: string;
    observationId: string;
    abilities: {
      abilityId: string;
    }[];
  };
}

interface StudentCardProps {
  readonly?: boolean;
  student: Student;
  toggleStudentExpand: (studentId: number, e: React.MouseEvent) => void;
  isStudentExpanded: (studentId: number) => boolean;
}

const tabs = [
  { id: 'guide', type: '2', label: '3-6 岁发展指南' },
  { id: 'leuven', type: '3', label: '幸福感与参与度' },
  { id: 'deeperLearning', type: '1', label: '深度学习' },
  { id: 'bloom', type: '4', label: '布卢姆分类法评估' }
];

function StudentCard({
  readonly = false,
  student,
  toggleStudentExpand,
  isStudentExpanded
}: StudentCardProps) {
  const router = useRouter();
  // 在组件内部管理 activeTab 状态
  const [activeTab, setActiveTab] = useState<string>('guide'); // 默认选中第一个 Tab
  const setStudent = useSetAtom(studentAtom);
  const [allEvaluations, setAllEvaluations] = useAtom(evaluationAtom);
  const [schoolScales, setSchoolScales] = useState<string[]>([]);

  const evaluations =
    student.evaluation?.abilities?.map((a: any) => a.abilityId) || [];

  useEffect(() => {
    getSchoolScales().then((res: any) => {
      setSchoolScales(res.dimensions || []);
    });
  }, []);

  return (
    <div
      key={student.id}
      className={clsx(
        'bg-white overflow-hidden',
        !readonly &&
          'border border-gray-100 rounded-lg shadow-sm transition-shadow hover:shadow-lg'
      )}
    >
      {/* 学生基本信息和简化版能力图标 */}
      <div
        className="flex justify-between items-center px-3 py-3"
        onClick={(e) => toggleStudentExpand(student.id, e)}
      >
        <div className="flex items-center">
          <img
            src={student.avatar}
            alt={student.name}
            className="w-10 h-10 rounded-full mr-2 border-2 border-white shadow-sm"
          />
          <div className="flex-grow">
            {student.name}
            {!readonly && (
              <div
                className="text-xs text-indigo-500"
                onClick={() => {
                  setStudent(student);
                  setAllEvaluations({
                    ...allEvaluations,
                    [student.id]: student.evaluation?.abilities?.reduce(
                      (acc, cur) => {
                        acc[cur.abilityId] = true;
                        return acc;
                      },
                      {}
                    )
                  });
                  router.push('/pbl/record/update/evaluation?isEdit=true');
                }}
              >
                编辑
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end items-center">
          {student.evaluation && !readonly && (
            <button
              type="button"
              onClick={(e) => toggleStudentExpand(student.id, e)}
              className="ml-2 p-1 rounded-full hover:bg-gray-100"
            >
              {isStudentExpanded(student.id) ? (
                <ChevronUp className="w-5 h-5 text-gray-400" />
              ) : (
                <ChevronDown className="w-5 h-5 text-gray-400" />
              )}
            </button>
          )}
        </div>
      </div>
      {readonly && student.evaluation?.observationText && (
        <div className="px-3 py-2">
          <div className="">{student.evaluation?.observationText || ''}</div>
        </div>
      )}

      {/* 展开后的能力详情 */}
      {isStudentExpanded(student.id) && student.evaluation && (
        <div className="px-4 pb-4 pt-2 border-t border-gray-100">
          <h3 className="text-md font-bold text-gray-700 mb-3 flex items-center gap-2">
            <ClipboardList className="w-5 h-5 text-amber-500" />
            <span>关联能力</span>
          </h3>
          {/* Tab 切换组件 */}
          <div className="mb-4">
            <div
              className="flex gap-2 overflow-x-auto scrollbar-hide whitespace-nowrap"
              style={{
                scrollbarWidth: 'none',
                WebkitOverflowScrolling: 'touch'
              }}
            >
              {tabs
                .filter((tab) => schoolScales.includes(tab.type))
                .map((tab) => (
                  <button
                    type="button"
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={clsx(
                      'px-3 py-0.5 text-xs rounded-full bg-gray-100',
                      activeTab === tab.id
                        ? 'bg-indigo-400 text-white shadow-sm'
                        : 'bg-gray-200 text-stone-900'
                    )}
                  >
                    {tab.label}
                  </button>
                ))}
            </div>
          </div>

          {/* 根据选中的 Tab 显示对应内容 */}
          {activeTab === 'guide' && (
            <DevelopmentGuide evaluations={evaluations} />
          )}
          {activeTab === 'leuven' && <LeuvenScales evaluations={evaluations} />}
          {activeTab === 'bloom' && <Bloom />}
          {activeTab === 'deeperLearning' && (
            <DeeperLearning evaluations={evaluations} />
          )}
        </div>
      )}
    </div>
  );
}

export default memo(StudentCard);
