'use client';

import { useState } from 'react';
import { <PERSON>up, TextArea, Toast, SpinLoading, Button } from 'antd-mobile';

interface EditTextDialogProps {
  visible: boolean;
  onClose: () => void;
  initialContent: string;
  onSave: (content: string) => void;
  onSuccess?: (newContent: string) => void;
  title?: string;
  placeholder?: string;
  rows?: number;
  height?: string;
  emptyMessage?: string;
  successMessage?: string;
  errorMessage?: string;
  cancelText?: string;
  saveText?: string;
}

export default function EditTextDialog({
  visible,
  onClose,
  initialContent,
  onSave,
  onSuccess,
  title = '编辑内容',
  placeholder = '请输入内容',
  rows = 12,
  height = '60vh',
  emptyMessage = '内容不能为空',
  successMessage = '保存成功',
  errorMessage = '保存失败，请重试',
  cancelText = '取消',
  saveText = '保存'
}: EditTextDialogProps) {
  const [content, setContent] = useState(initialContent);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSave = async () => {
    if (!content || !content.trim()) {
      Toast.show({
        content: emptyMessage,
        position: 'center'
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await onSave(content);

      Toast.show({
        icon: 'success',
        content: successMessage,
        position: 'center'
      });

      if (onSuccess) {
        onSuccess(content);
      }
      onClose();
    } catch (error) {
      console.error('保存失败：', error);
      Toast.show({
        icon: 'fail',
        content: errorMessage,
        position: 'center'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      position="bottom"
      bodyStyle={{
        height: height,
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        padding: '16px'
      }}
    >
      <div className="flex flex-col h-full">
        <div className="flex-1 overflow-auto">
          <h3 className="text-lg font-medium mb-2 text-center">{title}</h3>
          <TextArea
            value={content}
            onChange={setContent}
            placeholder={placeholder}
            rows={rows}
            className="bg-gray-50 rounded p-2 w-full"
            onFocus={(e) => {
              // 让输入框滚动到可视区域，避免被键盘遮挡
              e.target.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }}
          />
          {isSubmitting && (
            <div className="flex justify-center mt-4">
              <SpinLoading color="primary" />
            </div>
          )}
        </div>
        <div className="flex justify-between gap-2">
          <Button className="flex-1 mr-2" onClick={onClose}>
            {cancelText}
          </Button>
          <Button
            color="primary"
            className="flex-1 ml-2"
            loading={isSubmitting}
            disabled={isSubmitting}
            onClick={handleSave}
          >
            {saveText}
          </Button>
        </div>
      </div>
    </Popup>
  );
}
