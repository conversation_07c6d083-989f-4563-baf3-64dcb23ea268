'use client';

import { useEffect, useState } from 'react';
import { ClipboardList, Image } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getObservationDetail } from '@/api/pbl';

import Media from '../components/Media';
import StudentCard from '../components/StudentCard';
import { SpinLoading } from 'antd-mobile';
import { useImmer } from 'use-immer';

interface AbilityTarget {
  id: number;
  name: string;
  completed: boolean;
}

interface AbilityCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  targets: AbilityTarget[];
  subCategories?: {
    name: string;
    targets: AbilityTarget[];
  }[];
}

interface Student {
  id: string;
  name: string;
  avatar: string;
  abilities: string[];
  abilityCategories?: AbilityCategory[];
  progress?: string;
  evaluation: {
    observationId: string;
    abilities: {
      abilityId: string;
    }[];
  };
}

export default function App() {
  const searchParams = useSearchParams();
  const observationId = searchParams?.get('observationId');
  const studentId = searchParams?.get('studentId');

  const [loading, setLoading] = useState(true);
  const [record, setRecord] = useImmer({
    date: '',
    content: '',
    nextStepPlan: '',
    students: [],
    medias: [],
    createUser: {
      name: '',
      avatar: ''
    },
    createTime: '',
    deptId: '',
    conversation: []
  });

  const [student, setStudent] = useState<Student | null>(null);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录详情';
    }
  }, []);

  useEffect(() => {
    if (observationId) {
      setLoading(true);
      getObservationDetail(observationId)
        .then((res) => {
          setRecord(res);
          setStudent(res.students.find((student) => student.id === studentId));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [observationId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <SpinLoading color="primary" />
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col relative">
      <div className="flex-grow overflow-y-auto pb-20">
        {!!student && (
          <StudentCard
            readonly={true}
            student={student}
            isStudentExpanded={() => true}
          />
        )}

        <div className="px-4">
          {record.medias.length > 0 && (
            <>
              <h3 className="text-md font-bold text-gray-700 mb-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Image className="w-5 h-5 text-green-500" />
                  <span>关联文件</span>
                  <span className="text-xs text-gray-500 font-normal">
                    ({record.medias.length})
                  </span>
                </div>
              </h3>
              <Media media={record.medias} />
            </>
          )}
          {/* 观察记录内容 */}
          <div className="mb-6">
            <h3 className="text-md font-bold text-gray-700 mb-2 flex items-center gap-2">
              <ClipboardList className="w-5 h-5 text-amber-500" />
              <span>观察记录</span>
            </h3>
            <div className="text-gray-700 leading-relaxed text-base bg-amber-50 p-4 rounded-2xl">
              {record.content}
            </div>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={record.createUser?.avatar || ''}
                alt="老师头像"
                className="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <p className="text-sm font-medium text-dark">记录人</p>
                <p className="text-sm text-gray-600">
                  {record.createUser?.name} · {record.date} {record.createTime}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
