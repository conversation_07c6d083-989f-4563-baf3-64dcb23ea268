'use client';

import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { ErrorBlock, InfiniteScroll, PullToRefresh } from 'antd-mobile';
import { clsx } from 'clsx';
import { ChevronRight, Search, Users } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useCallback, useEffect, useState } from 'react';

import { getObservationList, getObservationStudentList } from '@/api/pbl';
import { getMessage } from '@/utils';

import AddRecordButton from '../list/components/AddRecordButton';
import RecordItem from '../list/components/RecordItem';

interface Child {
  studentId: number;
  name: string;
  avatar: string;
  cnt: number;
}

interface ChildItemProps {
  child: Child;
}

// --- 儿童项组件 ---
function ChildItem({ child }: ChildItemProps) {
  const router = useRouter();
  const { studentId, name, avatar } = child;

  return (
    <div className="border-b border-stone-100 p-4 transition-all duration-200 ease-in-out">
      <div
        className="flex items-center justify-between"
        onClick={() => {
          router.push(
            `/pbl/record/list?studentId=${studentId}&studentName=${name}`
          );
        }}
      >
        {/* 头像和姓名 */}
        <div className="flex items-center">
          <img
            src={avatar}
            alt={`${name} 的头像`}
            className="mr-3 size-12 cursor-pointer rounded-full border-2 border-gray-100 object-cover"
          />
          <span className="text-text-primary text-base font-medium">
            {name}
          </span>
        </div>
        <div>
          <span className="text-sm text-stone-400">{child.cnt} 条记录</span>
          <ChevronRight size={20} className="text-gray-400" />
        </div>
      </div>
    </div>
  );
}

const pageSize = 10;

export default function App() {
  const searchParams = useSearchParams();
  const classId = searchParams?.get('id') || '';
  const className = searchParams?.get('name') || '';
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useQueryState('activeTab', {
    defaultValue: 'all'
  });

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '班级观察记录';
    }
  }, []);

  // 防抖处理搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);

  // 获取班级观察记录列表（分页）
  const {
    data: infiniteObservationData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingObservations,
    refetch: refetchObservations
  } = useInfiniteQuery({
    queryKey: ['observationList', classId],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await getObservationList({
        deptId: classId,
        page: pageParam,
        perPage: pageSize
      });

      // 确保返回完整的分页信息，包括数据和分页状态
      // 以安全的方式访问返回数据
      const responseData = response as unknown;
      const records = Array.isArray(responseData)
        ? responseData
        : Array.isArray((responseData as any).list)
          ? (responseData as any).list
          : [];

      return {
        data: records,
        page: pageParam,
        hasMore: records.length >= pageSize
      };
    },
    getNextPageParam: (lastPage) => {
      // 如果没有更多数据，返回 undefined 表示不需要加载下一页
      if (
        lastPage.hasMore === false ||
        !lastPage.data ||
        lastPage.data.length < pageSize
      ) {
        return undefined;
      }
      return lastPage.page + 1;
    },
    enabled: activeTab === 'all' && !!classId,

    staleTime: 60 * 1000, // 1分钟内不会自动重新获取数据
    refetchOnWindowFocus: false // 窗口聚焦时不重新获取数据
  });

  useEffect(() => {
    getMessage(onMessage);
  }, []);

  // 获取到原生通知
  const onMessage = useCallback((event: { data: string }) => {
    console.log('获取到原生通知 data: ', event);
    try {
      const data = JSON.parse(event.data);
      console.log('🚀 ~ data:', data);
      if (data.activity_on_resume) {
        refetchObservations();
      }
    } catch (error) {
      console.log('onMessage', error);
    }
  }, []);

  // 获取班级学生列表（不分页）
  const {
    data: studentListData = [],
    isLoading: isLoadingStudents,
    refetch: refetchStudents
  } = useQuery({
    queryKey: ['observationStudentList', classId],
    queryFn: async () => {
      const response = await getObservationStudentList(classId);
      return ((response.data || response).list || []) as Child[];
    },
    enabled: activeTab === 'student' && !!classId,
    staleTime: 60 * 1000, // 1 分钟内不会自动重新获取数据
    refetchOnWindowFocus: false // 窗口聚焦时不重新获取数据
  });

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  // 处理标签页切换
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  // 处理全部记录标签页的下拉刷新
  const handleRefreshAll = async () => {
    // 直接使用 refetch 方法刷新数据
    return refetchObservations();
  };

  // 处理按学生标签页的下拉刷新
  const handleRefreshStudent = async () => {
    // 直接使用 refetch 方法刷新数据
    return refetchStudents();
  };

  // 从 infiniteObservationData 中提取记录数据
  const recordsList = infiniteObservationData?.pages
    ? infiniteObservationData.pages.flatMap((page) => {
        if (page) {
          return page.data || [];
        }
        return [];
      })
    : [];

  // 过滤学生列表
  const filteredStudentList = studentListData
    ? studentListData
        .filter((child: Child) =>
          child.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        )
        .sort((a: Child, b: Child) => {
          const aCnt = typeof a.cnt === 'number' ? a.cnt : 0;
          const bCnt = typeof b.cnt === 'number' ? b.cnt : 0;
          return aCnt - bCnt;
        })
    : [];

  return (
    <main className="min-h-screen bg-slate-50">
      {/* 页面头部 */}
      <div className="sticky top-0 z-10 bg-white px-4 py-3">
        {/* 班级信息 */}
        <div className="mb-3 flex items-center justify-between">
          <div className="flex items-center">
            <Users size={18} className="text-brand-primary mr-2" />
            <span className="text-base">{className}</span>
          </div>
          <div className="relative flex rounded-md bg-gray-200 p-0.5">
            <div
              className={clsx(
                'absolute z-0 h-8 w-20 rounded-sm bg-white shadow-sm shadow-gray-300/20 transition-transform duration-300 ease-in-out',
                {
                  'translate-x-0': activeTab === 'all',
                  'translate-x-full': activeTab === 'student'
                }
              )}
            />
            <button
              type="button"
              onClick={() => handleTabChange('all')}
              className="text-base-content z-10 h-8 w-20 rounded-sm"
            >
              全部
            </button>
            <button
              type="button"
              onClick={() => handleTabChange('student')}
              className="text-base-content z-10 h-8 w-20 rounded-sm"
            >
              按学生
            </button>
          </div>
        </div>
      </div>

      {/* 全部记录标签页 */}
      {activeTab === 'all' && (
        <div className="overflow-y-auto px-4 py-2">
          <PullToRefresh
            onRefresh={handleRefreshAll}
            canReleaseText="释放立即刷新"
            completeText="刷新成功"
            completeDelay={500}
          >
            {isLoadingObservations ? (
              <div className="py-10 text-center">加载中...</div>
            ) : infiniteObservationData?.pages.length === 0 ||
              recordsList.length === 0 ? (
              <div className="py-10 text-center">
                <ErrorBlock status="empty" title="暂无观察记录" />
              </div>
            ) : (
              <div className="mb-12">
                {/* 这里传递实际的记录数据给 Records 组件 */}
                {recordsList.map((record) => (
                  <div key={record.id}>
                    <RecordItem record={record} />
                  </div>
                ))}
                {/* 使用 InfiniteScroll 组件 */}
                <InfiniteScroll
                  loadMore={loadMore}
                  hasMore={!!hasNextPage}
                  threshold={250}
                  className="!py-2"
                >
                  {isFetchingNextPage ? (
                    <div className="py-3 text-center">
                      <span className="text-sm text-gray-500">
                        加载更多数据...
                      </span>
                    </div>
                  ) : hasNextPage ? (
                    <div className="py-3 text-center">
                      <span className="text-sm text-gray-500">
                        上拉加载更多
                      </span>
                    </div>
                  ) : (
                    <div className="py-3 text-center">
                      <span className="text-sm text-gray-500">
                        没有更多数据了
                      </span>
                    </div>
                  )}
                </InfiniteScroll>
              </div>
            )}
          </PullToRefresh>
        </div>
      )}

      {/* 按学生标签页 */}
      {activeTab === 'student' && (
        <div className="overflow-y-auto p-4">
          <PullToRefresh
            onRefresh={handleRefreshStudent}
            canReleaseText="释放立即刷新"
            completeText="刷新成功"
            completeDelay={500}
          >
            <div className="mb-4 flex items-center space-x-2">
              <div className="relative flex-1">
                <input
                  type="text"
                  placeholder="搜索幼儿姓名..."
                  className="focus:ring-brand-primary w-full rounded-lg border border-gray-200 py-2 pl-9 pr-3 text-sm focus:outline-none focus:ring-1"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                />
                {searchTerm && (
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    onClick={() => setSearchTerm('')}
                  >
                    ×
                  </button>
                )}
              </div>
            </div>

            {isLoadingStudents ? (
              <div className="py-10 text-center">加载中...</div>
            ) : filteredStudentList.length > 0 ? (
              <>
                <div className="mb-2 text-sm text-gray-500">
                  共 {studentListData?.length || 0} 名幼儿
                  {debouncedSearchTerm &&
                    `，搜索"${debouncedSearchTerm}"的结果 (${filteredStudentList.length})`}
                </div>
                <div className="mb-12 space-y-2 rounded-lg bg-white">
                  {filteredStudentList.map((child: Child) => (
                    <ChildItem key={child.studentId} child={child} />
                  ))}
                </div>
              </>
            ) : (
              <div className="py-10 text-center">
                <ErrorBlock status="empty" title="暂无符合条件的幼儿" />
              </div>
            )}
          </PullToRefresh>
        </div>
      )}
      <AddRecordButton classId={classId} />
    </main>
  );
}
