import React, { useState } from 'react';
import { Popup, Swiper } from 'antd-mobile';
import type { SwiperRef } from 'antd-mobile';

interface DevelopmentGuideExampleProps {
  visible: boolean;
  onClose: () => void;
  levels: {
    id: string;
    level: string;
    examples: string[];
  }[];
}

function DevelopmentGuideExample({
  visible,
  onClose,
  levels,
}: DevelopmentGuideExampleProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = React.useRef<SwiperRef>(null);

  const handleSwiperChange = (index: number) => {
    setActiveIndex(index);
  };

  return (
    <Popup visible={visible} onMaskClick={onClose} position="bottom">
      <div className="flex flex-col h-full bg-white">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h3 className="m-0 text-lg font-medium text-gray-800">
            发展水平参考示例
          </h3>
          <div
            className="text-2xl text-gray-500 cursor-pointer w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
            onClick={onClose}
          >
            ×
          </div>
        </div>

        <Swiper
          ref={swiperRef}
          defaultIndex={0}
          onIndexChange={handleSwiperChange}
          loop={false}
          slideSize={100}
          trackOffset={0}
          stuckAtBoundary={true}
        >
          {levels.map((level) => (
            <Swiper.Item key={level.id} className="p-4">
              <div className="h-full flex flex-col border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 py-3 text-xl font-semibold text-gray-800 mb-4 text-center">
                  {level.level}
                </div>
                <div className="flex-1 flex flex-col gap-3 p-4">
                  {level.examples.map((example, i) => (
                    <div
                      key={`${level.id}-example-${i}`}
                      className="text-base text-gray-600 leading-relaxed"
                    >
                      {example}
                    </div>
                  ))}
                </div>
              </div>
            </Swiper.Item>
          ))}
        </Swiper>
      </div>
    </Popup>
  );
}

export default DevelopmentGuideExample;
