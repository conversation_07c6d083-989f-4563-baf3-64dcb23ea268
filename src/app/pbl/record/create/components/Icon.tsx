import clsx from 'clsx';
import type { SVGProps } from 'react';
import {
  Calendar,
  Heart,
  MessageCircle,
  Users,
  Palette,
  Check,
  ChevronUp,
  ChevronDown,
  Send,
  PlayCircle,
  Atom,
  CircleHelp
} from 'lucide-react';

export type IconName =
  | 'Heart'
  | 'MessageCircle'
  | 'Users'
  | 'Palette'
  | 'Check'
  | 'ChevronUp'
  | 'ChevronDown'
  | 'Send'
  | 'PlayCircle'
  | 'Calendar'
  | 'Atom'
  | 'CircleHelp';

// 图标组件
export function Icon({
  name,
  className = '',
  ...props
}: { name: IconName; className?: string } & SVGProps<SVGSVGElement>) {
  const IconComponent = {
    Heart,
    MessageCircle,
    Users,
    Palette,
    Check,
    ChevronUp,
    ChevronDown,
    Send,
    PlayCircle,
    Calendar,
    Atom,
    CircleHelp
  }[name];

  // 如果找不到对应的图标组件，返回 null
  if (!IconComponent) {
    console.warn(`Icon component for "${name}" not found`);
    return null;
  }

  // 渲染对应的图标组件
  return (
    <IconComponent className={clsx('inline-block', className)} {...props} />
  );
}

export default Icon;
