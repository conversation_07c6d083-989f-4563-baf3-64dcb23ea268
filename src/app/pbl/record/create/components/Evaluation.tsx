import type React from 'react';
import {
  useState,
  useRef,
  useEffect,
  useCallback,
  forwardRef,
  useImperativeHandle
} from 'react';
import { useAtom } from 'jotai'; // 引入 useAtom
import { evaluationAtom } from '@/store/pbl'; // 引入 evaluationAtom
import DevelopmentGuide from './DevelopmentGuide';
import LeuvenScales from './LeuvenScales';
import Bloom from './Bloom';
import DeeperLearning from './DeeperLearning';
import { getSchoolScales } from '@/api/pbl';

// 定义评估类型数据结构
interface TabItem {
  id: string;
  type: string;
  title: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

// 定义组件引用暴露的方法类型
export interface EvaluationRef {
  // getEvaluations 返回当前学生的所有评估项 (abilityId: boolean)
  getEvaluations: () => Record<string, boolean>;
  // 不再需要 setEvaluations，父组件通过 atom 操作
}

interface EvaluationProps {
  studentId: string; // 添加 studentId prop
  // initialEvaluations 不再需要，数据来自 atom
}

const Evaluation = forwardRef<EvaluationRef, EvaluationProps>(
  function Evaluation({ studentId }, ref) {
    const [allEvaluations] = useAtom(evaluationAtom);

    const [activeTab, setActiveTab] = useState('guide');
    const [showLeftArrow, setShowLeftArrow] = useState(false);
    const [showRightArrow, setShowRightArrow] = useState(false);
    const [schoolScales, setSchoolScales] = useState<string[]>([]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getEvaluations: () => {
        // 返回当前 studentId 对应的评估数据
        return allEvaluations[studentId] || {};
      }
      // 移除 setEvaluations
    }));

    // 滚动相关
    const tabsRef = useRef<HTMLDivElement>(null);
    // 添加 tabRefs 对象，用于存储每个标签的 ref
    const tabRefs = useRef(new Map());

    useEffect(() => {
      getSchoolScales().then((res) => {
        setSchoolScales(res.dimensions || []);
      });
    }, []);

    // 滚动检测函数
    const checkScroll = useCallback(() => {
      if (!tabsRef.current) return;

      const { scrollLeft, scrollWidth, clientWidth } = tabsRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 5); // 5px 容差
    }, []);

    // 滚动函数
    const scrollTabs = (direction: 'left' | 'right') => {
      if (!tabsRef.current) return;

      const scrollAmount = 200; // 每次滚动的像素
      const currentScroll = tabsRef.current.scrollLeft;

      tabsRef.current.scrollTo({
        left:
          direction === 'left'
            ? currentScroll - scrollAmount
            : currentScroll + scrollAmount,
        behavior: 'smooth'
      });
    };

    // 添加滚动到中心的函数
    const scrollTabToCenter = useCallback(
      (tabId: string) => {
        if (!tabsRef.current || !tabRefs.current.has(tabId)) return;

        const tabElement = tabRefs.current.get(tabId);
        const container = tabsRef.current;

        // 计算居中位置
        const tabRect = tabElement.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // 计算需要滚动的位置，使标签居中
        const centerPosition =
          tabElement.offsetLeft + tabRect.width / 2 - containerRect.width / 2;

        // 使用平滑滚动效果
        container.scrollTo({
          left: Math.max(0, centerPosition),
          behavior: 'smooth'
        });

        // 滚动后检查是否需要显示左右滚动按钮
        setTimeout(checkScroll, 300);
      },
      [checkScroll]
    );

    // 监听滚动事件
    useEffect(() => {
      const tabsElement = tabsRef.current;
      if (tabsElement) {
        checkScroll();
        tabsElement.addEventListener('scroll', checkScroll);
        window.addEventListener('resize', checkScroll);

        return () => {
          tabsElement.removeEventListener('scroll', checkScroll);
          window.removeEventListener('resize', checkScroll);
        };
      }
    }, [checkScroll]);

    // 监听 activeTab 变化，自动滚动到选中的标签
    useEffect(() => {
      if (activeTab) {
        scrollTabToCenter(activeTab);
      }
    }, [activeTab, scrollTabToCenter]);

    // 定义评估类型数据
    const tabs: TabItem[] = [
      {
        id: 'guide',
        type: '2',
        title: '3-6 岁儿童学习与发展指南',
        icon: (
          <svg
            className={`w-4 h-4 me-2 ${activeTab === 'guide' ? 'text-indigo-600 dark:text-indigo-500' : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300'}`}
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 5a3 3 0 1 1 0 6 3 3 0 0 1 0-6Zm0 13a8.949 8.949 0 0 1-4.951-1.488A3.987 3.987 0 0 1 9 13h2a3.987 3.987 0 0 1 3.951 3.512A8.949 8.949 0 0 1 10 18Z" />
          </svg>
        ),
        // 传递 studentId 给子组件
        content: <DevelopmentGuide studentId={studentId} />
      },
      {
        id: 'luwen',
        type: '3',
        title: '幸福感与参与度',
        icon: (
          <svg
            className={`w-4 h-4 me-2 ${activeTab === 'luwen' ? 'text-indigo-600 dark:text-indigo-500' : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300'}`}
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 18 18"
          >
            <path d="M6.143 0H1.857A1.857 1.857 0 0 0 0 1.857v4.286C0 7.169.831 8 1.857 8h4.286A1.857 1.857 0 0 0 8 6.143V1.857A1.857 1.857 0 0 0 6.143 0Zm10 0h-4.286A1.857 1.857 0 0 0 10 1.857v4.286C10 7.169 10.831 8 11.857 8h4.286A1.857 1.857 0 0 0 18 6.143V1.857A1.857 1.857 0 0 0 16.143 0Zm-10 10H1.857A1.857 1.857 0 0 0 0 11.857v4.286C0 17.169.831 18 1.857 18h4.286A1.857 1.857 0 0 0 8 16.143v-4.286A1.857 1.857 0 0 0 6.143 10Zm10 0h-4.286A1.857 1.857 0 0 0 10 11.857v4.286c0 1.026.831 1.857 1.857 1.857h4.286A1.857 1.857 0 0 0 18 16.143v-4.286A1.857 1.857 0 0 0 16.143 10Z" />
          </svg>
        ),
        // 传递 studentId 给子组件
        content: <LeuvenScales studentId={studentId} />
      },
      {
        id: 'deeperLearning',
        type: '1',
        title: '深度学习',
        icon: (
          <svg
            className={`w-4 h-4 me-2 ${activeTab === 'luwen' ? 'text-indigo-600 dark:text-indigo-500' : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300'}`}
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 18 18"
          >
            <path d="M6.143 0H1.857A1.857 1.857 0 0 0 0 1.857v4.286C0 7.169.831 8 1.857 8h4.286A1.857 1.857 0 0 0 8 6.143V1.857A1.857 1.857 0 0 0 6.143 0Zm10 0h-4.286A1.857 1.857 0 0 0 10 1.857v4.286C10 7.169 10.831 8 11.857 8h4.286A1.857 1.857 0 0 0 18 6.143V1.857A1.857 1.857 0 0 0 16.143 0Zm-10 10H1.857A1.857 1.857 0 0 0 0 11.857v4.286C0 17.169.831 18 1.857 18h4.286A1.857 1.857 0 0 0 8 16.143v-4.286A1.857 1.857 0 0 0 6.143 10Zm10 0h-4.286A1.857 1.857 0 0 0 10 11.857v4.286c0 1.026.831 1.857 1.857 1.857h4.286A1.857 1.857 0 0 0 18 16.143v-4.286A1.857 1.857 0 0 0 16.143 10Z" />
          </svg>
        ),
        // 传递 studentId 给子组件
        content: <DeeperLearning studentId={studentId} />
      },
      {
        id: 'bloom',
        type: '4',
        title: '布卢姆分类法评估',
        icon: (
          <svg
            className={`w-4 h-4 me-2 ${activeTab === 'bloom' ? 'text-indigo-600 dark:text-indigo-500' : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300'}`}
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M5 11.424V1a1 1 0 1 0-2 0v10.424a3.228 3.228 0 0 0 0 6.152V19a1 1 0 1 0 2 0v-1.424a3.228 3.228 0 0 0 0-6.152ZM19.25 14.5A3.243 3.243 0 0 0 17 11.424V1a1 1 0 0 0-2 0v10.424a3.227 3.227 0 0 0 0 6.152V19a1 1 0 1 0 2 0v-1.424a3.243 3.243 0 0 0 2.25-3.076Zm-6-9A3.243 3.243 0 0 0 11 2.424V1a1 1 0 0 0-2 0v1.424a3.228 3.228 0 0 0 0 6.152V19a1 1 0 1 0 2 0V8.576A3.243 3.243 0 0 0 13.25 5.5Z" />
          </svg>
        ),
        content: <Bloom />
      }
    ];

    return (
      <div className="bg-white rounded-lg">
        {/* 标签导航栏 */}
        <div className="relative">
          {/* 左滚动按钮 */}
          {showLeftArrow && (
            <button
              type="button"
              onClick={() => scrollTabs('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 p-1 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-all"
              aria-label="向左滚动"
            >
              <svg
                className="w-5 h-5 text-gray-600 dark:text-gray-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>向左滚动</title>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          )}

          {/* 可滚动的标签容器 */}
          <div
            ref={tabsRef}
            className="overflow-x-auto hide-scrollbar mb-2"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            <ul className="flex whitespace-nowrap text-sm font-medium text-center text-gray-500 dark:text-gray-400">
              {tabs
                .filter((tab) => schoolScales.includes(tab.type))
                .map((tab) => (
                  <li
                    key={tab.id}
                    className="me-4"
                    ref={(el) => {
                      if (el) {
                        tabRefs.current.set(tab.id, el);
                      } else {
                        tabRefs.current.delete(tab.id);
                      }
                    }}
                  >
                    <button
                      onClick={() => {
                        setActiveTab(tab.id);
                        // 不需要在这里调用 scrollTabToCenter，因为我们已经在 useEffect 中监听了 activeTab 的变化
                      }}
                      className={`inline-flex items-center justify-center p-3 border-b-2 rounded-t-lg group transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'text-indigo-600 border-indigo-600 dark:text-indigo-500 dark:border-indigo-500'
                          : 'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
                      }`}
                      aria-current={activeTab === tab.id ? 'page' : undefined}
                      type="button"
                    >
                      {tab.icon}
                      <span className="font-medium">{tab.title}</span>
                    </button>
                  </li>
                ))}
            </ul>
          </div>

          {/* 右滚动按钮 */}
          {showRightArrow && (
            <button
              onClick={() => scrollTabs('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 p-1 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-all"
              aria-label="向右滚动"
              type="button"
            >
              <svg
                className="w-5 h-5 text-gray-600 dark:text-gray-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>向右滚动</title>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          )}
        </div>

        {/* 内容区域 */}
        <div className="">
          {tabs.find((tab) => tab.id === activeTab)?.content}
        </div>

        {/* 自定义样式 */}
        <style jsx>{`
          .hide-scrollbar::-webkit-scrollbar {
            display: none;
          }
        `}</style>
      </div>
    );
  }
);

export default Evaluation;
