import { getObservationEvaluation } from '@/api/pbl';
import React, { useEffect, useState } from 'react';
import clsx from 'clsx';
import { Check } from 'lucide-react';
import { useAtom } from 'jotai';
import { evaluationAtom } from '@/store/pbl';

export default function DeeperLearning({ studentId }: { studentId: string }) {
  const [allEvaluations, setAllEvaluations] = useAtom(evaluationAtom); // 获取 setter 用于更新
  const studentEvaluations = allEvaluations[studentId] || {};

  const [evaluationData, setEvaluationData] = useState<
    {
      id: string;
      abilityName: string;
      description: string;
    }[]
  >([]);

  useEffect(() => {
    getObservationEvaluation({
      dimensionType: 1
    }).then((res) => {
      console.log(res);
      //@ts-ignore
      const data = res?.abilities || [];
      setEvaluationData(data);
    });
  }, []);
  console.log('DeeperLearning for student:', studentId);
  return (
    <div>
      {evaluationData.map((item) => (
        <div
          key={item.id}
          className={clsx(
            'bg-white p-4 rounded-xl border mb-4 relative shadow-md',
            studentEvaluations[item.id]
              ? 'bg-indigo-50 border-indigo-400' // Highlight background for selected row
              : 'border-gray-300' // Hover effect for non-selected rows
          )}
          onClick={() => {
            setAllEvaluations({
              ...allEvaluations,
              [studentId]: {
                ...studentEvaluations,
                [item.id]: !studentEvaluations[item.id]
              }
            });
          }}
        >
          <h4 className="font-semibold mb-1.5">{item.abilityName}</h4>
          <p className="text-sm mb-2 text-gray-600">{item.description}</p>
          <div
            className={clsx(
              'absolute size-5 flex items-center justify-center top-2 right-2 rounded-full p-1 shadow-sm',
              studentEvaluations[item.id]
                ? 'bg-indigo-500' // Highlight background for selected row
                : 'bg-gray-300'
            )}
          >
            <Check className="size-5 text-white" />
          </div>
        </div>
      ))}
    </div>
  );
}
