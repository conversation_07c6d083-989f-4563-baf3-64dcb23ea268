'use client';

import { format } from 'date-fns';
import Image from 'next/image';
import React, { useLayoutEffect, useRef } from 'react';
import { useImmer } from 'use-immer';

import type { AudioPlayerRef } from '@/components/AudioPlayer';
import AudioPlayer from '@/components/AudioPlayer';
import { replaceAll } from '@/lib/helper';
import { getMessage, getNativeMessage, postMessage } from '@/utils';

export const dynamic = 'force-dynamic';

export default function Page() {
  const [title, setTitle] = React.useState('');
  const [htmlContent, setHtmlContent] = React.useState('');
  const [createUser, setCreateUser] = React.useState('--');
  const [createTime, setCreateTime] = React.useState(
    new Date().getTime() / 1000,
  );
  const [musicUrl, setMusicUrl] = React.useState('');
  const audioPlayerRef = useRef<AudioPlayerRef>(null);

  const [template, setTemplate] = useImmer({
    headerBackgroundImage: '',
    backgroundColor: '',
    footerBackgroundImage: '',
  });

  useLayoutEffect(() => {
    getNativeMessage(onNativeMessage);
    getMessage(onMessage);
    // 通知RN页面加载完成
    postMessage({ loaded: true });
  }, []);

  // 获取到RN的通知
  const onMessage = (event: any) => {
    const data = JSON.parse(event.data);
    console.log('获取到RN的通知 ------- ', JSON.stringify(data));
    if (data.title) {
      setTitle(data.title);
    }
    if (data.htmlContent) {
      const newContent = replaceAll(
        data.htmlContent,
        'http://mediatx.ancda.com',
        'https://mediatx.ancda.com',
      );
      setHtmlContent(newContent);
    }
    if (data.createUser) {
      setCreateUser(data.createUser);
    }
    if (data.createTime) {
      setCreateTime(data.createTime);
    }
    if (data.header !== undefined && data.bottom !== undefined) {
      setTemplate((draft) => {
        draft.headerBackgroundImage = data.header;
        draft.backgroundColor = data.bgColor;
        draft.footerBackgroundImage = data.bottom;
      });
    }
    if (data.musicUrl) {
      setMusicUrl(data.musicUrl);
    }
  };

  // 获取到RN的通知
  const onNativeMessage = (data: any) => {
    console.log('获取到RN的通知 ------- ', JSON.stringify(data));
    if (data.title) {
      setTitle(data.title);
    }
    if (data.htmlContent) {
      setHtmlContent(data.htmlContent);
    }
    if (data.createUser) {
      setCreateUser(data.createUser);
    }
    if (data.createTime) {
      setCreateTime(data.createTime);
    }
    if (data.header !== undefined && data.bottom !== undefined) {
      setTemplate((draft) => {
        draft.headerBackgroundImage = data.header;
        draft.backgroundColor = data.bgColor;
        draft.footerBackgroundImage = data.bottom;
      });
    }
    if (data.musicUrl) {
      setMusicUrl(data.musicUrl);
    }
  };

  return (
    <div className="relative h-screen w-screen bg-cover bg-top bg-no-repeat">
      {template.headerBackgroundImage ? (
        <Image
          alt="Mountains"
          src={template.headerBackgroundImage}
          quality={100}
          width={390}
          height={208}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={{
            width: '100%',
            height: 'auto',
          }}
        />
      ) : null}
      <div
        className={template.backgroundColor ? 'px-4' : 'px-2'}
        style={{ backgroundColor: template.backgroundColor ?? '#FFF' }}
      >
        <div className="rounded-xl bg-white p-4">
          <div className="mb-4">
            <h1 className="my-2">{title || '通知标题'}</h1>
            <div className="flex flex-row justify-between">
              <div className="text-sm text-gray-400">
                {format(new Date(createTime * 1000), 'yyyy-MM-dd HH:mm')}
              </div>
              <div
                className="text-sm text-gray-400"
                onClick={() => {
                  console.log('3333');
                }}
              >
                来源：{createUser}
              </div>
            </div>
          </div>
          <div
            className=""
            style={{
              minHeight: '200px',
            }}
            dangerouslySetInnerHTML={{ __html: htmlContent }}
          />
        </div>
      </div>
      {template.footerBackgroundImage ? (
        <Image
          alt="Mountains"
          src={template.footerBackgroundImage}
          quality={100}
          width={390}
          height={208}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={{
            width: '100%',
            height: 'auto',
          }}
        />
      ) : null}
      {!!musicUrl && <AudioPlayer ref={audioPlayerRef} musicUrl={musicUrl} />}
    </div>
  );
}
