'use client';

import { format } from 'date-fns';
import Head from 'next/head';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import React, { useLayoutEffect, useRef, useState } from 'react';

import { getNoticeInfo } from '@/api/notice';
import type { AudioPlayerRef } from '@/components/AudioPlayer';
import AudioPlayer from '@/components/AudioPlayer';
import Layout from '@/components/layout/Layout';
import { replaceAll } from '@/lib/helper';
import { wechatShare } from '@/utils/wechat';

export const dynamic = 'force-dynamic';

export default function Preview() {
  const searchParams = useSearchParams();
  const noticeId = searchParams?.get('noticeId');
  const [title, setTitle] = useState('');
  const [htmlContent, setHtmlContent] = useState('');
  const [createUser, setCreateUser] = useState('');
  const [createTime, setCreateTime] = useState(0);
  const [musicUrl, setMusicUrl] = useState('');
  const audioPlayerRef = useRef<AudioPlayerRef>(null);

  const [template, setTemplate] = useState({
    headerBackgroundImage: '',
    backgroundColor: '',
    footerBackgroundImage: '',
  });

  useLayoutEffect(() => {
    if (noticeId) {
      getNotice();
    }
  }, [noticeId]);

  // 获取到RN的通知
  const getNotice = () => {
    getNoticeInfo({ noticeId }).then((res: any) => {
      const { data } = res;
      if (data.title) {
        setTitle(data.title);
      }
      if (data.content) {
        const newContent = replaceAll(
          data.content,
          'http://mediatx.ancda.com',
          'https://mediatx.ancda.com',
        );
        setHtmlContent(newContent);
      }
      if (data.createUser) {
        setCreateUser(data.createUser);
      }
      if (data.createTime) {
        setCreateTime(data.createTime);
      }
      if (data.background) {
        setTemplate({
          headerBackgroundImage: data.background?.head,
          backgroundColor: data.background?.bgColor,
          footerBackgroundImage: data.background?.bottom,
        });
      }
      if (data.musicUrl) {
        setMusicUrl(data.musicUrl);
      }
      const wxData = {
        title: `${data?.createUser}分享了${data?.title}`,
        desc: data?.title,
        link: window.location.href,
        imgUrl: data?.cover || '',
      };
      wechatShare(wxData);
    });
  };

  return (
    <Layout>
      <Head>
        <title>通知详情</title>
      </Head>
      <div className="relative h-screen w-screen bg-cover bg-top bg-no-repeat">
        {template.headerBackgroundImage ? (
          <Image
            alt="Mountains"
            src={template.headerBackgroundImage}
            quality={100}
            width={390}
            height={208}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            style={{
              width: '100%',
              height: 'auto',
            }}
          />
        ) : null}
        <div
          className={template.backgroundColor ? 'px-4' : 'px-2'}
          style={{ backgroundColor: template.backgroundColor || '#FFF' }}
        >
          <div className="rounded-xl bg-white p-4">
            <div className="mb-4">
              <h1 className="my-2">{title}</h1>
              <div className="flex flex-row justify-between">
                <div className="text-sm text-gray-400">
                  {format(new Date(createTime * 1000), 'yyyy-MM-dd HH:mm')}
                </div>
                <div className="text-sm text-gray-400">来源：{createUser}</div>
              </div>
            </div>
            <div
              className=""
              style={{
                minHeight: '200px',
                lineHeight: '1.5',
              }}
              dangerouslySetInnerHTML={{ __html: htmlContent }}
            />
          </div>
        </div>
        {template.footerBackgroundImage ? (
          <Image
            alt="Mountains"
            src={template.footerBackgroundImage}
            quality={100}
            width={390}
            height={208}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            style={{
              width: '100%',
              height: 'auto',
            }}
          />
        ) : null}
        {!!musicUrl && <AudioPlayer ref={audioPlayerRef} musicUrl={musicUrl} />}
      </div>
    </Layout>
  );
}
