import { Editor } from '@tinymce/tinymce-react';
import React, { useLayoutEffect, useRef } from 'react';
import type { Editor as TinyMCEEditor } from 'tinymce';

import { getMessage, getNativeMessage, postMessage } from '@/utils';

import initFullProps from './initFullProps';

export default function App() {
  const editorRef = useRef<TinyMCEEditor | null>(null);
  const [data, setData] = React.useState('');

  useLayoutEffect(() => {
    getNativeMessage(onNativeMessage);
    getMessage(onMessage);
  }, []);

  // 获取到RN的通知
  const onMessage = (event: any) => {
    const data = JSON.parse(event.data);
    console.log('获取到RN的通知 ------- ', JSON.stringify(data));
    if (data.htmlContent) {
      setData(data.htmlContent);
    }
    if (editorRef.current) {
      // 插入图片
      if (data.imageUrl) {
        const imageHTML = `<p><br/></p><p><img src="${data.imageUrl}" alt="" style="max-width: 100%;"/></p><p><br/></p>`;
        editorRef.current.insertContent(imageHTML);
      }
      // 插入视频
      if (data.videoUrl) {
        const imageHTML = `<p><br/></p><p><video src="${data.videoUrl}" controls="controls" style="width: 100%;height: auto" poster="${data.videoUrl}?x-workflow-graph-name=video-thumbnail"></video></p><p><br/></p>`;
        editorRef.current.insertContent(imageHTML);
      }
    }
  };

  // 获取到RN的通知
  const onNativeMessage = (data: any) => {
    console.log('获取到RN的通知 ------- ', JSON.stringify(data));
    if (data.htmlContent) {
      setData(data.htmlContent);
    }
    if (editorRef.current) {
      // 插入图片
      if (data.imageUrl) {
        const imageHTML = `<p><br/></p><p><img src="${data.imageUrl}" alt="" style="max-width: 100%;"/></p><p><br/></p>`;
        editorRef.current.insertContent(imageHTML);
      }
      // 插入视频
      if (data.videoUrl) {
        const imageHTML = `<p><br/></p><p><video src="${data.videoUrl}" controls="controls" style="width: 100%;height: auto" poster="${data.videoUrl}?x-workflow-graph-name=video-thumbnail"></video></p><p><br/></p>`;
        editorRef.current.insertContent(imageHTML);
      }
    }
  };

  return (
    <Editor
      onInit={(evt, editor) => {
        editorRef.current = editor;
        // 通知RN加载完毕
        postMessage({ loaded: true });
      }}
      initialValue={data}
      tinymceScriptSrc="/tinymce/tinymce.min.js"
      init={{
        ...initFullProps,
      }}
      onEditorChange={(data) => {
        console.log('🚀 ~ file: MyEditor.tsx:56 ~ data:', data);
        postMessage({
          data,
        });
      }}
    />
  );
}
