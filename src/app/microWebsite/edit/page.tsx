'use client';

import { Editor } from '@tinymce/tinymce-react';
import { Toast } from 'antd-mobile';
import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef } from 'react';
import type { Editor as TinyMCEEditor } from 'tinymce';

import { getProfileInfo, updateProfileInfo } from '@/api/microWebsite';
import PreviewPage from '@/app/microWebsite/components/Preview';
import { initFullProps } from '@/utils/editor';

import { defaultHtml } from '../mock';

export default function EditContent() {
  const instId = Cookies.get('instId');
  const router = useRouter();
  const editorRef = useRef<TinyMCEEditor | null>(null);
  const [isPreview, setIsPreview] = React.useState(false);
  const [data, setData] = React.useState(defaultHtml);
  const dataRef = useRef(data);

  const handleViewClick = () => {
    setData(dataRef.current);
    setIsPreview(!isPreview);
  };

  useEffect(() => {
    getProfileInfo({
      instId
    }).then((res: any) => {
      if (res.profile) {
        dataRef.current = res.profile;
        setData(res.profile);
      }
    });
  }, [instId]);

  const updateContent = () => {
    updateProfileInfo({
      instId,
      profile: dataRef.current
    }).then((res) => {
      if (res) {
        router.back();
        Toast.show({
          content: '更新成功',
          position: 'top',
          duration: 3000
        });
      }
    });
  };
  return (
    <div className="h-screen">
      {isPreview ? (
        <PreviewPage
          html={dataRef.current}
          handleOutViewClick={handleViewClick}
        />
      ) : (
        <>
          <div
            style={{
              height: 'calc(100vh - 60px)'
            }}
          >
            <Editor
              onInit={(evt, editor) => {
                editorRef.current = editor;
              }}
              initialValue={data}
              tinymceScriptSrc="/tinymce/tinymce.min.js"
              init={{
                ...initFullProps
              }}
              onEditorChange={(data) => {
                dataRef.current = data;
              }}
            />
          </div>

          <div
            className="flex h-[60px] items-center justify-center space-x-4 border-t border-gray-200 bg-white px-6"
            style={{
              height: '60px'
            }}
          >
            <div
              onClick={handleViewClick}
              className="rounded-md border border-blue-600 px-6 py-2 text-blue-600 transition-colors duration-200 hover:bg-blue-50"
            >
              预览
            </div>
            <div
              onClick={updateContent}
              className="rounded-md bg-blue-600 px-6 py-2 text-white transition-colors duration-200 hover:bg-blue-700"
            >
              提交
            </div>
          </div>
        </>
      )}
    </div>
  );
}
