'use client';

import { motion } from 'framer-motion';
import { GraduationCap, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

const HomePage = () => {
  const router = useRouter();
  if (typeof document !== 'undefined') {
    document.title = '招生宣传';
  }

  return (
    <div
      className="
    ustify-center 
    flex 
    h-screen 
    items-start 
    bg-gradient-to-br from-orange-50 
    via-rose-50 to-amber-50 p-6"
      // 加上背景https://file.ancda.com/public/template/2021-07/nupe1b5bx.jpeg
      style={{
        backgroundImage:
          'url(https://file.ancda.com/public/template/2021-07/nupe1b5bx.jpeg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <div className="mx-auto mt-28 w-full  max-w-2xl space-y-2">
        {/* 招生宣传入口 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          whileHover={{ scale: 1.02 }}
          className="cursor-pointer rounded-xl bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md"
        >
          <div
            className="flex items-center space-x-4"
            onClick={() => router.push('/microWebsite/publicity')}
          >
            <div className="rounded-xl bg-orange-50 p-2">
              <GraduationCap className="size-6 text-orange-600" />
            </div>
            <h2 className="text-base font-medium text-gray-900">招生宣传</h2>
          </div>
        </motion.div>

        {/* 报名咨询入口 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          whileHover={{ scale: 1.02 }}
          className="cursor-pointer rounded-xl bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md"
        >
          <div
            className="flex items-center space-x-2"
            onClick={() => router.push('/microWebsite/consultation')}
          >
            <div className="rounded-xl bg-orange-50 p-2">
              <Users className="size-6 text-orange-600" />
            </div>
            <h2 className="text-base font-medium text-gray-900">报名咨询</h2>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default HomePage;
