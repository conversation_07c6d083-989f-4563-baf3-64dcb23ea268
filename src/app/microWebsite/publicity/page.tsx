'use client';

import { Toast } from 'antd-mobile';
import Cookies from 'js-cookie';
import { File<PERSON>enLine, Share2 } from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { useImmer } from 'use-immer';

import { getProfileInfo } from '@/api/microWebsite';
import { share } from '@/utils/index';

import EditButton from '../components/EditButton';
import FormIndex from '../components/FormIndex';
import NewFormIndex from '../components/NewFormIndex';
import { defaultHtml, whitelistInstId } from '../mock';

export default function Page() {
  if (typeof document !== 'undefined') {
    document.title = '招生宣传';
  }

  const searchParams = useSearchParams();
  const paramsInstId = searchParams.get('instId');
  const [htmlContent, setHtmlContent] = React.useState(defaultHtml);
  const instId = Cookies.get('instId');
  const instName = Cookies.get('instName');
  const [template] = useImmer({
    headerBackgroundImage:
      'https://file.ancda.com/public/template/2019-11/1qd8gecerg.jpeg',
    backgroundColor: '#098F5A',
    footerBackgroundImage:
      'https://file.ancda.com/public/template/2019-11/1qf9wm63ys.jpeg',
  });
  const formRef = React.useRef<HTMLDivElement>(null);
  const [showButton, setShowButton] = useState(true);
  const router = useRouter();
  const htmlContentMemo = useMemo(() => {
    return {
      __html: htmlContent,
    };
  }, [htmlContent]);
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setShowButton(!entry.isIntersecting);
        });
      },
      {
        // rootMargin: '0px 0px -100px 0px',
      },
    );
    if (formRef.current) {
      observer.observe(formRef.current);
    }
    return () => {
      if (formRef.current) {
        observer.unobserve(formRef.current);
      }
    };
  }, []);
  useEffect(() => {
    getProfileInfo({
      instId: instId || paramsInstId,
    }).then((res) => {
      if (res.profile) {
        setHtmlContent(res.profile);
      }
    });
  }, [instId, paramsInstId]);
  const handleButtonClick = () => {
    if (formRef.current) {
      formRef.current.scrollIntoView({
        behavior: 'smooth',
      });
    }
  };
  const handleEditClick = () => {
    console.log('handleEditClick: ');
    router.push('/microWebsite/edit');
  };

  const handleShareClick = async () => {
    // 要分享的数据
    Toast.show('已复制分享链接');

    const shareData = {
      type: 0,
      title: `宝贝分享了Ta喜欢的学校${instName}`,
      description: instName,
      thumbImage: template.headerBackgroundImage,
      url: `${window.location.href}?instId=${instId}`,
    };
    share(shareData);
  };
  return (
    <div className="relative h-screen w-screen bg-cover bg-top bg-no-repeat">
      {template.headerBackgroundImage && (
        <div className="relative">
          <Image
            alt="Mountains"
            src={template.headerBackgroundImage}
            quality={100}
            width={390}
            height={208}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            style={{
              width: '100%',
              height: 'auto',
            }}
          />
          <div
            style={{
              width: '100%',
              height: '40px',
              position: 'absolute',
              bottom: 0,
              background: `linear-gradient(to bottom,
            transparent,
           ${template.backgroundColor}
          )`,
            }}
          />
        </div>
      )}
      <div
        className={`${template.backgroundColor ? 'px-4' : 'px-2'}`}
        style={{
          backgroundColor: template.backgroundColor ?? '#FFF',
        }}
      >
        <div className="rounded-xl bg-white p-4">
          <div
            className=""
            style={{
              minHeight: '200px',
            }}
            dangerouslySetInnerHTML={htmlContentMemo}
          />
          {/* 预约报名 */}
          <div id="FormIndex" ref={formRef}>
            {whitelistInstId.includes(paramsInstId || instId) ? (
              <NewFormIndex
                backgroundColor={template.backgroundColor}
                instId={paramsInstId || instId}
              />
            ) : (
              <FormIndex
                backgroundColor={template.backgroundColor}
                instId={paramsInstId || instId}
              />
            )}
          </div>
        </div>
      </div>
      {template.footerBackgroundImage && (
        <Image
          alt="Mountains"
          src={template.footerBackgroundImage}
          quality={100}
          width={390}
          height={208}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={{
            width: '100%',
            height: 'auto',
          }}
        />
      )}
      <EditButton
        showButton={showButton}
        onClick={handleButtonClick}
        template={template}
      />
      {!paramsInstId && (
        <>
          <EditButton
            showButton
            onClick={handleEditClick}
            template={template}
            textAry={[FilePenLine, '编辑']}
            className="bottom-24 right-6"
          />
          <CopyToClipboard
            text={`${window.location.href}?instId=${instId}`}
            onCopy={(text: string, result: boolean) => {
              handleShareClick();
            }}
          >
            <EditButton
              showButton
              template={template}
              textAry={[Share2, '分享']}
              className="bottom-44 right-6"
            />
          </CopyToClipboard>
        </>
      )}
    </div>
  );
}
