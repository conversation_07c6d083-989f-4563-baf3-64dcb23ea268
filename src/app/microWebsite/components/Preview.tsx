'use client';

import Cookies from 'js-cookie';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useImmer } from 'use-immer';

import { whitelistInstId } from '../mock';
import EditButton from './EditButton';
import FormIndex from './FormIndex';
import NewFormIndex from './NewFormIndex';

export default function PreviewPage({ handleOutViewClick, html }) {
  const [template] = useImmer({
    headerBackgroundImage:
      'https://file.ancda.com/public/template/2019-11/1qd8gecerg.jpeg',
    backgroundColor: '#098F5A',
    footerBackgroundImage:
      'https://file.ancda.com/public/template/2019-11/1qf9wm63ys.jpeg',
  });
  const [showButton, setShowButton] = useState(true);
  const router = useRouter();

  const instId = Cookies.get('instId');

  const formRef = React.useRef<HTMLDivElement>(null);

  const htmlContentMemo = useMemo(() => {
    console.log('html', html);
    return {
      __html: html,
    };
  }, [html]);
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setShowButton(!entry.isIntersecting);
        });
      },
      {
        // rootMargin: '0px 0px -100px 0px',
      },
    );

    if (formRef.current) {
      observer.observe(formRef.current);
    }

    return () => {
      if (formRef.current) {
        observer.unobserve(formRef.current);
      }
    };
  }, []);
  const handleButtonClick = () => {
    if (formRef.current) {
      formRef.current.scrollIntoView({
        behavior: 'smooth',
      });
    }
  };

  return (
    <div className="relative h-screen w-screen bg-cover bg-top bg-no-repeat">
      {template.headerBackgroundImage && (
        <div className="relative">
          <Image
            alt="Mountains"
            src={template.headerBackgroundImage}
            quality={100}
            width={390}
            height={208}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            style={{
              width: '100%',
              height: 'auto',
            }}
          />
          <div
            style={{
              width: '100%',
              height: '40px',
              position: 'absolute',
              bottom: 0,
              background: `linear-gradient(to bottom,
            transparent,
           ${template.backgroundColor}
          )`,
            }}
          />
        </div>
      )}
      <div
        className={`${template.backgroundColor ? 'px-4' : 'px-2'}`}
        style={{
          backgroundColor: template.backgroundColor ?? '#FFF',
        }}
      >
        <div className="rounded-xl bg-white p-4">
          <div
            className=""
            style={{
              minHeight: '200px',
            }}
            dangerouslySetInnerHTML={htmlContentMemo}
          />
          {/* 预约报名 */}
          <div id="FormIndex" ref={formRef}>
            {whitelistInstId.includes(instId) ? (
              <NewFormIndex
                backgroundColor={template.backgroundColor}
                instId={instId}
              />
            ) : (
              <FormIndex
                backgroundColor={template.backgroundColor}
                instId={instId}
              />
            )}
          </div>
        </div>
      </div>
      {template.footerBackgroundImage && (
        <Image
          alt="Mountains"
          src={template.footerBackgroundImage}
          quality={100}
          width={390}
          height={208}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={{
            width: '100%',
            height: 'auto',
          }}
        />
      )}
      <EditButton
        showButton={showButton}
        onClick={handleButtonClick}
        template={template}
      />
      <EditButton
        showButton
        onClick={handleOutViewClick}
        template={template}
        textAry={['退出', '预览']}
        className="bottom-24 right-6"
      />
    </div>
  );
}
