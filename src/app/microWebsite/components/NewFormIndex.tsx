'use client';

import {
  Button,
  Checkbox,
  DatePicker,
  Form,
  Input,
  Picker,
  Radio,
  Toast,
} from 'antd-mobile';
import { format } from 'date-fns';
import React, { useState } from 'react';

import { postRegistration } from '@/api/microWebsite';
import { relations } from '@/utils/filters';
import { generateString, getEnv, uploadObs } from '@/utils/obs';

import CustomImageUploader from './CustomImageUploader';

const FormIndex = ({ backgroundColor, instId }) => {
  const [form] = Form.useForm();
  const [pickerVisible, setPickerVisible] = useState(false);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [parentIdentityLabel, setParentIdentityLabel] = useState(1);
  const [birthDateLabel, setBirthDateLabel] = useState('请选择出生日期');
  // 添加图片上传状态
  const [childHouseholdPhoto, setChildHouseholdPhoto] = useState([]);
  const [parentCertificatePhoto, setParentCertificatePhoto] = useState([]);
  const [childBirthCertificatePhoto, setChildBirthCertificatePhoto] = useState(
    [],
  );
  const [childPreventVaccinationPhoto, setChildPreventVaccinationPhoto] =
    useState([]);

  // 图片上传函数
  const uploadImage = async (file) => {
    return new Promise((resolve, reject) => {
      Toast.show({
        icon: 'loading',
        content: '上传中...',
        duration: 0,
      });

      const env = getEnv();
      const date = format(new Date(), 'yyyy-MM-dd');
      const key = `${env}/waitingForPickup/${date}/${generateString(8)}.png`;
      uploadObs(file, key, false)
        .then((url) => {
          resolve({
            url,
          });
        })
        .catch((err) => {
          console.log('upload error', err);
          Toast.show({
            icon: 'fail',
            content: '上传失败',
            position: 'bottom',
          });
          reject(err);
        })
        .finally(() => {
          Toast.clear();
        });
    });
  };

  const onFinish = async (values) => {
    console.log('values: ', values);
    try {
      await postRegistration({
        instId,
        name: values.babyName,
        gender: values.gender,
        birthday: values.birthDate ? values.birthDate?.getTime() / 1000 : null,
        parentName: values.parentName,
        parentMobile: values.parentPhone,
        parentRelation: values.parentIdentity,
        applyVisit: values.visitRequest,
        referrerId: 0,
        // 添加新字段
        childHouseholdPhoto: childHouseholdPhoto?.map((item) => item.url),
        parentCertificatePhoto: parentCertificatePhoto?.map((item) => item.url),
        childBirthCertificatePhoto: childBirthCertificatePhoto?.map(
          (item) => item.url,
        ),
        childPreventVaccinationPhoto: childPreventVaccinationPhoto?.map(
          (item) => item.url,
        ),
        motherName: values.motherName,
        motherMobile: values.motherMobile,
      });
      form.resetFields();
      // 重置图片上传状态
      setChildHouseholdPhoto([]);
      setParentCertificatePhoto([]);
      setChildBirthCertificatePhoto([]);
      setChildPreventVaccinationPhoto([]);
      Toast.show({
        content: '提交成功！',
        position: 'bottom',
        duration: 2000,
      });
    } catch (error) {
      Toast.show({
        content: '提交失败，请稍后再试',
        position: 'bottom',
        duration: 2000,
      });
      console.error('报名失败:', error);
    }
  };

  return (
    <div className="px2 mt-8 rounded-lg bg-white py-6">
      <div className="mb-8 text-center">
        <div className="relative inline-block px-8 py-2">
          <h3 className="relative z-10 text-2xl font-bold text-gray-900">
            预约报名
          </h3>
          {/* 底部装饰线 */}
          <div className="absolute bottom-0 left-0 -z-[1] h-3 w-full -rotate-1 bg-blue-100" />
          {/* 背景框 */}
          <div className="absolute inset-0 -z-[2] rounded-lg border-2 border-blue-200 bg-white/50" />
          {/* 装饰点缀 */}
          <div className="absolute -right-2 -top-2 size-4 rounded-full bg-blue-100" />
          <div className="absolute -bottom-2 -left-2 size-4 rounded-full bg-blue-100" />
        </div>
        <div className="mt-4 flex items-center justify-center gap-4">
          <div className="h-px w-16 bg-gray-300" />
          <span className="text-sm text-gray-500">欢迎您加入我们</span>
          <div className="h-px w-16 bg-gray-300" />
        </div>
      </div>

      <Form
        form={form}
        onFinish={onFinish}
        className="space-y-6"
        style={{ '--border-inner': 'none' }}
      >
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* 宝贝姓名 */}
          <div className="flex items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="babyName"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              宝贝姓名 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="babyName"
              rules={[{ required: true }]}
              className="m-0 flex-1 "
            >
              <Input
                placeholder="请输入宝贝姓名"
                className="h-7 border-none bg-transparent focus:border-blue-500 focus:outline-none"
                style={{ '--font-size': '14px' }}
              />
            </Form.Item>
          </div>

          {/* 宝贝性别 */}
          <div className="flex items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="gender"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              宝贝性别
            </label>
            <Form.Item name="gender" className="m-0 flex-1" initialValue="male">
              <Radio.Group>
                <Radio
                  value="1"
                  className="mr-4 text-sm"
                  style={{
                    '--icon-size': '18px',
                    '--font-size': '14px',
                    '--gap': '8px',
                  }}
                >
                  男
                </Radio>
                <Radio
                  value="2"
                  className="text-sm"
                  style={{
                    '--icon-size': '18px',
                    '--font-size': '14px',
                    '--gap': '8px',
                  }}
                >
                  女
                </Radio>
              </Radio.Group>
            </Form.Item>
          </div>

          {/* 出生日期 */}
          <div className="flex items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="birthDate"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              出生日期 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="birthDate"
              rules={[{ required: true }]}
              className="m-0 flex-1"
            >
              <div
                className="flex h-7 cursor-pointer items-center text-sm text-gray-500"
                onClick={() => setDatePickerVisible(true)}
              >
                {birthDateLabel}
              </div>
              <DatePicker
                visible={datePickerVisible}
                onClose={() => setDatePickerVisible(false)}
                max={new Date()} // 添加最大日期限制为当前日期
                onConfirm={(val) => {
                  form.setFieldValue('birthDate', val);
                  setBirthDateLabel(val.toLocaleDateString('zh-CN'));
                  setDatePickerVisible(false);
                }}
              />
            </Form.Item>
          </div>
          <div className="rounded-md border border-gray-300 p-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
              幼儿城区户口本 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="childHouseholdPhoto"
              rules={[{ required: true, message: '请上传幼儿城区户口本照片' }]}
            >
              <CustomImageUploader
                value={childHouseholdPhoto}
                onChange={setChildHouseholdPhoto}
                upload={uploadImage}
                multiple
                maxCount={9}
                showUpload={childHouseholdPhoto.length < 9}
                className="rounded-md"
              />
            </Form.Item>
          </div>
          <div className="rounded-md border border-gray-300 p-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
              法定监护人房产证 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="parentCertificatePhoto"
              rules={[
                { required: true, message: '请上传法定监护人房产证照片' },
              ]}
            >
              <CustomImageUploader
                value={parentCertificatePhoto}
                onChange={setParentCertificatePhoto}
                upload={uploadImage}
                multiple
                maxCount={9}
                showUpload={childHouseholdPhoto.length < 9}
                className="rounded-md"
              />
            </Form.Item>
          </div>
          <div className="rounded-md border border-gray-300 p-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
              幼儿出生证 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="childBirthCertificatePhoto"
              rules={[{ required: true, message: '请上传幼儿出生证照片' }]}
            >
              <CustomImageUploader
                value={childBirthCertificatePhoto}
                onChange={setChildBirthCertificatePhoto}
                upload={uploadImage}
                multiple
                maxCount={9}
                showUpload={childHouseholdPhoto.length < 9}
                className="rounded-md"
              />
            </Form.Item>
          </div>
          <div className="rounded-md border border-gray-300 p-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
              幼儿预防接种证 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="childPreventVaccinationPhoto"
              rules={[{ required: true, message: '请上传幼儿预防接种证照片' }]}
            >
              <CustomImageUploader
                value={childPreventVaccinationPhoto}
                onChange={setChildPreventVaccinationPhoto}
                upload={uploadImage}
                multiple
                maxCount={9}
                showUpload={childHouseholdPhoto.length < 9}
                className="rounded-md"
              />
            </Form.Item>
          </div>
          {/* 家长姓名 */}
          <div className="flex items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="parentName"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              父亲姓名 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="parentName"
              className="m-0 flex-1"
              rules={[{ required: true }]}
            >
              <Input
                placeholder="请输入父亲姓名"
                className="h-7 border-none bg-transparent focus:border-blue-500 focus:outline-none"
                style={{ '--font-size': '14px' }}
              />
            </Form.Item>
          </div>

          {/* 家长手机号 */}
          <div className="flex items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="parentPhone"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              父亲电话 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="parentPhone"
              rules={[
                { required: true },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
              ]}
              className="m-0 flex-1"
            >
              <Input
                placeholder="请输入手机号"
                type="tel"
                className="h-7 border-none bg-transparent focus:border-blue-500 focus:outline-none"
                style={{ '--font-size': '14px' }}
                maxLength={11}
              />
            </Form.Item>
          </div>
          {/* 家长姓名 */}
          <div className="flex items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="motherName"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              母亲姓名 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="motherName"
              className="m-0 flex-1"
              rules={[{ required: true }]}
            >
              <Input
                placeholder="请输入父亲姓名"
                className="h-7 border-none bg-transparent focus:border-blue-500 focus:outline-none"
                style={{ '--font-size': '14px' }}
              />
            </Form.Item>
          </div>

          {/* 家长手机号 */}
          <div className="flex items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="motherMobile"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              母亲电话 <span className="text-red-500">*</span>
            </label>
            <Form.Item
              name="motherMobile"
              rules={[
                { required: true },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
              ]}
              className="m-0 flex-1"
            >
              <Input
                placeholder="请输入手机号"
                type="tel"
                className="h-7 border-none bg-transparent focus:border-blue-500 focus:outline-none"
                style={{ '--font-size': '14px' }}
                maxLength={11}
              />
            </Form.Item>
          </div>

          {/* 家长身份 */}
          <div className="flex hidden items-center gap-4 rounded-md border border-gray-300 px-2">
            <label
              htmlFor="parentIdentity"
              className="block w-[170px] text-sm font-medium text-gray-700"
            >
              家长身份
            </label>
            <Form.Item name="parentIdentity" className="m-0 flex-1">
              <div
                className="flex h-7 cursor-pointer items-center text-sm text-gray-500"
                onClick={() => setPickerVisible(true)}
              >
                {parentIdentityLabel}
              </div>
              <Picker
                columns={[relations]}
                visible={pickerVisible}
                onClose={() => setPickerVisible(false)}
                onConfirm={(val) => {
                  form.setFieldValue('parentIdentity', val[0]);
                  const selectedOption = relations.find(
                    (option) => option.value === val[0],
                  );
                  setParentIdentityLabel(
                    selectedOption ? selectedOption.label : '请选择家长身份',
                  );
                  setPickerVisible(false);
                }}
              />
            </Form.Item>
          </div>
        </div>

        {/* 是否申请入校参观 */}
        <div className="flex hidden items-center gap-4 rounded-md">
          <Form.Item
            name="visitRequest"
            valuePropName="checked"
            className="m-0"
          >
            <Checkbox
              className="text-sm font-medium text-gray-700"
              style={{
                '--icon-size': '18px',
                '--font-size': '14px',
                '--gap': '6px',
              }}
            >
              申请入校参观
            </Checkbox>
          </Form.Item>
        </div>
        {/* 提交按钮 */}
        <div className="mt-6">
          <Button
            type="submit"
            block
            className="w-full rounded-md px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            style={{ backgroundColor }}
          >
            <span className=" text-white">提交报名</span>
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default FormIndex;
