'use client';

import React from 'react';

const EditButton = ({
  onClick,
  showButton,
  template,
  textAry = ['预约', '报名'],
  className = 'bottom-6 right-6 ',
}: any) => {
  return (
    <div
      onClick={onClick}
      className={`fixed ${className}
flex size-16 transform-gpu 
cursor-pointer flex-col items-center 
justify-center rounded-full
shadow-lg
transition-all duration-300 ease-in-out
${showButton ? 'scale-100' : 'scale-0'}`}
      style={{
        backgroundColor: template.backgroundColor ?? '#098F5A',
      }}
    >
      <div className="text-center font-bold text-white">
        {textAry.map((item, index) => {
          if (typeof item === 'string') {
            return (
              <div
                key={index.toString()}
                className="text-sm"
                style={{
                  textShadow: `
      -1px -1px 0 rgba(255,255,255,0.3),
      1px -1px 0 rgba(0,0,0,0.2),
      -1px 1px 0 rgba(0,0,0,0.2),
      1px 1px 0 rgba(0,0,0,0.2)
    `,
                }}
              >
                {item}
              </div>
            );
          }
          // 如果是组件，直接渲染它
          const IconComponent = item;
          return (
            <IconComponent
              key={index.toString()}
              className="size-4 text-white"
            />
          );
        })}
      </div>
    </div>
  );
};
export default EditButton;
