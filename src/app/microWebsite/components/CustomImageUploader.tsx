import { AddOutline, CloseCircleFill } from 'antd-mobile-icons';
import type { ChangeEvent } from 'react';
import React, { useRef } from 'react';

interface ImageItem {
  url: string;
  file: File;
}

interface CustomImageUploaderProps {
  value?: ImageItem[];
  onChange?: (images: ImageItem[]) => void;
  multiple?: boolean;
  maxCount?: number;
  showUpload?: boolean;
  upload?: (file: File) => Promise<{ url: string }>;
  className?: string;
}

const CustomImageUploader: React.FC<CustomImageUploaderProps> = ({
  value = [],
  onChange,
  multiple = false,
  maxCount = 9,
  showUpload = true,
  upload,
  className = '',
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (e: ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (!files || files.length === 0) return;

    const newImages: ImageItem[] = [];
    const filesToProcess = Array.from(files).slice(0, maxCount - value.length);

    for (const file of filesToProcess) {
      try {
        if (upload) {
          const result = await upload(file);
          newImages.push({ url: result.url, file });
        } else {
          const url = URL.createObjectURL(file);
          newImages.push({ url, file });
        }
      } catch (error) {
        console.error('图片处理失败:', error);
      }
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    onChange?.([...value, ...newImages]);
  };

  const handleDelete = (index: number) => {
    const newImages = [...value];
    if (newImages[index].url.startsWith('blob:')) {
      URL.revokeObjectURL(newImages[index].url);
    }
    newImages.splice(index, 1);
    onChange?.(newImages);
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {value.map((image, index) => (
        <div
          key={index}
          className="relative size-24 overflow-hidden rounded-md border border-gray-200"
        >
          <img
            src={image.url}
            alt={`上传图片 ${index + 1}`}
            className="size-full object-cover"
          />
          <button
            type="button"
            onClick={() => handleDelete(index)}
            className="absolute right-1 top-1 rounded-full bg-black bg-opacity-50 p-1 text-white"
          >
            <CloseCircleFill fontSize={16} />
          </button>
        </div>
      ))}

      {/* 上传按钮 */}
      {showUpload && value.length < maxCount && (
        <div
          onClick={triggerFileSelect}
          className="flex size-24 cursor-pointer flex-col items-center justify-center rounded-md border border-dashed border-gray-300 bg-gray-50 hover:border-blue-500 hover:bg-gray-100"
        >
          <AddOutline fontSize={24} className="text-gray-400" />
          <span className="mt-1 text-xs text-gray-500">上传图片</span>
        </div>
      )}

      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        accept="*"
        multiple={multiple}
        className="hidden"
        onChange={handleFileSelect}
      />
    </div>
  );
};

export default CustomImageUploader;
