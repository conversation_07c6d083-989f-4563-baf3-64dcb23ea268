'use client';

import {
  <PERSON><PERSON>,
  Dialog,
  ImageViewer,
  List,
  TextArea,
  Toast,
} from 'antd-mobile';
import { format } from 'date-fns';
import Cookies from 'js-cookie';
import { ChevronRight } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import {
  addFollowup,
  getRegistrationDetail,
  updateRegistrationDetail,
} from '@/api/microWebsite'; // Import your API functions here
import { relationsFilter } from '@/utils/filters';

import { whitelistInstId } from '../../mock';

const ConsultationDetail = () => {
  if (typeof document !== 'undefined') {
    document.title = '咨询详情';
  }
  const searchParams = useSearchParams();
  const registrationId = searchParams.get('registrationId');
  const instId = Cookies.get('instId') || '';
  const userId = Cookies.get('userName') || '';
  const userName = Cookies.get('userName') || '';
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<number>(1);
  const [note, setNote] = useState('');
  const [data, setData] = useState<any>({});
  // 添加图片预览相关状态
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [statusOptions] = useState([
    {
      label: '待处理',
      value: 1,
    },
    {
      label: '跟进中',
      value: 2,
    },
    {
      label: '已报名',
      value: 3,
    },
    {
      label: '无效',
      value: 4,
    },
  ]);
  const updateStatus = (status: number) => {
    updateRegistrationDetail({
      ...data,
      parentRelation: data.parentRelation || 1,
      instId,
      status,
    })
      .then((res) => {
        setStatus(status);
        setNote('');
      })
      .finally(() => {
        Dialog.clear();
      });
  };
  const handleImageClick = (images: string[], index: number) => {
    setCurrentImages(images);
    setCurrentIndex(index);
    setImageViewerVisible(true);
  };
  const handleStatusChange = () => {
    Dialog.show({
      content: (
        <div className="p-4">
          {statusOptions.map((option) => (
            <div
              key={option.value}
              className="border-b border-gray-200 py-3"
              onClick={() => {
                updateStatus(option.value);
              }}
            >
              {option.label}
            </div>
          ))}
        </div>
      ),
    });
  };

  const handleAddNote = () => {
    setVisible(true);
  };
  useEffect(() => {
    if (registrationId) {
      getRegistrationDetail({
        instId,
        registrationId,
      }).then((res) => {
        setData(res);
        setStatus(res.status);
      });
    }
  }, [registrationId]);
  const addFollowupLog = () => {
    if (!note) {
      Toast.show({
        content: '请输入回访内容',
      });
      return;
    }
    addFollowup({
      instId,
      registrationId: data.registrationId,
      content: note,
    }).then((res) => {
      const followUpsId = data.followUps[data.followUps.length - 1]?.id + 1;
      const followUps = [
        {
          id: followUpsId,
          content: note,
          followUpTime: new Date().getTime() / 1000,
          userId,
          userName,
        },
        ...data.followUps,
      ];
      setData((pre) => ({
        ...pre,
        followUps,
      }));
      setVisible(false);
    });
  };
  const getAge = (birthday) => {
    if (!birthday) {
      return 0;
    }
    const birthDate = new Date(birthday * 1000);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };
  return (
    <div className="h-screen bg-gray-100">
      <div className="flex-1 overflow-y-auto pb-20 pt-2">
        <List mode="card" className="mb-4">
          <List.Item extra={data.name}>宝贝姓名</List.Item>
          <List.Item extra={data.gender === 1 ? '男' : '女'}>
            宝贝性别
          </List.Item>
          <List.Item extra={`${getAge(data.birthday)}岁`}>宝贝年龄</List.Item>
          <List.Item
            extra={format(
              new Date(data.birthday ? data.birthday * 1000 : new Date()),
              'yyyy-MM-dd',
            )}
          >
            出生日期
          </List.Item>
        </List>

        <List mode="card" className="mb-4">
          {data.motherName && (
            <List.Item extra={data.motherName}>学生妈妈</List.Item>
          )}
          {data.motherMobile && (
            <List.Item extra={data.motherMobile}>妈妈手机</List.Item>
          )}
          {whitelistInstId.includes(instId) ? (
            <>
              <List.Item extra={data.parentName}>学生爸爸</List.Item>
              <List.Item extra={data.parentMobile}>爸爸手机</List.Item>
            </>
          ) : (
            <>
              <List.Item extra={data.parentName}>家长名称</List.Item>
              <List.Item extra={data.parentMobile}>家长手机</List.Item>
              <List.Item extra={relationsFilter(data.parentRelation)}>
                家长身份
              </List.Item>
              <List.Item extra={data.applyVisit ? '是' : '否'}>
                是否入园参观
              </List.Item>
            </>
          )}
        </List>

        <List mode="card" className="mb-4">
          {data.childHouseholdPhoto?.length > 0 && (
            <List.Item>
              <div className="mb-2 font-medium">幼儿城区户口本</div>
              <div className="flex flex-wrap gap-2">
                {data.childHouseholdPhoto.map((photo, index) => (
                  <img
                    key={index}
                    src={photo}
                    alt="幼儿城区户口本"
                    className="size-24 cursor-pointer rounded object-cover"
                    onClick={() =>
                      handleImageClick(data.childHouseholdPhoto, index)
                    }
                  />
                ))}
              </div>
            </List.Item>
          )}
          {data.parentCertificatePhoto?.length > 0 && (
            <List.Item>
              <div className="mb-2 font-medium">法定监护人房产证</div>
              <div className="flex flex-wrap gap-2">
                {data.parentCertificatePhoto.map((photo, index) => (
                  <img
                    key={index}
                    src={photo}
                    alt="法定监护人房产证"
                    className="size-24 cursor-pointer rounded object-cover"
                    onClick={() =>
                      handleImageClick(data.parentCertificatePhoto, index)
                    }
                  />
                ))}
              </div>
            </List.Item>
          )}
          {data.childBirthCertificatePhoto?.length > 0 && (
            <List.Item>
              <div className="mb-2 font-medium">幼儿出生证</div>
              <div className="flex flex-wrap gap-2">
                {data.childBirthCertificatePhoto.map((photo, index) => (
                  <img
                    key={index}
                    src={photo}
                    alt="幼儿出生证"
                    className="size-24 cursor-pointer rounded object-cover"
                    onClick={() =>
                      handleImageClick(data.childBirthCertificatePhoto, index)
                    }
                  />
                ))}
              </div>
            </List.Item>
          )}
          {data.childPreventVaccinationPhoto?.length > 0 && (
            <List.Item>
              <div className="mb-2 font-medium">幼儿预防接种证</div>
              <div className="flex flex-wrap gap-2">
                {data.childPreventVaccinationPhoto.map((photo, index) => (
                  <img
                    key={index.toString()}
                    src={photo}
                    alt="幼儿预防接种证"
                    className="size-24 cursor-pointer rounded object-cover"
                    onClick={() =>
                      handleImageClick(data.childPreventVaccinationPhoto, index)
                    }
                  />
                ))}
              </div>
            </List.Item>
          )}
        </List>

        <List mode="card">
          <List.Item
            extra={statusOptions[status - 1]?.label}
            onClick={handleStatusChange}
            arrow={<ChevronRight className="size-5" />}
          >
            跟进状态
          </List.Item>
          <List.Item>
            回访记录
            <div className="mt-4">
              {data.followUps?.map((item, index) => {
                return (
                  <div
                    key={item.id}
                    className="relative border-l-2 border-gray-300 pb-8 pl-6"
                  >
                    {index === 0 && (
                      <div
                        className="absolute  top-0 rounded-full bg-[var(--adm-color-primary)]"
                        style={{
                          width: '18px',
                          height: '18px',
                          left: '-9px',
                          borderRadius: '50%',
                        }}
                      />
                    )}
                    <div
                      className="absolute bottom-0  size-4 rounded-full bg-gray-300"
                      style={{
                        width: '18px',
                        height: '18px',
                        left: '-9px',
                        borderRadius: '50%',
                      }}
                    />
                    <div className="text-sm text-gray-500">{item.userName}</div>
                    <div className="mt-2 text-gray-500">
                      {format(
                        new Date(item.followUpTime * 1000),
                        'yyyy-MM-dd HH:mm',
                      )}
                    </div>
                    <div className="rounded-lg bg-white py-3">
                      <div className="text-sm">{item.content}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </List.Item>
        </List>
      </div>
      <div className="fixed bottom-0 w-full bg-white p-4">
        <Button
          block
          color="primary"
          onClick={handleAddNote}
          className="bg-green-500"
        >
          添加回访记录
        </Button>
      </div>

      {/* 添加图片查看器组件 */}
      {imageViewerVisible && (
        <ImageViewer
          image={currentImages}
          visible={imageViewerVisible}
          onClose={() => setImageViewerVisible(false)}
          defaultIndex={currentIndex}
        />
      )}

      <Dialog
        visible={visible}
        title="添加回访记录"
        content={
          <TextArea
            placeholder="请输入回访内容"
            value={note}
            onChange={setNote}
            rows={4}
          />
        }
        closeOnAction
        onClose={() => setVisible(false)}
        actions={[
          {
            key: 'cancel',
            text: '取消',
          },
          {
            key: 'confirm',
            text: '确认',
            bold: true,
            onClick: () => {
              addFollowupLog();
            },
          },
        ]}
      />
    </div>
  );
};

export default ConsultationDetail;
