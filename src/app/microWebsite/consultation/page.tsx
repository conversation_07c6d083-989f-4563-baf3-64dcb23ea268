'use client';

import {
  DotLoading,
  InfiniteScroll,
  List,
  PullToRefresh,
  Tabs,
} from 'antd-mobile';
import Cookies from 'js-cookie';
import { Mail, Phone } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';

import { getRegistrationsList } from '@/api/microWebsite';

import { whitelistInstId } from '../mock';

const ConsultationList = () => {
  if (typeof document !== 'undefined') {
    document.title = '咨询列表';
  }
  const router = useRouter();
  const instId = Cookies.get('instId');
  const [data, setData] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [limit, setLimit] = useState(20);
  const pageToken = useRef('');
  const tabs = [
    { key: '1', title: '待处理' },
    { key: '2', title: '跟进中' },
    { key: '3', title: '已报名' },
    { key: '4', title: '无效' },
  ];

  const initData = async (status: string) => {
    try {
      setLoading(true);
      setError(null);
      const resData = await getRegistrationsList({
        instId,
        status,
        perPage: limit,
        pageToken: pageToken.current,
      });
      pageToken.current = resData.nextPageToken || '';
      setData(resData.registrations);
      setHasMore(true);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadMore = async () => {
    if (!hasMore) return;
    try {
      setError(null);
      const resData = await getRegistrationsList({
        instId,
        status: activeTab,
        perPage: limit,
        pageToken: pageToken.current,
      });
      pageToken.current = resData.nextPageToken;
      setHasMore(resData.registrations.length >= limit);
      setData((val) => [...val, ...resData.registrations]);
    } catch (err) {
      setError(err.message);
    }
  };

  const onRefresh = async () => {
    pageToken.current = '';
    await initData(activeTab);
  };

  const handleCall = (e, phone) => {
    e.stopPropagation();
    window.location.href = `tel:${phone}`;
  };

  const handleMessage = (e, phone: number) => {
    e.stopPropagation();
    window.location.href = `sms:${phone}`;
  };
  useEffect(() => {
    pageToken.current = '';
    initData(activeTab);
  }, [activeTab]);
  const LoadingMore = () => (
    <div className="flex items-center justify-center py-4">
      <span className="mr-2">加载中</span>
      <DotLoading />
    </div>
  );
  const NoMoreData = () => (
    <div className="py-4 text-center text-sm text-gray-500">— 没有更多了 —</div>
  );
  // if (error) {
  //   return (
  //     <div className="flex h-screen items-center justify-center">
  //       <ErrorBlock
  //         title="加载失败"
  //         description={error}
  //         onRetry={() => initData(activeTab)}
  //       />
  //     </div>
  //   );
  // }
  // 根据出生日期算年龄
  const getAge = (birthday) => {
    const birthDate = new Date(birthday * 1000);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };
  return (
    <div className="min-h-screen bg-gray-100">
      <Tabs activeKey={activeTab} onChange={setActiveTab} className="bg-white">
        {tabs.map((tab) => (
          <Tabs.Tab
            title={tab.title}
            key={tab.key}
            style={{ '--title-font-size': '14px', '--color': '#333' }}
          />
        ))}
      </Tabs>
      <PullToRefresh onRefresh={onRefresh}>
        {loading && data.length === 0 ? (
          <div className="flex h-32 items-center justify-center">
            <span className="mr-2">加载中</span>
            <DotLoading />
          </div>
        ) : data.length === 0 ? (
          <div className="flex h-32 items-center justify-center text-gray-500">
            暂无数据
          </div>
        ) : (
          <>
            <List mode="card" className="mb-4">
              {data.map((item, index) => (
                <List.Item
                  key={item.registrationId}
                  className="mx-2 mb-2 rounded-lg bg-white"
                  onClick={() =>
                    router.push(
                      `/microWebsite/consultation/detail?registrationId=${item.registrationId}`,
                    )
                  }
                >
                  <div className="mb-2 flex items-center justify-between text-sm">
                    <span className="text-[#333]">{item.date}</span>
                  </div>
                  <div className="mb-2 flex items-center justify-between">
                    <div className="text-lg  text-[#333]">
                      <span className="mr-2 text-sm text-[#333]">
                        宝贝： {item.name}
                      </span>
                      <span className="text-sm text-[#333]">
                        {getAge(item.birthday)}岁
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <div
                        onClick={(e) => handleMessage(e, item.parentMobile)}
                        className="cursor-pointer rounded-full p-2 hover:bg-gray-100"
                      >
                        <Mail className="size-5 text-yellow-500" />
                      </div>
                      <div
                        onClick={(e) => handleCall(e, item.parentMobile)}
                        className="cursor-pointer rounded-full p-2 hover:bg-gray-100"
                      >
                        <Phone className="size-5 text-green-500" />
                      </div>
                    </div>
                  </div>

                  {whitelistInstId.includes(instId) ? (
                    <div className="text-sm text-[#333]">
                      学生爸爸：{item.parentName}
                      {item.parentMobile}
                    </div>
                  ) : (
                    <div className="text-sm text-[#333]">
                      家长：{item.parentName} {item.relation}{' '}
                      {item.parentMobile}
                    </div>
                  )}
                </List.Item>
              ))}
            </List>
            {hasMore ? (
              <InfiniteScroll loadMore={loadMore} hasMore={hasMore}>
                <LoadingMore />
              </InfiniteScroll>
            ) : (
              <NoMoreData />
            )}
          </>
        )}
      </PullToRefresh>
    </div>
  );
};

export default ConsultationList;
