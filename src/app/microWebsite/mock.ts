export const defaultHtml = `
<section data-role="outer" class="article135" label="edit by 135editor">
  <section
    style="background-color: #dbf0ff; box-sizing: border-box; text-align: justify;padding-bottom:20px"
    class=""
  >
    <section
      style="margin: 0px 0px -98px; box-sizing: border-box;"
      class="_135editor"
    >
      <section style="display: grid; width: 100%; overflow: hidden; align-self: flex-start; line-height: 1.6; letter-spacing: 0px; grid-template-columns: 100%; grid-template-rows: 100%; box-sizing: border-box; max-width: 100% !important;">
        <section style="width: 30.9333%; margin-left: 1.48173%; margin-top: 1.5389%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 30.9333% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="font-size: 5px; height: 100%; box-sizing: border-box;">
            <section style="display: flex; width: 100%; flex-flow: column nowrap; max-width: 100% !important; box-sizing: border-box;">
              <section style="z-index: 1; box-sizing: border-box;">
                <section style="text-align: center; margin: 0px; box-sizing: border-box;">
                  <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; box-sizing: border-box;">
                    <img
                      class="rich_pages wxw-img"
                      src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNww28Uzmr3MsygJ7sWn2SrQE4f4NHzVibhwtFaRSuLR7do7EE7ulKQww%2F640%3Fwx_fmt%3Dpng"
                      style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                      width="100%"
                      draggable="false"
                    />
                  </section>
                </section>
              </section>
            </section>
          </section>
        </section>
        <section style="width: 23.2%; margin-left: 44.3628%; margin-top: 2.3481%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 23.2% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; margin: 0px; font-size: 4px; height: 100%; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; height: auto; box-sizing: border-box;">
              <img
                src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_gif%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNE6oJZJjejYfzT3VoYicV2ibjXb0Bw1FIVTN0NI1lOO3wiaoLZVGOyAibyg%2F640%3Fwx_fmt%3Dgif"
                style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                width="100%"
                draggable="false"
              />
            </section>
          </section>
        </section>
        <section style="width: 28.2667%; margin-left: 32.2155%; margin-top: 11.7537%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 28.2667% !important; transform: rotateZ(351.5deg); -webkit-transform: rotateZ(351.5deg); -moz-transform: rotateZ(351.5deg); -o-transform: rotateZ(351.5deg);">
          <section style="font-size: 5px; height: 100%; box-sizing: border-box;">
            <section style="display: flex; width: 100%; flex-flow: column nowrap; max-width: 100% !important; box-sizing: border-box;">
              <section style="z-index: 1; box-sizing: border-box;">
                <section style="text-align: center; margin: 0px; box-sizing: border-box;">
                  <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; box-sizing: border-box;">
                    <img
                      src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNww28Uzmr3MsygJ7sWn2SrQE4f4NHzVibhwtFaRSuLR7do7EE7ulKQww%2F640%3Fwx_fmt%3Dpng"
                      style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                      width="100%"
                      draggable="false"
                    />
                  </section>
                </section>
              </section>
            </section>
          </section>
        </section>
        <section style="width: 26.1333%; margin-left: 70.5028%; margin-top: 3.3333%; grid-column-start: 1; grid-row-start: 1; height: max-content; transform-style: flat; box-sizing: border-box; max-width: 26.1333% !important; transform: perspective(0px) rotateZ(7.63deg); -webkit-transform: perspective(0px) rotateZ(7.63deg); -moz-transform: perspective(0px) rotateZ(7.63deg); -o-transform: perspective(0px) rotateZ(7.63deg);">
          <section style="font-size: 5px; height: 100%; box-sizing: border-box; transform: rotateY(180deg); -webkit-transform: rotateY(180deg); -moz-transform: rotateY(180deg); -o-transform: rotateY(180deg);">
            <section style="display: flex; width: 100%; flex-flow: column nowrap; max-width: 100% !important; box-sizing: border-box;">
              <section style="z-index: 1; box-sizing: border-box;">
                <section style="text-align: center; margin: 0px; box-sizing: border-box;">
                  <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; box-sizing: border-box;">
                    <img
                      src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNww28Uzmr3MsygJ7sWn2SrQE4f4NHzVibhwtFaRSuLR7do7EE7ulKQww%2F640%3Fwx_fmt%3Dpng"
                      style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                      width="100%"
                      draggable="false"
                    />
                  </section>
                </section>
              </section>
            </section>
          </section>
        </section>
        <section style="width: 28.2667%; margin-left: 72.0481%; margin-top: 21.3417%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 28.2667% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; margin-top: 10px; margin-bottom: 10px; font-size: 5px; height: 100%; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; box-sizing: border-box;">
              <img
                src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNlAucyC2BTzmc68tAInJia9SRNTfuaLzTuhsks93wwESqbuowTSu0DOA%2F640%3Fwx_fmt%3Dpng"
                style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                width="100%"
                draggable="false"
              />
            </section>
          </section>
        </section>
        <section style="width: 28%; margin-left: -0.44445%; margin-top: 21.0926%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 28% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; margin-top: 10px; margin-bottom: 10px; font-size: 5px; height: 100%; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; box-sizing: border-box;">
              <img
                class="rich_pages wxw-img"
                src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNsAyrgJibs4eVbEggovJlO9SUgicB04MbHMD9iaKjG85nySWYEkEko9Y4Q%2F640%3Fwx_fmt%3Dpng"
                style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                width="100%"
                draggable="false"
              />
            </section>
          </section>
        </section>
        <section style="height: 12.5333%; width: 12.5668%; margin-left: 23.2722%; margin-top: 52.6222%; grid-column-start: 1; grid-row-start: 1; box-sizing: border-box; max-width: 12.5668% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; font-size: 5px; height: 100%; pointer-events: none; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; pointer-events: none; box-sizing: border-box;">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewbox="0 0 218.931 218.931"
                width="100%"
                style="max-width: 100%; width: 100%; box-sizing: border-box;"
                xml:space="default"
              >
                <g style="box-sizing: border-box;">
                  <g style="box-sizing: border-box;">
                    <circle
                      cx="109.465"
                      cy="109.465"
                      r="109.465"
                      fill="rgb(248,132,119)"
                      style="box-sizing: border-box;"
                    />
                  </g>
                </g>
              </svg>
            </section>
          </section>
        </section>
        <section style="height: 13.0667%; width: 13.3333%; margin-left: 34.362%; margin-top: 38.1074%; grid-column-start: 1; grid-row-start: 1; box-sizing: border-box; max-width: 13.3333% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; font-size: 5px; height: 100%; pointer-events: none; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; pointer-events: none; box-sizing: border-box;">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewbox="0 0 218.931 218.931"
                width="100%"
                style="max-width: 100%; width: 100%; box-sizing: border-box;"
                xml:space="default"
              >
                <g style="box-sizing: border-box;">
                  <g style="box-sizing: border-box;">
                    <circle
                      cx="109.465"
                      cy="109.465"
                      r="109.465"
                      fill="rgb(255,237,175)"
                      style="box-sizing: border-box;"
                    />
                  </g>
                </g>
              </svg>
            </section>
          </section>
        </section>
        <section style="height: 11.7333%; width: 11.7647%; margin-left: 56.0198%; margin-top: 39.1741%; grid-column-start: 1; grid-row-start: 1; box-sizing: border-box; max-width: 11.7647% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; font-size: 5px; height: 100%; pointer-events: none; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; pointer-events: none; box-sizing: border-box;">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewbox="0 0 218.931 218.931"
                width="100%"
                style="max-width: 100%; width: 100%; box-sizing: border-box;"
                xml:space="default"
              >
                <g style="box-sizing: border-box;">
                  <g style="box-sizing: border-box;">
                    <circle
                      cx="109.465"
                      cy="109.465"
                      r="109.465"
                      fill="rgb(122,202,141)"
                      style="box-sizing: border-box;"
                    />
                  </g>
                </g>
              </svg>
            </section>
          </section>
        </section>
        <section style="height: 12%; width: 12.2995%; margin-left: 66.2767%; margin-top: 54.4593%; grid-column-start: 1; grid-row-start: 1; box-sizing: border-box; max-width: 12.2995% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; font-size: 5px; height: 100%; pointer-events: none; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; pointer-events: none; box-sizing: border-box;">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewbox="0 0 218.931 218.931"
                width="100%"
                style="max-width: 100%; width: 100%; box-sizing: border-box;"
                xml:space="default"
              >
                <g style="box-sizing: border-box;">
                  <g style="box-sizing: border-box;">
                    <circle
                      cx="109.465"
                      cy="109.465"
                      r="109.465"
                      fill="rgb(255,217,199)"
                      style="box-sizing: border-box;"
                    />
                  </g>
                </g>
              </svg>
            </section>
          </section>
        </section>
        <section style="width: 16.0428%; margin-left: 21.513%; margin-top: 52.5037%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 16.0428% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="height: 100%; box-sizing: border-box;">
            <section style="font-size: 27px; color: #ffffff; text-align: center; box-sizing: border-box;">
              <p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;">
                <strong style="box-sizing: border-box;">开</strong>
              </p>
            </section>
          </section>
        </section>
        <section style="width: 11.7647%; margin-left: 35.4963%; margin-top: 38.663%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 11.7647% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="height: 100%; box-sizing: border-box;">
            <section style="font-size: 27px; color: #855124; text-align: center; box-sizing: border-box;">
              <p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;">
                <strong style="box-sizing: border-box;">我</strong>
              </p>
            </section>
          </section>
        </section>
        <section style="width: 11.7647%; margin-left: 56.2203%; margin-top: 39.2889%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 11.7647% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="height: 100%; box-sizing: border-box;">
            <section style="font-size: 27px; color: #fff2c5; text-align: center; box-sizing: border-box;">
              <p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;">
                <strong style="box-sizing: border-box;">们</strong>
              </p>
            </section>
          </section>
        </section>
        <section style="width: 13.9037%; margin-left: 65.4445%; margin-top: 54.1852%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 13.9037% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="height: 100%; box-sizing: border-box;">
            <section style="font-size: 27px; color: #f88477; text-align: center; box-sizing: border-box;">
              <p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;">
                <strong style="box-sizing: border-box;">啦</strong>
              </p>
            </section>
          </section>
        </section>
        <section style="height: 11.7333%; width: 11.7647%; margin-left: 43.7537%; margin-top: 56.8556%; grid-column-start: 1; grid-row-start: 1; box-sizing: border-box; max-width: 11.7647% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; font-size: 5px; height: 100%; pointer-events: none; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; pointer-events: none; box-sizing: border-box;">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewbox="0 0 218.931 218.931"
                width="100%"
                style="max-width: 100%; width: 100%; box-sizing: border-box;"
                xml:space="default"
              >
                <g style="box-sizing: border-box;">
                  <g style="box-sizing: border-box;">
                    <circle
                      cx="109.465"
                      cy="109.465"
                      r="109.465"
                      fill="rgb(105,144,191)"
                      style="box-sizing: border-box;"
                    />
                  </g>
                </g>
              </svg>
            </section>
          </section>
        </section>
        <section style="width: 13.9037%; margin-left: 43.1111%; margin-top: 56.9556%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 13.9037% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="height: 100%; box-sizing: border-box;">
            <section style="font-size: 27px; color: #ffffff; text-align: center; box-sizing: border-box;">
              <p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;">
                <strong style="box-sizing: border-box;">学</strong>
              </p>
            </section>
          </section>
        </section>
        <section style="width: 19.4667%; margin-left: -0.351855%; margin-top: 51.0352%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 19.4667% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; margin-top: 10px; margin-bottom: 10px; font-size: 4px; height: 100%; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; height: auto; box-sizing: border-box;">
              <img
                class="rich_pages wxw-img"
                src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdN8QtevT8SUO3kaB1IbyoIP8mwB6NyOvNR2O373Y46oaA2CLdzhoeuSQ%2F640%3Fwx_fmt%3Dpng"
                style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                width="100%"
                draggable="false"
              />
            </section>
          </section>
        </section>
        <section style="width: 17.8667%; margin-left: 82.0408%; margin-top: 58.5185%; grid-column-start: 1; grid-row-start: 1; height: max-content; box-sizing: border-box; max-width: 17.8667% !important; transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);">
          <section style="text-align: center; margin-top: 10px; margin-bottom: 10px; font-size: 4px; height: 100%; box-sizing: border-box;">
            <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; box-sizing: border-box;">
              <img
                src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNo6CXaZ9lDvRB7t2OBlnNwvQex7zgM2Lwqb21HsynTTVricOw9UwUsicQ%2F640%3Fwx_fmt%3Dpng"
                style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                width="100%"
                draggable="false"
              />
            </section>
          </section>
        </section>
        <section style="grid-column-start: 1; grid-row-start: 1; padding-top: 100%; box-sizing: border-box;">
          <svg
            viewbox="0 0 1 1"
            style="float: left; line-height: 0; width: 0; vertical-align: top; box-sizing: border-box;"
            xml:space="default"
          />
        </section>
      </section>
    </section>
    <p
      style="white-space: normal; margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;"
      class="_135editor"
    />
    <p
      style="white-space: normal; margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;"
      class="_135editor"
    />
    <p
      style="white-space: normal; margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;"
      class="_135editor"
    />
    <section
      style="text-align: center; justify-content: center; display: flex; flex-flow: row nowrap; margin: 0px; box-sizing: border-box; transform: translate3d(1px, 0px, 0px); -webkit-transform: translate3d(1px, 0px, 0px); -moz-transform: translate3d(1px, 0px, 0px); -o-transform: translate3d(1px, 0px, 0px);"
      class="_135editor"
    >
      <section style="display: inline-block; width: 73%; vertical-align: top; flex: 0 0 auto; height: auto; background-color: #ffffff; border-radius: 30px; overflow: hidden; align-self: flex-start; box-sizing: border-box; max-width: 73% !important; border: 2px solid #6990bf;">
        <section style="justify-content: center; display: flex; flex-flow: row nowrap; margin: 0px; box-sizing: border-box;">
          <section style="display: inline-block; vertical-align: middle; width: 12%; align-self: center; flex: 0 0 auto; height: auto; box-sizing: border-box; max-width: 12% !important;">
            <section style=";transform-style: flat; box-sizing: border-box; transform: perspective(0px); -webkit-transform: perspective(0px); -moz-transform: perspective(0px); -o-transform: perspective(0px);">
              <section style="margin: 0px; box-sizing: border-box; transform: rotateY(180deg); -webkit-transform: rotateY(180deg); -moz-transform: rotateY(180deg); -o-transform: rotateY(180deg);">
                <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; height: auto; box-sizing: border-box;">
                  <img
                    src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_gif%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNXudPCwuZSM0pjiaAbr5MSNlqhqHyC6wU5Km0hdcPkBvLYIDrVgADfgg%2F640%3Fwx_fmt%3Dgif"
                    style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                    width="100%"
                    draggable="false"
                  />
                </section>
              </section>
            </section>
          </section>
          <section style="display: inline-block; vertical-align: middle; width: 64%; align-self: center; flex: 0 0 auto; height: auto; box-sizing: border-box; max-width: 64% !important;">
            <section style="color: #6990bf; letter-spacing: 2px; font-size: 17px;box-sizing: border-box;">
              <p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;">
                <b>学校信息</b>
              </p>
            </section>
          </section>
          <section style="display: inline-block; vertical-align: middle; width: 12%; align-self: center; flex: 0 0 auto; height: auto; box-sizing: border-box; max-width: 12% !important;">
            <section style="margin: 0px; box-sizing: border-box;">
              <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; height: auto; box-sizing: border-box;">
                <img
                  src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_gif%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNXudPCwuZSM0pjiaAbr5MSNlqhqHyC6wU5Km0hdcPkBvLYIDrVgADfgg%2F640%3Fwx_fmt%3Dgif"
                  style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                  width="100%"
                  draggable="false"
                />
              </section>
            </section>
          </section>
        </section>
      </section>
    </section>
    <section
      style="text-align: center; justify-content: center; display: flex; flex-flow: row nowrap;margin-top:20px;margin-bottom:20px"
    >
      <section
        style="text-align: center; justify-content: center; display: flex; flex-flow: row nowrap; margin: 0px; box-sizing: border-box; width: 73%; 
          min-height:200px;
          border-radius: 10px;
        background-color: #ffffff;"
      >
        </section>
    </section>
    <section
      style="text-align: right; margin: 0px; box-sizing: border-box; transform: translate3d(-86px, 0px, 0px); -webkit-transform: translate3d(-86px, 0px, 0px); -moz-transform: translate3d(-86px, 0px, 0px); -o-transform: translate3d(-86px, 0px, 0px);"
      class="_135editor"
    >
      <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 90px; height: auto; box-sizing: border-box;">
        <img
          src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_png%2FbHJ9DVuVdicfnDLnoQu0Xfl2R9nFINOL5NuKicoLA1nibewlIO0qf1TXe0FgOZE0Al32ZSuLROcBXHdM1cN2Xb0EQ%2F640%3Fwx_fmt%3Dpng"
          style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
          width="100%"
          draggable="false"
        />
      </section>
    </section>
    <section
      style="text-align: center; justify-content: center; display: flex; flex-flow: row nowrap; margin: 0px; box-sizing: border-box; transform: translate3d(1px, 0px, 0px); -webkit-transform: translate3d(1px, 0px, 0px); -moz-transform: translate3d(1px, 0px, 0px); -o-transform: translate3d(1px, 0px, 0px);"
      class="_135editor"
    >
      <section style="display: inline-block; width: 73%; vertical-align: top; flex: 0 0 auto; height: auto; background-color: #ffffff; border-radius: 30px; overflow: hidden; align-self: flex-start; box-sizing: border-box; max-width: 73% !important; border: 2px solid #6990bf;">
        <section style="justify-content: center; display: flex; flex-flow: row nowrap; margin: 0px; box-sizing: border-box;">
          <section style="display: inline-block; vertical-align: middle; width: 12%; align-self: center; flex: 0 0 auto; height: auto; box-sizing: border-box; max-width: 12% !important;">
            <section style=";transform-style: flat; box-sizing: border-box; transform: perspective(0px); -webkit-transform: perspective(0px); -moz-transform: perspective(0px); -o-transform: perspective(0px);">
              <section style="margin: 0px; box-sizing: border-box; transform: rotateY(180deg); -webkit-transform: rotateY(180deg); -moz-transform: rotateY(180deg); -o-transform: rotateY(180deg);">
                <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; height: auto; box-sizing: border-box;">
                  <img
                    src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_gif%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNXudPCwuZSM0pjiaAbr5MSNlqhqHyC6wU5Km0hdcPkBvLYIDrVgADfgg%2F640%3Fwx_fmt%3Dgif"
                    style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                    width="100%"
                    draggable="false"
                  />
                </section>
              </section>
            </section>
          </section>
          <section style="display: inline-block; vertical-align: middle; width: 52%; align-self: center; flex: 0 0 auto; box-sizing: border-box; max-width: 52% !important;">
            <section style="font-size: 17px; color: #6990bf; letter-spacing: 2px; box-sizing: border-box;">
              <p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;">
                <strong style="box-sizing: border-box;">招生简章</strong>
              </p>
            </section>
          </section>
        
          <section style="display: inline-block; vertical-align: middle; width: 12%; align-self: center; flex: 0 0 auto; height: auto; box-sizing: border-box; max-width: 12% !important;">
            <section style="margin: 0px; box-sizing: border-box;">
              <section style="max-width: 100%; vertical-align: middle; display: inline-block; line-height: 0; width: 100%; height: auto; box-sizing: border-box;">
                <img
                  src="https://mediatx.ancda.com/v4_notify%2Fmmbiz_gif%2FXpCHWDOHAicby3R0zPQVZzHmUeo6beTdNXudPCwuZSM0pjiaAbr5MSNlqhqHyC6wU5Km0hdcPkBvLYIDrVgADfgg%2F640%3Fwx_fmt%3Dgif"
                  style="vertical-align: middle; width: 100%; box-sizing: border-box; max-width: 100% !important;"
                  width="100%"
                  draggable="false"
                />
              </section>
            </section>
          </section>
        </section>
      </section>
    </section>
      <section
      style="text-align: center; justify-content: center; display: flex; flex-flow: row nowrap;margin-top:20px;margin-bottom:20px"
    >
      <section
        style="text-align: center; justify-content: center; display: flex; flex-flow: row nowrap; margin: 0px; box-sizing: border-box; width: 73%; 
          min-height:200px;
          border-radius: 10px;
        background-color: #ffffff;"
      >
        </section>
    </section>
    <p
      style="white-space: normal; margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;"
      class="_135editor"
    />
    <p
      style="white-space: normal; margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;"
      class="_135editor"
    />
    <p
      style="white-space: normal; margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;"
      class="_135editor"
    />
    <p
      style="white-space: normal; margin-top: 0px; margin-bottom: 0px; padding: 0px; box-sizing: border-box;"
      class="_135editor"
    />
  </section>
</section>
`;

export const whitelistInstId = [
  '69783062119973626',
  '78949173436612735',
  '78954218043670776',
];
