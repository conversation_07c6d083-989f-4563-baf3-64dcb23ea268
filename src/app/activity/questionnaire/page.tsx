'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Toast } from 'antd-mobile';
import { CheckCircle2, Send } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { hinaTrack, postMessage } from '@/utils';

const formSchema = z
  .object({
    cancelReason: z.enum(
      [
        '价格过高',
        '权益不符合需求',
        '使用频率低',
        '转向了其他产品',
        'APP体验问题（加载慢/卡顿等）',
        'other',
      ],
      {
        required_error: '请选择取消订阅的主要原因',
      },
    ),
    otherReasonText: z.string().optional(),
    valuePerception: z.enum(
      ['非常不值得', '不太值得', '一般', '比较值得', '非常值得'],
      {
        required_error: '请选择您对会员权益价值的看法',
      },
    ),
    priceConsideration: z
      .enum([
        '维持当前价格但增加新的权益',
        '降价10%-20%',
        '提供更灵活的付费周期（如按周付）',
        '暂时不考虑续费',
      ])
      .optional(),
    desiredBenefits: z.array(z.string()).optional(),
    otherBenefitText: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.cancelReason === '价格过高' && !data.priceConsideration) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '请选择您可能考虑续费的价格范围',
        path: ['priceConsideration'],
      });
    }
    if (
      data.cancelReason === '权益不符合需求' &&
      (!data.desiredBenefits || data.desiredBenefits.length === 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '请选择您希望新增的权益',
        path: ['desiredBenefits'],
      });
    }
  });
const benefits = {
  ai: 'AI相关功能（例如AI育儿助手、AI生成写真集等）',
  exclusiveContent: ' 独家内容/课程',
  multiDevice: '多设备同时登陆',
  videoArchive: '宝贝在线视频存档',
  exclusiveSkins: '会员专享皮肤/图标',
  morePoints: '更多积分兑换商品',
  other: '其他权益',
};
export default function SurveyForm() {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      desiredBenefits: [],
    },
  });

  const watchCancelReason = form.watch('cancelReason');
  const watchDesiredBenefits = form.watch('desiredBenefits');
  const showPriceQuestion = watchCancelReason === '价格过高';
  const showOtherReasonInput = watchCancelReason === 'other';
  const showOtherBenefitInput = watchDesiredBenefits?.includes('other');

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
    if (values.cancelReason === 'other' && !values.otherReasonText?.trim()) {
      Toast.show('请填写您取消订阅的主要原因');
      return;
    }
    if (
      values.desiredBenefits?.includes('other') &&
      !values.otherBenefitText?.trim()
    ) {
      Toast.show('请填写您希望的具体权益');
      return;
    }
    const postData = {
      ...values,
      cancelReason:
        values.cancelReason === 'other'
          ? `其他：${values.otherReasonText}`
          : values.cancelReason,
      // 如果包含其他权益，将具体权益附加到desiredBenefits中
      desiredBenefits: values.desiredBenefits?.map((benefit) =>
        benefit === 'other'
          ? `其他：${values.otherBenefitText}`
          : benefits[benefit as keyof typeof benefits],
      ),
    };
    hinaTrack('cancelSurvey', {
      cancelReason: postData.cancelReason,
      valuePerception: postData.valuePerception,
      priceConsideration: postData.priceConsideration,
      desiredBenefits: postData.desiredBenefits?.toString(),
    });
    postMessage({
      type: 'cancelSurvey',
    });
    setIsSubmitted(true);
  }
  const goBackApp = () => {
    postMessage({
      type: 'goBackApp',
    });
  };
  if (isSubmitted) {
    return (
      <div className="w-full ">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-white">
            <CheckCircle2 className="size-10 text-green-600" />
          </div>
          <CardTitle className="text-[38px] text-green-700">
            自动续费已经取消，感谢您的反馈！
          </CardTitle>
          <CardDescription className="mt-2 text-[26px] text-[#333]">
            您的意见对我们非常重要，我们将努力改进产品体验
          </CardDescription>
        </CardHeader>
        {/* <CardContent className="text-center">
          <p className="mb-6 text-muted-foreground">
            我们已经为您准备了一份小礼品，请查看您的邮箱
          </p>
          <img
            src="https://images.unsplash.com/photo-1513201099705-a9746e1e201f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
            alt="Gift"
            className="mx-auto size-40 rounded-lg object-cover"
          />
        </CardContent> */}
        <CardFooter>
          <Button
            className="w-full bg-green-600 hover:from-green-600"
            onClick={goBackApp}
          >
            返回APP
          </Button>
        </CardFooter>
      </div>
    );
  }

  return (
    <Card className="w-full border-2 border-primary/10 shadow-lg">
      <CardHeader className="space-y-1">
        <CardDescription className="text-[28px]">
          很遗憾您选择取消订阅，您的反馈能帮助我们改进数百万用户的体验，感谢您的耐心填写
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="cancelReason"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="flex items-center gap-2 text-base font-medium">
                    <span className="text-back flex size-6 items-center justify-center text-sm">
                      1、
                    </span>
                    您取消订阅的主要原因是什么？
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-2"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="价格过高" />
                        </FormControl>
                        <FormLabel className="flex items-center gap-2 font-normal">
                          价格过高
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="权益不符合需求" />
                        </FormControl>
                        <FormLabel className="flex items-center gap-2 font-normal">
                          权益不符合需求
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="使用频率低" />
                        </FormControl>
                        <FormLabel className="flex items-center gap-2 font-normal">
                          使用频率低
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="转向了其他产品" />
                        </FormControl>
                        <FormLabel className="flex items-center gap-2 font-normal">
                          转向了其他产品
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="APP体验问题（加载慢/卡顿等）" />
                        </FormControl>
                        <FormLabel className="flex items-center gap-2 font-normal">
                          APP体验问题（加载慢/卡顿等）
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          其他，请说明
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            {showOtherReasonInput && (
              <FormField
                control={form.control}
                name="otherReasonText"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="请说明您取消订阅的其他原因..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}

            <Separator />

            <FormField
              control={form.control}
              name="valuePerception"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="flex items-center gap-2 text-base font-medium">
                    <span className="text-back flex size-6 items-center  justify-center text-sm">
                      2、
                    </span>
                    您认为当前会员权益给予的价值如何？
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-2"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="非常不值得" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          非常不值得
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="不太值得" />
                        </FormControl>
                        <FormLabel className="font-normal">不太值得</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="一般" />
                        </FormControl>
                        <FormLabel className="font-normal">一般</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="比较值得" />
                        </FormControl>
                        <FormLabel className="font-normal">比较值得</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="非常值得" />
                        </FormControl>
                        <FormLabel className="font-normal">非常值得</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {watchCancelReason === '价格过高' && (
              <FormField
                control={form.control}
                name="priceConsideration"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className="flex items-center gap-2 text-base font-medium">
                      <span className="text-back flex size-6 items-center   justify-center text-sm">
                        3、
                      </span>
                      如果调整价格您可能考虑续费的范围是？
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="维持当前价格但增加新的权益" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            维持当前价格但增加新的权益
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="降价10%-20%" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            降价10%-20%
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="提供更灵活的付费周期（如按周付）" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            提供更灵活的付费周期（如按周付）
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="暂时不考虑续费" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            暂时不考虑续费
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {showPriceQuestion && <Separator />}

            {watchCancelReason === '权益不符合需求' && (
              <FormField
                control={form.control}
                name="desiredBenefits"
                render={() => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2 text-base font-medium">
                      <span className="text-back flex size-6 items-center  justify-center text-sm">
                        3、
                      </span>
                      您最希望我们新增哪类权益？
                    </FormLabel>
                    <FormDescription>最多可选3项</FormDescription>
                    <div className="mt-2 grid grid-cols-1 gap-3 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="desiredBenefits"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes('ai')}
                                onCheckedChange={(checked) => {
                                  const currentValues = field.value || [];
                                  if (checked && currentValues.length >= 3) {
                                    Toast.show('最多只能选择3项');
                                    return;
                                  }
                                  return checked
                                    ? field.onChange([
                                        ...(field.value || []),
                                        'ai',
                                      ])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== 'ai',
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              AI相关功能
                              <div>(例如AI育儿助手、AI生成写真集等)</div>
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="desiredBenefits"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(
                                  'exclusiveContent',
                                )}
                                onCheckedChange={(checked) => {
                                  const currentValues = field.value || [];
                                  if (checked && currentValues.length >= 3) {
                                    return;
                                  }
                                  return checked
                                    ? field.onChange([
                                        ...(field.value || []),
                                        'exclusiveContent',
                                      ])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) =>
                                            value !== 'exclusiveContent',
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              独家内容/课程
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="desiredBenefits"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes('multiDevice')}
                                onCheckedChange={(checked) => {
                                  const currentValues = field.value || [];
                                  if (checked && currentValues.length >= 3) {
                                    return;
                                  }
                                  return checked
                                    ? field.onChange([
                                        ...(field.value || []),
                                        'multiDevice',
                                      ])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== 'multiDevice',
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              多设备同时登陆
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="desiredBenefits"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes('videoArchive')}
                                onCheckedChange={(checked) => {
                                  const currentValues = field.value || [];
                                  if (checked && currentValues.length >= 3) {
                                    return;
                                  }
                                  return checked
                                    ? field.onChange([
                                        ...(field.value || []),
                                        'videoArchive',
                                      ])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== 'videoArchive',
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              宝贝在线视频存档
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="desiredBenefits"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes('morePoints')}
                                onCheckedChange={(checked) => {
                                  const currentValues = field.value || [];
                                  if (checked && currentValues.length >= 3) {
                                    return;
                                  }
                                  return checked
                                    ? field.onChange([
                                        ...(field.value || []),
                                        'morePoints',
                                      ])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== 'morePoints',
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              更多积分兑换商品
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="desiredBenefits"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(
                                  'exclusiveSkins',
                                )}
                                onCheckedChange={(checked) => {
                                  const currentValues = field.value || [];
                                  if (checked && currentValues.length >= 3) {
                                    return;
                                  }
                                  return checked
                                    ? field.onChange([
                                        ...(field.value || []),
                                        'exclusiveSkins',
                                      ])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== 'exclusiveSkins',
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              会员专享皮肤/图标
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="desiredBenefits"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes('other')}
                                onCheckedChange={(checked) => {
                                  const currentValues = field.value || [];
                                  if (checked && currentValues.length >= 3) {
                                    return;
                                  }
                                  return checked
                                    ? field.onChange([
                                        ...(field.value || []),
                                        'other',
                                      ])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== 'other',
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              其他权益，请说明
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {showOtherBenefitInput && (
              <FormField
                control={form.control}
                name="otherBenefitText"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="请说明您希望的其他权益..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="pt-4">
              <Button
                type="submit"
                className="w-full border-0 bg-gradient-to-r from-blue-500 to-blue-700 text-white hover:from-blue-600 hover:to-blue-800"
                size="lg"
              >
                <Send className="mr-2 size-4" /> 提交问卷
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
