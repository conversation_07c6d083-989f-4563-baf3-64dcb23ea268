export const metadata = {
  title: {
    default: '活动规则',
  },
};

export default function Rules() {
  return (
    <div className="content p-4">
      <h4 className="text-amber-600	mt-2">
        一、
        <span className="relative z-10 inline-block px-1 font-semibold uppercase after:absolute after:-bottom-2 after:right-0 after:-z-10 after:h-2 after:w-full after:-translate-y-2 after:bg-[#FFE1D4] after:content-['']">
          活动时间
        </span>
      </h4>
      <p className="py-2">
        参与时间：<span className="font-bold">每天00:00~23:59</span>
        <br />
        开奖时间：<span className="font-bold">参与后次日10:00开奖</span>
        <br />
        领奖时间：<span className="font-bold">次日10:00~22:00</span>
      </p>
      <h4 className="text-amber-600	mt-2">
        二、
        <span className="relative z-10 inline-block px-1 font-semibold uppercase after:absolute after:-bottom-2 after:right-0 after:-z-10 after:h-2 after:w-full after:-translate-y-2 after:bg-[#FFE1D4] after:content-['']">
          参与方式
        </span>
      </h4>
      <p className="py-2">
        用户打开 掌心APP-签到-瓜分小红花 按照操作指引进行操作，即可参与
      </p>
      <h4 className="text-amber-600	mt-2">
        三、
        <span className="relative z-10 inline-block px-1 font-semibold uppercase after:absolute after:-bottom-2 after:right-0 after:-z-10 after:h-2 after:w-full after:-translate-y-2 after:bg-[#FFE1D4] after:content-['']">
          活动说明
        </span>
      </h4>
      <p className="py-2">
        1. 每场活动持续2天，分2个阶段： 参与阶段（每日00:00~23:59）
        开奖&领取阶段（参与活动后次日10:00~22:00）， 超出领奖时间则无法领取
      </p>
      <p className="py-2">2. 用户成功参与活动后可获得一个小红花红包</p>
      <p className="py-2">
        3. 小红花红包必须在规定时间内领取，超出时间无法领取
      </p>
      <p className="py-2">4. 活动解释权归平台所有</p>
    </div>
  );
}
