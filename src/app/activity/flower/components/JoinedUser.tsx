'use client';

import Image from 'next/image';
import { PiDotsThreeBold } from '@/components/Icons';
import { joinUsersList } from '@/api/flowerActivity';
import React, { useEffect, useState, memo } from 'react';

function JoinedUsers() {
  const [joinUsers, setJoinUsers] = useState([]); // 参与用户列表
  const [joinUsersCount, setJoinUsersCount] = useState(0); // 参与用户数量

  useEffect(() => {
    getJoinUsersList();
  }, []);

  const getJoinUsersList = async () => {
    joinUsersList().then((res) => {
      console.log('获取到参与用户列表', res);
      // 取前3个用户
      const users = Array.isArray(res.data) ? res.data.slice(0, 3) : [];
      setJoinUsers(users);
      setJoinUsersCount(res.count || 0);
    });
  };

  return (
    <div className="mx-auto h-[64px] mb-3 flex flex-row justify-center items-center">
      <div className="flex -space-x-2 rtl:space-x-reverse">
        {joinUsers.map((user, index) => {
          return (
            <Image
              key={index}
              alt=""
              width={100}
              height={100}
              className="w-[64px] h-[64px] border-2 border-white rounded-full"
              src={
                user.avatar ||
                'https://mediatx.ancda.com/app_avatar_teacher_man.png'
              }
            />
          );
        })}

        <div className="flex items-center justify-center w-[64px] h-[64px] border-2 border-white bg-gray-200 rounded-full">
          <PiDotsThreeBold fontSize={32} color="#999" />
        </div>
      </div>
      <span className="text-base text-white ml-2">
        {joinUsersCount}人已参与
      </span>
    </div>
  );
}

export default memo(JoinedUsers);
