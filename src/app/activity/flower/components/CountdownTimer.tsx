'use client';

import React, { useState, useEffect, memo } from 'react';

const CountdownTimer = ({ seconds, onComplete }) => {
  const [timeLeft, setTimeLeft] = useState(seconds);

  useEffect(() => {
    if (seconds === 0) return; // 如果初始秒数为0，不启动计时器

    if (timeLeft === 0) {
      onComplete?.();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          onComplete?.();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onComplete, timeLeft, seconds]);

  const formatTime = (time) => {
    if (time <= 0) {
      return '00:00:00';
    }
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  return <span>{formatTime(timeLeft)}</span>;
};

export default memo(CountdownTimer);
