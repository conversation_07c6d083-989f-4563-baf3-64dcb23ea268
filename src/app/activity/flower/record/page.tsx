'use client';
import awardTitle from '@/assets/images/flower/award-title.png';

import React, { useEffect, useState } from 'react';
import { InfiniteScroll } from 'antd-mobile';
import envelopeIcon from '@/assets/images/flower/envelope-icon.png';
import Image from 'next/image';
import { Button, Toast, Mask } from 'antd-mobile';
import { activityRecords, finishTask } from '@/api/flowerActivity';
import Empty from '@/components/Empty';
import boxSmall from '@/assets/images/flower/box-small.png';

if (typeof document !== 'undefined') {
  document.title = '活动记录';
}
const buttonStyle2 = {
  background: 'linear-gradient(180deg, #FFFAE3 0%, #FFB141 100%)',
  '--border-color': '#FFC86F',
  '--border-width': '2.5px',
  '--text-color': '#874911',
};
export default function Record() {
  const [records, setRecords] = useState<
    {
      id: string;
      addDate: string;
      isAward: number;
      awardScore: number;
      score?: string;
      periodName: string;
      isExpiration: number;
      canAward: number;
    }[]
  >([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [winFlowerPopupVisible, setWinFlowerPopupVisible] = useState(false); // 获取到小红花弹窗
  const [winFlowerCount, setWinFlowerCount] = useState(0); // 获取到小红花数量

  useEffect(() => {
    // loadMore();
  }, []);

  const loadMore = async () => {
    try {
      const response = await activityRecords({
        page,
        perPage: 10,
      });
      console.log('🚀 ~ response:', response);
      if (Array.isArray(response.list) && response.list.length > 0) {
        setRecords((prevData) => [...prevData, ...response.list]);
        setPage((prevPage) => prevPage + 1);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      setHasMore(false);
    }
  };

  const confirmJoinTask = (periodName: string) => {
    finishTask({ taskId: 'point_activity_add' }).then((res: any) => {
      setWinFlowerPopupVisible(true);

      if (res.score) {
        setWinFlowerCount(res.score);
        const index = records.findIndex(
          (item) => item.periodName === periodName,
        );
        if (index !== -1) {
          setRecords((prevData) => {
            const newData = [...prevData];
            newData[index].isAward = 1;
            newData[index].awardScore = res.score;
            return newData;
          });
        }
      }
    });
  };

  return (
    <>
      {records.length === 0 ? (
        <div className="h-screen">
          <Empty title="暂无记录" />
        </div>
      ) : (
        <ul>
          {records.map((record, index) => (
            <li
              key={record.id}
              className="px-6 py-4 flex items-center justify-between"
            >
              <div className="flex items-center">
                <div className="flex items-center justify-center">
                  <Image
                    alt=""
                    src={envelopeIcon}
                    width={0}
                    height={0}
                    className="w-[44px] h-[52px]"
                  />
                </div>
                <div className="ml-4">
                  <p className="text-base font-medium text-gray-900">
                    {record.periodName}
                  </p>
                  <p className="text-sm text-gray-500">{record.addDate}</p>
                </div>
              </div>
              {record.isAward === 0 && record.isExpiration === 0 && (
                <Button
                  size="small"
                  shape="rounded"
                  style={{
                    background:
                      'linear-gradient(270deg, #FB413B 0%, #FF917C 100%)',
                    '--border-width': '0',
                    '--text-color': '#fff',
                  }}
                  onClick={() => {
                    if (record.canAward === 0) {
                      Toast.show('别急哟，开奖后才可领取小红花红包~');
                      return;
                    }
                    confirmJoinTask(record.periodName);
                  }}
                >
                  开小红花红包
                </Button>
              )}
              {record.isExpiration === 1 && (
                <span className=" text-gray-500">小红花红包已过期</span>
              )}

              {record.isAward === 1 && record.awardScore > 0 && (
                <span className=" font-medium text-gray-900">
                  {record.awardScore} 朵
                </span>
              )}
            </li>
          ))}
        </ul>
      )}

      <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />

      <Mask
        visible={winFlowerPopupVisible}
        opacity={0.7}
        onMaskClick={() => {}}
      >
        <div className="absolute right-0 left-0 top-[20%] bottom-0">
          <Image
            src={awardTitle}
            alt=""
            width={0}
            height={0}
            className="mx-auto w-[272px] h-[70px] mb-4"
          />
          <div className="relative w-[423px] h-[482px] mx-auto">
            <Image
              src={boxSmall}
              alt=""
              width={0}
              height={0}
              className="mx-auto  w-[423px] h-[482åpx]"
            />
            <div className="mx-auto absolute right-0 left-0 top-[100px] w-[300px] text-center">
              <div className="text-lg mb-2">恭喜分到</div>
              <div className="text-xl text-[#F74D4F]">
                {winFlowerCount} 朵小红花
              </div>
            </div>
            <div className="mx-auto absolute right-0 left-0 bottom-[80px] w-[240px] text-center">
              <Button
                block
                shape="rounded"
                style={buttonStyle2}
                onClick={() => {
                  setWinFlowerPopupVisible(false);
                }}
              >
                我知道了
              </Button>
            </div>
          </div>
        </div>
      </Mask>
    </>
  );
}
