'use client';

import Image from 'next/image';
import bg from '@/assets/images/flower/bg.jpg';
import boxSmall from '@/assets/images/flower/box-small.png';
import boxLarge from '@/assets/images/flower/box-large.png';
import envelopeBg from '@/assets/images/flower/envelope-bg.png';
import title1 from '@/assets/images/flower/title1.png';
import title2 from '@/assets/images/flower/title2.png';
import awardTitle2 from '@/assets/images/flower/award-title2.png';
import flower from '@/assets/images/flower/flower.png';
import envelope from '@/assets/images/flower/envelope.png';
import awardTitle from '@/assets/images/flower/award-title.png';
import notice from '@/assets/images/flower/notice.png';
import { Button, Popup, Mask, Dialog, Toast, SpinLoading } from 'antd-mobile';
import {
  getMessage,
  openNotificationsSetting,
  getNotificationsEnabled,
} from '@/utils';
import React, { useEffect, useState, useRef } from 'react';
import { PiXCircleFill } from '@/components/Icons';
import {
  activityInfo,
  finishTask,
  totalFlowers,
  subscribeNotification,
} from '@/api/flowerActivity';
import { useRouter } from 'next/navigation';
import JoinedUsers from './components/JoinedUser';
import CountdownTimer from './components/CountdownTimer';
import clsx from 'clsx';
// import Script from 'next/script';

const buttonStyle = {
  '--background-color': '#FCF9E7',
  '--border-color': '#FFA781',
  '--border-width': '2px',
  '--text-color': '#D06B31',
};

const buttonStyle2 = {
  background: 'linear-gradient(180deg, #FFFAE3 0%, #FFB141 100%)',
  '--border-color': '#FFC86F',
  '--border-width': '2.5px',
  '--text-color': '#874911',
};

if (typeof document !== 'undefined') {
  document.title = '瓜分小红花';
}

function secondsUntilTomorrowMidnight() {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);

  const diffInSeconds = Math.floor((tomorrow - now) / 1000);
  return diffInSeconds;
}
// 报名截止时间
const signEndTime = secondsUntilTomorrowMidnight();

const MINE_FLOWER = 100;
let checkInterval: ReturnType<typeof setInterval>;

export default function Flower() {
  const router = useRouter();
  const [isFirst, setIsFirst] = useState(false); // 是否是第一次参与
  const [isJoin, setIsJoin] = useState(false); // 今天是否参与
  const [isEnabledNotification, setIsEnabledNotification] = useState(false); // 是否开启通知
  const [isSubscribe, setIsSubscribe] = useState(false); // 是否订阅
  const [isJoinPopupVisible, setIsJoinPopupVisible] = useState(false); // 参与底部弹窗
  const [myFlower, setMyFlower] = useState(0); // 我的小红花
  const [awardPopupVisible, setAwardPopupVisible] = useState(false); // 获奖弹窗
  const [winFlowerPopupVisible, setWinFlowerPopupVisible] = useState(false); // 获取到小红花弹窗
  const [winFlowerCount, setWinFlowerCount] = useState(0); // 获取到小红花数量
  const [enabledNotificationPopupVisible, setEnabledNotificationPopupVisible] =
    useState(false); // 设置通知弹窗
  const [isShowBottomNotification, setIsShowBottomNotification] =
    useState(false); // 是否显示底部领奖弹窗

  const [awardTime, setAwardTime] = useState(0); // 开奖时间

  const hasGotoNotification = useRef(false); // 是否已经去设置通知,开始监听通知开启状态

  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!isEnabledNotification) {
      getNotificationsEnabled();
    }
  }, [isEnabledNotification]);

  useEffect(() => {
    getMessage(onMessage);
    getActivityInfo();
    getMyFlower();
  }, []);

  const getMyFlower = async () => {
    totalFlowers().then((res) => {
      console.log('获取到我的小红花', res);
      setMyFlower(res.totalPointUser);
    });
  };

  const getActivityInfo = async () => {
    activityInfo()
      .then((res: any) => {
        console.log('获取到活动信息', res);
        setIsFirst(res.todayIsBase === 1);
        setIsJoin(res.isSignToday === 1);
        const isJoinYesterday =
          res.isSignYesterday === 1 &&
          res.awardYesterday === 0 &&
          res.openPrizeGapTime <= 0;
        setIsSubscribe(res.isOpenNotify === 1);
        if (isJoinYesterday) {
          setAwardPopupVisible(true);
        }
        setAwardTime(res.openPrizeGapTime || 0);
      })
      .finally(() => {
        setIsLoaded(true);
      });
  };

  // 获取到RN的通知
  const onMessage = (event: any) => {
    try {
      const data = JSON.parse(event.data);
      console.log('🚀 ~ data:', data);
      if ('areNotificationsEnabled' in data) {
        setIsEnabledNotification(data.areNotificationsEnabled);
        if (data.areNotificationsEnabled) {
          clearInterval(checkInterval);
          if (hasGotoNotification.current) {
            confirmJoinTask('open_notify');
            hasGotoNotification.current = false;
            confirmJoinTask('point_activity_sign');
          }
        }
      }
      console.log('获取到RN的通知 onMessage ', JSON.stringify(data));
    } catch (error) {}
  };

  // 订阅领奖提醒
  const subscribe = () => {
    Dialog.confirm({
      content: '订阅领奖提醒',
      onConfirm: async () => {
        subscribeNotification({ isOpenNotify: 1 }).then(() => {
          Toast.show({
            icon: 'success',
            content: '订阅成功',
          });
          getActivityInfo();
        });
      },
    });
  };

  const handleJoin = () => {
    if (isEnabledNotification) {
      if (isFirst) {
        confirmJoinTask('point_activity_sign');
      } else {
        setIsJoinPopupVisible(true);
      }
    } else {
      setEnabledNotificationPopupVisible(true);
    }
  };

  const handleOpenNotification = () => {
    setEnabledNotificationPopupVisible(false);
    hasGotoNotification.current = true;
    openNotificationsSetting();
    checkInterval = setInterval(() => {
      getNotificationsEnabled();
    }, 2000);
  };

  const handleJoinNext = () => {
    setWinFlowerPopupVisible(false);
    setIsJoinPopupVisible(true);
  };

  const confirmJoin = () => {
    if (!isFirst) {
      if (myFlower < MINE_FLOWER) {
        Toast.show({
          icon: 'fail',
          content: '小红花数量不足',
        });
        return;
      }
    }
    confirmJoinTask('point_activity_sign');
  };

  const confirmJoinTask = (taskId: string) => {
    finishTask({ taskId }).then((res: any) => {
      console.log('完成任务', res);
      if (res.score > 0) {
        setWinFlowerCount(res.score);
        if (taskId === 'open_notify') {
          // 开启通知
          setWinFlowerPopupVisible(true);
        } else if (taskId === 'point_activity_add') {
          // 瓜分小红花
          setWinFlowerPopupVisible(true);
          getMyFlower();
        }
      }
      if (taskId === 'point_activity_sign') {
        // 报名
        setIsJoin(true);
        setIsJoinPopupVisible(false);
        if (!isFirst) {
          Toast.show({
            icon: 'success',
            content: '参与成功',
          });
        }
        setAwardTime(0);
        getActivityInfo();
      }
    });
  };

  const handleWinFlower = () => {
    confirmJoinTask('point_activity_add');
    setAwardPopupVisible(false);
    setWinFlowerPopupVisible(true);
  };

  if (!isLoaded) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <SpinLoading />
      </div>
    );
  }

  return (
    <div className="relative bg-[#E82A3F]">
      <Image
        src={bg}
        alt=""
        width={0}
        height={0}
        sizes="100vw"
        className="absolute right-0 top-0 z-0 w-screen object-cover"
      />
      <div className="relative z-10 flex flex-col min-h-screen">
        <div className="flex flex-row justify-end pt-2 pr-2 space-x-2">
          {/* <Button shape="rounded" style={buttonStyle}>
            我参与的
          </Button>
          <Button shape="rounded" style={buttonStyle}>
            活动规则
          </Button> */}
          <Button
            size="small"
            shape="rounded"
            style={buttonStyle}
            onClick={() => {
              router.push('/activity/flower/record');
            }}
          >
            我参与的
          </Button>
          <Button
            size="small"
            shape="rounded"
            style={buttonStyle}
            onClick={() => {
              router.push('/activity/flower/rules');
            }}
          >
            活动规则
          </Button>
        </div>
        {isJoin ? (
          <div className="absolute mx-auto right-0 left-0 top-[386px] w-[672px] h-[832px]">
            <Image
              src={boxLarge}
              alt=""
              width={0}
              height={0}
              className="mx-auto absolute right-0 left-0 w-[672px] h-[832px]"
            />
            <Image
              src={envelope}
              alt=""
              width={0}
              height={0}
              className="mx-auto absolute right-0 left-0 top-[140px] w-[438px] h-[291px]"
            />
            <div className="mx-auto absolute right-0 left-0 top-[74px] w-[460px] text-center py-1 bg-[#FF6670] text-white rounded-full">
              您已获得小红花红包，等待开奖
            </div>
            {awardTime > 0 ? (
              <div className="mx-auto absolute right-0 left-0 top-[340px] text-center text-[#D06B31]">
                距离开奖还有{' '}
                <CountdownTimer
                  seconds={awardTime}
                  onComplete={() => {
                    confirmJoinTask('point_activity_add');
                  }}
                />
              </div>
            ) : null}

            <div className="mx-auto absolute right-0 left-0 top-[366px] text-center text-[#F74D4F]">
              <span className="text-xl">瓜分</span>
              <span className="text-5xl">19999</span>
              <span className="text-xl">朵</span>
            </div>
            <div className="mx-auto absolute right-0 left-0 bottom-[90px] w-[540px] text-center">
              <JoinedUsers />
              {!isSubscribe && (
                <div
                  className="text-base text-white"
                  onClick={() => {
                    subscribe();
                  }}
                >
                  明天10点开奖，
                  <span className="underline underline-offset-8">
                    去订阅领奖提醒
                  </span>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="absolute mx-auto right-0 left-0 top-[386px] w-[672px] h-[733px]">
            <Image
              src={boxSmall}
              alt=""
              width={0}
              height={0}
              className="mx-auto absolute right-0 left-0 w-[672px] h-[733px]"
            />
            <div className="mx-auto absolute right-0 left-0 top-[120px] text-center text-base text-[#D06B31]">
              --- <CountdownTimer seconds={signEndTime} onComplete={() => {}} />{' '}
              后截止报名 ---
            </div>
            <div className="mx-auto absolute right-0 left-0 top-[200px] text-center text-[#F74D4F]">
              <span className="text-xl">瓜分</span>
              <span className="text-5xl">19999</span>
              <span className="text-xl">朵</span>
            </div>
            <div className="mx-auto absolute right-0 left-0 bottom-[60px] w-[520px] text-center">
              <JoinedUsers />
              <Button
                block
                shape="rounded"
                style={buttonStyle2}
                className="pulse"
                onClick={() => {
                  handleJoin();
                }}
              >
                {isFirst ? '立即参与' : `${MINE_FLOWER}小红花参与`}
              </Button>
            </div>
          </div>
        )}
      </div>
      <Popup
        visible={isJoinPopupVisible}
        showCloseButton
        closeOnMaskClick
        onClose={() => {
          setIsJoinPopupVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
        }}
      >
        <div className="p-6 pt-2 flex flex-col justify-center items-center">
          <div className="text-base">用小红花参与瓜分</div>
          <div className="flex flex-col justify-center items-center p-4 text-stone-500">
            <div>
              需消耗：
              <span className="text-2xl text-red-500">{MINE_FLOWER}朵</span>
            </div>
            <div>我的小红花：{myFlower}</div>
            {myFlower < MINE_FLOWER && (
              <div className="text-red-500 mt-2">小红花数量不足</div>
            )}
          </div>
          <Button
            block
            disabled={myFlower < MINE_FLOWER}
            shape="rounded"
            style={{
              width: '240px',
              '--background-color': '#FFF4C4',
              '--border-width': '1',
              '--text-color': '#874911',
            }}
            onClick={() => {
              confirmJoin();
            }}
          >
            {myFlower >= MINE_FLOWER ? '立即参与' : '去做任务赚取小红花吧'}
          </Button>
        </div>
      </Popup>
      <Mask
        visible={enabledNotificationPopupVisible}
        opacity={0.7}
        onMaskClick={() => {}}
      >
        <div className="absolute right-0 left-0 top-[20%] bottom-0">
          <Image
            src={title2}
            alt=""
            width={0}
            height={0}
            className="mx-auto w-[473px] h-[156px] mb-4"
          />
          <div className="relative w-[319px] h-[415px] mx-auto">
            <Image
              src={envelopeBg}
              alt=""
              width={0}
              height={0}
              className="mx-auto  w-[319px] h-[415px]"
            />
            <Image
              src={flower}
              alt=""
              width={0}
              height={0}
              className=" absolute mx-auto left-0 right-0 top-[60px] w-[160px] h-[160px]"
            />
            <div className="mx-auto absolute right-0 left-0 bottom-[60px] w-[240px] text-center">
              <Button
                block
                size="small"
                shape="rounded"
                style={buttonStyle2}
                onClick={() => handleOpenNotification()}
              >
                去开启通知
              </Button>
            </div>
          </div>
        </div>
      </Mask>
      <Mask
        visible={awardPopupVisible}
        opacity={0.7}
        onMaskClick={() => {
          setAwardPopupVisible(false);
          setIsShowBottomNotification(true);
        }}
      >
        <div className="absolute right-0 left-0 top-[20%] bottom-0">
          <Image
            src={title1}
            alt=""
            width={0}
            height={0}
            className="mx-auto w-[473px] h-[164px] mb-4"
          />
          <div className="relative w-[319px] h-[415px] mx-auto">
            <Image
              src={envelopeBg}
              alt=""
              width={0}
              height={0}
              className="mx-auto  w-[319px] h-[415px]"
            />
            <Image
              src={flower}
              alt=""
              width={0}
              height={0}
              className=" absolute mx-auto left-0 right-0 top-[60px] w-[160px] h-[160px]"
            />
            <div className="mx-auto absolute right-0 left-0 bottom-[60px] w-[200px] text-center">
              <Button
                block
                size="small"
                shape="rounded"
                style={buttonStyle2}
                onClick={() => handleWinFlower()}
              >
                开心收下
              </Button>
            </div>
          </div>
          <div
            className="flex justify-center items-center mt-4"
            onClick={() => {
              setAwardPopupVisible(false);
              setIsShowBottomNotification(true);
            }}
          >
            <PiXCircleFill fontSize={36} color="#FFFFFF" />
          </div>
        </div>
      </Mask>
      <Mask
        visible={winFlowerPopupVisible}
        opacity={0.7}
        onMaskClick={() => {}}
      >
        <div className="absolute right-0 left-0 top-[20%] bottom-0">
          <Image
            src={isFirst ? awardTitle2 : awardTitle}
            alt=""
            width={0}
            height={0}
            className={clsx('mx-auto mb-4  h-[70px]', {
              'w-[272px]': !isFirst,
              'w-[488px]': isFirst,
            })}
          />
          <div className="relative w-[423px] h-[482px] mx-auto">
            <Image
              src={boxSmall}
              alt=""
              width={0}
              height={0}
              className="mx-auto  w-[423px] h-[482åpx]"
            />
            <div className="mx-auto absolute right-0 left-0 top-[100px] w-[300px] text-center">
              <div className="text-lg mb-2">
                {isFirst ? '恭喜获得' : '恭喜分到'}
              </div>
              <div className="text-xl text-[#F74D4F]">
                {winFlowerCount} 朵小红花
              </div>
            </div>
            <div className="mx-auto absolute right-0 left-0 bottom-[80px] w-[240px] text-center">
              <Button
                block
                shape="rounded"
                style={buttonStyle2}
                onClick={() => {
                  if (isFirst) {
                    setWinFlowerPopupVisible(false);
                  } else {
                    handleJoinNext();
                  }
                }}
              >
                {isFirst ? '我知道了' : '参与下一期'}
              </Button>
            </div>
          </div>
          {!isFirst && (
            <div
              className="flex justify-center items-center mt-4"
              onClick={() => {
                setWinFlowerPopupVisible(false);
              }}
            >
              <PiXCircleFill fontSize={36} color="#FFFFFF" />
            </div>
          )}
        </div>
      </Mask>
      {isShowBottomNotification && (
        <div
          className="absolute mx-auto right-0 left-0 bottom-[40px] w-[616px] h-[128px] z-10"
          onClick={() => {
            confirmJoinTask('point_activity_add');
          }}
        >
          <Image
            src={notice}
            alt=""
            width={0}
            height={0}
            className="mx-auto absolute right-0 left-0 w-[616px] h-[128px]"
          />
          <div
            className="flex justify-center items-center absolute w-[100px] h-[128px] right-0 top-0 z-20"
            onClick={(event) => {
              console.log('点击了关闭');
              event.stopPropagation();
              event.preventDefault();
              setIsShowBottomNotification(false);
            }}
          >
            <PiXCircleFill fontSize={32} color="#aaa" />
          </div>
        </div>
      )}
      {/* <Script
        src="//cdn.jsdelivr.net/npm/eruda"
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
      /> */}
    </div>
  );
}
