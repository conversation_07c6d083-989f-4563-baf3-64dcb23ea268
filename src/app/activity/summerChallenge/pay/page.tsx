'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { hinaTrack } from '@/utils';
import course from '@/public/images/activity/summerChallenge/course.webp'
import wechat from '@/assets/images/wechat.png';
import alipay from '@/assets/images/aliPay.png';
import success from '@/public/images/activity/summerChallenge/success.png';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  activitySign,
} from '@/api/summerActivity';
import { canCallAppPay, appPay, getMessage, navigationToNativePage } from '@/utils';
import { Toast } from 'antd-mobile';

const SummerPayPage = () => {
  const searchParams = useSearchParams();
  const [selectedPayMethod, setSelectedPayMethod] = useState('2'); // 默认支付宝

  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const orderIdRef = useRef(''); // 使用useRef替代let变量
  const taskId = searchParams.get('taskId'); // 亲子任务Id
  const router = useRouter();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '支付';
    }
  }, []);

  // 获取到RN的通知
  const onMessage = (event: any) => {
    try {
      const data = JSON.parse(event.data);
      console.log('🚀 ~ data:', data);
      if ('pay' in data) {
        if (data.pay) {
          // 支付成功，显示弹窗
          setShowSuccessModal(true);
        } else {
          navigationToNativePage(`rn://SunshineStack?initialRoute=VideoServiceOrderDetailScreen&orderId=${orderIdRef.current}`);
          // 支付失败，显示跳转到订单详情页面
          router.back();
        }
      }
      console.log('获取到RN的通知 onMessage ', JSON.stringify(data));
    } catch (error) {
      console.error('解析失败', error);
    }
  };
  useEffect(() => {
    getMessage(onMessage);

    // 清理函数，移除事件监听器
    return () => {
      window.removeEventListener('message', onMessage, false);
      document.removeEventListener('message', onMessage, false);
    };
  }, [onMessage]);

  // 支付方式选择
  const handlePayMethodSelect = (method: string) => {
    setSelectedPayMethod(method);
    hinaTrack('summer_pay_method_select', {
      pay_method: method
    });
  };

  // 立即支付
  const handlePay = () => {
    if (isLoading) return;
    if (canCallAppPay() === false) {
      Toast.show({
        content: '请更新App版本后再试！'
      });
      setIsLoading(false);
      return
    }
    setIsLoading(true);
    activitySign({ payMethod: selectedPayMethod }).then((res) => {
      console.log('🚀 ~ handlePay ~ res:', res);
      orderIdRef.current = res.orderId; // 直接赋值给ref.current
      appPay({ payMethod: selectedPayMethod, orderNo: res.orderNo });
    }).finally(() => {
      setIsLoading(false);
    });
  };

  // 关闭成功弹窗
  const handleCloseSuccessModal = () => {
    router.back();
    setShowSuccessModal(false);
  };

  // 去打卡
  const handleGoClockIn = () => {
    setShowSuccessModal(false);
    router.back();
    // 这里可以添加跳转到打卡页面的逻辑
    navigationToNativePage(`rn://BabyTaskStack?initialRoute=SubmitListScreen&taskId=${taskId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 活动奖励说明 */}
      <div className="bg-white px-4 py-4">
        <h1 className="text-lg font-bold text-gray-900 mb-4">活动奖励说明</h1>

        {/* 报名奖励 */}
        <div className="mb-4">
          <div className="flex items-center mb-3">
            <span className="text-base font-medium text-gray-900">报名奖励</span>
            <span className="ml-2 text-sm text-gray-600">报名成功立即获得</span>
          </div>
          <Image
            src={course}
            alt="报名奖励内容1"
            className="w-40 h-40 object-contain rounded-md"
          />
          <p className="text-left text-base font-medium text-gray-900 mt-3">
            30天亲子专注力训练课程
          </p>
        </div>

        {/* 挑战成功奖励 */}
        <div className="mb-4">
          <h2 className="text-base font-medium text-gray-900 mb-3">挑战成功奖励</h2>
          <div className="flex justify-normal items-center py-1">
            <img src="/images/activity/summerChallenge/reward_1.jpg" alt="2元报名" className="w-20 h-20 object-contain rounded-md" />
            <img src="/images/activity/summerChallenge/reward_2.jpg" alt="瓜分奖金" className="w-20 h-20 object-contain rounded-md h-14 ml-4 object-contain rounded-md" />
          </div>
          <p className="text-left text-sm text-gray-700 mt-3">
            2元报名费全额返 + 瓜分挑战失败者奖金池
          </p>
        </div>

        {/* TA们已参与 */}
        <div className="mb-4">
          <div className="flex items-center">
            <span className="text-base font-bold text-gray-900 mr-3">TA们已参与</span>
            <div className="flex -space-x-2 mr-3">
              <img src="/images/avatar.png" alt="Avatar 1" className="w-8 h-8 rounded-full" />
              <img src="/images/avatar1.png" alt="Avatar 2" className="w-8 h-8 rounded-full" />
              <img src="/images/avatar2.png" alt="Avatar 3" className="w-8 h-8 rounded-full" />
            </div>
            <span className="text-gray-500 text-sm">999+人</span>
          </div>
        </div>
      </div>

      {/* 支付方式 */}
      <div className="bg-white mt-1 px-4 py-4">
        <h2 className="text-lg font-bold text-gray-900 mb-4">支付方式</h2>

        <div className="space-y-3">
          {/* 支付宝 */}
          <div
            className={`flex items-center justify-between p-4 rounded-lg border-2 cursor-pointer transition-colors ${selectedPayMethod === '2'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 bg-white'
              }`}
            onClick={() => handlePayMethodSelect('2')}
          >
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center mr-3">
                <Image
                  src={alipay}
                  alt="支付宝"
                  width={40}
                  height={40}
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-base font-medium text-gray-900">支付宝</span>
            </div>
            <div
              className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${selectedPayMethod === '2'
                ? 'border-green-500 bg-green-500'
                : 'border-gray-300'
                }`}
            >
              {selectedPayMethod === '2' && (
                <span className="text-white text-xs">✓</span>
              )}
            </div>
          </div>

          {/* 微信支付 */}
          <div
            className={`flex items-center justify-between p-4 rounded-lg border-2 cursor-pointer transition-colors ${selectedPayMethod === '1'
              ? 'border-green-500 bg-green-50'
              : 'border-gray-200 bg-white'
              }`}
            onClick={() => handlePayMethodSelect('1')}
          >
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center mr-3">
                <Image
                  src={wechat}
                  alt="微信支付"
                  width={40}
                  height={40}
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-base font-medium text-gray-900">微信</span>
            </div>
            <div
              className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${selectedPayMethod === '1'
                ? 'border-green-500 bg-green-500'
                : 'border-gray-300'
                }`}
            >
              {selectedPayMethod === '1' && (
                <span className="text-white text-xs">✓</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 底部支付栏 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4 safe-area-pb">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-gray-600 text-base mr-2">需付款：</span>
            <span className="text-red-500 text-xl font-bold">¥ 2.0元</span>
          </div>

          <button
            onClick={handlePay}
            disabled={isLoading}
            className={`px-8 py-3 rounded-full text-white font-medium text-base transition-colors ${isLoading
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-500 hover:bg-green-600 active:bg-green-700'
              }`}
          >
            {isLoading ? '支付中...' : '立即支付'}
          </button>
        </div>
      </div>

      {/* 支付成功弹窗 */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
          <div className="bg-clear rounded-2xl w-full max-w-xs mx-auto relative" style={{ aspectRatio: '6/8' }}>

            {/* 弹窗内容容器 */}
            <div
              className="w-full h-full flex flex-col justify-center items-center cursor-pointer"
              onClick={handleGoClockIn}
            >
              {/* 关闭按钮 */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleCloseSuccessModal();
                }}
                className="bg-clear absolute top-3 right-3 w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 z-10"
              >
                <img src="/images/activity/summerChallenge/close.png" alt="关闭" className="w-6 h-6 object-contain rounded-md" />
              </button>
              {/* success图片 - 占据主要空间 */}
              <div className="flex justify-center flex-1 flex items-center">
                <Image
                  src={success}
                  alt="支付成功"
                  width={200}
                  height={200}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 底部安全区域占位 */}
      <div className="h-20"></div>
    </div>
  );
};

export default SummerPayPage;