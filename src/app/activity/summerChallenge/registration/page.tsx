'use client';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { activityStatus, getFamilyTaskInfo, summerTasks } from "@/api/summerActivity";
import { getMobile, navigationToNativePage } from "@/utils";
import { Toast } from 'antd-mobile';
import ChallengeResultModal from './ChallengeResultModal';

const style1 = 'text-gray-500 bg-gray-300';
const style2 = 'text-white bg-green-500';
// 定义报名状态映射规则
const registerStatusMap: { [key: number]: { type: number, btnText: string, receiveStyle: string, btnStyle: string }; } = {
  0: { type: 0, btnText: '活动未开始', receiveStyle: style1, btnStyle: style1 },
  1: { type: 1, btnText: '立即参与', receiveStyle: style1, btnStyle: style2 },
  2: { type: 2, btnText: '报名已截止', receiveStyle: style1, btnStyle: style1 },
  3: { type: 3, btnText: '立即打卡', receiveStyle: style2, btnStyle: style2 },
  4: { type: 4, btnText: '活动已结束', receiveStyle: style1, btnStyle: style1 },
  5: { type: 5, btnText: '领取奖励', receiveStyle: style2, btnStyle: style2 },
  6: { type: 6, btnText: '已领取奖励', receiveStyle: style2, btnStyle: style1 },
  7: { type: 7, btnText: '挑战失败', receiveStyle: style2, btnStyle: style1 },
};

const SummerChallengePage = () => {
  const router = useRouter();
  const startDate = new Date(2025, 6, 1);  // 2025年7月1日
  const registerEndDate = new Date(2025, 6, 15); // 2025年7月15日
  const endDate = new Date(2025, 7, 14); // 2025年8月14日

  const [status, setStatus] = useState(registerStatusMap[0]); // 0: 未开始, 1: 进行中, 2: 已结束
  const [regStatus, setRegStatus] = useState(0); // 0: 未开始, 1: 进行中, 2: 已截止
  const [showResultModal, setShowResultModal] = useState(false); //

  let taskId = "2759"
  const HAS_SHOWN_CHALLENGE_RESULT_MODAL = 'hasShownChallengeResultModal';

  useEffect(() => {
    document.title = '暑期打卡挑战活动';
    const currentDate = new Date()
    let dateStatus = currentDate > endDate ? 4 : currentDate >= startDate && currentDate <= endDate ? 1 : 0
    const canTRegister = currentDate >= startDate && currentDate <= registerEndDate  //过了报名日期，不能再报名了
    let currentActivityStatus = registerStatusMap[dateStatus]
    setRegStatus(canTRegister ? 1 : currentDate < startDate ? 0 : 2)
    const fetchStatus = async () => {
      try {
        const activityRes: any = await activityStatus();
        const tasksRes: any = await summerTasks();
        taskId = tasksRes.taskId
        if (dateStatus === 1 && !canTRegister) {
          currentActivityStatus = registerStatusMap[2] // 报名已截止
        } else if (activityRes.isSign) {
          if (currentDate <= endDate) {
            currentActivityStatus = registerStatusMap[3]; // 立即打卡
          } else if (dateStatus === 4) {
            const familyTaskInfo: any = await getFamilyTaskInfo(taskId ?? "");
            if (familyTaskInfo.data.finishNum >= familyTaskInfo.data.needClockinNum) {
              if (activityRes.isReceivedReward) {
                currentActivityStatus = registerStatusMap[6]; // 已领取奖励
              } else {
                currentActivityStatus = registerStatusMap[5]; // 已完成任务
                setShowResultModal(true)
              }
            } else {
              currentActivityStatus = registerStatusMap[7]; // 挑战失败
              showChallengeResultModal()
            }
          }
        }
      } catch (error) {
        Toast.show({ content: `Failed to fetch activity status: ${error}` })
        console.error("Failed to fetch activity status:", error);
      } finally {
        setStatus(currentActivityStatus);
      }
    };

    fetchStatus();
  }, []); // 空数组表示只在组件挂载和卸载时运行一次

  // 去领取课程
  function handleReceiveCourse() {
    const type = status?.type === 3 || (status?.type ?? 0) >= 5 ? 2 : 1
    router.push(`/activity/summerChallenge/course?type=${type}`);
  }

  // 立即参与
  function handleParticipateNow() {
    if (status?.type === 1) { // 报名
      router.push(`/activity/summerChallenge/pay?taskId=${taskId}`);
    } else if (status?.type === 3) { // 去打卡
      navigationToNativePage(`rn://BabyTaskStack?initialRoute=SubmitListScreen&taskId=${taskId}`);
    } else if (status?.type === 5) { // 提现
      setShowResultModal(true)
    }
  }

  // 显示挑战结果失败弹框，
  function showChallengeResultModal() {
    if (typeof window !== 'undefined' && !localStorage.getItem(HAS_SHOWN_CHALLENGE_RESULT_MODAL)) {
      setShowResultModal(true)
      localStorage.setItem(HAS_SHOWN_CHALLENGE_RESULT_MODAL, 'true');
    }
  }

  function handleCloseModal(): void {
    setShowResultModal(false)
  }

  function handleClaimReward() {
    try {
      const device = getMobile();
      if (device === 'android') {
        if (window.android && window.android.launchWeChatConfirmReceipt) {
          claimReward(device)
        } else {
          console.error('android.launchWeChatConfirmReceipt function is not available.');
          Toast.show({ content: "请先升级App后再试" })
        }
      } else if (device !== 'ios') {
        if (window.webkit.messageHandlers.launchWeChatConfirmReceipt) {
          claimReward(device)
        } else {
          console.error('window.webkit.messageHandlers.launchWeChatConfirmReceipt function is not available.');
          Toast.show({ content: "请先升级App后再试" })
        }
      } else {
        console.log("device is not available.");
      }
    } catch (e: any) {
      console.log(e.message);
    }
  }

  //获取奖励
  function claimReward(device: string) {
    if (device === 'android') {
      // window.android.launchWeChatConfirmReceipt("")
    } else {
      // window.webkit.messageHandlers.launchWeChatConfirmReceipt.postMessage(data);
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header - GIF Banner */}
      <header className="relative w-full bg-white">
        {/* 使用用户提供的GIF图片 */}
        <img src="/images/activity/summerChallenge/banner.gif" alt="暑期打卡挑战" className="w-full object-cover" />
        <h1 className="text-sm font-bold px-4 py-2 w-full">暑期30天打卡挑战 | 玩亲子游戏·瓜分奖金</h1>
      </header>

      {/* Scrollable Content */}
      <main className="flex-grow overflow-y-auto">
        {/* 活动详情 */}
        <section>
          <div className="px-4">
            <div className="flex justify-normal items-center py-1">
              <span className="text-gray-800 font-bold">报名时间</span>
              <div>
                <span className="text-gray-800 ml-4" style={{ fontSize: '15px' }}>7月1日~7月15日</span>
                <span className={`${regStatus === 1 ? style2 : style1} text-xs px-2 py-1 ml-2 rounded-full`}>{regStatus === 0 ? "未开始" : regStatus === 1 ? "进行中" : "已截止"}</span>
              </div>
            </div>
            <div className="flex justify-normal items-center py-1">
              <span className="text-gray-800 font-bold">挑战周期</span>
              <span className="text-gray-800 ml-4" style={{ fontSize: '15px' }}>
                7月1日~8月14日
                <span className="text-gray-500 text-sm" style={{ fontSize: '15px' }}>(45天)</span>
              </span>
            </div>
            <div className="flex justify-normal items-center py-1">
              <span className="text-gray-800 font-bold">挑战目标</span>
              <span className="text-gray-800 ml-4" style={{ fontSize: '15px' }}>玩亲子专注力训练游戏 | 累计打卡满30天</span>
            </div>
          </div>
        </section>

        {/* 报名奖励 */}
        <section>
          <div className="px-4">
            <div className="flex justify-normal items-center pt-1">
              <span className="text-gray-800 font-bold">报名奖励</span>
              <span className="text-gray-800 ml-4" style={{ fontSize: '15px' }}>报名成功立即获得</span>
            </div>
            <div className="flex justify-normal">
              <img src="/images/activity/summerChallenge/course.webp" alt="2元报名" className="w-20 h-20 object-contain rounded-md" />
              <button disabled={status?.type === 0 || status?.type === 1 || status?.type === 2 || status?.type === 4} onClick={handleReceiveCourse} className={`${status?.receiveStyle} h-8 self-end mb-2 px-2.5 py-1 ml-4 rounded-sm text-base`} style={{ fontSize: '15px' }}>去领取</button>
            </div>
            <p className="text-gray-800" style={{ fontSize: '15px' }}>30天亲子专注力训练课程</p>
          </div>
        </section>

        {/* 挑战奖励 */}
        <section>
          <div className="px-4 pt-3">
            <div className="flex justify-normal items-center pt-1">
              <span className="text-gray-800 font-bold">挑战奖励</span>
            </div>
            <div className="flex justify-normal items-center py-1">
              <img src="/images/activity/summerChallenge/reward_1.jpg" alt="2元报名" className="w-14 h-14 object-contain rounded-md" />
              <img src="/images/activity/summerChallenge/reward_2.jpg" alt="瓜分奖金" className="w-14 h-14 ml-4 object-contain rounded-md" />
            </div>
            <p className="text-gray-800" style={{ fontSize: '15px' }}>2元报名费全额返 + 瓜分未挑战成功者奖金池</p>
          </div>
        </section>

        {/* TA们已参与 */}
        <section>
          <div className="px-4 py-3 flex items-center -space-x-3 mr-3">
            <span className="text-gray-800 font-bold mr-8">TA们已参与</span>
            <img src="/images/avatar.png" alt="Avatar 1" className="w-8 h-8 rounded-full" />
            <img src="/images/avatar1.png" alt="Avatar 2" className="w-8 h-8 rounded-full" />
            <img src="/images/avatar2.png" alt="Avatar 3" className="w-8 h-8 rounded-full" />
            <div className="flex items-center">
              <span className="text-gray-400 ml-6" style={{ fontSize: '14px' }}>999+人</span>
            </div>
          </div>
        </section>

        <section>
          <div className="relative mt-3" style={{ position: 'relative' }}
          >
            <img src="/images/activity/summerChallenge/activityPlay.png" alt="活动玩法介绍" className="w-full" />
            <img
              onClick={handleReceiveCourse}
              src="/images/activity/summerChallenge/courseview.png"
              alt="查看课程详情"
              className="absolute pulse"
              style={{
                top: '46.8%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '40%',
              }}
            />
          </div>
        </section>
      </main>

      <footer className="fixed bottom-0 left-0 right-0 p-4">
        <button
          className={`${status?.btnStyle} w-full py-3 text-base font-medium rounded-md`}
          onClick={handleParticipateNow}
          disabled={status?.type === 0 || status?.type === 2 || status?.type === 4 || status?.type === 6} >
          {status?.btnText}
        </button>
      </footer>

      {showResultModal && (
        <ChallengeResultModal
          isOpen={showResultModal}
          isSuccess={status?.type === 5}
          onClose={handleCloseModal}
          onClaimReward={handleClaimReward}
        />
      )}
    </div>
  );
};

export default SummerChallengePage;