'use client';

import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  FaBookOpen,
  FaCalendarAlt,
  FaChalkboardTeacher,
  FaChild,
  FaCrown,
  FaGift,
  FaHandsHelping,
  FaMedal,
  FaSchool,
  FaStar,
  FaTasks,
  FaTrophy,
  FaUsers,
} from 'react-icons/fa';
import {
  MdFace,
  MdFoodBank,
  MdOutlineChildCare,
  MdOutlineFamilyRestroom,
  MdPhotoCamera,
} from 'react-icons/md';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function schoolOpeningRules() {
  const searchParams = useSearchParams() as any;
  const [activeTab, setActiveTab] = useState('rules');
  const [type] = useState<any>(searchParams.get('type') ?? '0'); // q,a,z
  useEffect(() => {
    const originalTitle = document.title;
    document.title = '活动规则详情';
    return () => {
      document.title = originalTitle;
    };
  }, []);
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-indigo-950 dark:to-purple-950">
      <header className="bg-gradient-to-r from-indigo-600 to-purple-600 py-8 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="mb-2 flex items-center justify-center text-4xl font-bold">
              <FaSchool className="mr-3 size-8" />
              园所活跃挑战
            </h1>
            <p className="mx-auto max-w-3xl text-xl">
              完成每日任务，积累活跃度，30天内累计21天完成挑战即可赢得环创礼包大奖！
            </p>
          </motion.div>

          <div className="mt-6 flex flex-wrap justify-center gap-4">
            <Badge
              variant="outline"
              className="bg-white/20 px-4 py-2 text-sm text-white"
            >
              <FaCalendarAlt className="mr-2" /> 30天挑战
            </Badge>
            <Badge
              variant="outline"
              className="bg-white/20 px-4 py-2 text-sm text-white"
            >
              <FaTasks className="mr-2" /> 7项任务
            </Badge>
            <Badge
              variant="outline"
              className="bg-white/20 px-4 py-2 text-sm text-white"
            >
              <FaTrophy className="mr-2" /> 丰厚奖励
            </Badge>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Tabs
            defaultValue="rules"
            className="mb-8"
            onValueChange={setActiveTab}
          >
            <TabsList className="mb-6 grid grid-cols-3 rounded-xl bg-white p-1 shadow-md dark:bg-gray-800">
              <TabsTrigger
                value="rules"
                className="rounded-lg data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700 dark:data-[state=active]:bg-indigo-900 dark:data-[state=active]:text-indigo-100"
              >
                <FaBookOpen className="mr-2" /> 活动规则
              </TabsTrigger>
              <TabsTrigger
                value="tasks"
                className="rounded-lg data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700 dark:data-[state=active]:bg-indigo-900 dark:data-[state=active]:text-indigo-100"
              >
                <FaTasks className="mr-2" /> 每日任务
              </TabsTrigger>
              <TabsTrigger
                value="rewards"
                className="rounded-lg data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700 dark:data-[state=active]:bg-indigo-900 dark:data-[state=active]:text-indigo-100"
              >
                <FaGift className="mr-2" /> 额外奖励
              </TabsTrigger>
            </TabsList>

            <TabsContent value="rules" className="space-y-6">
              <Card className="overflow-hidden border-0 shadow-lg">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-2xl text-indigo-700 dark:text-indigo-300">
                    <FaSchool className="mr-3 size-6" />
                    园所活跃挑战规则
                  </CardTitle>
                  <CardDescription className="text-lg">
                    园所完成每日任务挑战，积累活跃度
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="rounded-lg bg-indigo-50 p-4 dark:bg-indigo-950/50">
                    <p className="text-lg">
                      活跃度到达一定标准则视为完成当日挑战。30天内累计21天完成挑战即可赢得环创礼包大奖！
                    </p>
                  </div>

                  <div className="space-y-4">
                    <h3 className="flex items-center text-xl font-semibold text-indigo-700 dark:text-indigo-300">
                      <FaSchool className="mr-2 size-5" />
                      园所活跃挑战达标要求：
                    </h3>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                      {type === 'q' && (
                        <div className="rounded-lg border-l-4 border-indigo-400 bg-white p-4 shadow-md dark:bg-gray-800">
                          <h4 className="mb-2 flex items-center font-semibold">
                            <FaChild className="mr-2 text-indigo-400" />{' '}
                            园所要求
                          </h4>
                          <p>全园每日活跃度指数达到100（不可累计、不可跨日）</p>
                        </div>
                      )}
                      {type === 'a' && (
                        <div className="rounded-lg border-l-4 border-purple-400 bg-white p-4 shadow-md dark:bg-gray-800">
                          <h4 className="mb-2 flex items-center font-semibold">
                            <FaChild className="mr-2 text-purple-400" />{' '}
                            园所要求
                          </h4>
                          <p>全园每日活跃度指数达到200（不可累计、不可跨日）</p>
                        </div>
                      )}
                      {type === 'z' && (
                        <div className="rounded-lg border-l-4 border-pink-400 bg-white p-4 shadow-md dark:bg-gray-800">
                          <h4 className="mb-2 flex items-center font-semibold">
                            <FaChild className="mr-2 text-pink-400" /> 园所要求
                          </h4>
                          <p>全园每日活跃度指数达到300（不可累计、不可跨日）</p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tasks" className="space-y-6">
              <Card className="overflow-hidden border-0 shadow-lg">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-2xl text-blue-700 dark:text-blue-300">
                    <FaTasks className="mr-3 size-6" />
                    每日任务
                  </CardTitle>
                  <CardDescription className="text-lg">
                    完成以下任务获取活跃度积分
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {[
                      {
                        id: 1,
                        title: '发布有效动态',
                        description:
                          '老师发布有效动态（图文内容完整+包含互动引导话术+家长有有效的互动）即可获得活跃度',
                        points: 3,
                        limit: '每日最多20条',
                        icon: <FaChalkboardTeacher className="text-blue-500" />,
                        color: 'blue',
                        name: '班级动态',
                      },
                      {
                        id: 2,
                        title: '动态互动达标',
                        description:
                          '老师发布班级动态后，单条动态浏览量达到20次，同时点赞评论数达到10次视为任务完成，每日前20次完成累计积分，每次完成累计5分。',
                        points: 5,
                        limit: '每日最多20次',
                        icon: <FaHandsHelping className="text-indigo-500" />,
                        color: 'indigo',
                        name: '班级动态',
                      },
                      {
                        id: 3,
                        title: '录入家长人脸',
                        description:
                          '园所内每录入成功一位家长人脸视为完成任务一次，录入人脸任务不限次数，每一位成功录入的人脸累计5积分。',
                        points: 5,
                        limit: '不限次数',
                        icon: <MdFace className="text-purple-500" />,
                        color: 'purple',
                        name: '人脸入录',
                      },
                      {
                        id: 4,
                        title: '全员人脸录入',
                        description:
                          '园所内部全部学生，每位学生有2个及以上的已录入家长人脸，视为完成任务，该任务为一次性任务，单次完成累计100积分。',
                        points: 100,
                        limit: '一次性任务',
                        icon: <MdPhotoCamera className="text-pink-500" />,
                        color: 'pink',
                        name: '人脸入录',
                      },
                      {
                        id: 5,
                        title: '发布校园食谱',
                        description:
                          '老师成功发布校园食谱视为完成任务一次，每天可完成一次该任务，该任务累计20积分。',
                        points: 20,
                        limit: '每日1次',
                        icon: <MdFoodBank className="text-orange-500" />,
                        color: 'orange',
                        name: '校园食谱',
                      },
                      {
                        id: 6,
                        title: '发布亲子任务',
                        description:
                          '老师成功发布一项亲子任务视为完成任务一次，每日完成该任务不限次数，每次完成可累计10积分。',
                        points: 10,
                        limit: '不限次数',
                        icon: (
                          <MdOutlineFamilyRestroom className="text-green-500" />
                        ),
                        color: 'green',
                        name: '亲子任务',
                      },
                      {
                        id: 7,
                        title: '亲子任务高完成率',
                        description:
                          '老师发布的亲子动态，完成率达到70%以上，视为完成该任务一次，每日不限次数，每次完成可累计60积分。',
                        points: 60,
                        limit: '不限次数',
                        icon: <MdOutlineChildCare className="text-teal-500" />,
                        color: 'teal',
                        name: '亲子任务',
                      },
                    ].map((task) => (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: task.id * 0.1 }}
                        className={`border- flex items-start space-x-4 rounded-lg border-l-4 bg-white p-5 shadow-md dark:bg-gray-800${task.color}-400`}
                      >
                        {/* <div
                          className={`bg-${task.color}-100 dark:bg-${task.color}-900/30 text-${task.color}-500 flex size-12 shrink-0 items-center justify-center rounded-full`}
                        >
                          {task.icon}
                        </div> */}
                        <div className="flex-1">
                          <div className="mt-3 flex items-center justify-between">
                            <h3 className="text-lg font-semibold">
                              {task.title}
                            </h3>
                            <Badge className="bg-blue-100 text-[16px] font-semibold text-blue-700">
                              {task.name}
                            </Badge>
                          </div>
                          <p className="mt-1 text-gray-600 dark:text-gray-300">
                            {task.description}
                          </p>
                          <div className="mt-3 flex items-center justify-between">
                            <Badge
                              variant="outline"
                              className="bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                            >
                              {task.limit}
                            </Badge>
                            <Badge
                              className={`bg-${task.color}-100 dark:bg-${task.color}-900/30 text-${task.color}-700 dark:text-${task.color}-300 px-3 py-1 text-sm font-semibold`}
                            >
                              +{task.points} 积分/次
                            </Badge>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="rewards" className="space-y-6">
              <Card className="overflow-hidden border-0 shadow-lg">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-2xl text-amber-700 dark:text-amber-300">
                    <FaGift className="mr-3 size-6" />
                    老师奖励规则
                  </CardTitle>
                  <CardDescription className="text-lg">
                    参与活动获得丰厚奖励
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                      className="rounded-lg border-t-4 border-amber-400 bg-white p-5 shadow-md dark:bg-gray-800"
                    >
                      <h3 className="mb-3 flex items-center text-lg font-semibold text-amber-600 dark:text-amber-400">
                        <FaGift className="mr-2 size-5" />
                        启动激励
                      </h3>
                      <p>活动第一天发送动态即得3元红包</p>
                      <div className="mt-3 flex justify-end">
                        <Badge className="bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300">
                          <FaStar className="mr-1" /> 简单获取
                        </Badge>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                      className="rounded-lg border-t-4 border-indigo-400 bg-white p-5 shadow-md dark:bg-gray-800"
                    >
                      <h3 className="mb-3 flex items-center text-lg font-semibold text-indigo-600 dark:text-indigo-400">
                        <FaGift className="mr-2 size-5" />
                        首周激励
                      </h3>
                      <p>
                        第一周，10条以上有效动态，且每条互动率都达到60%，奖励现金红包
                      </p>
                      <div className="mt-3 flex justify-end">
                        <Badge className="bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300">
                          <FaStar className="mr-1" /> 互动奖励
                        </Badge>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 }}
                      className="rounded-lg border-t-4 border-purple-400 bg-white p-5 shadow-md dark:bg-gray-800"
                    >
                      <h3 className="mb-3 flex items-center text-lg font-semibold text-purple-600 dark:text-purple-400">
                        <FaGift className="mr-2 size-5" />
                        14天激励
                      </h3>
                      <p>
                        活动开始后第14天将统计每位老师负责班级的活跃度贡献，对活跃度最高的十位老师，赠送现金奖励
                      </p>
                      <div className="mt-3 flex justify-end">
                        <Badge className="bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">
                          <FaCrown className="mr-1" /> 排名奖励
                        </Badge>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.4 }}
                      className="rounded-lg border-t-4 border-pink-400 bg-white p-5 shadow-md dark:bg-gray-800"
                    >
                      <h3 className="mb-3 flex items-center text-lg font-semibold text-pink-600 dark:text-pink-400">
                        <FaGift className="mr-2 size-5" />
                        月度激励
                      </h3>
                      <p>
                        活动结束后，统计整个活动期间的有效动态数+整体的互动率。根据排名发放奖励
                      </p>
                      <ul className="mt-2 list-disc space-y-1 pl-5">
                        <li>一等奖（1-3）：100元</li>
                        <li>二等奖（4-10）：50元</li>
                      </ul>
                      <div className="mt-3 flex justify-end">
                        <Badge className="bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300">
                          <FaMedal className="mr-1" /> 顶级奖励
                        </Badge>
                      </div>
                    </motion.div>
                  </div>

                  <Separator className="my-6" />

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800"
                  >
                    <h3 className="mb-4 flex items-center text-xl font-semibold text-blue-600 dark:text-blue-400">
                      <FaTrophy className="mr-2 size-6" />
                      半月园所奖励
                    </h3>
                    <p className="mb-4">
                      在活动开始第14天时统计前半个月的园所活动表现，根据园所的活跃率和家长参与率进行排名，对排名靠前的园所赠送绘本套装奖励。
                    </p>
                  </motion.div>

                  <Separator className="my-6" />

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                    className="rounded-lg bg-gradient-to-r from-amber-500 to-orange-500 p-6 text-white shadow-md"
                  >
                    <h3 className="mb-3 flex items-center text-xl font-semibold">
                      <FaCrown className="mr-2 size-6" />
                      额外奖励规则
                    </h3>
                    <p className="text-lg">
                      21天以外每多一天完成挑战，多30元现金红包奖励，最多320元。
                    </p>
                  </motion.div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>

        {activeTab !== 'rewards' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="mb-8 overflow-hidden border-0 shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-2xl text-green-700 dark:text-green-300">
                  <FaUsers className="mr-3 size-6" />
                  任务指引
                </CardTitle>
                <CardDescription className="text-lg">
                  如何完成各项任务
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div>
                  <div className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
                    <h3 className="mb-4 flex items-center text-xl font-semibold text-green-600 dark:text-green-400">
                      <FaChalkboardTeacher className="mr-2 size-5" />
                      班级动态、校园食谱、亲子任务发布位置
                    </h3>
                    <p className="mb-4 text-lg">
                      掌心智校/掌心宝贝园丁端APP首页
                    </p>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      <div className="overflow-hidden rounded-lg shadow-md">
                        <img
                          src="https://unicorn-media.ancda.com/production/app/activity/taskRelease1.png"
                          alt="App interface"
                          className=" w-full object-cover"
                        />
                        <div className="bg-green-50 p-3 text-center text-green-700 dark:bg-green-900/30 dark:text-green-300">
                          APP首页发布
                        </div>
                      </div>
                      <div className="overflow-hidden rounded-lg shadow-md">
                        <img
                          src="https://unicorn-media.ancda.com/production/app/activity/taskRelease2.png"
                          alt="App interface"
                          className="w-full object-cover"
                        />
                        <div className="bg-green-50 p-3 text-center text-green-700 dark:bg-green-900/30 dark:text-green-300">
                          发布功能入口
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
                    <h3 className="mb-4 flex items-center text-xl font-semibold text-purple-600 dark:text-purple-400">
                      <MdFace className="mr-2 size-5" />
                      录入人脸位置
                    </h3>
                    <p className="mb-4 text-lg">
                      掌心APP/宝贝园丁端APP，底部选项栏点击工作台--下滑找到安全健康--点击人脸录入图标。
                    </p>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <div className="overflow-hidden rounded-lg shadow-md">
                        <img
                          src="https://unicorn-media.ancda.com/production/app/activity/faceTask.jpg"
                          alt="App interface"
                          className="w-full object-cover"
                        />
                        <div className="bg-purple-50 p-3 text-center text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">
                          点击人脸录入
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="rounded-lg bg-white p-6  dark:bg-gray-800">
                    <h3 className="mb-4 flex items-center text-xl font-semibold text-blue-600 dark:text-blue-400">
                      <FaHandsHelping className="mr-2 size-5" />
                      家长参与互动方式
                    </h3>
                    <div className="rounded-lg bg-blue-50 p-4 text-lg dark:bg-blue-900/30">
                      老师发布班级动态、校园食谱、亲子任务后，家长即可再掌心智校/掌心宝贝家长端APP首页看到老师的动态，点击即可互动。
                    </div>
                  </div>

                  <Separator />

                  <div className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
                    <h3 className="mb-4 flex items-center text-xl font-semibold text-amber-600 dark:text-amber-400">
                      <MdFace className="mr-2 size-5" />
                      家长录入人脸位置
                    </h3>
                    <p className="mb-4 text-lg">
                      掌心智校/掌心宝贝家长端APP首页：
                    </p>
                    <div className="overflow-hidden rounded-lg shadow-md">
                      <img
                        src="https://unicorn-media.ancda.com/production/app/activity/parentFace.png"
                        alt="Parent app interface"
                        className="w-full object-cover"
                      />
                      <div className="bg-amber-50 p-3 text-center text-amber-700 dark:bg-amber-900/30 dark:text-amber-300">
                        家长端APP首页
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </main>

      <footer className="bg-gradient-to-r from-indigo-600 to-purple-600 py-8 text-white">
        <div className="container mx-auto px-4 text-center">
          <p className="text-white/80">© 2025 掌心智校/掌心宝贝 </p>
        </div>
      </footer>
    </div>
  );
}
