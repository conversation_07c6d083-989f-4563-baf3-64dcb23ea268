'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { hinaTrack } from '@/utils';

if (typeof document !== 'undefined') {
  document.title = '掌心视频大赛';
}

function page() {
  useEffect(() => {
    hinaTrack('activity_index');
  }, []);

  return (
    <div className="relative w-full h-screen overflow-y-scroll">
      <Image
        src="https://unicorn-media.ancda.com/production/app/activity/video-competition.jpeg"
        alt="intro"
        width={750}
        height={4062}
        className="w-full"
      />
    </div>
  );
}

export default page;
