'use client';
import React, { useEffect } from 'react';
import { hinaTrack } from '@/utils';

if (typeof document !== 'undefined') {
  document.title = '开学第一周';
}

const data = `
  <section data-role="outer" class="article135" style="padding: 0px; background-color: rgb(241, 254, 224);box-sizing: border-box;">
	<script>
    function getMobile() {
      var u = navigator.userAgent;
      var isAndroid = u.includes('Android') || u.includes('Adr'); // android终端
      if (isAndroid) {
        return 'android';
      }
      return 'ios';
    };
    function navigationToNativePage(path) {
      try {
        var device = getMobile();
        if (device === 'android') {
          window.android.launch(path);
        } else if (device === 'ios') {
          window.webkit.messageHandlers.launch.postMessage({
            path,
          });
        } else {
          throw new Error('方法执行出错');
        }
      } catch (e) {
        console.log(e);
      }
    };
    window.onload = function() {
      // JavaScript代码
      console.log("dddd")
    }
  </script>
  <section  data-tools="135编辑器" data-id="105041">
		<section data-role="absolute-layout" data-mode="svg" data-width="375" data-height="436" data-ratio="1.1626666666666667" style="font-size:16px;overflow:hidden;grid-template-rows:100%;grid-template-columns:100%;display:grid;max-width:100% !important; width: 100%;box-sizing:border-box;">
			<section data-role="ratio" style="grid-row-start: 1; grid-column-start: 1; height: 100%;">
				<svg viewbox="0 0 375 436" style="max-width:100% !important;pointer-events: none; display: inline-block; width: 100%; vertical-align: top; -webkit-tap-highlight-color: transparent; user-select: none;box-sizing:border-box;"></svg>
			</section>
			<section data-role="block" style=";width:102%;margin-top:-0.26666666666666666%;margin-left:-3.4666676666666665%;grid-row-start:1;grid-column-start:1;height:max-content;max-width:102% !important;line-height:0; display: block; font-size: 27.2px;box-sizing:border-box;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 383 127">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/561796142e94a4db9ea68529d5948556.png"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:102%;margin-top:6.4%;margin-left:-3.4666676666666665%;grid-row-start:1;grid-column-start:1;height:max-content;max-width:102% !important;line-height:0; display: block; font-size: 27.2px;box-sizing:border-box;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 383 105">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/a496fbd504ea285e14fa797cf050a5f9.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:27%;margin-top:7.733333333333333%;margin-left:-3.4666676666666665%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 7.2px;box-sizing:border-box;max-width:27% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 101 130">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/21295eca55d82d5bb62ae0b24d845951.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:25%;margin-top:-2.1333333333333333%;margin-left:16.266666666666666%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 6.66667px;box-sizing:border-box;max-width:25% !important;transform: rotate(353deg);-webkit-transform: rotate(353deg);-moz-transform: rotate(353deg);-o-transform: rotate(353deg);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 94 120">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/21295eca55d82d5bb62ae0b24d845951.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:60%;margin-top:-0.26666666666666666%;margin-left:41.333333333333336%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 16px;box-sizing:border-box;max-width:60% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 225 95">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/4420775245b68d2ac70f1883eade2a44.png"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:85%;margin-top:35.46666666666667%;margin-left:7.733333333333333%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 22.6667px;box-sizing:border-box;max-width:85% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 319 260">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/4b21210597205e816519f4ab484da98f.png"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:7%;margin-top:37.6%;margin-left:20.266666666666666%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 10.1818px;box-sizing:border-box;max-width:7% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 26 63">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/54bb3f6756bb548bc4243ea50de0aeaf.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:7%;margin-top:42.66666666666667%;margin-left:11.200000000000001%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 10.1818px;box-sizing:border-box;max-width:7% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 26 63">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/97c93638923194314d46807ac4354672.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:4%;margin-top:58.13333333333334%;margin-left:2.1333333333333333%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 12.8px;box-sizing:border-box;max-width:4% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 15 40">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/fa2d3e6ebefec8f7f7d638f5a0c34517.png"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:4%;margin-top:28.53333333333333%;margin-left:41.333333333333336%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 10.6667px;box-sizing:border-box;max-width:4% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 15 20">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/a9d7aa5b797db148955a007a0c59c77d.png"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:6%;margin-top:32.800000000000004%;margin-left:85.06666666666666%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 1.6px;box-sizing:border-box;max-width:6% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 23 18">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/18ac4d75cb43ec3bee78eb835eb5b332.png"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:20%;margin-top:26.666666666666668%;margin-left:63.2%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 5.33333px;box-sizing:border-box;max-width:20% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 75 75">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/c95d6d01f50f62d9863a092d7b6af171.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:15%;margin-top:40.266666666666666%;margin-left:82.13333333333334%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 12px;box-sizing:border-box;max-width:15% !important;transform: scale(1) rotate(94deg);-webkit-transform: scale(1) rotate(94deg);-moz-transform: scale(1) rotate(94deg);-o-transform: scale(1) rotate(94deg);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 56 59">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/c95d6d01f50f62d9863a092d7b6af171.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:39%;margin-top:87.73333333333333%;margin-left:3.733333333333334%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 10.4px;box-sizing:border-box;max-width:39% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 146 85">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/a89ecdf7c3f8fc8d2722b64ffab0e8aa.png" class=""/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:20%;margin-top:70.13333333333334%;margin-left:0.26666666666666666%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 5.33333px;box-sizing:border-box;max-width:20% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 75 97">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/4c94eb99f83f743be438d1cfbf1b0649.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:12%;margin-top:86.66666666666667%;margin-left:18.133333333333333%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 12px;box-sizing:border-box;max-width:12% !important;transform: scale(1) rotate(7deg);-webkit-transform: scale(1) rotate(7deg);-moz-transform: scale(1) rotate(7deg);-o-transform: scale(1) rotate(7deg);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 45 69">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/b68519c9b030ac442609013c0c2f6e94.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:36%;margin-top:81.06666666666666%;margin-left:61.33333333333333%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 9.6px;box-sizing:border-box;max-width:36% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 135 98">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/5c0cb75487d7d000600ac93551ac9a32.png"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:9%;margin-top:91.2%;margin-left:63.2%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 2.4px;box-sizing:border-box;max-width:9% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 34 57">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/46c7bb26467ff6c647fcfed3a8113807.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:9%;margin-top:93.60000000000001%;margin-left:56.8%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 2.4px;box-sizing:border-box;max-width:9% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.6; overflow: visible;box-sizing:border-box;" viewbox="0 0 34 53">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<img data-role="target" style="overflow:hidden;width: 100%;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/60661917e9ffd0932e70d4ce3054ac7b.gif"/>
					</foreignobject>
				</svg>
			</section>
			<section data-role="block" style=";width:85%;margin-top:45.33333333333333%;margin-left:7.733333333333333%;grid-row-start:1;grid-column-start:1;height:max-content;line-height:0; display: block; font-size: 13.4603px;box-sizing:border-box;max-width:85% !important;transform: scale(1);-webkit-transform: scale(1);-moz-transform: scale(1);-o-transform: scale(1);" >
				<svg style="max-width:100% !important;display: inline-block; width: 100%; vertical-align: top; line-height: 1.5; overflow: visible;box-sizing:border-box;" viewbox="0 0 319 147">
					<foreignobject data-role="block-content" height="100%" width="100%">
						<section style="overflow:hidden;">
							<section style="text-align: center;letter-spacing: 0.053em; color: #92c568;" class="">
								<span style="font-size:1.5em;letter-spacing: 0.053em; color: #f5a623;"><strong class="135brush" data-brushtype="text">开学第一周</strong></span>
							</section>
							<section style="text-align: center;letter-spacing: 0.053em; color: #92c568;" class="">
								<span style="font-size:2.5em;letter-spacing: 0.053em; color: #92c568;"><strong class="135brush" data-brushtype="text">幼儿园老师的</strong></span>
							</section>
							<section style="text-align: center;letter-spacing: 0.053em; color: #92c568;" class="">
								<span style="font-size:2.5em;letter-spacing: 0.053em; color: #92c568;"><strong class="135brush" data-brushtype="text" >五个魔法时刻</strong></span>
							</section>
						</section>
					</foreignobject>
				</svg>
			</section>
		</section>
	</section>
	<section style="line-height: 27.2px;" class="">
		<p>
			<br/>
		</p>
	</section>
	<section  data-tools="135编辑器" data-id="155376" data-width="95%" style="width: 95%; flex: 0 0 95%; margin-left: auto; margin-right: auto;box-sizing:border-box;max-width:95% !important;">
		<section style="margin: 10px auto;" class="">
			<section style="background-color: #ffffff;border-radius: 15px;padding: 20px 10px;box-sizing: border-box;" class="">
				<section style="text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;color: #333333;background-color: transparent;">
					<section style="line-height: 1.75em;" class="135brush" data-autoskip="1">
						<p>
							亲爱的幼师伙伴，当教室重新亮起彩色的气球，当走廊再次响起哒哒的小脚步声，你是否也悄悄收藏着这份独属于开学季的期待与忐忑？这5件小事，或许能让你在忙碌中触摸到教育的温度。
						</p>
					</section>
				</section>
			</section>
			<section style="width: 100%;margin-top: -20px;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
				<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 504.01 24.95" style="display: block;">
					<g id="图层_2" data-name="图层 2">
						<g id="图层_1-2" data-name="图层 1">
							<path d="M503.76.43S442.82,35.8,351.41,18.1,188.87,33.79,161.33,21.78c-14.64-8.53-3.23-25.23,6.53-16.2s-7.89,21.8-41.35,14-81.27-30-126.23,0" style="fill: none;stroke: #87c44b;"/>
						</g>
					</g>
				</svg>
			</section>
			<section class="assistant" style="width: 25px;margin-left: 20px;margin-top: -11px;box-sizing:border-box;transform: rotateZ(0deg);-webkit-transform: rotateZ(0deg);-moz-transform: rotateZ(0deg);-o-transform: rotateZ(0deg);">
				<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/cac2152d929dbb12974f2c77e9d2566c.gif" data-width="100%" draggable="false"/>
			</section>
			<section class="assistant" style="width: 35px;margin-left: auto;margin-top: -40px;margin-right: 25px;box-sizing:border-box;transform: rotateZ(0deg);-webkit-transform: rotateZ(0deg);-moz-transform: rotateZ(0deg);-o-transform: rotateZ(0deg);">
				<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/cac2152d929dbb12974f2c77e9d2566c.gif" data-width="100%" draggable="false"/>
			</section>
		</section>
	</section>
	<section style="line-height: 27.2px;" class="">
		<p>
			<br/>
		</p>
	</section>
	<section  data-tools="135编辑器" data-id="155376" data-width="95%" style="width: 95%; flex: 0 0 95%; margin-left: auto; margin-right: auto;box-sizing:border-box;max-width:95% !important;">
		<section style="margin: 10px auto;" class="">
			<section style="background-color: #ffffff;border-radius: 15px;padding: 20px 10px;box-sizing: border-box;" class="">
				<section>
					<section>
						<section style="text-align: center;" class="">
							<img class="vip_img template-cover" referrerpolicy="no-referrer" data-model="Image" style="width: 100%; margin-bottom: 15px;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" alt="" src="https://bcn.135editor.com/files/images/editor_styles/37987f596d63e6d84b6993903632cf1c.jpg" draggable="false" data-ratio="0.6666666666666666"/>
						</section>
					</section>
				</section>
				<section>
					<section data-tools="135编辑器" data-id="155347">
						<section style="margin: 10px auto;" class="">
							<section class="box-edit" style="margin-bottom: 25px;">
								<section style="display: flex;flex-direction: column;">
									<section class="assistant" style="width: 35px;margin-bottom: -30px;z-index: 3;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/21295eca55d82d5bb62ae0b24d845951.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
									<section style="background-color: #fff7d7;padding: 10px 15px;border-radius: 25px;box-sizing: border-box;" class="">
										<section style="font-size: 16px;color: #57794d;text-align: center;" class="">
											<strong class="135brush" data-brushtype="text">
												魔法一：用一面照片墙叩响心门
											</strong>
										</section>
									</section>
									<section class="assistant" style="width: 40px;margin-left: auto;margin-top: -30px;z-index: 3;margin-right: 20px;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/9fe7e53a9a95d520ba6be2b88570b4f9.png" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
								</section>
								<section style="text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;color: #333333;background-color: transparent;padding-top: 15px;padding: 15px 15px 0;box-sizing: border-box;" class="">
									<section style="line-height: 1.75em;" class="135brush" data-autoskip="1">
										<p>
											在教室布置专属的“笑脸捕捉区”，打开 APP 的<strong>人脸录入</strong>功能，蹲下身与每个孩子玩“眨眼睛数三秒”游戏。当您录入孩子正面照时，记得悄悄说：“这颗小酒窝会变成魔法钥匙哦！”。笑脸照片上传后，未来的每一条动态照片都将智能归类生成每个宝贝的专属成长档案，让科技成为您的隐形助手。
										</p>
										<p>
											 <button onclick="window.navigationToNativePage && window.navigationToNativePage('rn://FaceStack')" style="
      background-color: #92c568;
      color: white;
      padding: 5px 12px;
      border: none;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
      margin: 8px 0;
    ">
      去录人脸
    </button>
										</p>
									</section>
								</section>
								<section style="width: 100%;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 688.74 59.51" style="display: block;">
										<g id="图层_2" data-name="图层 2">
											<g id="图层_1-2" data-name="图层 1">
												<path d="M.5,30.63c31.64,18.15,123.1,22.54,216,4,125.3-25,188.23-15.3,233-5,40.57,9.34,35.38-34.34,10-28-30.51,7.63-38.26,100.09,229,32" style="fill: none;stroke: #acca85;stroke-width: 2px;"/>
											</g>
										</g>
									</svg>
								</section>
								<section class="assistant" style="width: 26px;margin-left: auto;margin-top: -4.1%;margin-right: 40px;z-index: 3;box-sizing:border-box;">
									<img class="assistant" style="width: 100%; display: block;  margin-top: -50px;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/46c7bb26467ff6c647fcfed3a8113807.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
								</section>
							</section>
							<section class="box-edit" style="margin-bottom: 25px;">
								<section style="display: flex;flex-direction: column;" class="">
									<section class="assistant" style="width: 35px;margin-bottom: -30px;z-index: 3;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/21295eca55d82d5bb62ae0b24d845951.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
									<section style="background-color: #fff7d7;padding: 10px 15px;border-radius: 25px;box-sizing: border-box;" class="">
										<section style="font-size: 16px;color: #57794d;text-align: center;" class="">
											<strong class="135brush" data-brushtype="text">魔法二：启动「会讲故事的魔法相册」</strong>
										</section>
									</section>
									<section class="assistant" style="width: 40px;margin-left: auto;margin-top: -30px;z-index: 3;margin-right: 20px;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/9fe7e53a9a95d520ba6be2b88570b4f9.png" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
								</section>
								<section style="text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;color: #333333;background-color: transparent;padding-top: 15px;padding: 15px 15px 0;box-sizing: border-box;" class="">
									<section style="line-height: 1.75em;" class="135brush" data-autoskip="1">
										<p>
											午睡后阳光最好的时刻，轻轻举起手机：“宝贝，你的笑容比云朵还软！”抓拍孩子专注玩积木的侧脸、吃饭时鼓起的脸颊、奔跑时飞扬的发梢。——这些照片会自动聚集成《成长档案》，期末时你会惊喜发现，当初那个揪着你衣角的孩子，早已在时光里悄悄抽枝发芽。
										</p>
                    <p>
                    <button onclick="window.navigationToNativePage && window.navigationToNativePage('app://app/moment/publish')" style="
                        background-color: #92c568;
                        color: white;
                        padding: 5px 12px;
                        border: none;
                        border-radius: 5px;
                        font-size: 16px;
                        cursor: pointer;
                        margin: 8px 0;
                      ">
                        去发动态
                      </button>
                    </p>
									</section>
								</section>
								<section style="width: 100%;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 688.74 59.51" style="display: block;">
										<g id="图层_2" data-name="图层 2">
											<g id="图层_1-2" data-name="图层 1">
												<path d="M.5,30.63c31.64,18.15,123.1,22.54,216,4,125.3-25,188.23-15.3,233-5,40.57,9.34,35.38-34.34,10-28-30.51,7.63-38.26,100.09,229,32" style="fill: none;stroke: #acca85;stroke-width: 2px;"/>
											</g>
										</g>
									</svg>
								</section>
								<section class="assistant" style="width: 26px;margin-left: auto;margin-top: -4.1%;margin-right: 40px;z-index: 3;box-sizing:border-box;">
									<img class="assistant" style="width: 100%; display: block;  margin-top: -50px;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/46c7bb26467ff6c647fcfed3a8113807.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
								</section>
							</section>
							<section class="box-edit" style="margin-bottom: 25px;">
								<section style="display: flex;flex-direction: column;" class="">
									<section class="assistant" style="width: 35px;margin-bottom: -30px;z-index: 3;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/21295eca55d82d5bb62ae0b24d845951.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
									<section style="background-color: #fff7d7;padding: 10px 15px;border-radius: 25px;box-sizing: border-box;" class="">
										<section style="font-size: 16px;color: #57794d;text-align: center;" class="">
											<strong class="135brush" data-brushtype="text">魔法三：三个黄金时间点观察记录</strong>
										</section>
									</section>
									<section class="assistant" style="width: 40px;margin-left: auto;margin-top: -30px;z-index: 3;margin-right: 20px;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/9fe7e53a9a95d520ba6be2b88570b4f9.png" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
								</section>
								<section style="text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;color: #333333;background-color: transparent;padding-top: 15px;padding: 15px 15px 0;box-sizing: border-box;" class="">
									<section style="line-height: 1.75em;" class="135brush" data-autoskip="1">
										<p>
											晨间入园时攥着衣角的小手，午餐时偷偷挑出的胡萝卜，午睡蜷缩的独特姿势...这些稍纵即逝的细节，正是读懂孩子的密码。随时拍下宝贝们的这些瞬间，批量上传到每天的班级动态，系统自动归类到每个宝宝的成长档案里，让家长看见专业之外的用心。
										</p>
                     <p>
                    <button onclick="window.navigationToNativePage && window.navigationToNativePage('app://app/moment/publish')" style="
                        background-color: #92c568;
                        color: white;
                        padding: 5px 12px;
                        border: none;
                        border-radius: 5px;
                        font-size: 16px;
                        cursor: pointer;
                        margin: 8px 0;
                      ">
                        去发动态
                      </button>
                    </p>
									</section>
								</section>
								<section style="width: 100%;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 688.74 59.51" style="display: block;">
										<g id="图层_2" data-name="图层 2">
											<g id="图层_1-2" data-name="图层 1">
												<path d="M.5,30.63c31.64,18.15,123.1,22.54,216,4,125.3-25,188.23-15.3,233-5,40.57,9.34,35.38-34.34,10-28-30.51,7.63-38.26,100.09,229,32" style="fill: none;stroke: #acca85;stroke-width: 2px;"/>
											</g>
										</g>
									</svg>
								</section>
								<section class="assistant" style="width: 26px;margin-left: auto;margin-top: -4.1%;margin-right: 40px;z-index: 3;box-sizing:border-box;">
									<img class="assistant" style="width: 100%; display: block;  margin-top: -50px;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/46c7bb26467ff6c647fcfed3a8113807.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
								</section>
							</section>
							<section class="box-edit" style="margin-bottom: 25px;">
								<section style="display: flex;flex-direction: column;" class="">
									<section class="assistant" style="width: 35px;margin-bottom: -30px;z-index: 3;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/21295eca55d82d5bb62ae0b24d845951.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
									<section style="background-color: #fff7d7;padding: 10px 15px;border-radius: 25px;box-sizing: border-box;" class="">
										<section style="font-size: 16px;color: #57794d;text-align: center;" class="">
											<strong class="135brush" data-brushtype="text">魔法四：给家长发送「3秒治愈剂」</strong>
										</section>
									</section>
									<section class="assistant" style="width: 40px;margin-left: auto;margin-top: -30px;z-index: 3;margin-right: 20px;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/9fe7e53a9a95d520ba6be2b88570b4f9.png" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
								</section>
								<section style="text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;color: #333333;background-color: transparent;padding-top: 15px;padding: 15px 15px 0;box-sizing: border-box;" class="">
									<section style="line-height: 1.75em;" class="135brush" data-autoskip="1">
										<p>
											不必写冗长的观察记录，午间抽1分钟发张照片：“看！小满自己吃了三颗丸子”“朵朵交到了第一个朋友”。当人脸照片自动关联孩子在园动态生成生成成长档案，让家长透过屏幕触摸到真实的成长。
										</p>
									</section>
								</section>
								<section style="width: 100%;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 688.74 59.51" style="display: block;">
										<g id="图层_2" data-name="图层 2">
											<g id="图层_1-2" data-name="图层 1">
												<path d="M.5,30.63c31.64,18.15,123.1,22.54,216,4,125.3-25,188.23-15.3,233-5,40.57,9.34,35.38-34.34,10-28-30.51,7.63-38.26,100.09,229,32" style="fill: none;stroke: #acca85;stroke-width: 2px;"/>
											</g>
										</g>
									</svg>
								</section>
								<section class="assistant" style="width: 26px;margin-left: auto;margin-top: -4.1%;margin-right: 40px;z-index: 3;box-sizing:border-box;">
									<img class="assistant" style="width: 100%; display: block;  margin-top: -50px;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/46c7bb26467ff6c647fcfed3a8113807.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
								</section>
							</section>
							<section class="box-edit" style="margin-bottom: 25px;">
								<section style="display: flex;flex-direction: column;" class="">
									<section class="assistant" style="width: 35px;margin-bottom: -30px;z-index: 3;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/21295eca55d82d5bb62ae0b24d845951.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
									<section style="background-color: #fff7d7;padding: 10px 15px;border-radius: 25px;box-sizing: border-box;" class="">
										<section style="font-size: 16px;color: #57794d;text-align: center;" class="">
											<strong class="135brush" data-brushtype="text">魔法五：给自己预留「续杯时间」</strong>
										</section>
									</section>
									<section class="assistant" style="width: 40px;margin-left: auto;margin-top: -30px;z-index: 3;margin-right: 20px;box-sizing:border-box;">
										<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/9fe7e53a9a95d520ba6be2b88570b4f9.png" data-width="100%" draggable="false" title="undefined" alt=""/>
									</section>
								</section>
								<section style="text-align: justify;line-height: 1.75em;letter-spacing: 1.5px;font-size: 14px;color: #333333;background-color: transparent;padding-top: 15px;padding: 15px 15px 0;box-sizing: border-box;" class="">
									<section style="line-height: 1.75em;" class="135brush" data-autoskip="1">
										<p>
											在教案本里夹一张便签纸，每天记录一个被治愈的瞬间：可能是孩子突然塞给你的半块饼干，也可能是他们用歪歪扭扭的线条画出的“老师”。这些碎片会在某个疲惫的傍晚，变成你最珍贵的能量补剂。&nbsp;&nbsp;
										</p>
									</section>
								</section>
								<section style="width: 100%;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 688.74 59.51" style="display: block;">
										<g id="图层_2" data-name="图层 2">
											<g id="图层_1-2" data-name="图层 1">
												<path d="M.5,30.63c31.64,18.15,123.1,22.54,216,4,125.3-25,188.23-15.3,233-5,40.57,9.34,35.38-34.34,10-28-30.51,7.63-38.26,100.09,229,32" style="fill: none;stroke: #acca85;stroke-width: 2px;"/>
											</g>
										</g>
									</svg>
								</section>
								<section class="assistant" style="width: 26px;margin-left: auto;margin-top: -4.1%;margin-right: 40px;z-index: 3;box-sizing:border-box;">
									<img class="assistant" style="width: 100%; display: block;  margin-top: -50px;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/46c7bb26467ff6c647fcfed3a8113807.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
								</section>
							</section>
						</section>
					</section>
				</section>
				<section>
					<section data-tools="135编辑器" data-id="150515" style="margin: 15px 0px 0px; box-sizing: border-box;">
						<section style="margin: 10px auto; display: flex; justify-content: center;">
							<section style="width: 58%;display: flex;flex-direction: column;box-sizing:border-box;max-width:58% !important;" data-width="58%" class="">
								<section style="width: 55px;margin: 0 auto -20px;z-index: 3;box-sizing:border-box;" class="">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 84 59.99" style="display: block;">
										<g id="图层_2" data-name="图层 2">
											<g id="组_194" data-name="组 194">
												<path d="M79.89,17.48,84,20l-4.53,2.49h0L14,58.41h0L11.14,60v-4.8L6.87,53.27l.65-4.62L3.45,47.14l.65-4.62L0,40l2.88-1.58h0l65-35.68h0L72.85,0V4.81l4.29,1.91-.65,4.63,4.06,1.5Z" style="fill: #92c568;fill-rule: evenodd;isolation: isolate;opacity: 0.4000000059604645;"/>
											</g>
										</g>
									</svg>
								</section>
								<section style="background-color: #f1fee0;padding: 10px 10px 20px;box-sizing: border-box;transform: rotate(7deg);-webkit-transform: rotate(7deg);-moz-transform: rotate(7deg);-o-transform: rotate(7deg);" class="">
									<section style="width: 100%;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
										<img style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bexp.135editor.com/files/users/1111/11114339/202502/u5znOB6C_2pLy.jpg?auth_key=1740326399-0-0-ec28848405109262000762e2f0702271" data-width="100%" draggable="false" data-ratio="0.6666666666666666" alt="istockphoto-544978924-612x612.jpg" data-w="4023"/>
									</section>
								</section>
								<section class="assistant" style="width: 80px;margin-left: auto;margin-top: -14px;margin-right: auto;z-index: 3;box-sizing:border-box;">
									<img class="assistant" style="width: 100%; display: block;  margin-top: -65px;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/4c94eb99f83f743be438d1cfbf1b0649.gif" data-width="100%" draggable="false" title="undefined" alt=""/>
								</section>
							</section>
							<section style="width: 52%;display: flex;flex-direction: column;margin-left: -10.1%;z-index: 4;box-sizing:border-box;max-width:52% !important;" data-width="52%" class="">
								<section style="width: 70px;margin-left: auto;margin-bottom: -30px;margin-right: 10px;z-index: 5;box-sizing:border-box;" class="">
									<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 122.01 136.03" style="display: block;">
										<g id="图层_2" data-name="图层 2">
											<g id="图层_1-2" data-name="图层 1">
												<path d="M85.88,108a13,13,0,1,1-13,13A13,13,0,0,1,85.88,108Z" style="fill: #fff;stroke: #ffc572;stroke-width: 4px;fill-rule: evenodd;"/>
												<path d="M1,0S-.59,49.76,60.51,49c37.94-.45,60.76,7.43,60.5,34,.2,39.88-39.67,41-39.67,41" style="fill: none;stroke: #9e0000;stroke-width: 2px;stroke-dasharray: 8,4;"/>
											</g>
										</g>
									</svg>
								</section>
								<section style="background-color: #ffffff;padding: 5px 5px 20px;box-sizing: border-box;transform: rotate(-5deg);-webkit-transform: rotate(-5deg);-moz-transform: rotate(-5deg);-o-transform: rotate(-5deg);" class="">
									<section style="width: 100%;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
										<img style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bexp.135editor.com/files/users/1111/11114339/202502/ySND7hjm_hJKO.jpg?auth_key=1740326399-0-0-742438f73540d9e69903fd197fc8cbbf" data-width="100%" draggable="false" data-ratio="0.6666666666666666" alt="istockphoto-1334673937-612x612.jpg" data-w="4023"/>
									</section>
								</section>
							</section>
						</section>
					</section>
				</section>
			</section>
			<section style="width: 100%;margin-top: -20px;max-width: 100% !important;box-sizing:border-box;" data-width="100%" class="">
				<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 504.01 24.95" style="display: block;">
					<g id="图层_2" data-name="图层 2">
						<g id="图层_1-2" data-name="图层 1">
							<path d="M503.76.43S442.82,35.8,351.41,18.1,188.87,33.79,161.33,21.78c-14.64-8.53-3.23-25.23,6.53-16.2s-7.89,21.8-41.35,14-81.27-30-126.23,0" style="fill: none;stroke: #87c44b;"/>
						</g>
					</g>
				</svg>
			</section>
			<section class="assistant" style="width: 25px;margin-left: 20px;margin-top: -11px;box-sizing:border-box;transform: rotateZ(0deg);-webkit-transform: rotateZ(0deg);-moz-transform: rotateZ(0deg);-o-transform: rotateZ(0deg);">
				<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/cac2152d929dbb12974f2c77e9d2566c.gif" data-width="100%" draggable="false"/>
			</section>
			<section class="assistant" style="width: 35px;margin-left: auto;margin-top: -40px;margin-right: 25px;box-sizing:border-box;transform: rotateZ(0deg);-webkit-transform: rotateZ(0deg);-moz-transform: rotateZ(0deg);-o-transform: rotateZ(0deg);">
				<img class="assistant" style="width: 100%; display: block;vertical-align:baseline;box-sizing:border-box;max-width:100% !important;" src="https://bcn.135editor.com/files/images/editor_styles/cac2152d929dbb12974f2c77e9d2566c.gif" data-width="100%" draggable="false"/>
			</section>
		</section>
	</section>
	<section style="line-height: 27.2px;" class="">
		<p>
			<br/>
		</p>
	</section>
	<section style="line-height: 27.2px;" class="">
		<p>
			<br/>
		</p>
	</section>
	<section  data-tools="135编辑器" data-id="141692"></section>
	<section >
		<section style="line-height: 30.75px;text-align: center;letter-spacing: 1px; font-size: 14px; color: #595959;" class="135brush" data-autoskip="1">
			<p>
				<br/>
			</p>
		</section>
	</section>
  </section>
  `;

function page() {
  useEffect(() => {
    hinaTrack('activity_index');
  }, []);
  return (
    <div className="">
      <article dangerouslySetInnerHTML={{ __html: data }} />
    </div>
  );
}

export default page;
