'use client';
import React, { useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Wand2,
  <PERSON><PERSON><PERSON><PERSON>ig,
  FileCheck2,
  PiggyBank,
  Rocket
} from 'lucide-react';
import clsx from 'clsx';
import YSF from '@neysf/qiyu-web-sdk';

function page() {
  const { useState, useEffect, Fragment } = React;
  const ysfInstance = useRef<any>(undefined);

  const initYSF = async () => {
    if (!ysfInstance.current) {
      ysfInstance.current = await YSF.init(
        `5486e9261b85af09ba48accfc5f4dcaf`,
        {
          hidden: true
        },
        null
      );
    }
  };

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '成长档案';
    }

    initYSF();
  }, []);

  const handleService = () => {
    window.location.href = `${ysfInstance.current('url')}`;
  };

  // 帮助函数：用于延迟动画效果
  const useAnimatedElements = (count) => {
    const [show, setShow] = useState(Array(count).fill(false));

    useEffect(() => {
      const timers = [];
      for (let i = 0; i < count; i++) {
        timers.push(
          setTimeout(() => {
            setShow((prev) => {
              const newShow = [...prev];
              newShow[i] = true;
              return newShow;
            });
          }, i * 200)
        ); // 每个元素延迟 200ms
      }
      return () => timers.forEach(clearTimeout);
    }, [count]);

    return show;
  };

  // 特性数据
  const features = [
    {
      icon: 'party-popper',
      title: '零成本使用',
      description: '免平台费，让每所学校都能轻松记录孩子的成长足迹！',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      iconColor: 'text-blue-500'
    },
    {
      icon: 'wand-2',
      title: '智能省时',
      description:
        '老师发动态→系统自动人脸识别生成孩子成长档案，工作量直降 90%！',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      iconColor: 'text-green-500'
    },
    {
      icon: 'bar-chart-big',
      title: '专业赋能',
      description: '内置发展评估体系，科学追踪成长轨迹，教学质量 UP！',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-700',
      iconColor: 'text-yellow-500'
    },
    {
      icon: 'file-check-2',
      title: '合规利器',
      description: '档案格式完全符合检查要求，迎检准备效率翻倍！',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-700',
      iconColor: 'text-purple-500'
    }
  ];

  const animatedFeatures = useAnimatedElements(features.length);
  const animatedOffer = useAnimatedElements(1);

  return (
    <div>
      <div className="flex-grow overflow-y-auto pb-12 bg-blue-50">
        <div className="relative text-white p-4 pt-12 text-center min-h-[300px] flex flex-col justify-center items-center">
          <div
            className="absolute inset-0 w-full h-full bg-cover bg-center"
            style={{
              backgroundImage: 'url(/images/activity/archives/archives1.jpg)'
            }}
          >
            <div className="absolute inset-0 w-full h-full bg-black bg-opacity-40" />
          </div>
          <div className="relative z-10 animate-fade-in-up">
            <h1 className="font-serif text-4xl font-bold mb-3 leading-10 ">
              童年每一刻
              <br />
              成长全记录
            </h1>
            <p className="text-lg font-sans opacity-90 mb-4">
              专为幼儿园打造的智能成长档案解决方案
            </p>
          </div>
        </div>
        <div className="p-4">
          <div
            className={clsx(
              ' mb-6 p-6 rounded-xl shadow-xl bg-gradient-to-r from-yellow-900 via-yellow-800 to-yellow-900 text-amber-100	 card-hover-effect',
              animatedOffer[0] ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            )}
            style={{ animationDelay: `${features.length * 150}ms` }}
          >
            <div className="flex items-center space-x-4">
              <div>
                <h3 className="font-serif text-xl font-bold mb-1">
                  🌟 成本直降，福利加倍
                </h3>
                <p className="text-sm font-sans opacity-90">
                  学期末专属档案册打印优惠限时开放，行业最底价，助力园所省心又省力！
                </p>
              </div>
            </div>
          </div>
          <div className="p-4 bg-white rounded-xl mb-6 pb-0">
            <div className="p-4">
              <p className="text-base font-sans mb-4">
                「掌心成长档案」运用前沿 AI 技术，用智能化工具生成宝贝成长档案，
                学期末给家长一份孩子成长的可视化珍贵纪念，提升园所专业度和家长满意度。
              </p>
            </div>
            <div className="pb-6 space-y-5">
              {features.map((feature, index) => (
                <div
                  key={feature.title}
                  className={clsx(
                    'p-5 rounded-xl shadow-lg flex items-start space-x-4 card-hover-effect',
                    feature.bgColor,
                    animatedFeatures[index]
                      ? 'animate-fade-in-up opacity-100'
                      : 'opacity-0'
                  )}
                  style={{ animationDelay: `${index * 150}ms` }} // 为Tailwind JIT 无法直接处理的动态延迟添加
                >
                  <div
                    className={clsx(
                      'p-3 rounded-full bg-white shadow-md',
                      feature.iconColor
                    )}
                  >
                    {feature.icon === 'party-popper' && (
                      <PartyPopper className="w-6 h-6" />
                    )}
                    {feature.icon === 'wand-2' && <Wand2 className="w-6 h-6" />}
                    {feature.icon === 'bar-chart-big' && (
                      <BarChartBig className="w-6 h-6" />
                    )}
                    {feature.icon === 'file-check-2' && (
                      <FileCheck2 className="w-6 h-6" />
                    )}
                  </div>
                  <div>
                    <h3
                      className={clsx(
                        'font-serif text-lg font-bold mb-1',
                        feature.textColor
                      )}
                    >
                      {feature.title}
                    </h3>
                    <p
                      className={clsx(
                        'text-sm font-sans',
                        feature.textColor,
                        'opacity-80'
                      )}
                    >
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="fixed bottom-0 left-0 right-0 p-4 rounded-b-3xl">
        <button
          type="button"
          className="w-full  bg-indigo-500/80  hover:bg-indigo-500 text-white font-sans font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] flex items-center justify-center space-x-2"
          onClick={handleService}
        >
          <Rocket className="w-5 h-5" />
          <span>联系客服获取优惠</span>
        </button>
      </div>
    </div>
  );
}

export default page;
