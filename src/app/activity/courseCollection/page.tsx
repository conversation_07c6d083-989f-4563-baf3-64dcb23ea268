'use client';

import { Modal, Toast } from 'antd-mobile';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';

import { hinaTrack, navigationToNativePage } from '@/utils';
// 自定义确认弹窗组件
interface CustomConfirmationModalProps {
  showModal: boolean;
  wordUrl: string;
  currentCourseType: number;
  onClose: () => void;
  onDownload: () => void;
}

const CustomConfirmationModal = ({
  showModal,
  wordUrl,
  currentCourseType,
  onClose,
  onDownload,
}: CustomConfirmationModalProps) => {
  if (!showModal) return null;

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'>
      <div className='w-4/5 max-w-md rounded-lg bg-white p-6'>
        <div className='mb-4 flex items-center justify-between'>
          <h3 className='text-lg font-medium'>文件已准备好</h3>
          <button
            type='button'
            onClick={onClose}
            className='text-gray-400 hover:text-gray-500'
          >
            关闭
          </button>
        </div>
        <div className='mb-4'>
          <p className='mb-2'>如果下载无响应，试试复制链接到浏览器下载</p>
        </div>
        <div className='flex justify-end space-x-3'>
          <CopyToClipboard
            text={wordUrl}
            onCopy={(result) => {
              onClose();
              Toast.show({
                content: '复制成功',
              });
            }}
          >
            <button
              type='button'
              className='rounded bg-gray-100 px-4 py-2 text-gray-700 hover:bg-gray-200'
            >
              复制链接
            </button>
          </CopyToClipboard>
          <button
            type='button'
            onClick={onDownload}
            className='rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700'
          >
            下载文件
          </button>
        </div>
      </div>
    </div>
  );
};

const CourseCollection = () => {
  const searchParams = useSearchParams();
  const type = searchParams.get('type'); // 1未开通,2已开通
  const [showOutlineModal, setShowOutlineModal] = useState(false);
  const [currentOutlineType, setCurrentOutlineType] = useState(4);
  const [showModal, setShowModal] = useState(false);
  const [currentCourseType, setCurrentCourseType] = useState(4);
  const [wordUrl, setWordUrl] = useState('');

  const handleCloseModal = () => {
    setShowModal(false);
  };
  const handleDownloadClick = () => {
    // 这里可以添加实际的下载逻辑
    console.log('下载课程:', currentCourseType);
    hinaTrack('focusCourse_download_click');
    // 模拟下载
    if (wordUrl) {
      const link = document.createElement('a');
      link.href = wordUrl;
      link.download = `course-${currentCourseType}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    setShowModal(false);
  };

  const courseOutlineOneContent = (
    <div className='w-full bg-gradient-to-b from-[#fffadc] to-[#d5fdff] px-6 pb-4 pt-2 text-sm leading-relaxed'>
      <div className='mb-4 flex flex-col '>
        <h3 className='mb-2 text-center text-lg font-bold'> 第一阶段</h3>
        <h4 className='text-left  text-sm text-[#f7943a]'>
          唤醒专注力与建立习惯 (第1~7天 | 5~9分钟/天)
        </h4>
        <p className='mb-2 text-xs  text-[#f7943a]'>
          训练侧重点:听觉广度、听觉加工速度、视觉广度、视觉分辨
        </p>
        <div className='w-full space-y-3 rounded-lg bg-white p-4'>
          <div>
            <h5 className='font-medium text-[#48d7e0]'>第1天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>听知觉训练(4分钟)=听词复述+听词语做动作</p>
              <p className='text-sm'> 视知觉训练(4分钟)=找不同+走迷宫</p>
            </div>
          </div>
          <div>
            <h5 className='font-medium text-[#48d7e0]'>第2天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>听知觉训练(5分钟)=故事仿说+听词语做动作</p>
              <p className='text-sm'>视知觉训练(4分钟)=找相同+连线追踪对应</p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第3天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=听记数列+听故事回答问题
              </p>
              <p className='text-sm'>视知觉训练(3分钟)=找不同+坐标定位</p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第4天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>听知觉训练(5分钟)=故事仿说+听词语做动作</p>
              <p className='text-sm'>视知觉训练(4分钟)=镜像训练+走迷宫</p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第5天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>听知觉训练(5分钟)=听词复述+听指令画图</p>
              <p className='text-sm'>视知觉训练(3分钟)=找相同+坐标定位</p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第6天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>听知觉训练(5分钟)=故事仿说+听词语做动作</p>
              <p className='text-sm'>视知觉训练(4分钟)=看图数数+连线追踪对应</p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第7天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=听数字复述+听故事回答问题
              </p>
              <p className='text-sm'>视知觉训练(3分钟)=镜像训练+连线追踪对应</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  const courseOutlineTwoContent = (
    <div className='w-full bg-gradient-to-b from-[#fffadc] to-[#d5fdff] px-6 pb-4 pt-2 text-sm leading-relaxed'>
      <div className='mb-4 flex flex-col '>
        <h3 className='mb-2 text-center text-lg font-bold'> 第二阶段</h3>
        <h4 className='text-sm font-semibold text-[#f7943a]'>
          延长专注时间 (第8~14天 | 9~12分钟/天)
        </h4>
        <p className='mb-2 text-xs  text-[#f7943a]'>
          训练侧重点:听觉广度、听觉加工速度、听觉稳定性(抗干扰)、视觉广度、视觉分辨、视觉追踪及手部精细
        </p>
        <div className='w-full space-y-3 rounded-lg bg-white p-4'>
          <div>
            <h5 className='font-medium text-[#48d7e0]'>第8天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=故事仿说+听数字做动作+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(6分钟)=看图数数+3阶舒尔特方格+按点描线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第9天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(7分钟)=听词复述+听词语做动作+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(6分钟)=镜像训练+走迷宫+数字连线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第10天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(7分钟)=听记数列+听故事回答问题+数字组合的数字
              </p>
              <p className='text-sm'>
                视知觉训练(6分钟)=找不同+3阶舒尔特方格+按点描线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第11天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=故事仿说+听数字做动作+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(5分钟)=看图数数+连线追踪对应+按点描线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第12天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=听记数列+听故事回答问题+数字组合的数字
              </p>
              <p className='text-sm'>
                视知觉训练(6分钟)=找不同+3阶舒尔特方格+按点描线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第13天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=故事仿说+听数字做动作+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(5分钟)=看图数数+连线追踪对应+按点描线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第14天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=听记数列+听故事回答问题+数字组合的数字
              </p>
              <p className='text-sm'>
                视知觉训练(6分钟)=看图数数+3阶舒尔特方格+运笔练习
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  const courseOutlineThreeContent = (
    <div className='w-full bg-gradient-to-b from-[#fffadc] to-[#d5fdff] px-6 pb-4 pt-2 text-sm leading-relaxed'>
      <div className='mb-4 flex flex-col '>
        <h3 className='mb-2 text-center text-lg font-bold'> 第三阶段</h3>
        <h4 className='text-sm font-semibold text-[#f7943a]'>
          深化专注能力 (第15~22天 | 12~15分钟/天)
        </h4>
        <p className='mb-2 text-xs  text-[#f7943a]'>
          训练侧重点:听觉广度、听觉加工速度、听觉稳定性(抗干扰)、视觉广度、视觉分辨、视觉追踪及手部精细
        </p>
        <div className='w-full space-y-3 rounded-lg bg-white p-4'>
          <div>
            <h5 className='font-medium text-[#48d7e0]'>第15天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(7分钟)=故事仿说+听数字做动作+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=看图数数+连线追踪对应+运笔练习
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第16天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(8分钟)=听记数列+听故事回答问题+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(6分钟)=镜像训练+走迷宫+数字连线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第17天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(8分钟)=听词复述+听词语做动作+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(7分钟)=找不同+3阶舒尔特方格+按点描线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第18天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(8分钟)=听记数列+听故事回答问题+数字组合的数字
              </p>
              <p className='text-sm'>
                视知觉训练(7分钟)=看图数数+连线追踪对应+运笔练习
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第19天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(6分钟)=听词复述+听词语做动作+数字组合的数字
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=看图数数+3阶舒尔特方格+运笔练习
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第20天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(7分钟)=听记数列+听故事回答问题+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=找不同+连线追踪对应+运笔练习
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第21天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(8分钟)=听记数列+听故事回答问题+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=找不同+3阶舒尔特方格+按点描线
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第22天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(7分钟)=听记数列+听故事回答问题+听数字出现的次数
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=找不同+连线追踪对应+运笔练习
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  const courseOutlineFourContent = (
    <div className='w-full bg-gradient-to-b from-[#fffadc] to-[#d5fdff] px-6 pb-4 pt-2 text-sm leading-relaxed'>
      <div className='mb-4 flex flex-col '>
        <h3 className='mb-2 text-center text-lg font-bold'> 第四阶段</h3>
        <h4 className='text-sm font-semibold text-[#f7943a]'>
          综合应用与生活迁移 (第23~30天 | 15~18分钟/天)
        </h4>
        <p className='mb-2 text-xs  text-[#f7943a]'>
          训练侧重点:听觉广度、听觉加工速度、听觉稳定性(抗干扰)、视觉广度、视觉分辨、视觉追踪及手部精细
        </p>
        <div className='w-full space-y-3 rounded-lg bg-white p-4'>
          <div>
            <h5 className='font-medium text-[#48d7e0]'>第23天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(10分钟)=故事仿说+听数字做动作+听数字出现的次数+正话反说
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=找不同+4阶舒尔特方格+运笔练习+见觉译码
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第24天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(8分钟)=听记数列+听数字做动作+听数字出现的次数+听遗漏的数字
              </p>
              <p className='text-sm'>
                视知觉训练(10分钟)=找不同+连线追踪对应+运笔练习+记忆排序
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第25天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(10分钟)=听词复述+听故事回答问题+听数字出现的次数+听觉译码
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=镜像训练+4阶舒尔特方格+按点描线+记忆排序
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第26天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(9分钟)=听记数列+听数字做动作+数字组合的数字+正话反说
              </p>
              <p className='text-sm'>
                视知觉训练(9分钟)=看图数数+连线追踪对应+按点描线+视觉译码
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第27天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(9分钟)=故事仿说+听数字做动作+听数字出现的次数+听遗漏的数字
              </p>
              <p className='text-sm'>
                视知觉训练(9分钟)=找不同+4阶舒尔特方格+按点描线+记记忆排序
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第28天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(8分钟)=听记数列+听数字做动作+听数字出现的次数+听觉译码
              </p>
              <p className='text-sm'>
                视知觉训练(10分钟)=镜像训练+4阶舒尔特方格-+运笔练习+视觉译码
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第29天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(9分钟)=听记数列+听数字做动作+听数字出现的次数+听遗漏的数字
              </p>
              <p className='text-sm'>
                视知觉训练(9分钟)=找不同+运笔练习+按点描线+记忆排序
              </p>
            </div>
          </div>

          <div>
            <h5 className='font-medium text-[#48d7e0]'>第30天</h5>
            <div className='text-gray-600'>
              <p className='text-sm'>
                听知觉训练(10分钟)=故事仿说+听故事回答问题+听数字出现的次数+听觉译码
              </p>
              <p className='text-sm'>
                视知觉训练(8分钟)=镜像训练+4阶舒尔特方格+按点描线视觉译码
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  const handleShowOutlineModal = (val: number) => {
    let pageType = '';
    if (type === '1') {
      pageType = '宣传页';
    } else if (type === '2') {
      pageType = '领取页';
    }
    hinaTrack('focusCourse_viewsyllabus_click', {
      focusCourse_pagetype: pageType,
    });
    setCurrentOutlineType(val);
    setShowOutlineModal(true);
  };

  const getCurrentOutlineContent = () => {
    switch (currentOutlineType) {
      case 4:
        return courseOutlineOneContent;
      case 5:
        return courseOutlineTwoContent;
      case 6:
        return courseOutlineThreeContent;
      case 7:
        return courseOutlineFourContent;
      default:
        return courseOutlineOneContent;
    }
  };
  const receiveCourse = (val: number) => {
    setCurrentCourseType(val);
    // 根据课程类型设置下载链接
    const courseUrls = {
      4: 'https://unicorn-media.ancda.com/production/app/activity/collection/30%E5%A4%A9%E4%B8%93%E6%B3%A8%E5%8A%9B%E8%AF%BE%EF%BC%88%E7%AC%AC%E4%B8%80%E9%98%B6%E6%AE%B51-7%E5%A4%A9%EF%BC%89%E8%82%B2%E5%84%BF%E8%B5%84%E6%96%99%E5%8C%85.zip',
      5: 'https://unicorn-media.ancda.com/production/app/activity/collection/30%E5%A4%A9%E4%B8%93%E6%B3%A8%E5%8A%9B%E8%AE%AD%E7%BB%83%E8%AF%BE%EF%BC%88%E7%AC%AC%E4%BA%8C%E9%98%B6%E6%AE%B5%E7%AC%AC8-14%E5%A4%A9%EF%BC%89.zip',
      6: 'https://unicorn-media.ancda.com/production/app/activity/collection/30%E5%A4%A9%E4%B8%93%E6%B3%A8%E5%8A%9B%E8%AF%BE%EF%BC%88%E7%AC%AC%E4%B8%89%E9%98%B6%E6%AE%B515-22%E5%A4%A9%EF%BC%89.zip',
      7: 'https://unicorn-media.ancda.com/production/app/activity/collection/30%E5%A4%A9%E4%B8%93%E6%B3%A8%E5%8A%9B%E8%AF%BE%EF%BC%88%E7%AC%AC%E5%9B%9B%E9%98%B6%E6%AE%B523-30%E5%A4%A9%EF%BC%89.zip',
    };

    setWordUrl(courseUrls[val as keyof typeof courseUrls] || '');
    setShowModal(true);
  };
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '30天专注力训练课';
    }
    if (type === '1') {
      hinaTrack('promote_focusCourse_view');
    }
    if (type === '2') {
      hinaTrack('redeem_focusCourse_view');
    }
  }, [type]);
  return (
    <div className={`max-w-[750px] ${type === '1' ? 'pb-14' : ''}`}>
      <CustomConfirmationModal
        showModal={showModal}
        wordUrl={wordUrl}
        currentCourseType={currentCourseType}
        onClose={handleCloseModal}
        onDownload={handleDownloadClick}
      />
      {/* 加tabs */}
      {Array.from({ length: 11 }).map((_, index) => {
        return (
          <div key={index.toString()} className='relative'>
            {index === 4 && (
              <div className='absolute bottom-2 left-1/2 flex -translate-x-1/2 items-center gap-2'>
                {type === '2' && (
                  <div
                    className='min-w-[180px] pulse-no-offset cursor-pointer rounded-full bg-[#ff9750]  py-2 text-center text-xs text-white shadow-lg'
                    onClick={() => receiveCourse(4)}
                  >
                    点击领取
                  </div>
                )}
                <div
                  className='min-w-[190px] cursor-pointer rounded-full bg-[#57dcd9] py-2 text-center text-xs text-white  transition-colors hover:bg-[#4bc9c6]'
                  onClick={() => handleShowOutlineModal(4)}
                >
                  查看课程大纲
                </div>
              </div>
            )}
            {index === 5 && (
              <div className='absolute bottom-2 left-1/2 flex -translate-x-1/2 items-center gap-2'>
                {type === '2' ? (
                  <div
                    className='min-w-[180px] pulse-no-offset cursor-pointer rounded-full bg-[#ff9750]  py-2 text-center text-xs text-white shadow-lg'
                    onClick={() => receiveCourse(5)}
                  >
                    点击领取
                  </div>
                ) : null}
                <div
                  className='min-w-[190px] cursor-pointer rounded-full bg-[#57dcd9] py-2 text-center text-xs text-white transition-colors hover:bg-[#4bc9c6]'
                  onClick={() => handleShowOutlineModal(5)}
                >
                  查看课程大纲
                </div>
              </div>
            )}
            {index === 6 && (
              <div className='absolute bottom-2 left-1/2 flex -translate-x-1/2 items-center gap-2'>
                {type === '2' ? (
                  <div
                    className='min-w-[180px] pulse-no-offset cursor-pointer rounded-full bg-[#ff9750]  py-2 text-center text-xs text-white shadow-lg'
                    onClick={() => receiveCourse(6)}
                  >
                    点击领取
                  </div>
                ) : null}
                <div
                  className='min-w-[190px] cursor-pointer rounded-full bg-[#57dcd9] py-2 text-center text-xs text-white transition-colors hover:bg-[#4bc9c6]'
                  onClick={() => handleShowOutlineModal(6)}
                >
                  查看课程大纲
                </div>
              </div>
            )}
            {index === 7 && (
              <div className='absolute bottom-2 left-1/2 flex -translate-x-1/2 items-center gap-2'>
                {type === '2' ? (
                  <div
                    className='min-w-[180px] pulse-no-offset cursor-pointer rounded-full bg-[#ff9750]  py-2 text-center text-xs text-white shadow-lg'
                    onClick={() => receiveCourse(7)}
                  >
                    点击领取
                  </div>
                ) : null}
                <div
                  className='min-w-[190px] cursor-pointer rounded-full bg-[#57dcd9] py-2 text-center text-xs text-white transition-colors hover:bg-[#4bc9c6]'
                  onClick={() => handleShowOutlineModal(7)}
                >
                  查看课程大纲
                </div>
              </div>
            )}
            {index < 10 ? (
              <Image
                src={`https://unicorn-media.ancda.com/production/app/activity/content_${index + 1}.webp`}
                alt='courseCollection'
                width={1000}
                height={1000}
                className='h-auto w-full'
              />
            ) : type === '1' ? (
              <Image
                src={`https://unicorn-media.ancda.com/production/app/activity/content_${index + 1}.webp`}
                alt='courseCollection'
                width={1000}
                height={1000}
                className='h-auto w-full'
              />
            ) : null}
          </div>
        );
      })}

      {type === '1' && (
        <div className='fixed inset-x-0 bottom-0'>
          <button
            type='button'
            className=' flex w-full cursor-pointer items-center 
          justify-center rounded-t-lg border-2 
          border-[#ff6b1a] bg-gradient-to-r from-[#ff9750]
           to-[#ff7a2e] py-4 text-center text-lg font-bold 
           text-white shadow-2xl transition-all duration-300
           '
            onClick={() => {
              hinaTrack('focusCourse_openVIP_click');
              navigationToNativePage(
                'rn://MemberStack?initialRoute=ParentsIndexScreen',
              );
            }}
          >
            <span className='flex pulse-no-offset items-center gap-2 text-lg'>
              去开通掌心会员
            </span>
          </button>
        </div>
      )}
      <Modal
        visible={showOutlineModal}
        content={getCurrentOutlineContent()}
        closeOnAction
        showCloseButton
        onClose={() => setShowOutlineModal(false)}
        className='w-full max-w-none   [&_.adm-center-popup-wrap]:min-w-[90vw]  [&_.adm-modal-content]:!p-0 [&_.adm-modal-footer.adm-space]:!hidden'
        bodyClassName='w-full bg-[#fffadc] max-w-none !pt-0'
      />
    </div>
  );
};

export default CourseCollection;
