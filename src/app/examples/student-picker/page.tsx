'use client';

import React, { useState } from 'react';
import StudentPicker from '@/components/StudentPicker';
import { Toast } from 'antd-mobile';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
}

export default function StudentPickerExample() {
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedClassName, setSelectedClassName] = useState<string>('');
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  const handleStudentSelect = (
    classId: string,
    className: string,
    student: Student | null
  ) => {
    setSelectedClass(classId);
    setSelectedClassName(className);
    setSelectedStudent(student);

    Toast.show({
      content: `已选择：${className} - ${student?.studentName || ''}`,
      position: 'bottom'
    });
  };

  return (
    <div className="p-4 max-w-md mx-auto">
      <h1 className="text-xl font-bold mb-6 text-center">学生选择器示例</h1>

      <div className="mb-8">
        <StudentPicker
          onSelect={handleStudentSelect}
          placeholder="请选择班级和学生"
        />
      </div>

      {selectedStudent && (
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h2 className="text-lg font-medium mb-3">已选择信息</h2>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-500">班级：</span>
              <span className="font-medium">{selectedClassName}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500">学生：</span>
              <span className="font-medium">{selectedStudent.studentName}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500">学生 ID：</span>
              <span className="font-medium">
                {selectedStudent.studentId || '无'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500">性别：</span>
              <span className="font-medium">
                {selectedStudent.gender === 1 ? '男' : '女'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
