'use client';

import React, { useState } from 'react';
import StudentPicker from '@/components/StudentPicker';
import { Toast } from 'antd-mobile';
import { defaultAvatar } from '@/constant/config';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
}

function MultiStudentPickerExample() {
  const [selectedStudents, setSelectedStudents] = useState<Student[]>([]);

  const handleStudentsSelect = (students: Student[]) => {
    setSelectedStudents(students);

    Toast.show({
      content: `已选择 ${students.length} 名学生`,
      position: 'bottom'
    });
  };

  return (
    <div className="p-4 max-w-md mx-auto">
      <h1 className="text-xl font-bold mb-6 text-center">多选学生选择器示例</h1>

      <div className="mb-8">
        <StudentPicker
          onMultiSelect={handleStudentsSelect}
          multiValue={selectedStudents}
          placeholder="请选择班级和学生"
          multiple={true}
        />
      </div>

      {selectedStudents.length > 0 && (
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h2 className="text-lg font-medium mb-3">已选择学生信息</h2>

          <div className="space-y-3">
            {selectedStudents.map((student) => (
              <div
                key={student.studentId}
                className="flex items-center space-x-3 p-2 border-b border-gray-100"
              >
                <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
                  {student.avatar && (
                    <img
                      src={student.avatar}
                      alt={student.studentName}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = defaultAvatar;
                      }}
                    />
                  )}
                </div>
                <div>
                  <div className="font-medium">{student.studentName}</div>
                  <div className="text-xs text-gray-500">
                    性别: {student.gender === 1 ? '男' : '女'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default MultiStudentPickerExample;
