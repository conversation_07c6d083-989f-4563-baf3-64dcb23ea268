'use client';

import FormRender, { useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getSubmitDetail } from '@/api/form';

import address from '../../workflow/create/components/widgets/Address';
import attachment from '../../workflow/create/components/widgets/Attachment';
import checkbox from '../../workflow/create/components/widgets/Checkbox';
import checkboxes from '../../workflow/create/components/widgets/Checkboxes';
import image from '../../workflow/create/components/widgets/Image';
import richText from '../../workflow/create/components/widgets/RichText';
import signature from '../../workflow/create/components/widgets/Signature';
import video from '../../workflow/create/components/widgets/Video';

type State = {
  instanceId: string;
  instanceName: string;
  form: object;
  content: object;
  status: number;
  submitTime: number;
  settings: string;
};

function Page() {
  const searchParams = useSearchParams();
  const instanceId = searchParams?.get('instanceId');
  const submitId = searchParams?.get('submitId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }
  const form = useForm();

  const [data, setData] = useState<State>({
    instanceId: '',
    instanceName: '',
    form: {},
    content: {},
    status: 0,
    submitTime: 0,
    settings: '',
  });

  useEffect(() => {
    if (submitId) {
      getSubmitDetail(submitId).then((res: any) => {
        if (typeof res === 'object') {
          const data = {
            ...res,
            form: JSON.parse(res.form),
            content: JSON.parse(res.content),
          };
          setData(data);
        }
      });
    }
  }, [submitId]);

  const onMount = () => {
    if (data.content) {
      form.setValues(data.content);
    }
    // setLoading(false);
  };

  return (
    <div className="h-screen bg-[#F7F9FF]">
      {/* <div className="flex items-center justify-between bg-white p-4 " /> */}
      <div className="relative p-4">
        <div className="rounded-xl bg-white bg-gradient-to-b from-[#EAF0FF] to-[#FFFFFF] to-40% p-2">
          <div className="px-4 pt-2 text-base font-bold">
            {data.instanceName}
          </div>
          {data.form && Object.keys(data.form).length > 0 && (
            <FormRender
              schema={data.form}
              // displayType="column"
              readOnly
              form={form}
              onFinish={() => {
                console.log('onFinish');
              }}
              onMount={onMount}
              widgets={{
                checkbox,
                checkboxes,
                richText,
                signature,
                image,
                video,
                attachment,
                address,
              }}
              style={{
                '--border-bottom': 'none',
                '--border-inner': 'none',
                '--border-top': 'none',
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default Page;
