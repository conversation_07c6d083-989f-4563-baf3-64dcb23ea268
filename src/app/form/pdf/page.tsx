'use client';

import { format } from 'date-fns';
import FormRender, { useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getSubmitDetail } from '@/api/form';

import address from '../../workflow/create/components/widgets/Address';
import attachment from '../../workflow/create/components/widgets/Attachment';
import checkbox from '../../workflow/create/components/widgets/Checkbox';
import checkboxes from '../../workflow/create/components/widgets/Checkboxes';
import image from '../../workflow/create/components/widgets/Image';
import richText from '../../workflow/create/components/widgets/RichText';
import signature from '../../workflow/create/components/widgets/Signature';
import video from '../../workflow/create/components/widgets/Video';

export const dynamic = 'force-dynamic';

type State = {
  instanceId: string;
  instanceName: string;
  form: object;
  content: object;
  status: number;
  submitTime: number;
  submitUserName: string;
  settings: string;
};

function Page() {
  const searchParams = useSearchParams();
  const submitId = searchParams?.get('submitId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }
  const form = useForm();
  const [data, setData] = useState<State>({
    instanceId: '',
    instanceName: '',
    form: {},
    content: {},
    status: 0,
    submitTime: 0,
    submitUserName: '',
    settings: '',
  });

  useEffect(() => {
    if (submitId) {
      getSubmitDetail(submitId).then((res: any) => {
        if (typeof res === 'object') {
          const formJSON = JSON.parse(res.form);
          // 删除不打印的控件
          Object.keys(formJSON.properties).forEach((key) => {
            if (formJSON.properties[key]?.printable === false) {
              delete formJSON.properties[key];
            }
          });
          const d = {
            ...res,
            form: formJSON,
            content: JSON.parse(res.content),
          };
          setData(d);
        }
      });
    }
  }, [submitId]);

  const onMount = () => {
    if (data.content) {
      form.setValues(data.content);
    }
    // setLoading(false);
  };

  return (
    <div className="h-screen">
      <div className="relative p-4">
        <div className="px-4 pt-2 text-2xl font-bold">{data.instanceName}</div>
        <div className="px-4 pt-2 text-sm text-stone-400">
          {data.submitUserName} 提交于：
          {format(data.submitTime * 1000, 'yyyy-MM-dd HH:mm')}
        </div>
        {/* <div className="px-4 pt-4 text-base font-bold">提交内容：</div> */}
        {data.form && Object.keys(data.form).length > 0 && (
          <FormRender
            schema={data.form}
            // displayType="column"
            readOnly
            form={form}
            onFinish={() => {
              console.log('onFinish');
            }}
            onMount={onMount}
            widgets={{
              checkbox,
              checkboxes,
              richText,
              signature,
              image,
              video,
              attachment,
              address,
            }}
            style={{
              '--border-bottom': 'none',
              '--border-inner': 'none',
              '--border-top': 'none',
            }}
          />
        )}
      </div>
    </div>
  );
}

export default Page;
