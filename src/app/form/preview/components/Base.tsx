'use client';

import { List } from 'antd-mobile';
import Image from 'next/image';
import React from 'react';

function Base({ data }: any) {
  return (
    <div className="opacity-90">
      <List mode="card" header="基础信息">
        <List.Item disabled extra={data.instanceName || ''}>
          名称
        </List.Item>
        <List.Item
          disabled
          extra={
            data.iconUrl ? (
              <Image
                src={data.iconUrl}
                alt=""
                width="0"
                height="0"
                sizes="64px"
                className="h-[64px] w-[64px] rounded object-cover"
              />
            ) : null
          }
        >
          图标
        </List.Item>
        <List.Item disabled extra={data.cateName || ''}>
          分组
        </List.Item>
      </List>
    </div>
  );
}

export default Base;
