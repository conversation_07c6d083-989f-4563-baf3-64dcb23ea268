'use client';

import { List, Switch } from 'antd-mobile';
import { format } from 'date-fns';
import React from 'react';

const cycleType = {
  1: '每天',
  2: '每周',
  3: '每月',
  0: '不限周期',
};

const weekType = {
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
  7: '周日',
};

function Settings({ data }: any) {
  const renderUser = (arr) => {
    if (Array.isArray(arr) && arr.length) {
      return arr.map((item) => item.objName || '未知姓名').join(',');
    }
    return '-';
  };

  function convertMinutes(minutes: number) {
    if (minutes === 0) return '未设置';
    const days = Math.floor(minutes / 1440);
    const hours = Math.floor((minutes % 1440) / 60);
    const mins = minutes % 60;

    return `截止前${days ? `${days}天` : ''} ${hours ? `${hours}小时` : ''}${
      mins ? `${mins}分` : ''
    }`;
  }
  return (
    <div>
      <List mode="card">
        <List.Item disabled extra={renderUser(data.scope)}>
          谁可填写
        </List.Item>
        <List.Item disabled extra={cycleType[data.cycleType] || ''}>
          填写周期
        </List.Item>
        {data.cycleType === 1 && (
          <List.Item
            disabled
            extra={JSON.parse(data.cycleTime)
              .map((item) => weekType[String(item)])
              .join(',')}
          >
            提交日期
          </List.Item>
        )}
        {data.cycleType === 0 && (
          <List.Item
            disabled
            extra={
              data.endTime
                ? format(data.endTime * 1000, 'yyyy-MM-dd HH:mm')
                : '无'
            }
          >
            截止时间
          </List.Item>
        )}
        <List.Item
          disabled
          extra={<Switch disabled defaultChecked={data.isRepeat === 1} />}
        >
          是否允许重复提交
        </List.Item>
        <List.Item
          extra={<Switch disabled defaultChecked={data.isHoliday === 1} />}
        >
          是否自动跳过节假日
        </List.Item>
        <List.Item disabled extra={convertMinutes(data.remindTime)}>
          提醒时间
        </List.Item>
      </List>
    </div>
  );
}

export default Settings;
