'use client';

import { <PERSON><PERSON>, <PERSON>Loading, Toast } from 'antd-mobile';
import FormRender, { useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getFormDetail, getSubmitDetail, submit, update } from '@/api/form';
import { postMessage } from '@/utils';

// import { schema as testSchema } from '../create/components/data';
import address from '../../workflow/create/components/widgets/Address';
import attachment from '../../workflow/create/components/widgets/Attachment';
import checkbox from '../../workflow/create/components/widgets/Checkbox';
import checkboxes from '../../workflow/create/components/widgets/Checkboxes';
import image from '../../workflow/create/components/widgets/Image';
import richText from '../../workflow/create/components/widgets/RichText';
import signature from '../../workflow/create/components/widgets/Signature';
import video from '../../workflow/create/components/widgets/Video';

export const dynamic = 'force-dynamic';

const findLabels = (value: any[], options: any[]) => {
  if (!isValidateArray(value) || !isValidateArray(options)) return [];

  return value.map((v) => options.find((o) => o.value === v)?.label);
};

export function getFormat(format) {
  switch (format) {
    case 'date':
      return 'YYYY-MM-dd';
    case 'year':
      return 'YYYY';
    case 'month':
      return 'YYYY-MM';
    case 'week':
      return 'YYYY-w';
    case 'hour':
      return 'YYYY-MM-dd hh';
    case 'minute':
      return 'YYYY-MM-dd hh:mm';
    case 'second':
      return 'YYYY-MM-dd hh:mm:ss';
    case 'week-day':
      return 'w-d';
    default:
      return 'YYYY-MM-dd';
  }
}

const flatCascaderOptions = (options: any[]) => {
  const result = [];

  const walk = (list: any[]) => {
    list.forEach((i) => {
      result.push(i);
      if (isValidateArray(i.children)) {
        walk(i.children);
      }
    });
  };

  walk(options);
  return result;
};

const isValidateArray = (list: unknown) =>
  Array.isArray(list) && list.length > 0;

const getLabel = (value: any, schema: any) => {
  const { props } = schema;
  console.log('🚀 ~ props:', props);

  let __html: string;

  switch (schema.widget) {
    case 'input':
    case 'textArea':
    case 'inputNumber':
    case 'amountNumber':
    case 'phoneNumber':
    case 'richText':
    case 'rate':
    case 'stepper':
    case 'datePicker':
      __html = value;
      break;
    case 'image':
      __html = value?.length ? `${value.length}张图片` : '-';
      break;
    case 'video':
      __html = value?.length ? `${value.length}个视频` : '-';
      break;
    case 'attachment':
      __html = value?.length ? `${value.length}个附件` : '-';
      break;
    case 'signature':
      __html = value ? '已签名' : '-';
      break;
    case 'slider':
      if (isValidateArray(value)) {
        __html = value.join(' - ');
      } else {
        __html = value;
      }
      break;
    case 'checkboxes':
    case 'selector':
      {
        const { options } = props;
        if (isValidateArray(value)) {
          __html = findLabels(value, options).join('，');
        }
      }
      break;
    case 'switch':
      const { uncheckedText = '否', checkedText = '是' } = props;
      __html = value ? checkedText : uncheckedText;
      break;
    case 'address':
      if (isValidateArray(value)) {
        __html = value.map((item) => item.label).join(' - ');
      }
      break;
    case 'radio':
    case 'checkbox':
      {
        const { options } = props;
        __html = options.find((o) => o.value === value)?.label;
      }
      break;
    case 'picker': {
      const { options } = props;
      if (options && options.length) {
        __html = findLabels(value, options).join('-') || '-';
      }
      break;
    }
    case 'group': {
      // 地址控件
      const { area, detail } = props;
      if (area && detail) {
        const areaArr = value.area.map((item) => item.label).join('-');
        __html = `${areaArr} ${value.detail || '-'}`;
      }
      break;
    }
    default:
      __html = '-';
  }
  return __html;
};

export default function Index() {
  const searchParams = useSearchParams();
  const form = useForm();
  const instanceId = searchParams?.get('instanceId');
  const submitId = searchParams?.get('submitId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const [schema, setSchema] = useState<any>(null);
  const [formData, setFormData] = useState<any>(null);
  const [dataLoading, setDataLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    if (submitId) {
      setDataLoading(true);
      getSubmitDetail(submitId)
        .then((res: any) => {
          setDataLoading(false);
          if (typeof res === 'object') {
            setSchema(JSON.parse(res.form));
            setFormData(JSON.parse(res.content));
          }
        })
        .catch(() => {
          setDataLoading(false);
        });
    } else if (instanceId) {
      setDataLoading(true);
      getFormDetail(instanceId)
        .then((res: any) => {
          setDataLoading(false);
          if (res.form) {
            setSchema(JSON.parse(res.form));
          }
        })
        .catch(() => {
          setDataLoading(false);
        });
    }
  }, []);

  const onFinish = (formData: any) => {
    console.log('🚀 ~ formData:', formData);
    console.log('🚀 ~ schema:', schema);

    const formDataCopy = { ...formData };
    const { properties } = schema;
    for (const key in formDataCopy) {
      if (Object.prototype.hasOwnProperty.call(formDataCopy, key)) {
        formDataCopy[key] = getLabel(formDataCopy[key], properties[key]);
      }
    }
    const data = {
      instanceId,
      form: JSON.stringify(schema),
      content: JSON.stringify(formData),
      result: formDataCopy,
    };
    setSubmitLoading(true);
    if (submitId) {
      update(submitId, data)
        .then((res: any) => {
          setSubmitLoading(false);
          Toast.show('更新成功');
          postMessage({ goBack: 2, instanceId: res.instanceId });
        })
        .catch(() => {
          setSubmitLoading(false);
        });
    } else {
      submit(data)
        .then((res: any) => {
          setSubmitLoading(false);
          Toast.show('提交成功');
          postMessage({ goBack: 2, instanceId: res.instanceId });
        })
        .catch(() => {
          setSubmitLoading(false);
        });
    }
  };

  const onMount = () => {
    // setLoading(false);
    setIsMounted(true);
    if (submitId && formData) {
      form.setValues(formData);
    }
  };

  return (
    <div className="relative flex  h-screen flex-col bg-[#F7F9FF] p-4">
      {dataLoading && (
        <div className="flex h-screen w-full items-center justify-center">
          <SpinLoading />
        </div>
      )}
      <div className="flex-1 overflow-y-scroll rounded-xl bg-white">
        {!!schema && (
          <FormRender
            schema={schema}
            // requiredMarkStyle="text-required"
            // displayType="column"
            mode="card"
            form={form}
            onFinish={onFinish}
            onMount={onMount}
            widgets={{
              checkbox,
              checkboxes,
              richText,
              signature,
              image,
              video,
              attachment,
              address,
            }}
            // className="-m-2"
          />
        )}
      </div>
      {isMounted && (
        <div className="w-full p-4 pb-0">
          <Button
            block
            type="submit"
            color="primary"
            size="large"
            loading={submitLoading}
            onClick={() => {
              form.submit();
            }}
          >
            {submitId ? '更新' : '提交'}
          </Button>
        </div>
      )}
    </div>
  );
}
