'use client';

import { But<PERSON>, Toast } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import React from 'react';

import { createForm, updateForm } from '@/api/form';
import { useFormStore } from '@/store/useFormStore';
import { postMessage } from '@/utils';
import Emitter from '@/utils/emitter';

type Props = { instanceId?: string; templateId?: string };

function convertArrayToMinutes(timeArray: [number, number, number, number]) {
  const minutesInDay = 24 * 60;
  const minutesInHour = 60;

  const totalMinutes =
    timeArray[1] * minutesInDay + timeArray[2] * minutesInHour + timeArray[3];
  return totalMinutes;
}

export default function Index({ instanceId, templateId }: Props) {
  const forms = useFormStore((state) => state.forms);
  const baseForm = useFormStore((state) => state.baseForm);
  const settingForm = useFormStore((state) => state.settingForm);
  const router = useRouter();

  const submit = () => {
    console.log('forms', forms);

    // 验证基础信息表单
    Emitter.emit('validateFields', () => {});

    if (baseForm.instanceName === '') {
      Toast.show({
        content: '请填写表单名称',
      });
      return;
    }
    if (baseForm.iconUrl === '') {
      Toast.show({
        content: '请选择一个图标',
      });
      return;
    }
    if (baseForm.cateId === '') {
      Toast.show({
        content: '请选择分组',
      });
      return;
    }

    if (!(Array.isArray(settingForm.scope) && settingForm.scope.length !== 0)) {
      Toast.show({
        content: '请选择填写对象',
      });
      return;
    }

    if (settingForm.cycleType === 1 && settingForm.cycleTime.length === 0) {
      Toast.show({
        content: '按天提交必须选择设置提交日期',
      });
      return;
    }

    const newForms = JSON.parse(JSON.stringify(forms)).map(
      (item: any, index: number) => ({ ...item, order: index }),
    );

    if (newForms.length === 0) {
      Toast.show({
        content: '表单信息没有添加任何控件',
      });
      return;
    }

    const obj = newForms.reduce((acc: any, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});

    const formSchema = {
      type: 'object',
      displayType: 'column',
      properties: obj,
    };

    const settingsData = {
      ...settingForm,
      scope: settingForm.scope.map((item) => ({
        objType: item.type === 'dept' ? 2 : 1,
        objId: item.id,
        objName: item.name,
      })),
      remindTime: convertArrayToMinutes(settingForm.remindTime),
      endTime: settingForm.cycleType === 0 ? settingForm.endTime : 0,
      cycleTime:
        settingForm.cycleType === 1
          ? JSON.stringify(settingForm.cycleTime.map((item) => Number(item)))
          : '[]',
    };

    // 后端需要传标题
    const titles = () => {
      const keys: string[] = [];
      const values: string[] = [];
      newForms.forEach((item) => {
        keys.push(item.id);
        values.push(item.title);
      });
      return {
        keys,
        values,
      };
    };

    const postData = {
      settings: '',
      status: 1,
      titles: titles(),
      templateId: Number(templateId),
      form: JSON.stringify(formSchema),
      ...baseForm,
      ...settingsData,
    };
    console.log('🚀 ~ file: index.tsx:17 ~ postData:', postData);
    if (instanceId) {
      updateForm({ ...postData, instanceId }).then(() => {
        Toast.show({
          icon: 'success',
          content: '更新成功',
        });
        postMessage({ goBack: 2 });
      });
    } else {
      createForm(postData).then(() => {
        Toast.show({
          icon: 'success',
          content: '保存成功',
        });
        postMessage({
          goBack: 2,
          from: templateId !== '0' ? 'template' : 'new',
        });
      });
    }
  };
  return (
    <div className="flex items-center justify-evenly bg-white px-4 py-3">
      <Button color="primary" fill="solid" className="w-full" onClick={submit}>
        发布
      </Button>
    </div>
  );
}
