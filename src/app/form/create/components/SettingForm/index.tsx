'use client';

import {
  ActionSheet,
  Checkbox,
  DatePicker,
  Dialog,
  Form,
  Picker,
  Popup,
  Space,
  Switch,
} from 'antd-mobile';
import type { FormInstance } from 'antd-mobile/es/components/form';
import { addDays, format, getUnixTime, startOfDay } from 'date-fns';
import React, { useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';

import type { TeacherPickerRef } from '@/components/TeacherPicker';
import TeacherPicker from '@/components/TeacherPicker';
import { useFormStore } from '@/store/useFormStore';

const cycleType = {
  1: '每天',
  2: '每周',
  3: '每月',
  0: '不限周期',
};

const weekType = {
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
  7: '周日',
};

const monthDays = Array.from({ length: 30 }, (_, index) => ({
  value: index,
  label: `${index}天`,
}));

const weekDays = Array.from({ length: 7 }, (_, index) => ({
  value: index,
  label: `${index}天`,
}));

const hours = Array.from({ length: 24 }, (_, index) => ({
  value: index,
  label: `${index}时`,
}));

const minutes = [
  {
    value: 0,
    label: '0分',
  },
  {
    value: 15,
    label: '15分',
  },
  {
    value: 30,
    label: '30分',
  },
  {
    value: 45,
    label: '45分',
  },
];

export default function SettingForm({ instanceId }: { instanceId: string }) {
  const settingForm = useFormStore((state) => state.settingForm);
  const setSettingForm = useFormStore((state) => state.setSettingForm);
  const [form] = Form.useForm();
  const formRef = React.useRef<FormInstance>(null);
  const teacherPicker = useRef<TeacherPickerRef>(null);

  const [cycleActionSheetVisible, setCycleActionSheetVisible] = useState(false);
  const [cycleDaysPopupVisible, setCycleDaysPopupVisible] = useState(false);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [cycleDays, setCycleDays] = useState<Array<string>>(
    settingForm.cycleTime,
  );
  const [pickerVisible, setPickerVisible] = useState(false);
  const [remindTime, setRemindTime] = useState(settingForm.remindTime);
  const [basicColumns, setBasicColumns] = useImmer([
    [
      {
        label: '截止前',
        value: 0,
      },
    ],
    monthDays,
    hours,
    minutes,
  ]);

  useEffect(() => {
    if (settingForm.cycleType === 1) {
      setBasicColumns((draft) => {
        draft[1] = [
          {
            label: '0天',
            value: 0,
          },
        ];
      });
    } else if (settingForm.cycleType === 2) {
      setBasicColumns((draft) => {
        draft[1] = weekDays;
      });
    } else if (settingForm.cycleType === 3) {
      setBasicColumns((draft) => {
        draft[1] = monthDays;
      });
    }
  }, [settingForm.cycleType]);

  const renderUser = () => {
    if (Array.isArray(settingForm.scope) && settingForm.scope.length) {
      return settingForm.scope.map((item) => item.name).join(',');
    }
    return '请选择';
  };

  const renderTime = (time: any[]) => {
    if (time.every((item) => item === 0)) {
      return '未设置';
    }
    return `截止前${time[1] ? `${time[1]}天` : ''}${
      time[2] ? `${time[2]}时` : ''
    }${time[3]}分`;
  };

  const onFinish = (values: any) => {
    Dialog.alert({
      content: <pre>{JSON.stringify(values, null, 2)}</pre>,
    });
  };

  return (
    <div>
      <Form
        ref={formRef}
        form={form}
        mode="card"
        layout="horizontal"
        onFinish={onFinish}
        style={{
          '--prefix-width': '240px',
        }}
      >
        <Form.Item
          label="谁可填写"
          shouldUpdate={(prevValues, curValues) =>
            prevValues.icon !== curValues.icon
          }
          extra={<div>{renderUser()}</div>}
          clickable
          arrow
          disabled={instanceId}
          onClick={() => {
            teacherPicker.current?.toggle();
            teacherPicker.current?.initData([], true);
          }}
          style={{
            '--prefix-width': '100px',
          }}
        />
        <Form.Item label="开启填写要求" childElementPosition="right">
          <Switch
            defaultChecked={settingForm.isCustom}
            onChange={(value) => {
              setSettingForm({ ...settingForm, isCustom: value });
            }}
          />
        </Form.Item>
        {settingForm.isCustom && (
          <>
            <Form.Item
              label="填写周期"
              rules={[{ required: true, message: '请选择' }]}
              extra={<div>{cycleType[settingForm.cycleType]}</div>}
              arrow
              onClick={() => {
                setCycleActionSheetVisible(true);
              }}
            />
            {settingForm.cycleType === 1 && (
              <Form.Item
                label="提交日期"
                rules={[{ required: true, message: '请选择' }]}
                extra={
                  <div>
                    {settingForm.cycleTime
                      .map((item) => weekType[item])
                      .join(',')}
                  </div>
                }
                arrow
                onClick={() => {
                  setCycleDaysPopupVisible(true);
                  setCycleDays(settingForm.cycleTime);
                }}
                style={{
                  '--prefix-width': '100px',
                }}
              />
            )}
            {settingForm.cycleType === 0 && (
              <Form.Item
                name="time"
                label="截止时间"
                extra={
                  <div className="text-right text-stone-400">
                    {settingForm.endTime
                      ? format(settingForm.endTime * 1000, 'yyyy-MM-dd HH:mm')
                      : '未设置'}
                  </div>
                }
                style={{
                  '--prefix-width': '100px',
                }}
                onClick={() => {
                  setDatePickerVisible(true);
                }}
              />
            )}

            <Form.Item label="每个周期可提交多次" childElementPosition="right">
              <Switch
                defaultChecked={!!settingForm.isRepeat}
                onChange={(value) => {
                  setSettingForm({ ...settingForm, isRepeat: value ? 1 : 0 });
                }}
              />
            </Form.Item>
            <Form.Item label="是否自动跳过节假日" childElementPosition="right">
              <Switch
                defaultChecked={!!settingForm.isHoliday}
                onChange={(value) => {
                  setSettingForm({ ...settingForm, isHoliday: value ? 1 : 0 });
                }}
              />
            </Form.Item>
            {((settingForm.cycleType === 0 && settingForm.endTime > 0) ||
              settingForm.cycleType !== 0) && (
              <Form.Item
                label="提醒时间"
                rules={[{ required: true, message: '请选择' }]}
                extra={<div>{renderTime(remindTime)}</div>}
                style={{
                  '--prefix-width': '100px',
                }}
                onClick={() => {
                  setPickerVisible(true);
                }}
              />
            )}
          </>
        )}
      </Form>
      <TeacherPicker
        title="添加成员"
        ref={teacherPicker}
        onConfirm={(value) => {
          setSettingForm({ ...settingForm, scope: value });
        }}
      />
      {/* 填写周期设置 */}
      <ActionSheet
        cancelText="取消"
        visible={cycleActionSheetVisible}
        actions={Object.keys(cycleType).map((key) => {
          return {
            key,
            text: cycleType[key],
          };
        })}
        onAction={(action) => {
          console.log('🚀 ~ action:', action);
          if (action.key) {
            setSettingForm({ ...settingForm, cycleType: Number(action.key) });
          }
          setRemindTime([0, 0, 0, 0]);
          setCycleActionSheetVisible(false);
        }}
        onClose={() => setCycleActionSheetVisible(false)}
      />
      <Popup
        visible={cycleDaysPopupVisible}
        onMaskClick={() => {
          setCycleDaysPopupVisible(false);
        }}
        onClose={() => {
          setCycleDaysPopupVisible(false);
        }}
        bodyStyle={{ height: '50vh' }}
      >
        <div className="p-4">
          <div className="mb-4 flex items-center justify-between text-base">
            <div
              className="text-stone-400"
              onClick={() => setCycleDaysPopupVisible(false)}
            >
              取消
            </div>
            <div className="text-lg text-stone-900">请选择</div>
            <div
              className="text-[#3B82F7]"
              onClick={() => {
                setSettingForm({
                  ...settingForm,
                  cycleTime: cycleDays as string[],
                });
                setCycleDaysPopupVisible(false);
              }}
            >
              确定
            </div>
          </div>
          <Checkbox.Group
            value={cycleDays}
            onChange={(val) => {
              console.log('🚀 ~ val:', val);
              setCycleDays(val as string[]);
            }}
          >
            <Space direction="vertical">
              {Object.keys(weekType).map((key) => {
                return <Checkbox value={key}>{weekType[key]}</Checkbox>;
              })}
            </Space>
          </Checkbox.Group>
        </div>
      </Popup>
      {/* 截止时间 */}
      <DatePicker
        title="时间选择"
        precision="minute"
        visible={datePickerVisible}
        onClose={() => {
          setDatePickerVisible(false);
        }}
        min={startOfDay(addDays(new Date(), 1))} // 今天往后一天0点
        onConfirm={(val) => {
          setSettingForm({
            ...settingForm,
            endTime: getUnixTime(new Date(val)),
          });
        }}
      />
      {/* 提醒时间 */}
      <Picker
        columns={basicColumns}
        visible={pickerVisible}
        onClose={() => {
          setPickerVisible(false);
        }}
        onConfirm={(v) => {
          console.log('value', v);
          setSettingForm({
            ...settingForm,
            remindTime: v,
          });
          setRemindTime(v);
        }}
      />
    </div>
  );
}
