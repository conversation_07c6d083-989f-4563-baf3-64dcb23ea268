'use client';

import { ActionSheet, Form, Input, Toast } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import type { FormInstance } from 'antd-mobile/es/components/form';
import Image from 'next/image';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { getGroup } from '@/api/form';
import { useFormStore } from '@/store/useFormStore';
import { debounce } from '@/utils';
import Emitter from '@/utils/emitter';

import type { Ref } from '../../../../workflow/create/components/IconPicker';
import IconPicker from '../../../../workflow/create/components/IconPicker';

const icons = Array.from({ length: 15 }).map(
  (_, index) =>
    `https://unicorn-media.ancda.com/production/app/form/icon/${index + 1}.png`,
);

export default function BaseForm() {
  const baseForm = useFormStore((state) => state.baseForm);
  const setBaseForm = useFormStore((state) => state.setBaseForm);

  const [form] = Form.useForm();
  const formRef = useRef<FormInstance>(null);
  const iconPickerRef = useRef<Ref>(null);

  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [actions, setActions] = useState<Action[]>([]);

  const onChange = debounce((value: any) => {
    setBaseForm({ ...baseForm, instanceName: value });
  }, 500);

  useEffect(() => {
    getGroup().then((res: any) => {
      if (Array.isArray(res.list)) {
        setActions(
          res.list.map((item: any) => ({
            key: item.cateId,
            text: item.name,
          })),
        );
        // const action = res.list.find((item) => item.key === groupId);
        // return action?.text || '';
      }
    });
  }, []);

  useEffect(() => {
    form.setFieldsValue(baseForm);
  }, [baseForm]);

  useEffect(() => {
    Emitter.on('validateFields', validateFields);
    return () => {
      Emitter.off('validateFields', validateFields);
    };
  }, []);

  const validateFields = () => {
    return new Promise((resolve, reject) => {
      form
        .validateFields()
        .then((values) => {
          resolve(values);
        })
        .catch((err) => {
          if (Array.isArray(err.errorFields)) {
            Toast.show({
              content: err.errorFields[0].errors[0],
            });
          }
          reject(err);
        });
    });
  };

  const getGroupName = useCallback(
    (cateId: string) => {
      if (actions.length) {
        const action = actions.find((item) => item.key === cateId);
        return action?.text || '';
      }
    },
    [actions],
  );

  return (
    <>
      <Form
        ref={formRef}
        form={form}
        mode="card"
        layout="horizontal"
        // requiredMarkStyle="text-required"
      >
        <Form.Header>基础信息</Form.Header>
        <Form.Item
          name="instanceName"
          label="名称"
          rules={[{ required: true }]}
          className="border-none"
        >
          <Input
            placeholder="请输入名称"
            maxLength={50}
            minLength={2}
            // defaultValue={baseForm.name}
            // value={baseForm.name}
            style={{ '--text-align': 'right' }}
            onChange={onChange}
          />
        </Form.Item>
        <Form.Item
          name="iconUrl"
          label="图标"
          rules={[{ required: true, message: '请选择一个图标' }]}
          extra={
            <div className="text-[#CCC]">
              {baseForm.iconUrl ? (
                <Image
                  src={baseForm.iconUrl}
                  alt=""
                  width="0"
                  height="0"
                  sizes="64px"
                  className="h-[64px] w-[64px] rounded object-cover"
                />
              ) : (
                '请选择图标'
              )}
            </div>
          }
          clickable
          arrow
          onClick={() => {
            iconPickerRef.current?.toggle();
          }}
        />
        <Form.Item
          name="cateId"
          label="分组"
          rules={[{ required: true, message: '请选择分组' }]}
          extra={
            baseForm.cateId ? (
              <div className="text-[#333]">{getGroupName(baseForm.cateId)}</div>
            ) : (
              <div className="text-[#CCC]">请选择分组</div>
            )
          }
          arrow
          onClick={() => {
            setActionSheetVisible(true);
          }}
        />
      </Form>
      <IconPicker
        ref={iconPickerRef}
        icons={icons}
        onSelect={(value) => {
          setBaseForm({ ...baseForm, iconUrl: value });
        }}
      />
      <ActionSheet
        extra="请选择"
        cancelText="取消"
        visible={actionSheetVisible}
        actions={actions}
        onAction={(action) => {
          if (action.key) {
            setBaseForm({ ...baseForm, cateId: String(action.key) });
          }
          setActionSheetVisible(false);
        }}
        onClose={() => setActionSheetVisible(false)}
      />
    </>
  );
}
