import Image from 'next/image';
import React from 'react';

type Item = {
  type: string;
  name: string;
  url: string;
  size: string;
};

type Props = {
  files: Array<Item>;
};

function Attachment({ files }: Props) {
  if (!Array.isArray(files) || files.length === 0) {
    return null;
  }
  const download = (url: string) => {
    window.open(url);
  };

  const images = files.filter((item) => item.type === 'image');
  const attachments = files.filter((item) => item.type !== 'image');

  return (
    <div>
      {Array.isArray(attachments) && (
        <>
          <div className="flex w-full flex-wrap">
            {images.map((image: any, index: number) => (
              <Image
                key={`image${index}`}
                src={image.url || ''}
                alt=""
                width="0"
                height="0"
                sizes="120px"
                className="mb-2 mr-2 size-[136px] rounded object-cover "
              />
            ))}
          </div>
          {attachments.length > 0 && (
            <ul
              role="list"
              className="mt-2 divide-y divide-gray-100 rounded-md border border-gray-200"
            >
              {attachments.map((attachment: any, index: any) => (
                <li
                  key={`attachment${index}`}
                  className="flex items-center justify-between px-3 py-4 text-sm leading-6"
                  onClick={() => download(attachment.url)}
                >
                  <span className="truncate font-medium">
                    {attachment.name}
                  </span>
                  <a
                    href={attachment.url}
                    target="_blank"
                    className="text-[#3B82F7]"
                  >
                    下载
                  </a>
                </li>
              ))}
            </ul>
          )}
        </>
      )}
    </div>
  );
}

export default Attachment;
