import Image from 'next/image';
import React, { memo, useEffect, useState } from 'react';

import { batchGetUserInfo } from '@/api/common';
import { defaultAvatar } from '@/constant/config';
import { useWorkflowStore } from '@/store/useWorkflowStore';

type Props = {
  userId: string;
  withBackground?: boolean;
};

function User({ userId, withBackground }: Props) {
  if (!userId) {
    return null;
  }
  const [userInfo, setUserInfo] = useState<any>({
    name: '',
    avatar: '',
  });
  const users = useWorkflowStore((state) => state.users);
  const getUser = useWorkflowStore((state) => state.getUser);
  const addUser = useWorkflowStore((state) => state.addUser);

  useEffect(() => {
    console.log('🚀 ~ file: User.tsx:22 ~ users:', users);
    const user = users[userId];
    console.log('🚀 ~ file: User.tsx:27 ~ user:', user);
    if (!user) {
      // 如果用户信息不存在，从接口获取并保存到 Zustand 中
      batchGetUserInfo({ staffIds: [userId] }).then((res: any) => {
        if (Array.isArray(res.list) && res.list.length) {
          addUser(res.list[0]);
          setUserInfo(res.list[0]);
        }
      });
    } else {
      setUserInfo(user);
    }
  }, []);

  if (withBackground) {
    return (
      <div className="relative flex h-[60px] items-center rounded-[30px] bg-slate-100 pl-[8px] pr-3">
        <Image
          src={userInfo.avatar || defaultAvatar}
          alt=""
          width="0"
          height="0"
          sizes="24px"
          className="size-[48px] rounded-[24px] object-cover"
        />
        <div>
          <span className="ml-2 text-sm">{userInfo.name}</span>
        </div>
        {userInfo.isLeave === 1 && (
          <span className="absolute right-0 top-0 -translate-y-1/2 translate-x-1/2 rounded-full bg-rose-500 p-1 text-[12px] font-medium leading-none text-white">
            离职
          </span>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center">
      <Image
        src={userInfo.avatar || defaultAvatar}
        alt=""
        width="0"
        height="0"
        sizes="100px"
        className="mr-2 size-10 object-cover"
      />
      <span className="text-base">{userInfo.name}</span>
    </div>
  );
}

export default memo(User);
