import { imageThumbnail, videoThumbnail } from '@/utils';
import { compress, generateKey, uploadObs } from '@/utils/obs';

// 供 antd-mobile ImageUploader 使用
export async function upload(file: File) {
  console.log('🚀 ~ file: index.tsx:17 ~ file:', file);
  try {
    const key = generateKey(file.name, 'workflow');
    // 图片压缩
    if (file.type.indexOf('image') > -1) {
      const tempFile: File = await compress(file);
      const url = await uploadObs(tempFile, key);
      if (!url) {
        throw new Error('Fail to upload');
      }
      return {
        thumbnailUrl: imageThumbnail(url, 300, 300),
        url,
        type: 'image',
      };
    }
    if (file.type.indexOf('video') > -1) {
      const url = await uploadObs(file, key);
      if (!url) {
        throw new Error('Fail to upload');
      }
      return {
        thumbnailUrl: videoThumbnail(url),
        url,
        type: 'video',
      };
    }
  } catch (err: any) {
    throw new Error(err.message);
  }
}
