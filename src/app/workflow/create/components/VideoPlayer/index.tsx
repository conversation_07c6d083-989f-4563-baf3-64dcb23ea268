import 'plyr-react/plyr.css';

import type { APITypes } from 'plyr-react';
import { useRef } from 'react';
import ReactPlayer from 'react-player';

const PlyrComponent = ({ videoId = '' }) => {
  const ref = useRef<APITypes>(null);

  return (
    <ReactPlayer
      url={videoId}
      controls
      width="auto"
      style={{
        maxWidth: '100%',
        maxHeight: '60vh',
      }}
    />
  );
};

export default PlyrComponent;
