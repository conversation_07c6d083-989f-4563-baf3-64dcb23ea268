import React from 'react';

import {
  BiFontFamily,
  PiCheckSquare,
  PiDeviceMobileSpeaker,
  PiImage,
  PiMapPin,
  PiNote,
  PiRadioButton,
  PiSignature,
  PiTextT,
  PiToggleLeft,
  PiVideo,
  RiAttachment2,
  RiMoneyCnyCircleLine,
  RxCalendar,
  TbCalendarTime,
} from '@/components/Icons';

const Icon = (props) => {
  const { iconName, text, type, onSelect } = props;
  const icon = React.createElement(iconName, { size: 24, color: '#333' });
  return (
    <div
      className="flex h-[140px] w-[150px] flex-col items-center justify-center rounded-md bg-stone-100 p-2"
      onClick={() => {
        console.log('item', type);
        onSelect(type);
      }}
    >
      {icon}
      <span className="mt-1 text-xs">{text}</span>
    </div>
  );
};

const ItemList = [
  {
    name: '单行文本',
    iconName: PiTextT,
    type: 'input',
  },
  {
    name: '多行文本',
    iconName: BiFontFamily,
    type: 'textArea',
  },
  {
    name: '说明文字',
    iconName: PiNote,
    type: 'richText',
  },
  {
    name: '数字',
    iconName: BiFontFamily,
    type: 'inputNumber',
  },
  {
    name: '金额',
    iconName: RiMoneyCnyCircleLine,
    type: 'amountNumber',
  },
  {
    name: '日期',
    iconName: RxCalendar,
    type: 'date',
  },
  {
    name: '日期-时间',
    iconName: TbCalendarTime,
    type: 'dateTime',
  },
  {
    name: '单选',
    iconName: PiRadioButton,
    type: 'picker',
  },
  {
    name: '多选',
    iconName: PiCheckSquare,
    type: 'checkboxes',
  },
  {
    name: '开关',
    iconName: PiToggleLeft,
    type: 'switch',
  },
  {
    name: '地址',
    iconName: PiMapPin,
    type: 'address',
  },
  {
    name: '图片',
    iconName: PiImage,
    type: 'image',
  },
  {
    name: '视频',
    iconName: PiVideo,
    type: 'video',
  },
  {
    name: '附件',
    iconName: RiAttachment2,
    type: 'attachment',
  },
  {
    name: '签名',
    iconName: PiSignature,
    type: 'signature',
  },
  {
    name: '手机号',
    iconName: PiDeviceMobileSpeaker,
    type: 'phoneNumber',
  },
];

function Index({ onSelect }) {
  return (
    <div className="grid grid-cols-4 gap-4 p-4">
      {ItemList.map((item, index) => (
        <Icon
          key={index}
          iconName={item.iconName}
          text={item.name}
          type={item.type}
          onSelect={onSelect}
        />
      ))}
    </div>
  );
}

export default Index;
