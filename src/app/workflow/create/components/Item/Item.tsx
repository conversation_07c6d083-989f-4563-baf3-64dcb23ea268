import type { DraggableSyntheticListeners } from '@dnd-kit/core';
import type { Transform } from '@dnd-kit/utilities';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';

import { Handle, Remove } from './components';
import styles from './Item.module.scss';

export interface Props {
  dragOverlay?: boolean;
  color?: string;
  disabled?: boolean;
  dragging?: boolean;
  handle?: boolean;
  handleProps?: any;
  height?: number;
  index?: number;
  fadeIn?: boolean;
  transform?: Transform | null;
  listeners?: DraggableSyntheticListeners;
  sorting?: boolean;
  style?: React.CSSProperties;
  transition?: string | null;
  wrapperStyle?: React.CSSProperties;
  value: any;
  onRemove?(): void;
  setWidgetId: (id: string) => void;
  renderItem?(args: {
    dragOverlay: boolean;
    dragging: boolean;
    sorting: boolean;
    index: number | undefined;
    fadeIn: boolean;
    listeners: DraggableSyntheticListeners;
    ref: React.Ref<HTMLElement>;
    style: React.CSSProperties | undefined;
    transform: Props['transform'];
    transition: Props['transition'];
    value: Props['value'];
  }): React.ReactElement<any>;
}

export const Item = React.memo(
  React.forwardRef<HTMLLIElement, Props>(
    (
      {
        color,
        dragOverlay,
        dragging,
        disabled,
        fadeIn,
        handle,
        handleProps,
        height,
        index,
        listeners,
        onRemove,
        renderItem,
        sorting,
        style,
        transition,
        transform,
        value,
        wrapperStyle,
        setWidgetId,
        ...props
      },
      ref,
    ) => {
      const router = useRouter();

      useEffect(() => {
        if (!dragOverlay) {
          return;
        }
        if (typeof document !== 'undefined') {
          document.body.style.cursor = 'grabbing';

          return () => {
            document.body.style.cursor = '';
          };
        }
      }, [dragOverlay]);

      return renderItem ? (
        renderItem({
          dragOverlay: Boolean(dragOverlay),
          dragging: Boolean(dragging),
          sorting: Boolean(sorting),
          index,
          fadeIn: Boolean(fadeIn),
          listeners,
          ref,
          style,
          transform,
          transition,
          value,
        })
      ) : (
        <li
          className={clsx(
            styles.Wrapper,
            fadeIn && styles.fadeIn,
            sorting && styles.sorting,
            dragOverlay && styles.dragOverlay,
          )}
          style={
            {
              ...wrapperStyle,
              transition: [transition, wrapperStyle?.transition]
                .filter(Boolean)
                .join(', '),
              '--translate-x': transform
                ? `${Math.round(transform.x)}px`
                : undefined,
              '--translate-y': transform
                ? `${Math.round(transform.y)}px`
                : undefined,
              '--scale-x': transform?.scaleX
                ? `${transform.scaleX}`
                : undefined,
              '--scale-y': transform?.scaleY
                ? `${transform.scaleY}`
                : undefined,
              '--index': index,
              '--color': color,
            } as React.CSSProperties
          }
          ref={ref}
        >
          <div
            className={clsx(
              styles.Item,
              dragging && styles.dragging,
              handle && styles.withHandle,
              dragOverlay && styles.dragOverlay,
              disabled && styles.disabled,
              color && styles.color,
            )}
            style={style}
            data-cypress="draggable-item"
            {...(!handle ? listeners : undefined)}
            {...props}
            tabIndex={!handle ? 0 : undefined}
          >
            <div className="mb-2 flex w-full justify-between border-b border-slate-100 pb-2">
              <div className="flex-1 text-base text-gray-900">
                {value.title}
              </div>
              <div className="w-7 text-gray-400">
                {value.required ? '必填' : null}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {onRemove ? (
                  <Remove className={styles.Remove} onClick={onRemove} />
                ) : null}
                <span className="ml-2 text-sm text-stone-500">删除</span>
              </div>
              <div className="flex items-center">
                <div
                  className="ml-3 text-base primary-color"
                  onClick={() => {
                    // router.push(`/workflow/create/widgetSetting/${value.id}`, {
                    //   scroll: false,
                    // });
                    setWidgetId(value.id);
                  }}
                >
                  控件设置
                </div>
                <div className={clsx(styles.Actions, 'ml-3')}>
                  {handle ? <Handle {...handleProps} {...listeners} /> : null}
                </div>
              </div>
            </div>
          </div>
        </li>
      );
    },
  ),
);
