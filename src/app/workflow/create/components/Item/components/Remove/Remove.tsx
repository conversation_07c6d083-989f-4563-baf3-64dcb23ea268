'use client';

import React from 'react';

import { PiMinusCircleFill } from '@/components/Icons';

import type { ActionProps } from '../Action';
import { Action } from '../Action';

export function Remove(props: ActionProps) {
  return (
    <Action
      {...props}
      active={{
        fill: 'rgba(255, 70, 70, 0.95)',
        background: 'rgba(255, 70, 70, 0.1)',
      }}
    >
      <PiMinusCircleFill fontSize={20} color="#DBDBDB" />
    </Action>
  );
}
