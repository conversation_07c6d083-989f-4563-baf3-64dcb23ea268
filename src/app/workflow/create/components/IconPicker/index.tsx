import { ImageUploader, Popup, Tabs } from 'antd-mobile';
import Image from 'next/image';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { upload } from '../utils/upload';

export interface Ref {
  toggle(): void;
}
interface Props {
  icons: string[];
  onSelect: (icon: string) => void;
}

const IconPicker = forwardRef<Ref, Props>((props, ref) => {
  const { onSelect, icons } = props;
  const [iconPopupVisible, setIconPopupVisible] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  useImperativeHandle(ref, () => ({
    toggle() {
      setIconPopupVisible((prev) => !prev);
    },
  }));

  return (
    <Popup
      visible={iconPopupVisible}
      onMaskClick={() => {
        setIconPopupVisible(false);
      }}
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        minHeight: '56vh',
        paddingTop: '10px',
      }}
    >
      <Tabs
        activeLineMode="fixed"
        style={{
          '--fixed-active-line-width': '30px',
        }}
      >
        <Tabs.Tab title="默认图标" key="1" className="w-1/2">
          <div className="grid grid-cols-5 gap-4 p-2">
            {icons.map((item, index) => (
              <div
                key={`icon${index}`}
                className="flex justify-center"
                onClick={() => {
                  setIconPopupVisible(false);
                  onSelect(item);
                }}
              >
                <Image src={item} alt="" width="60" height="60" />
              </div>
            ))}
          </div>
        </Tabs.Tab>
        <Tabs.Tab title="自定义图标" key="2" className="w-1/2">
          <div className="p-2">
            <ImageUploader
              value={fileList}
              maxCount={1}
              onChange={setFileList}
              upload={upload as any}
              preview={false}
              onPreview={(_, file) => {
                onSelect(file.url);
                setIconPopupVisible(false);
              }}
              onDelete={() => {
                onSelect('');
              }}
            />
          </div>
        </Tabs.Tab>
      </Tabs>
    </Popup>
  );
});

export default IconPicker;
