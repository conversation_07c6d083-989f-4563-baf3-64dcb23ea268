import { Button, Toast } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import React from 'react';

import { createApproval, updateApproval } from '@/api/approval';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { postMessage } from '@/utils';
import Emitter from '@/utils/emitter';

import { typeMap } from '../data';

type Props = { modelId?: string; templateId?: string };

// 替换空的children为null
function replaceEmptyChildrenWithNull(node: any) {
  if (
    node.children &&
    node.children !== null &&
    !Array.isArray(node.children) &&
    Object.keys(node.children).length === 0
  ) {
    node.children = null;
  }

  for (const key in node) {
    if (node[key] !== null && typeof node[key] === 'object') {
      replaceEmptyChildrenWithNull(node[key]);
    }
  }
}

// 检查当前节点的assignedUser是否为空数组
function checkEmptyAssignedUser(node: any) {
  // 检查当前节点的assignedUser是否为空数组
  if (
    node.type !== 'ROOT' &&
    node.props?.assignedUser &&
    Array.isArray(node.props.assignedUser) &&
    node.props.assignedUser.length === 0
  ) {
    Toast.show({
      content: `流程设计: 未选择[${typeMap[node.type] || '人员'}]${
        node.type === 'CC' ? '，如无需抄送，请删除抄送节点' : ''
      }`,
    });
    return true;
  }
  // 如果当前节点有子节点，递归检查子节点
  if (node.children) {
    if (checkEmptyAssignedUser(node.children)) {
      return true;
    }
  }
  return false;
}

export default function Index({ modelId, templateId }: Props) {
  const forms = useWorkflowStore((state) => state.forms);
  const process = useWorkflowStore((state) => state.process);
  const baseForm = useWorkflowStore((state) => state.baseForm);
  const settingForm = useWorkflowStore((state) => state.settingForm);
  const router = useRouter();

  const submit = () => {
    console.log('forms', forms);

    // 验证基础信息表单
    Emitter.emit('validateFields', () => {});

    if (baseForm.name === '') {
      Toast.show({
        content: '请填写表单名称',
      });
      return;
    }
    if (baseForm.iconUrl === '') {
      Toast.show({
        content: '请选择一个图标',
      });
      return;
    }
    if (baseForm.groupId === '') {
      Toast.show({
        content: '请选择分组',
      });
      return;
    }

    const newForms = JSON.parse(JSON.stringify(forms)).map(
      (item: any, index: number) => ({ ...item, order: index }),
    );

    if (newForms.length === 0) {
      Toast.show({
        content: '表单信息没有添加任何控件',
      });
      return;
    }

    const obj = newForms.reduce((acc: any, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});

    const newProcess = JSON.parse(JSON.stringify(process));

    if (checkEmptyAssignedUser(newProcess)) {
      return;
    }

    replaceEmptyChildrenWithNull(newProcess);
    const postData = {
      ...baseForm,
      form: {
        type: 'object',
        displayType: 'column',
        properties: obj,
      },
      process: newProcess,
      settings: settingForm,
      templateId,
    };
    console.log('🚀 ~ file: index.tsx:17 ~ postData:', postData);
    if (modelId) {
      updateApproval(modelId, postData).then(() => {
        Toast.show({
          icon: 'success',
          content: '更新成功',
        });
        postMessage({ goBack: 2 });
      });
    } else {
      createApproval(postData).then(() => {
        Toast.show({
          icon: 'success',
          content: '保存成功',
        });
        postMessage({
          goBack: 2,
          from: templateId !== '0' ? 'template' : 'new',
        });
      });
    }
  };
  return (
    <div className="flex items-center justify-evenly bg-white px-4 py-3">
      {/* <Button
        color="primary"
        fill="outline"
        className="w-2/5"
        onClick={() => {
          router.push('/workflow/preview', { scroll: false });
        }}
      >
        预览
      </Button> */}
      <Button color="primary" fill="solid" className="w-full" onClick={submit}>
        发布
      </Button>
    </div>
  );
}
