import { omit } from 'lodash-es';
import Image from 'next/image';
import React, { useRef, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';

import { PiArrowCounterClockwise } from '@/components/Icons';

export default function Signature(props: any) {
  const signatureRef = useRef<SignatureCanvas>(null);
  const windowSize = useRef(
    typeof window !== 'undefined'
      ? [window.innerWidth, window.innerHeight]
      : [0, 0],
  );
  const { readOnly, value, onChange, ...rest } = omit(props, [
    'addons',
    'schema',
  ]);
  const [isShowSignature, setIsShowSignature] = useState(true);

  const submit = () => {
    const dataUrl = signatureRef.current
      ?.getTrimmedCanvas()
      .toDataURL('image/png');
    onChange(dataUrl);
  };

  const handleClear = () => {
    signatureRef.current?.clear();
  };

  if (readOnly) {
    return (
      <div className="bg-white">
        {!!value && (
          <Image
            src={value}
            alt=""
            width="0"
            height="0"
            sizes="100%"
            className="w-full object-cover"
          />
        )}
      </div>
    );
  }

  return (
    <div className="relative flex flex-col items-center">
      <SignatureCanvas
        penColor="black"
        backgroundColor="rgb(247 249 255)"
        canvasProps={{
          className: 'w-full h-[300px]',
        }}
        ref={signatureRef}
        onEnd={() => {
          submit();
        }}
      />
      <div className="absolute right-3 top-3" onClick={handleClear}>
        <PiArrowCounterClockwise fontSize={20} color="#333" />
      </div>
      {isShowSignature && !!value ? (
        <>
          <Image
            src={value}
            alt=""
            width="0"
            height="0"
            sizes="100vw"
            className="z-1 absolute right-0 top-0 w-full object-cover"
            onClick={() => {
              setIsShowSignature(false);
            }}
          />
          <div
            className="absolute right-3 top-3"
            onClick={() => {
              setIsShowSignature(false);
            }}
          >
            重签
          </div>
        </>
      ) : null}
    </div>
  );
}
