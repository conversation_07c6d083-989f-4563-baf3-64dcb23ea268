import { Radio, Space } from 'antd-mobile';
import { omit } from 'lodash-es';
import React, { memo } from 'react';

function Checkbox(props: any) {
  const { readOnly, value, options, ...rest } = omit(props, [
    'addons',
    'schema',
  ]);

  console.log('🚀 ~ file: index.tsx:15 ~ value:', value);
  console.log('🚀 ~ file: index.tsx:15 ~ value:', rest);
  if (readOnly) {
    const __html = options.find((o) => o.value === rest.checked)?.label;
    return <div dangerouslySetInnerHTML={{ __html }} />;
  }

  return (
    <Radio.Group {...rest} value={rest.checked}>
      <Space direction="vertical" wrap>
        {options.map((item: any) => {
          return (
            <Radio value={item.value} key={item.value}>
              {item.label}
            </Radio>
          );
        })}
      </Space>
    </Radio.Group>
  );
}

export default memo(Checkbox);
