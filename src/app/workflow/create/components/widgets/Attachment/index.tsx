import { ProgressBar, Toast } from 'antd-mobile';
import { omit } from 'lodash-es';
import React, { useEffect, useRef, useState } from 'react';

import { PiTrash, PiUploadSimple } from '@/components/Icons';
import { generateKey, uploadObs } from '@/utils/obs';

import { FileIcon } from './ImageConfig';

type FileType = {
  name: string;
  size: number;
  type: string;
  url: string;
};

const maxSize = '50MB';

function formatFileSize(bytes: number, decimalPoint = 2) {
  if (bytes === 0) return '0 Bytes';
  const k = 1000;
  const dm = decimalPoint || 2;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}

const Attachment = (props: any) => {
  const {
    onChange,
    value,
    max = 10,
    ...rest
  } = omit(props, ['addons', 'schema']);
  const readOnly = rest.readOnly || false;

  const wrapperRef = useRef(null);

  const [fileList, setFileList] = useState<FileType[]>([]);
  const [uploadingFile, setUploadingFile] = useState<FileType | null>(null); // 正在上传的文件
  const [progress, setProgress] = useState<number>(0); // 上传进度

  useEffect(() => {
    // 初始化默认值
    if (Array.isArray(value)) {
      setFileList(value);
    }
  }, [value]);

  const progressCallback = (progress: number) => {
    setProgress(progress);
  };

  const onFileDrop = async (e) => {
    const file = e.target.files[0];
    console.log('🚀 ~ file: index.tsx:42 ~ file:', file);
    if (file.size > 50 * 1024 * 1024) {
      Toast.show(`请选择小于 ${maxSize} 的文件`);
      return null;
    }
    if (file) {
      setUploadingFile(file);
      setProgress(0);
      try {
        const key = generateKey(file.name, 'workflow');
        const url = await uploadObs(file, key, false, progressCallback);
        if (!url) {
          throw new Error('Fail to upload');
        }
        const newFile = {
          url,
          name: file.name,
          type: file.type,
          size: file.size,
        };
        const updatedList = [...fileList, newFile];
        setUploadingFile(null);
        setProgress(0);
        setFileList(updatedList);
        onChange(updatedList);
      } catch (err: any) {
        throw new Error(err.message);
      }
    }
  };

  const fileRemove = (index: number) => {
    const updatedList = [...fileList];
    updatedList.splice(index, 1);
    setFileList(updatedList);
    onChange(updatedList);
  };

  const download = (url: string) => {
    window.open(url);
  };

  return (
    <div className="w-full">
      {fileList.length > 0 ? (
        <div className="w-full">
          {fileList.map((item, index) => (
            <div
              key={index}
              className="mt-2 flex items-center rounded-md border border-gray-200 p-2"
            >
              <div
                className="flex flex-1 items-center"
                onClick={() => download(item.url)}
              >
                <FileIcon fileType={item.type} />
                <div className="ml-1 flex flex-1  flex-col">
                  <div className="w-full overflow-hidden text-ellipsis break-all text-base">
                    {item.name}
                  </div>
                  <div className="text-sm text-stone-400">
                    {formatFileSize(item.size)}
                  </div>
                </div>
              </div>
              {!readOnly && (
                <div className="w-[40px]" onClick={() => fileRemove(index)}>
                  <PiTrash fontSize={20} color="#666" />
                </div>
              )}
            </div>
          ))}
        </div>
      ) : null}
      {uploadingFile ? (
        <div className="w-full">
          <div className="mt-2 rounded-md border border-gray-200">
            <div className="flex items-center p-2">
              <FileIcon fileType={uploadingFile.type} />
              <div className="ml-2 flex flex-1 flex-col">
                <div className="w-full overflow-hidden text-ellipsis break-all text-base">
                  {uploadingFile.name}
                </div>
                <div className="text-sm text-stone-400">
                  {formatFileSize(uploadingFile.size)}
                </div>
              </div>
            </div>
            <ProgressBar
              percent={progress}
              style={{
                '--track-width': '4px',
              }}
            />
          </div>
        </div>
      ) : null}
      {!readOnly && fileList.length < max && (
        <>
          <div
            ref={wrapperRef}
            className="relative mt-4 flex items-center justify-center"
          >
            <div className="flex items-center">
              <PiUploadSimple fontSize={20} color="#3B82F7" />
              <span className="ml-2 text-[#3B82F7]">上传文件</span>
            </div>
            <input
              type="file"
              value=""
              onChange={onFileDrop}
              className="absolute left-0 top-0 size-full cursor-pointer opacity-0"
            />
          </div>
          <div className="mt-4 text-sm text-stone-400">
            上限{max}个文件，最大50MB/个
          </div>
        </>
      )}
    </div>
  );
};

export default Attachment;
