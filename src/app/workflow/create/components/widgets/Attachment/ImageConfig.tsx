'use client';

import FileAudio from './assets/file-type-audio.svg';
import FileCompressed from './assets/file-type-compressed-package.svg';
import FileExcel from './assets/file-type-excel.svg';
import FileExe from './assets/file-type-exe.svg';
import FileGif from './assets/file-type-gif.svg';
import FilePdf from './assets/file-type-pdf.svg';
import FileImage from './assets/file-type-picture.svg';
import FilePpt from './assets/file-type-ppt.svg';
import FileDefault from './assets/file-type-unknown.svg';
import FileVideo from './assets/file-type-video.svg';

export const ImageConfig = {
  default: () => <FileDefault className="w-[80px]" />,
  pdf: () => <FilePdf className="w-[80px]" />,
  png: () => <FileImage className="w-[80px]" />,
  jpeg: () => <FileImage className="w-[80px]" />,
  gif: () => <FileGif className="w-[80px]" />,
  mp4: () => <FileVideo className="w-[80px]" />,
  ppt: () => <FilePpt className="w-[80px]" />,
  exe: () => <FileExe className="w-[80px]" />,
  excel: () => <FileExcel className="w-[80px]" />,
  zip: () => <FileCompressed className="w-[80px]" />,
  audio: () => <FileAudio className="w-[80px]" />,
};

export function FileIcon({ fileType }: { fileType: string }) {
  console.log('🚀 ~ file: ImageConfig.tsx:27 ~ fileType:', fileType);

  const type: any = fileType.split('/')[1];
  console.log('🚀 ~ file: ImageConfig.tsx:30 ~ type:', type);

  switch (type) {
    case 'pdf':
      return <FilePdf className="w-[80px]" />;
    case 'png':
      return <FileImage className="w-[80px]" />;
    case 'jpeg':
      return <FileImage className="w-[80px]" />;
    case 'gif':
      return <FileGif className="w-[80px]" />;
    case 'mp4':
      return <FileVideo className="w-[80px]" />;
    case 'ppt':
      return <FilePpt className="w-[80px]" />;
    case 'exe':
      return <FileExe className="w-[80px]" />;
    case 'excel':
      return <FileExcel className="w-[80px]" />;
    case 'zip':
      return <FileCompressed className="w-[80px]" />;
    case 'audio':
      return <FileAudio className="w-[80px]" />;
    default:
      return <FileDefault className="w-[80px]" />;
  }
}
