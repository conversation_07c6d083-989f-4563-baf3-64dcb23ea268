import { Cascader } from 'antd-mobile';
import { omit } from 'lodash-es';
import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { useImmer } from 'use-immer';

import api from '@/lib/api';

type areaItemApi = { name: string; areaCode: string };
type areaItem = { label: string; value: string; children: areaItem[] };

async function getArea(parentCode = '0') {
  return new Promise<[]>((resolve, reject) => {
    api
      .get('/v1/contact/areas', { params: { parentCode } })
      .then((response) => resolve(response.data))
      .catch((error) => reject(new Error(error)));
  });
}

export default function Address(props: any) {
  const {
    placeholder = '请选择',
    value,
    onChange,
    ...rest
  } = omit(props, ['addons', 'schema']);

  const pickerRef: any = useRef(null);
  const [area, setArea] = useImmer<Array<areaItem>>([]);

  // 使用useImperativeHandle暴露方法给外部
  useImperativeHandle(props.addons.fieldRef, () => ({
    ...pickerRef?.current,
  }));

  useEffect(() => {
    // 首次加载
    init();
  }, []);

  const init = () => {
    if (area.length === 0) {
      getArea().then((data: Array<areaItemApi>) => {
        if (Array.isArray(data)) {
          const arr: Array<areaItem> = data.map((item) => ({
            label: item.name,
            value: item.areaCode,
            children: [],
          }));
          setArea(arr);
        }
      });
    }
  };

  const onCascaderChange = async (val: any) => {
    if (val.length === 3) {
      return;
    }
    const province = val[0];
    const city = val[1];
    const provinceIndex = area.findIndex(
      (item: areaItem) => item.value === province,
    );
    if (provinceIndex > -1) {
      const needRequestCity = area[provinceIndex].children.length === 0;
      if (needRequestCity) {
        const data: Array<areaItemApi> = await getArea(province);
        if (Array.isArray(data)) {
          const arr: Array<areaItem> = data.map((item) => ({
            label: item.name,
            value: item.areaCode,
            children: [],
          }));
          setArea((draft) => {
            draft[provinceIndex].children = arr;
          });
        }
      }
      if (city) {
        const cityIndex = area[provinceIndex].children.findIndex(
          (item: areaItem) => item.value === city,
        );
        if (cityIndex > -1) {
          const needRequestArea =
            area[provinceIndex].children[cityIndex].children.length === 0;
          if (needRequestArea) {
            const data: Array<areaItemApi> = await getArea(city);
            if (Array.isArray(data)) {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              const arr: Array<areaItem> = data.map((item) => ({
                label: item.name,
                value: item.areaCode,
              }));
              setArea((draft) => {
                draft[provinceIndex].children[cityIndex].children = arr;
              });
            }
          }
        }
      }
    }
  };
  const onConfirm = (val: Array<string | number>, extend: any) => {
    const data = [{}, {}, {}];
    extend.items.forEach((item: any, index: number) => {
      if (index < 3) {
        data[index] = {
          value: item.value,
          label: item.label,
        };
      }
    });
    onChange(data);
  };

  return (
    <Cascader
      {...rest}
      ref={pickerRef}
      options={area}
      onConfirm={onConfirm}
      onSelect={onCascaderChange}
    >
      {(items) => {
        if (items.every((i) => i === null)) {
          // 显示初始化值
          if (Array.isArray(value) && value.length > 0) {
            return value.map((item) => item.label).join('-');
          }
          return <span style={{ color: '#ccc' }}>{placeholder}</span>;
        }
        if (Array.isArray(items) && items.length > 0) {
          return items.map((i) => i?.label ?? '未选择').join('-');
        }
      }}
    </Cascader>
  );
}
