import { ActionSheet, Form, Input, Toast } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import type { FormInstance } from 'antd-mobile/es/components/form';
import Image from 'next/image';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { getGroup } from '@/api/approval';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { debounce } from '@/utils';
import Emitter from '@/utils/emitter';

import type { Ref } from '../IconPicker';
import IconPicker from '../IconPicker';

const icons = Array.from({ length: 20 }).map(
  (_, index) =>
    `https://unicorn-media.ancda.com/production/app/workflow/icon/${
      index + 1
    }.png`,
);

export default function BaseForm() {
  const baseForm = useWorkflowStore((state) => state.baseForm);
  const setBaseForm = useWorkflowStore((state) => state.setBaseForm);

  const [form] = Form.useForm();
  const formRef = useRef<FormInstance>(null);
  const iconPickerRef = useRef<Ref>(null);

  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [actions, setActions] = useState<Action[]>([]);

  const onChange = debounce((value: any) => {
    setBaseForm({ ...baseForm, name: value });
  }, 500);

  useEffect(() => {
    form.setFieldsValue(baseForm);
  }, [baseForm]);

  useEffect(() => {
    Emitter.on('validateFields', validateFields);
    return () => {
      Emitter.off('validateFields', validateFields);
    };
  }, []);

  const validateFields = () => {
    return new Promise((resolve, reject) => {
      form
        .validateFields()
        .then((values) => {
          resolve(values);
        })
        .catch((err) => {
          if (Array.isArray(err.errorFields)) {
            Toast.show({
              content: err.errorFields[0].errors[0],
            });
          }
          reject(err);
        });
    });
  };

  const getGroupName = useCallback(
    (groupId: string) => {
      if (actions.length) {
        const action = actions.find((item) => item.key === groupId);
        return action?.text || '';
      }
      getGroup().then((res: any) => {
        if (Array.isArray(res.list)) {
          setActions(
            res.list.map((item: any) => ({
              key: item.groupId,
              text: item.groupName,
            })),
          );
          const action = res.list.find((item) => item.key === groupId);
          return action?.text || '';
        }
      });
    },
    [actions],
  );

  return (
    <>
      <Form
        ref={formRef}
        form={form}
        mode="card"
        layout="horizontal"
        // requiredMarkStyle="text-required"
      >
        <Form.Header>审批基础信息</Form.Header>
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true }]}
          className="border-none"
        >
          <Input
            placeholder="请输入审批名称"
            maxLength={20}
            minLength={2}
            // defaultValue={baseForm.name}
            // value={baseForm.name}
            style={{ '--text-align': 'right' }}
            onChange={onChange}
          />
        </Form.Item>
        <Form.Item
          name="iconUrl"
          label="图标"
          rules={[{ required: true, message: '请选择一个图标' }]}
          extra={
            <div>
              {baseForm.iconUrl ? (
                <Image
                  src={baseForm.iconUrl}
                  alt=""
                  width="0"
                  height="0"
                  sizes="64px"
                  className="size-[64px] rounded object-cover"
                />
              ) : null}
            </div>
          }
          clickable
          arrow
          onClick={() => {
            iconPickerRef.current?.toggle();
          }}
        />
        <Form.Item
          name="groupId"
          label="分组"
          rules={[{ required: true, message: '请选择分组' }]}
          extra={<div>{getGroupName(baseForm.groupId)}</div>}
          arrow
          onClick={() => {
            setActionSheetVisible(true);
          }}
        />
      </Form>
      <IconPicker
        ref={iconPickerRef}
        icons={icons}
        onSelect={(value) => {
          setBaseForm({ ...baseForm, iconUrl: value });
        }}
      />
      <ActionSheet
        extra="请选择"
        cancelText="取消"
        visible={actionSheetVisible}
        actions={actions}
        onAction={(action) => {
          if (action.key) {
            setBaseForm({ ...baseForm, groupId: String(action.key) });
          }
          setActionSheetVisible(false);
        }}
        onClose={() => setActionSheetVisible(false)}
      />
    </>
  );
}
