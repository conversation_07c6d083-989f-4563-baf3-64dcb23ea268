import {
  Button,
  Form,
  Input,
  NavBar,
  Switch,
  TextArea,
  Toast,
} from 'antd-mobile';
import type { FormInstance } from 'antd-mobile/es/components/form';
import React, { useEffect, useRef } from 'react';

import { PiTrash } from '@/components/Icons';

import type { ItemType } from '../../types';

interface Props {
  widgetId: string;
  updateFormItem: (id: string, data: any) => void;
  forms: Array<ItemType>;
  setWidgetId: (id: string) => void;
}

export default function Index({
  widgetId,
  updateFormItem,
  forms,
  setWidgetId,
}: Props) {
  const formRef = useRef<FormInstance>(null);

  console.log('widgetId', widgetId);

  const widgetData: ItemType | undefined = forms.find(
    (item) => item.id === widgetId,
  );

  useEffect(() => {
    if (widgetData) {
      console.log('🚀 ~ file: page.tsx:28 ~ widgetData:', widgetData);
      formRef.current?.setFieldsValue(widgetData);
    }
  }, [widgetData]);

  const onFinish = (values: any) => {
    console.log('🚀 ~ file: page.tsx:34 ~ values:', values);
    if (!widgetId) {
      return;
    }
    const data = { ...values, id: widgetId };
    updateFormItem(widgetId, data);
    setWidgetId('');
    formRef.current?.resetFields();
  };

  const removeOption = (index: number) => {
    const value = formRef.current?.getFieldValue(['props', 'options']);
    if (value.length === 1) {
      Toast.show({
        content: '至少保留一个选项',
      });
      return;
    }
    const newValue = value.filter((item: any, i: number) => i !== index);
    formRef.current?.setFieldsValue({
      props: {
        options: newValue,
      },
    });
  };

  return (
    <div className="max-h-screen  overflow-scroll bg-gray-50">
      <NavBar
        onBack={() => {
          setWidgetId('');
        }}
      >
        设置控件
      </NavBar>
      <Form
        ref={formRef}
        mode="card"
        onFinish={onFinish}
        footer={
          <Button block type="submit" color="primary">
            确定
          </Button>
        }
      >
        <Form.Header>设置控件{widgetData?.title || ''}</Form.Header>
        <Form.Item name="title" label="控件名称" rules={[{ required: true }]}>
          <Input placeholder="请输入控件名称" maxLength={20} />
        </Form.Item>
        <Form.Item name="description" label="控件说明">
          <Input placeholder="请输入控件说明" maxLength={50} />
        </Form.Item>
        <Form.Item name="type" label="数据类型" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="placeholder" label="默认提示" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="widget" label="组件名" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="readOnlyWidget" label="组件名" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="layout" label="组件名" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="properties" label="属性" hidden>
          <Input />
        </Form.Item>
        {widgetData?.widget ? (
          <>
            {['picker', 'checkbox', 'checkboxes'].includes(
              widgetData?.widget,
            ) && (
              <Form.Item name="props" label="添加选项">
                <Form.Array
                  name={['props', 'options']}
                  onAdd={(operation) => {
                    const value = formRef.current?.getFieldValue([
                      'props',
                      'options',
                    ]);
                    console.log('🚀 ~ file: index.tsx:51 ~ value:', value);
                    operation.add({
                      label: `选项${value.length + 1}`,
                      value: String(value.length + 1),
                    });
                  }}
                  renderAdd={() => (
                    <Button color="primary" fill="outline">
                      添加选项
                    </Button>
                  )}
                >
                  {(fields) => {
                    return fields.map(({ index }) => (
                      <Form.Item
                        key={`item${index}`}
                        name={[index, 'label']}
                        rules={[
                          { required: true, message: '选项名称不能为空' },
                        ]}
                        extra={
                          <div
                            className=""
                            onClick={() => {
                              removeOption(index);
                            }}
                          >
                            <PiTrash fontSize={18} color="#666" />
                          </div>
                        }
                      >
                        <Input placeholder="请输入选项名称" maxLength={20} />
                      </Form.Item>
                    ));
                  }}
                </Form.Array>
              </Form.Item>
            )}
            {widgetData?.widget === 'richText' && (
              <Form.Item name="props" label="说明文字" noStyle>
                <Form.Item
                  name={['props', 'value']}
                  label="说明文字"
                  // rules={[{ required: true, message: '说明文字不能为空' }]}
                  className="h-[400px]"
                >
                  <TextArea maxLength={500} showCount />
                </Form.Item>
              </Form.Item>
            )}
          </>
        ) : null}
        {widgetData?.widget !== 'richText' && (
          <Form.Item
            name="required"
            label="是否必填"
            childElementPosition="right"
            layout="horizontal"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        )}

        <Form.Item
          name="printable"
          label="参与打印"
          childElementPosition="right"
          layout="horizontal"
          help="若取消勾选，打印时将不显示本控件"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </div>
  );
}
