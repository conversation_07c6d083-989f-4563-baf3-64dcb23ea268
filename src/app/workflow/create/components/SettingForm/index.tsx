import { Dialog, Form, Switch } from 'antd-mobile';
import type { FormInstance } from 'antd-mobile/es/components/form';
import React from 'react';

import { useWorkflowStore } from '@/store/useWorkflowStore';

export default function SettingForm() {
  const settingForm = useWorkflowStore((state) => state.settingForm);
  const setSettingForm = useWorkflowStore((state) => state.setSettingForm);
  const [form] = Form.useForm();
  const formRef = React.useRef<FormInstance>(null);

  const onFinish = (values: any) => {
    Dialog.alert({
      content: <pre>{JSON.stringify(values, null, 2)}</pre>,
    });
  };

  return (
    <Form
      ref={formRef}
      form={form}
      mode="card"
      layout="horizontal"
      onFinish={onFinish}
      style={{
        '--prefix-width': '240px',
      }}
    >
      <Form.Item
        name="delivery"
        label="审批人离职自动交接给管理员"
        childElementPosition="right"
        disabled
      >
        <Switch defaultChecked />
      </Form.Item>
      <Form.Item
        name="delivery"
        label="是否允许撤回"
        childElementPosition="right"
      >
        <Switch
          defaultChecked={settingForm.allowCancel}
          onChange={(value) => {
            setSettingForm({ ...settingForm, allowCancel: value });
          }}
        />
      </Form.Item>
      <Form.Item
        name="delivery"
        label="审批时需要签名"
        childElementPosition="right"
        help="若开启，所有的办理和审批同意都需要签字"
      >
        <Switch
          defaultChecked={settingForm.sign}
          onChange={(value) => {
            setSettingForm({ ...settingForm, sign: value });
          }}
        />
      </Form.Item>
    </Form>
  );
}
