import { nanoid } from '@/utils';

type TypeKeys = 'ROOT' | 'APPROVAL' | 'CC' | 'TASK';

export const typeMap: Record<TypeKeys, string> = {
  ROOT: '发起人',
  APPROVAL: '审批人',
  CC: '抄送人',
  TASK: '办理人',
};

export const modeType: { [key: string]: string } = {
  AND: '会签（须所有成员通过）',
  OR: '或签（一名成员通过即可）',
  NEXT: '依次审批（按人员顺序依次审批）',
};

export const properties: any = {
  input: {
    title: '单行文本',
    description: '',
    type: 'string',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    required: true,
    props: {
      maxLength: 20,
    },
  },
  textArea: {
    title: '多行文本',
    description: '',
    type: 'string',
    layout: 'column',
    widget: 'textArea',
    placeholder: '请输入',
    printable: true,
    required: true,
    props: {
      rows: 5,
      showCount: true,
      maxLength: 200,
    },
  },
  richText: {
    title: '说明文字',
    description: '',
    type: 'string',
    widget: 'richText',
    readOnlyWidget: 'richText',
    printable: true,
    required: false,
    props: {
      value: ``,
    },
  },
  inputNumber: {
    title: '数字',
    description: '',
    type: 'number',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    required: true,
    props: {
      type: 'number',
    },
  },
  amountNumber: {
    title: '金额',
    description: '',
    type: 'number',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    required: true,
    props: {
      type: 'number',
    },
  },
  phoneNumber: {
    title: '手机号',
    description: '',
    type: 'number',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    required: true,
    props: {
      type: 'number',
    },
    rules: [{ pattern: /^1[34578]\d{9}$/, message: '请输入正确的手机号！' }],
  },
  signature: {
    title: '签名',
    description: '',
    type: 'string',
    widget: 'signature',
    placeholder: '请签名',
    readOnlyWidget: 'signature',
    layout: 'column',
    printable: true,
    required: true,
  },
  image: {
    title: '图片',
    description: '',
    type: 'array',
    widget: 'image',
    placeholder: '请上传图片',
    readOnlyWidget: 'image',
    layout: 'column',
    printable: true,
    required: true,
    props: {
      max: 20,
    },
  },
  video: {
    title: '视频',
    description: '',
    type: 'array',
    widget: 'video',
    placeholder: '请上传视频',
    readOnlyWidget: 'video',
    layout: 'column',
    printable: true,
    required: true,
    props: {
      max: 5,
    },
  },
  attachment: {
    title: '附件',
    description: '',
    type: 'array',
    widget: 'attachment',
    placeholder: '请上传附件',
    readOnlyWidget: 'attachment',
    layout: 'column',
    printable: true,
    required: true,
    props: {
      max: 20,
    },
  },
  // 下拉选择
  picker: {
    title: '单选',
    description: '',
    type: 'array',
    widget: 'picker',
    placeholder: '请选择',
    printable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  checkboxes: {
    title: '多选',
    description: '',
    type: 'array',
    widget: 'checkboxes',
    placeholder: '请选择',
    readOnlyWidget: 'checkboxes',
    printable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  date: {
    title: '日期',
    description: '',
    type: 'string',
    widget: 'datePicker',
    placeholder: '请选择',
    printable: true,
    required: true,
    props: {
      precision: 'day',
    },
    // 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' | 'week' | 'week-day'
  },
  dateTime: {
    title: '日期时间',
    description: '',
    type: 'string',
    widget: 'datePicker',
    placeholder: '请选择',
    printable: true,
    required: true,
    props: {
      format: 'YYYY-MM-DD HH:mm',
      precision: 'minute',
    },
  },
  slider: {
    title: '滑动条',
    description: '',
    type: 'string',
    widget: 'slider',
    printable: true,
    required: true,
    props: {
      range: true,
    },
  },
  switch: {
    title: '开关',
    description: '',
    type: 'bool',
    widget: 'switch',
    placeholder: '请选择',
    printable: true,
    required: true,
    props: {},
  },
  address: {
    title: '地址',
    description: '',
    type: 'object',
    widget: 'group',
    layout: 'column',
    printable: true,
    properties: {
      area: {
        title: '地区',
        type: 'array',
        widget: 'address',
        readOnlyWidget: 'address',
        placeholder: '请选择',
      },
      detail: {
        title: '详细地址',
        type: 'string',
        layout: 'column',
        widget: 'textArea',
        placeholder: '请输入详细地址',
        props: {
          rows: 2,
          maxLength: 100,
        },
      },
    },
  },
  rate: {
    title: '评分',
    description: '',
    type: 'string',
    widget: 'rate',
    printable: true,
    required: true,
    props: {
      count: 5,
    },
  },
  selector: {
    title: '选择组单选',
    description: '',
    type: 'array',
    widget: 'selector',
    printable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  selector2: {
    title: '选择组多选',
    description: '',
    type: 'array',
    widget: 'selector',
    printable: true,
    required: true,
    props: {
      multiple: true,
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  stepper: {
    title: '步进器',
    description: '',
    type: 'number',
    widget: 'stepper',
    printable: true,
    required: true,
  },
  cascader: {
    title: '级联',
    description: '',
    type: 'array',
    widget: 'cascader',
    printable: true,
    required: true,
    props: {
      options: [
        {
          label: '浙江',
          value: 1,
          children: [{ label: '杭州', value: 2 }],
        },
      ],
    },
  },
  radio: {
    title: '单选',
    description: '',
    type: 'string',
    widget: 'radio',
    printable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  // 垂直列表选择
  checkbox: {
    title: '单选',
    description: '',
    type: 'string',
    widget: 'checkbox',
    readOnlyWidget: 'checkbox',
    printable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  group1: {
    type: 'object',
    description: '',
    widget: 'group',
    title: '分组1',
    printable: true,
    required: true,
    properties: {
      input: {
        title: '输入框 A',
        type: 'string',
        widget: 'input',

        placeholder: '请输入',
      },
      input2: {
        title: '输入框 B',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
    },
  },
  group2: {
    type: 'object',
    title: '分组2',
    widget: 'group',
    description: '这是一个对象类型',
    properties: {
      input1: {
        title: '输入框 A',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
      input2: {
        title: '输入框 B',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
    },
  },
  card: {
    type: 'object',
    widget: 'card',
    title: '卡片主题',
    description: '这是一个对象类型',
    properties: {
      input1: {
        title: '输入框 A',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
      input2: {
        title: '输入框 B',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
      input3: {
        title: '输入框 C',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
    },
  },
  list: {
    title: '对象数组',
    description: '',
    type: 'array',
    widget: 'cardList',
    items: {
      type: 'object',
      widget: 'card',
      properties: {
        input1: {
          title: '输入框 A',
          type: 'string',
          widget: 'input',
        },
        input2: {
          title: '输入框 B',
          type: 'string',
          widget: 'input',
        },
        input3: {
          title: '输入框 B',
          type: 'string',
          widget: 'input',
        },
      },
    },
  },
};

export const schema = {
  type: 'object',
  displayType: 'row',
  properties: {
    ...properties,
    richText: {
      title: '富文本',
      type: 'string',
      widget: 'richText',
      readOnlyWidget: 'richText',
      displayType: 'vertical',
      noStyle: true,
      props: {
        value: `<section data-role="outer" class="article" style="padding: 0px; box-sizing: border-box;" data-tplid="134825">
            <div class="text-red-400">test</div>
          </section>`,
      },
    },
  },
};

export const processJson: any = {
  id: 'root',
  desc: '任何人',
  name: '发起人',
  type: 'ROOT',
  props: {
    formPerms: [],
    listeners: {},
    assignedUser: [],
    operationPerm: {
      agree: {
        show: true,
        alias: '提交',
      },
    },
  },
  children: {
    id: `node-${nanoid(12)}`,
    name: '审批人',
    type: 'APPROVAL',
    props: {
      mode: 'AND',
      role: [],
      sign: false,
      leader: {
        level: 1,
        skipEmpty: true,
      },
      nobody: {
        handler: 'TO_ADMIN',
        assignedUser: [],
      },
      refuse: {
        type: 'TO_END',
        target: '',
      },
      formDept: '',
      formUser: '',
      formPerms: [],
      leaderTop: {
        endLevel: 0,
        skipEmpty: false,
        endCondition: 'TOP',
      },
      listeners: {},
      timeLimit: {
        handler: {
          type: 'REFUSE',
          notify: {
            hour: 1,
            once: true,
          },
        },
        timeout: {
          unit: 'H',
          value: 0,
        },
      },
      selfSelect: {
        multiple: false,
      },
      assignedDept: [],
      assignedType: 'ASSIGN_USER',
      assignedUser: [],
      operationPerm: {
        agree: {
          show: true,
          alias: '同意',
        },
        refuse: {
          show: true,
          alias: '拒绝',
        },
      },
    },
    children: {
      id: `node-${nanoid(12)}`,
      name: '抄送人',
      type: 'CC',
      props: {
        assignedType: 'ASSIGN_USER',
        formPerms: [],
        shouldAdd: false,
        assignedUser: [],
      },
      children: {},
      // parentId: 'node-669820554802',
    },
    parentId: 'root',
  },
  parentId: null,
};

export const formItems = Object.keys(properties).map((key) => ({
  ...properties[key],
  id: `form-${nanoid(12)}`,
}));
