import Image from 'next/image';
import React from 'react';

function Base({ baseInfo }) {
  return (
    <div className="p-4">
      <div className=" rounded-md bg-white p-4">
        <div className="mb-2 flex">
          <div>
            <Image
              src={baseInfo.iconUrl}
              alt=""
              width="0"
              height="0"
              sizes="100px"
              className="mr-2 size-[100px] rounded object-cover"
            />
          </div>
          <div>
            <div className="text-lg font-bold">{baseInfo.name}</div>
            <div>
              <span className="text-stone-500">所在分组：</span>
              {baseInfo.groupName}
            </div>
          </div>
        </div>
        <div>
          <div>
            <span className="text-stone-500">是否允许撤回：</span>
            {baseInfo.settings.allowCancel ? '允许' : '不允许'}撤回
          </div>
          <div>
            <span className="text-stone-500">审批时强制签名：</span>
            {baseInfo.settings.sign ? '需要' : '不需要'}签名
          </div>
        </div>
      </div>
    </div>
  );
}

export default Base;
