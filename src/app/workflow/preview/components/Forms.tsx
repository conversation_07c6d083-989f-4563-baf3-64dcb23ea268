import { Dialog, SpinLoading } from 'antd-mobile';
import FormRender, { useForm } from 'form-render-mobile';
// import Script from 'next/script';
import React, { useEffect } from 'react';

import address from '../../create/components/widgets/Address';
import attachment from '../../create/components/widgets/Attachment';
import checkbox from '../../create/components/widgets/Checkbox';
import checkboxes from '../../create/components/widgets/Checkboxes';
import image from '../../create/components/widgets/Image';
import richText from '../../create/components/widgets/RichText';
import signature from '../../create/components/widgets/Signature';
import video from '../../create/components/widgets/Video';

export default function Forms({ formSchema }) {
  console.log('render page index');
  const form = useForm();
  const [loading, setLoading] = React.useState(true);

  const onFinish = (formData: any) => {
    Dialog.alert({
      content: <pre>{JSON.stringify(formData, null, 2)}</pre>,
    });
  };

  useEffect(() => {
    console.log('init');
  }, []);

  const onMount = () => {
    console.log('onMount');
    setLoading(false);
  };

  return (
    <div className="-m-2 flex flex-col">
      {loading && (
        <div className="flex h-screen w-full items-center justify-center">
          <SpinLoading />
        </div>
      )}
      <FormRender
        schema={formSchema}
        displayType="column"
        form={form}
        mode="card"
        disabled
        onFinish={onFinish}
        onMount={onMount}
        widgets={{
          checkbox,
          checkboxes,
          richText,
          signature,
          image,
          video,
          attachment,
          address,
        }}
      />
    </div>
  );
}
