'use client';

import { Tabs } from 'antd-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getApproval } from '@/api/approval';

import ProcessView from '../submit/components/ProcessView';
import Base from './components/Base';
import Forms from './components/Forms';

export const dynamic = 'force-dynamic';

export default function Index() {
  const searchParams = useSearchParams();
  const modelId = searchParams?.get('modelId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }
  const [baseInfo, setBaseInfo] = useState({
    name: '-',
    iconUrl:
      'https://unicorn-media.ancda.com/production/app/workflow/icon/1.png',
    remark: '',
    groupId: '',
    groupName: '-',
    settings: {
      allowCancel: false,
      sign: false,
    },
  });
  const [formSchema, setFormSchema] = useState(null);
  const [process, setProcess] = useState(null);
  const [activeIndex, setActiveIndex] = useState('1');

  useEffect(() => {
    if (modelId) {
      getApproval(modelId).then((res: any) => {
        const {
          modelName,
          iconUrl,
          remark,
          groupId,
          groupName,
          form,
          process,
          settings,
        } = res;
        setBaseInfo({
          name: modelName,
          iconUrl,
          remark,
          groupId,
          groupName,
          settings,
        });
        if (form && typeof form === 'object') {
          setFormSchema(form);
        }
        if (
          process &&
          typeof process === 'object' &&
          Object.keys(process).length > 0
        ) {
          setProcess(process);
        }
      });
    }
  }, []);

  return (
    <div className="flex h-screen flex-col bg-[#F7F9FF]">
      <div className="fixed top-0 z-10 w-full bg-white">
        <Tabs
          activeLineMode="fixed"
          style={{
            '--fixed-active-line-width': '30px',
            '--content-padding': '0',
            '--active-line-height': '4px',
            '--active-line-color':
              'linear-gradient(90deg, #31C3FF 0%, #4E78FF 100%)',
            '--active-line-border-radius': '2px',
          }}
          onChange={(key) => {
            setActiveIndex(key);
          }}
        >
          <Tabs.Tab title="基础信息" key="1" className="w-1/3" forceRender />
          <Tabs.Tab title="流程设计" key="2" className="w-1/3" forceRender />
        </Tabs>
      </div>
      <div className="flex flex-1 flex-col overflow-scroll">
        {activeIndex === '1' && (
          <div className="flex flex-1 flex-col overflow-scroll bg-[#F7F9FF] pt-[80px]">
            <Base baseInfo={baseInfo} />
            <div className="px-4 text-base text-stone-500">表单信息预览</div>
            <Forms formSchema={formSchema} />
          </div>
        )}
        {activeIndex === '2' &&
          process &&
          typeof process === 'object' &&
          Object.keys(process).length > 0 && (
            <div className="mt-10 rounded-md bg-white p-4 text-base">
              <ProcessView currentData={process} />
            </div>
          )}
      </div>
    </div>
  );
}
