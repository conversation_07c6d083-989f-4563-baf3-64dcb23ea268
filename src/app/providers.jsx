'use client';

import React, { useEffect } from 'react';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import Cookies from 'js-cookie';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Script from 'next/script';
import { useCommonStore } from '@/store/useCommonStore';
import { getBrowser } from '@/utils';

function setThemeByDomain() {
  const { hostname } = window.location;

  if (hostname.includes('baby-mobile')) {
    document.documentElement.style.setProperty(
      '--adm-color-primary',
      '#17C5A6'
    );
  } else {
    document.documentElement.style.setProperty(
      '--adm-color-primary',
      '#4E78FF'
    );
  }
}

// 检查是否在微信中打开
function checkWechatEnvironment() {
  const browser = getBrowser();
  const isWechat = browser === 'wechat';

  console.log('页面打开环境检测:', isWechat ? '微信' : '其他浏览器');

  if (isWechat) {
    setTimeout(() => {
      // Cookies.set('Authorization', '**********');
    }, 1000);
    // 在微信中打开时的处理逻辑
    console.log('当前页面在微信中打开');
    // 这里可以添加微信特有的初始化逻辑
    // 例如：初始化微信 JSSDK、设置微信分享等
  } else {
    // 不在微信中打开时的处理逻辑
    console.log('当前页面不在微信中打开');
    // 这里可以添加非微信环境的处理逻辑
    // 例如：显示提示信息、限制某些功能等
  }

  return isWechat;
}

export default function Providers({ children }) {
  const [queryClient] = React.useState(() => new QueryClient());
  const setAuthorization = useCommonStore((state) => state.setAuthorization);
  const setVersion = useCommonStore((state) => state.setVersion);
  const setBrand = useCommonStore((state) => state.setBrand);
  const setAppType = useCommonStore((state) => state.setAppType);
  useEffect(() => {
    const authorization = Cookies.get('Authorization');
    const version = Cookies.get('Version');
    const Brand = Cookies.get('Brand');
    const AppType = Cookies.get('App-Type');
    setAuthorization(authorization);
    setVersion(version);
    setBrand(Brand);
    setAppType(AppType);
    setThemeByDomain();
    // 检查微信环境
    checkWechatEnvironment();
  }, []);

  return (
    <NuqsAdapter>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </NuqsAdapter>
  );
}
