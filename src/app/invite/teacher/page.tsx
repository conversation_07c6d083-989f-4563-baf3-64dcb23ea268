'use client';

import {
  ActionSheet,
  Button,
  Checkbox,
  Dialog,
  Form,
  Input,
  Popup,
  Toast
} from 'antd-mobile';
import axios from 'axios';
import Head from 'next/head';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';

import { createTeacher, getClassList } from '@/api/invite';
import DepartmentSelect from '@/components/DepartmentSelect';
import HeaderDown from '@/components/HeaderDown';
import { PiCheckCircleFill } from '@/components/Icons';
// import { weChatInit } from '@/utils/wechat';

interface IGenderType {
  [key: string]: string; // 这里使用字符串索引签名，表示该接口可以包含任意键
}

const genderTypeMap: IGenderType = {
  2: '女',
  1: '男'
};

function Page() {
  const searchParams = useSearchParams();
  const instId = searchParams?.get('instId') || '';
  const classId = searchParams?.get('classId') || '';
  const instName = searchParams?.get('instName') || '';

  const [loading, setLoading] = useState(false);

  const [postData, setPostData] = useImmer<{
    name: string;
    gender: number;
    mobile: string;
    reCode: string;
    deptIds: string[];
  }>({
    name: '',
    gender: 2,
    mobile: '',
    reCode: '86',
    deptIds: []
  });

  const [contentHtml, setContentHtml] = useState('');

  const [checked, setChecked] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [selectedClass, setSelectedClass] = useState<string[]>([]);
  const [classData, setClassData] = useImmer<any[]>([]);

  const [genderSelectVisible, setGenderSelectVisible] = useState(false);
  const [classPickerVisible, setClassPickerVisible] = useState(false);
  const [departmentPickerVisible, setDepartmentPickerVisible] = useState(false);
  const departmentList = useRef<string[]>([]);

  const [form] = Form.useForm();

  useEffect(() => {
    if (instId) {
      getClass();
      // weChatInit();
    }
    // 获取隐私协议数据
    async function fetchData() {
      const response = await axios('/api/mdx?filename=privacy-teacher');
      console.log('🚀 ~ response:', response);
      const privacyContent = response.data.contentHtml;
      setContentHtml(privacyContent);
    }

    fetchData();
  }, [instId]);

  const getClass = () => {
    getClassList({
      instId
    })
      .then((res: any) => {
        const { list } = res;
        list.forEach((item: any, index: number) => {
          item.checked = false;
          if (Array.isArray(item.classList) && item.classList.length > 0) {
            item.classList.forEach((classItem: any) => {
              classItem.checked = false;
            });
          } else {
            delete list[index];
          }
        });
        setClassData(list);
      })
      .catch((err) => {
        Toast.show(err.message);
      });
  };

  const onFinish = (values: any) => {
    console.log('values: ', values);
    if (!instId) {
      Toast.show({
        content: '请求参数有误',
        icon: 'fail'
      });
      return;
    }
    if (!checked) {
      Toast.show('请先阅读并同意隐私协议');
      return;
    }
    if (!postData.deptIds.length) {
      Toast.show('请选择所属部门');
      return;
    }
    const data = {
      ...postData,
      instId,
      classId,
      class_ids: selectedClass.map((item: any) => item.id)
    };
    setLoading(true);

    createTeacher(data)
      .then((res: any) => {
        setLoading(false);

        const { status } = res;
        if (status === 1) {
          Toast.show(`【${postData.mobile}】已提交在待审核，请勿重复提交`);
          return;
        }
        if (status === 2) {
          Toast.show(`【${postData.mobile}】已经注册，请勿重复提交`);
          return;
        }
        Toast.show({ icon: 'success', content: '创建成功' });
        setIsSuccess(true);
      })
      .catch((err) => {
        setLoading(false);

        console.log('err:', err);
      });
  };

  const selectParent = (prevChecked: boolean, index: number) => {
    setClassData((draft) => {
      draft[index].checked = prevChecked;
      draft[index].classList.forEach((item: any) => {
        item.checked = prevChecked;
      });
    });
  };

  // 选择学生
  const selectChild = (
    prevChecked: boolean,
    index: number,
    parentIndex: number
  ) => {
    setClassData((draft) => {
      draft[parentIndex].classList[index].checked = prevChecked;
      // 处理班级全选状态
      if (draft[parentIndex].classList.every((user: any) => user.checked)) {
        // 判断是否每个子元素都选中
        draft[parentIndex].checked = true;
      } else {
        draft[parentIndex].checked = false;
      }
    });
  };

  // 计算得到已选择的人员
  const getSelectedItem = () => {
    const selectedStudentTemp: any[] = [];
    classData.forEach((gradeItem: any) => {
      gradeItem.classList.forEach((classItem: any) => {
        if (classItem.checked) {
          selectedStudentTemp.push({
            id: classItem.id,
            name: classItem.name
          });
        }
      });
    });
    setSelectedClass(selectedStudentTemp);
  };

  const openClassPicker = () => {
    // 用以选数据处理班级选中状态
    setClassData((draft) => {
      draft.forEach((gradeItem: any) => {
        if (
          gradeItem.classList.every((classItem: any) =>
            selectedClass.some((item: any) => item.id === classItem.id)
          )
        ) {
          // 判断是否每个子元素都选中
          gradeItem.checked = true;
        } else {
          gradeItem.checked = false;
        }
        gradeItem.classList.forEach((classItem: any) => {
          if (selectedClass.some((item: any) => item.id === classItem.id)) {
            classItem.checked = true;
          } else {
            classItem.checked = false;
          }
        });
      });
    });

    setClassPickerVisible(true);
  };
  const confirmClassPicker = () => {
    getSelectedItem();
    setClassPickerVisible(false);
  };

  const onSelectGender = (value: number) => {
    setPostData((draft) => {
      draft.gender = value;
    });
    setGenderSelectVisible(false);
  };

  const confirmDepartmentPicker = () => {
    setPostData((draft) => {
      draft.deptIds = departmentList?.current.map((item: any) => item.id) || [];
    });
    setDepartmentPickerVisible(false);
  };

  if (isSuccess) {
    return (
      <>
        <Head>
          <title>提交成功</title>
        </Head>
        <main className="h-screen">
          <HeaderDown isTeacher />
          <section className="flex h-full flex-col items-center justify-center">
            <PiCheckCircleFill fontSize={60} color="#4E78FF" />
            <div className="py-5">已经提交入校申请，耐心等待审核</div>
          </section>
        </main>
      </>
    );
  }

  return (
    <div className="h-screen bg-gray-50">
      <Head>
        <title>邀请加入学校</title>
      </Head>
      <div className="mx-4 mt-4 rounded-lg bg-white p-4 py-6 ">
        <div>
          <div className="text-lg">{instName}</div>
          <div className="text-sx my-2 text-stone-400">邀请你加入园所</div>
        </div>
        <Form
          layout="vertical"
          form={form}
          onFinish={onFinish}
          footer={
            <div className="m-4">
              <Button
                shape="rounded"
                color="primary"
                type="submit"
                block
                loading={loading}
              >
                提交
              </Button>
            </div>
          }
        >
          <Form.Item
            name="useName"
            rules={[{ required: true, message: '请填写姓名' }]}
            label="姓名"
          >
            <Input
              placeholder="请填写姓名"
              maxLength={15}
              onChange={(value) =>
                setPostData((draft) => {
                  draft.name = value;
                })
              }
            />
          </Form.Item>
          <Form.Item
            initialValue="2"
            name="gender"
            rules={[{ required: true, message: '请选择性别' }]}
            label="性别"
            onClick={(_, action) => {
              setGenderSelectVisible(true);
            }}
          >
            {genderTypeMap[postData.gender] || '女'}
          </Form.Item>
          <Form.Item
            name="mobile"
            label="手机号"
            rules={[
              {
                validator: (_, value) => {
                  if (/^1\d{10}$/.test(value)) {
                    return Promise.resolve(true);
                  }
                  return Promise.reject(new Error('请输入正确的手机号码'));
                }
              }
            ]}
          >
            <Input
              placeholder="请输入手机号"
              type="tel"
              maxLength={11}
              onChange={(value) =>
                setPostData((draft) => {
                  draft.mobile = value;
                })
              }
            />
          </Form.Item>
          <Form.Item
            name="deptIds"
            label="所属部门"
            trigger="onConfirm"
            onClick={() => {
              setDepartmentPickerVisible(true);
            }}
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (postData.deptIds.length > 0) {
                    return Promise.resolve(true);
                  }
                  return Promise.reject(new Error('请选择所属部门'));
                }
              }
            ]}
          >
            {departmentList.current.length > 0 ? (
              departmentList.current.map((item: any) => item.name).join(', ')
            ) : (
              <span className="text-stone-300">请选择所属部门</span>
            )}
          </Form.Item>
          <Form.Item
            // name="classes"
            label="任教班级"
            trigger="onConfirm"
            onClick={() => {
              openClassPicker();
            }}
            rules={[{ required: true, message: '请选择任教班级' }]}
          >
            {selectedClass.length > 0 ? (
              selectedClass.map((item: any) => item.name).join(', ')
            ) : (
              <span className="text-stone-300">请选择任教班级</span>
            )}
          </Form.Item>
        </Form>
        <div className="align-center mt-2 flex justify-center">
          <div className="inline-block">
            <Checkbox checked={checked} onChange={setChecked}>
              我已阅读并同意
            </Checkbox>
          </div>
          <div
            className="inline-block text-base text-blue-600"
            onClick={() => {
              Dialog.alert({
                title: '隐私协议',
                content: (
                  <div>
                    <article
                      dangerouslySetInnerHTML={{
                        __html: contentHtml
                      }}
                      className="prose-xs text-left"
                    />
                  </div>
                )
              });
            }}
          >
            《隐私协议》
          </div>
        </div>
      </div>
      <Popup
        visible={classPickerVisible}
        onClose={() => {
          setClassPickerVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px'
        }}
      >
        <div className="p-6">
          <div className="mb-5 flex justify-between text-base">
            <div
              className="text-stone-400"
              onClick={() => setClassPickerVisible(false)}
            >
              取消
            </div>
            <div className="">请选择任教班级</div>
            <div className="text-blue-500" onClick={() => confirmClassPicker()}>
              确定
            </div>
          </div>
          {classData.map((gradeItem: any, index) => {
            return (
              <div className="mb-6" key={gradeItem.id}>
                <div className="">
                  <Checkbox
                    checked={gradeItem.checked}
                    onChange={(val) => selectParent(val, index)}
                  >
                    <div className="font-semibold text-gray-700">
                      {gradeItem.name}
                    </div>
                  </Checkbox>
                </div>

                <div className="ml-6">
                  {gradeItem.classList.map(
                    (classItem: any, classIndex: number) => {
                      return (
                        <Checkbox
                          key={classItem.id}
                          checked={classItem.checked}
                          onChange={(val) =>
                            selectChild(val, classIndex, index)
                          }
                          className="mr-2 pt-4"
                        >
                          <div className="text-stone-500">{classItem.name}</div>
                        </Checkbox>
                      );
                    }
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </Popup>
      <ActionSheet
        visible={genderSelectVisible}
        onClose={() => {
          setGenderSelectVisible(false);
        }}
        extra="请选择性别"
        actions={
          Object.entries(genderTypeMap).map(([key, value]) => ({
            text: value,
            key,
            onClick: () => onSelectGender(Number(key))
          })) as any
        }
      />
      <Popup
        visible={departmentPickerVisible}
        onClose={() => {
          setClassPickerVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh'
        }}
      >
        <div className="p-5">
          <div className="mb-5 flex items-center justify-between text-base">
            <div
              className="text-stone-400"
              onClick={() => setDepartmentPickerVisible(false)}
            >
              取消
            </div>
            <div className="">请选择所在部门</div>
            <div
              className="text-blue-500"
              onClick={() => confirmDepartmentPicker()}
            >
              确定
            </div>
          </div>
          {!!instId && (
            <DepartmentSelect
              instId={String(instId)}
              onSelect={(val: any[]) => {
                if (Array.isArray(val) && val.length > 0) {
                  departmentList.current = val.filter(
                    (item) => item.level !== 0
                  );
                }
              }}
            />
          )}
        </div>
      </Popup>
    </div>
  );
}

export default Page;
