.customBoxShadow{
  box-shadow:6px 6px 12px rgba(79, 79, 79, 0.15), -6px -6px 12px rgba(79, 79, 79, 0.15)
}
.customListAdmListBody :global(.adm-list-default .adm-list-body) {
  border-top: 0 !important;
  border-bottom: 0 !important;
}
.customListAdmListItem :global(.adm-list-item + .adm-list-item) {
  padding-top:20px  !important;
  padding-bottom:20px  !important;
  border-top: 1px solid #f1f1f1 !important;
}
.customListAdmListItemContent :global(.adm-list-item .adm-list-item-content) {
  border-top: 0 !important;
}
.customListAdmListItemTitle :global(.adm-list-item-title) {
  padding-bottom:20px  !important;
}
.customListAdmListItemContentArrow :global(.adm-list-item-content-arrow) {
  padding-top:50px  !important;
}
/* setup2 */
.customListAdm :global(.adm-list-card) {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin: 0;
}
.buttonAnimation {
  animation: breathe 1.5s ease-in-out infinite;
}
@keyframes breathe {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}