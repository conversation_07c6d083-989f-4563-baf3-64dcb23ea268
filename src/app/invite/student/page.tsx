'use client';

import {
  ActionSheet,
  Button,
  CascadePicker,
  Checkbox,
  DatePicker,
  Dialog,
  Form,
  Input,
  Toast,
} from 'antd-mobile';
import axios from 'axios';
import { format, getUnixTime } from 'date-fns';
import Head from 'next/head';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useImmer } from 'use-immer';

import {
  checkStudentInfo,
  createStudent,
  getClassList,
  getRoleList,
} from '@/api/invite';
import {
  PiCalculatorThin,
  PiCaretDownFill,
  PiCheckCircleFill,
  PiWarningCircleFill,
} from '@/components/Icons';
import { isPalmBaby } from '@/lib/utils';
import { customToDecimal, hinaTrack } from '@/utils';
import { weChatInit, wechatShare } from '@/utils/wechat';

import styles from './index.module.css';

export const dynamic = 'force-dynamic';

if (typeof document !== 'undefined') {
  document.title = '入园信息登记';
}

interface IGenderType {
  [key: string]: string; // 这里使用字符串索引签名，表示该接口可以包含任意键
}

const genderTypeMap: IGenderType = {
  2: '女',
  1: '男',
};
type CascadePickerOption = {
  label: string;
  value: string;
  children?: CascadePickerOption[];
};
// 转换班级数据结构
function convertToCascadePickerOptions(treeArray) {
  return treeArray.map((item) => {
    const option: CascadePickerOption = {
      label: item.name,
      value: item.id.toString(),
    };
    if (item.classList && item.classList.length > 0) {
      option.children = convertToCascadePickerOptions(item.classList);
    }
    return option;
  });
}

function Page() {
  const searchParams = useSearchParams();
  const instId = searchParams?.get('instId') || '';
  const classId = searchParams?.get('classId') || '';
  const className = searchParams?.get('className') || '';
  const instName = searchParams?.get('instName') || '';

  const isPalmBabyApp =
    typeof window !== 'undefined'
      ? isPalmBaby(window.location.hostname)
      : false;

  const [contentHtml, setContentHtml] = useState('');
  const [stepId, setStepId] = useState(1);
  const [isApprove, setIsApprove] = useState(false);
  const [isShowShare, setIsShowShare] = useState(false);
  const [checked, setChecked] = useState(false); // 隐私协议
  const [password, setPassword] = useState('');
  const [roleList, setRoleList] = useState<any[]>([]);
  const [initialValuesList, setInitialValuesList] = useState<any[]>([]);
  const [genderSelectVisible, setGenderSelectVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [classListData, setClassListData] = useState([]);

  const [postData, setPostData] = useImmer({
    name: '',
    birthday: getUnixTime(new Date()),
    gender: 1,
    reCode: '86',
    instId,
    classId,
  });
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const titles = ['监护人', '紧急联系人', '亲友团'];
  const tips = [
    '孩子的成长离不开父母的共同呵护,爸爸的陪伴与关爱, 和妈妈一样,对孩子的身心发展至关重要,缺一不可！',
    '在紧急情况下,如无法及时联系到爸爸妈妈，园所将优先联系紧急联系人,确保孩子的安全得到全方位守护！',
    '邀请更多亲友加入家庭组,随时关注并了解孩子在校的点滴情况,共同见证宝贝的成长！',
  ];
  const initClassPickerData = () => {
    getClassList({
      instId,
    }).then((res: any) => {
      if (Array.isArray(res.list)) {
        setClassListData(convertToCascadePickerOptions(res.list));
      }
    });
  };
  useEffect(() => {
    hinaTrack('studentInvite_index');
  }, []);
  useEffect(() => {
    if (instId) {
      getRoleList({
        instId,
      }).then((res: any) => {
        if (Array.isArray(res.list)) {
          if (res.list.length > 4) {
            setRoleList([
              [res.list[0], res.list[1]],
              [res.list[2], res.list[3]],
              res.list.slice(4),
            ]);
          } else {
            const twoDimensionalArrayTemp = res.list.reduce(
              (result: any[], item: any, index: number) => {
                if (index % 2 === 0) {
                  if (index === res.list.length - 1) {
                    result.push([{ ...item, mobile: '' }]);
                  } else {
                    result.push([
                      { ...item, mobile: '' },
                      { ...res.list[index + 1], mobile: '' },
                    ]);
                  }
                }
                return result;
              },
              [],
            );

            setRoleList(twoDimensionalArrayTemp);
          }
          const roleListTemp = res.list.map((item: any) => ({
            ...item,
            mobile: '',
          }));
          setInitialValuesList(roleListTemp);
        }
      });
      weChatInit();
      if (!classId) {
        initClassPickerData();
      }
    }

    // 获取隐私协议数据
    async function fetchData() {
      const response = await axios('/api/mdx?filename=privacy');
      console.log('🚀 ~ response:', response);
      const privacyContent = response.data.contentHtml;
      setContentHtml(privacyContent);
    }

    fetchData();
  }, [instId, classId]);

  const onFinishOne = () => {
    form.validateFields().then(() => {
      if (!postData.instId || !postData.classId) {
        Toast.show({
          content: '请求参数有误',
          icon: 'fail',
        });
        return;
      }
      checkStudentInfo({
        instId: postData.instId,
        classId: postData.classId,
        name: postData.name,
      }).then((res: any) => {
        hinaTrack('studentInvite_index_next');
        const { isExist, status } = res;
        if (isExist) {
          Toast.show(`当前班级已存在【${postData.name}】，请勿重复提交`);
        } else if (status === 1) {
          Toast.show(`【${postData.name}】已提交在待审核，请勿重复提交`);
        } else {
          setStepId(2);
        }
      });
    });
  };

  const onFinishTwo = (values: any) => {
    if (!checked) {
      Toast.show('请先阅读并同意隐私协议');
      return;
    }
    const data = values.items.filter((item: any) => item.mobile !== '');
    if (data.length < 2) {
      Toast.show('请至少填写两个亲人手机号码');
      return;
    }
    hinaTrack('studentInvite_index_submit');
    if (data.length <= 2) {
      Dialog.confirm({
        title: '温馨提示',
        content:
          '为了避免漏接、误接、甚至出现拐卖等安全事故，建议添加多个联系人，若出现孩子意外走失时，能够提供给公安系统进行大数据比对，增加孩子找回的可能性',
        onConfirm: () => {
          if (loading) {
            Toast.show('正在创建用户，请勿重复提交');
            return;
          }
          hinaTrack('studentInvite_index_submit_continue');
          submit();
        },
        onCancel: () => {
          hinaTrack('studentInvite_index_submit_more');
        },
        confirmText: '继续提交',
        cancelText: '添加更多亲人',
      });
      return;
    }
    submit();
  };

  const onSelectGender = (value: number) => {
    setPostData((draft) => {
      draft.gender = value;
    });
    setGenderSelectVisible(false);
  };

  const submit = () => {
    const parents = form2
      .getFieldsValue()
      .items.filter((item: any) => item.mobile !== '');
    if (parents.length >= 2) {
      const isMobileDuplicated = parents.some(
        (item: any, index: number, array: any[]) => {
          const duplicatedItems = array.filter(
            (i: any) => i.mobile === item.mobile,
          );
          return duplicatedItems.length > 1;
        },
      );

      if (isMobileDuplicated) {
        Toast.show('请勿填写重复的手机号码');
        return;
      }
    }
    const data = {
      ...postData,
      parents: parents.map((item: any) => ({
        mobile: item.mobile,
        relation: item.id,
      })),
    };
    setLoading(true);

    createStudent(data)
      .then((res: any) => {
        setLoading(false);

        const { mobile, isAudit, password, status } = res;
        if (Array.isArray(mobile) && mobile.length) {
          Toast.show(`${mobile.join(',')}手机号已存在`);
          return;
        }
        if (status === 1) {
          Toast.show(`【${postData.name}】已提交在待审核，请勿重复提交`);
          return;
        }
        Toast.show({ icon: 'success', content: '创建成功' });
        setPassword(password || '手机号后6位');
        setIsApprove(isAudit === true);
        setStepId(3);
      })
      .catch((err) => {
        console.log(err);
        setLoading(false);
      });
  };

  const share = () => {
    setIsShowShare(true);
    setTimeout(() => {
      setIsShowShare(false);
    }, 4000);
    const wxData = {
      title: '邀请您下载使用APP',
      desc: '一起守护孩子安全',
      link: 'https://kid-mobile.ancda.com/download', // TODO
      imgUrl:
        'https://unicorn-media.ancda.com/production/app/logo/logo-parents.png',
    };
    wechatShare(wxData);
  };

  const downloadApp = () => {
    hinaTrack('studentInvite_finish_download');
    const url = isPalmBabyApp
      ? 'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents'
      : 'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.parents';
    window.location.href = url;
  };

  return (
    <div className="h-screen bg-white">
      <Head>
        <title>邀请加入班级</title>
      </Head>
      <div className="bg-white">
        {stepId === 1 && (
          <div className="pt-4">
            <div
              className={`${styles.customBoxShadow} mx-4 rounded-lg bg-white p-4 py-6`}
            >
              <div>
                <div className="text-center text-lg">{instName}</div>
                <div className="text-sx my-2 text-[#333]">
                  亲爱的家长： <br />
                  为实现幼儿园教学、保育、家园沟通高效开展， 我园邀请你加入
                  {className}，请各位家长如实登记信息！
                </div>
              </div>
              <Form
                layout="vertical"
                form={form}
                className={`${styles.customListAdmListBody} ${styles.customListAdmListItem} ${styles.customListAdmListItemContent} ${styles.customListAdmListItemTitle}`}
                onFinish={onFinishOne}
              >
                <Form.Item
                  name="name"
                  rules={[{ required: true, message: '请填写姓名' }]}
                  label="宝贝姓名"
                >
                  <Input
                    placeholder="请填写姓名"
                    maxLength={15}
                    onChange={(value) =>
                      setPostData((draft) => {
                        draft.name = value;
                      })
                    }
                  />
                </Form.Item>
                <Form.Item
                  initialValue={1}
                  name="gender"
                  rules={[{ required: true, message: '请选择性别' }]}
                  label="性别"
                  className={styles.customListAdmListItemContentArrow}
                  onClick={() => {
                    setGenderSelectVisible(true);
                  }}
                  trigger="onConfirm"
                  arrow={<PiCaretDownFill fontSize={20} color="#999999" />}
                >
                  {genderTypeMap[postData.gender] || '请选择性别'}
                </Form.Item>
                <Form.Item
                  name="birthday"
                  label="出生日期"
                  rules={[{ required: true, message: '请选择出生日期' }]}
                  className={styles.customListAdmListItemContentArrow}
                  trigger="onConfirm"
                  onClick={(_, action) => {
                    action?.current?.open();
                  }}
                  arrow={<PiCalculatorThin fontSize={20} color="#999999" />}
                >
                  <DatePicker
                    min={new Date(2010, 0, 1)}
                    max={new Date()}
                    onConfirm={(value: Date) => {
                      setPostData((draft) => {
                        draft.birthday = getUnixTime(new Date(value));
                      });
                    }}
                  >
                    {(val: Date | null) =>
                      val ? (
                        format(new Date(val), 'yyyy-MM-dd')
                      ) : (
                        <span className="text-stone-300">请选择出生日期</span>
                      )
                    }
                  </DatePicker>
                </Form.Item>
                {!classId && (
                  <Form.Item
                    name="classId"
                    label="请选择班级"
                    rules={[{ required: true, message: '请选择班级' }]}
                    trigger="onConfirm"
                    // onClick={(_, action) => {
                    //   setClassPickerVisible(true);
                    // }}
                    className={styles.customListAdmListItemContentArrow}
                    onClick={(e, datePickerRef) => {
                      datePickerRef.current?.open(); // ⬅️
                    }}
                  >
                    <CascadePicker
                      title="选择班级"
                      options={classListData}
                      // visible={classPickerVisible}
                      // onClose={() => {
                      //   setClassPickerVisible(false);
                      // }}
                      // onConfirm={(val, extend) => {
                      //   if (extend.items.length === 2) {
                      //     console.log('onConfirm', val, extend.items);
                      //     console.log('🚀 ~ val[1]:', val[1]);
                      //     setCurrentClass(extend.items[1]);
                      //     setPostData((draft) => {
                      //       draft.classId = val[1];
                      //     });
                      //   }
                      // }}
                      onSelect={(val) => {
                        console.log('onSelect', val);
                        if (val.length === 2) {
                          setPostData((draft) => {
                            draft.classId = val[1];
                          });
                        }
                      }}
                    >
                      {(value) => {
                        console.log('🚀 ~ value:', value);
                        return Array.isArray(value) && value.length > 1 ? (
                          `${value[0]?.label || ''} - ${value[1]?.label || ''}`
                        ) : (
                          <span className="text-stone-300">请选择班级</span>
                        );
                      }}
                    </CascadePicker>
                  </Form.Item>
                )}
              </Form>
            </div>
            <div className="mt-10 px-10">
              <Button
                shape="rounded"
                type="submit"
                block
                color="primary"
                onClick={onFinishOne}
              >
                下一步（ {stepId} / 2 ）
              </Button>
            </div>
          </div>
        )}
        {stepId === 2 && roleList.length > 0 && (
          <div className="bg-white py-6 pt-4">
            <div className="px-6 text-lg font-semibold">请输入手机号</div>
            <div className="my-4 px-6 text-sm text-[#333]">
              孩子的成长离不开家人共同关爱。当发生紧急情况时，学校将通知紧急联系人。多填一个号码，就多一份触达！
              <p className="text-[#FF6767]">至少需要 2 个亲人手机号!</p>
            </div>
            <Form
              form={form2}
              onFinish={onFinishTwo}
              className={`${styles.customListAdmListItemContent} ${styles.customListAdm} ${styles.customListAdmListItem}`}
              footer={
                <div className="mt-10 ">
                  <div className="align-center my-2 flex justify-center">
                    <div className="inline-block">
                      <Checkbox checked={checked} onChange={setChecked}>
                        我已阅读并同意
                      </Checkbox>
                    </div>
                    <div
                      className="inline-block text-base text-blue-600"
                      onClick={() => {
                        Dialog.alert({
                          title: '隐私协议',
                          content: (
                            <div>
                              <article
                                dangerouslySetInnerHTML={{
                                  __html: contentHtml,
                                }}
                                className="prose-xs text-left"
                              />
                            </div>
                          ),
                        });
                      }}
                    >
                      《隐私协议》
                    </div>
                  </div>
                  <Button
                    shape="rounded"
                    type="submit"
                    block
                    color="primary"
                    loading={loading}
                  >
                    提交
                  </Button>
                  <div className="mt-3 text-center text-stone-400">
                    通知家庭成员下载登录APP，共同关注孩子成长
                  </div>
                </div>
              }
              initialValues={{
                items: initialValuesList,
              }}
            >
              <Form.Array name="items">
                {() =>
                  roleList.map((item, index) => {
                    return (
                      <div
                        key={`cl${index}`}
                        className={`${styles.customBoxShadow} m-4 rounded-lg border-2 bg-white p-4`}
                      >
                        <p className="border-b pb-4 text-center text-sm">
                          {titles[index]}
                        </p>
                        <p className="my-4 text-left text-sm text-[#FFA51E]">
                          {tips[index]}
                        </p>
                        {item.map((nodeItem: any, nodeIndex: any) => (
                          <Form.Item
                            key={`${index}${nodeIndex}`}
                            name={[
                              customToDecimal(`${index}${nodeIndex}`),
                              'mobile',
                            ]}
                            label={nodeItem.name}
                            rules={[
                              {
                                validator: (_, value) => {
                                  if (!value || /^1\d{10}$/.test(value)) {
                                    return Promise.resolve(true);
                                  }
                                  return Promise.reject(
                                    new Error('请输入正确的手机号码'),
                                  );
                                },
                              },
                            ]}
                          >
                            <Input
                              type="tel"
                              placeholder="请输入手机号"
                              maxLength={11}
                            />
                          </Form.Item>
                        ))}
                      </div>
                    );
                  })
                }
              </Form.Array>
            </Form>
          </div>
        )}
        {stepId === 3 &&
          (isApprove ? (
            <>
              <Image
                src="/images/invite/inviteBG.png"
                alt=""
                width="0"
                height="0"
                sizes="100vw"
                className="z-0 h-screen w-full object-cover"
              />
              <div className="absolute inset-0 flex h-screen flex-col items-center">
                <div
                  className="relative mb-4 mt-8 flex items-center justify-center"
                  style={{
                    width: '60px',
                    height: '60px',
                    borderRadius: '100%',
                  }}
                >
                  <div
                    className="absolute   inset-0 bg-white"
                    style={{
                      width: '30px',
                      height: '30px',
                      borderRadius: '100%',
                      zIndex: 0,
                      top: '15px',
                      left: '15px',
                    }}
                  />
                  <PiCheckCircleFill
                    fontSize={60}
                    style={{ zIndex: 1 }}
                    color={isPalmBabyApp ? '#17C5A6' : '#FECB30'}
                  />
                </div>
                <div className="mb-2 text-center text-lg font-semibold">
                  入园信息登记提交成功~
                </div>
                <div className="text-center leading-6">请耐心等待老师审核~</div>
                <div
                  className={`mt-[170px] flex w-[50%] items-center justify-center ${styles.buttonAnimation}`}
                >
                  <Button
                    shape="rounded"
                    type="submit"
                    color="primary"
                    block
                    onClick={() => downloadApp()}
                  >
                    提前下载App
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <>
              <Image
                src="/images/invite/inviteBG.png"
                alt=""
                width="0"
                height="0"
                sizes="100vw"
                className="z-0 h-screen w-full object-cover"
              />
              <div className="absolute inset-0 flex h-screen flex-col items-center">
                <div
                  className="relative mb-4 mt-8 flex items-center justify-center"
                  style={{
                    width: '60px',
                    height: '60px',
                    borderRadius: '100%',
                  }}
                >
                  <div
                    className="absolute   inset-0 bg-white"
                    style={{
                      width: '30px',
                      height: '30px',
                      borderRadius: '100%',
                      zIndex: 0,
                      top: '15px',
                      left: '15px',
                    }}
                  />
                  <PiCheckCircleFill
                    fontSize={60}
                    style={{ zIndex: 1 }}
                    color={isPalmBabyApp ? '#17C5A6' : '#FECB30'}
                  />
                </div>
                <div className="mb-2 text-center text-lg font-semibold">
                  入园信息登记提交成功~
                </div>
                <div className="w-3/4 text-left leading-6 text-[#333]">
                  就差 <span className="text-red-500">1</span>{' '}
                  步了，请打开App完成头像上传，
                  便于老师核对身份，确保孩子接送安全！
                </div>
                <div className="mt-[128px] w-3/4 rounded-lg bg-[#FFFFFF6A] px-[69px] py-[30px] leading-6">
                  您的APP登录账号为手机号
                  <br /> 登录密码是:{' '}
                  <span className="text-yellow-500">{password}</span>
                </div>
                <div className={`mt-8 w-1/2 ${styles.buttonAnimation}`}>
                  <Button
                    shape="rounded"
                    type="submit"
                    color="primary"
                    block
                    onClick={() => downloadApp()}
                  >
                    立即下载App
                  </Button>
                </div>
              </div>
              {isShowShare ? (
                <div className="absolute inset-0 h-screen bg-black/90 text-white">
                  <Image
                    src="/images/invite/wechat-guide.png"
                    alt=""
                    width="0"
                    height="0"
                    sizes="80vw"
                    className="absolute right-0 top-0 z-0 w-4/5 object-cover"
                  />
                  <div className="flex h-full flex-col items-center justify-center text-center">
                    <div className="mb-4 text-2xl">邀请家庭成员</div>
                    <div className="text-base">
                      <div>分享链接通知家庭成员下载登录APP</div>
                      <div>一起守护孩子安全</div>
                    </div>
                    <div className="mt-4 flex items-center justify-center text-stone-400">
                      <PiWarningCircleFill fontSize={20} color="#f87171" />
                      请尽快邀请亲友下载登录APP，24小时后链接失效
                    </div>
                  </div>
                </div>
              ) : null}
            </>
          ))}
      </div>
      <ActionSheet
        visible={genderSelectVisible}
        onClose={() => {
          setGenderSelectVisible(false);
        }}
        extra="请选择性别"
        cancelText="取消"
        actions={
          Object.entries(genderTypeMap).map(([key, value]) => ({
            text: value,
            key,
            onClick: () => onSelectGender(Number(key)),
          })) as any
        }
      />
    </div>
  );
}

export default Page;
