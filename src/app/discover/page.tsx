'use client';

import { Toast } from 'antd-mobile';
import { useEffect } from 'react';

import { hinaTrack } from '@/utils';

import styles from './page.module.css';
import { CopyToClipboard } from 'react-copy-to-clipboard';
const Page = () => {
  useEffect(() => {
    hinaTrack('machine_leafletopen');
  }, []);
  return (
    <div className="h-screen w-full">
      <img
        className="max-w-full"
        src="https://unicorn-media.ancda.com/prod/discover/branner.jpg"
        alt=""
      />
      <div className="fixed  bottom-0 w-full py-4">
        <div
          className={`flex w-full items-center justify-center text-center ${styles.copyLinkBox}`}
        >
          <CopyToClipboard
            text={
              'https://unicorn-media.ancda.com/production/app/apk/release-1.0.0%E6%8E%8C%E5%BF%83%E8%B5%84%E6%BA%90%E5%BA%93.apk'
            }
            onCopy={(text: string, result: boolean) => {
              hinaTrack('machine_leafletAndroid');
              Toast.show({ content: result ? '复制成功' : '复制失败' });
            }}
          >
            <div className={`flex-1 ${styles.copyLink}`}>
              点击复制一体机/电脑端下载地址（android）
            </div>
          </CopyToClipboard>
        </div>
        <div
          className={`flex w-full items-center justify-center text-center ${styles.copyLinkBox}`}
        >
          <CopyToClipboard
            text={
              'https://unicorn-media.ancda.com/production/app/apk/%E6%8E%8C%E5%BF%83%E8%B5%84%E6%BA%90%E5%BA%93_1.0.0.exe'
            }
            onCopy={(text: string, result: boolean) => {
              hinaTrack('machine_leafletWindowns');
              Toast.show({ content: result ? '复制成功' : '复制失败' });
            }}
          >
            <div className={`flex-1 ${styles.copyLink}`}>
              点击复制一体机/电脑端下载地址（windows）
            </div>
          </CopyToClipboard>
        </div>
      </div>
    </div>
  );
};
export default Page;
