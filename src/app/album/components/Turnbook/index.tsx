'use client'

import { forwardRef, useEffect, useImperativeHandle, useRef, useState, type JSX } from 'react';

import styles from '@/styles/album.module.css';
import { isBigScreen } from '@/utils';

import AudioPlayer, { type AudioPlayerRef } from '../audioPlayer';

let CANVAS_CONTAINER_WIDTH = 100;
if (typeof window !== 'undefined') {
  CANVAS_CONTAINER_WIDTH = isBigScreen()
    ? window.screen.width * 0.9
    : window.screen.width * 0.8;
}

const CANVAS_CONTAINER__HEIGHT = CANVAS_CONTAINER_WIDTH * 1.415;
const PAGE_WIDTH = CANVAS_CONTAINER_WIDTH;
const PAGE_HEIGHT = CANVAS_CONTAINER_WIDTH * 1.415;
const CANVAS_PADDING_HORIZONTAL = 0;
const CANVAS_PADDING_VERITICAL = 10;
let flips: any = [];
let PAGENUMBER = 0;
let MOVESTARTX = 0;
let MOVERENDX = 0;
let TOUCHSTARTTIME = 0;
let foldWidth = 0;
let touchDragging = false;
let currentPosition = 'left';
interface Props {
  children: React.ReactElement<any>;
  pageContainer: {
    current: any[];
  };
  pageNumber: number;
  startTurnpages: (pageIndex: number) => void;
  endTurnpages: (pageIndex: number) => void;
}
const musicList = [
  'https://edu-media.ancda.com/prod/archives/album/music/music1.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music2.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music3.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music4.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music5.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music6.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music7.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music8.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music9.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music10.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music11.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music12.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music13.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music14.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music15.mp3',
  'https://edu-media.ancda.com/prod/archives/album/music/music16.mp3',
];
const randomIndex = Math.floor(Math.random() * musicList.length);
const randomSong = musicList[randomIndex];

const Turnbook = (
  { children, pageContainer, pageNumber, startTurnpages, endTurnpages }: Props,
  ref: any,
): JSX.Element => {
  const pageflipCanvas = useRef<any>(null);
  const isDrawingRef = useRef<any>(null);
  const audioPlayerRef = useRef<AudioPlayerRef>(null);
  const pageContainerRef = useRef<any>(null);
  pageContainerRef.current = pageContainer.current;
  const [isDrawing, setIsDrawing] = useState(false); // 是否拖动
  const setDrawingRef = (val: boolean) => {
    setIsDrawing(val);
    isDrawingRef.current = val;
  };
  // 初始化画布位置及大小
  useEffect(() => {
    if (pageflipCanvas.current) {
      pageflipCanvas.current.width =
        CANVAS_CONTAINER_WIDTH + CANVAS_PADDING_HORIZONTAL * 2;
      pageflipCanvas.current.height =
        CANVAS_CONTAINER__HEIGHT + CANVAS_PADDING_VERITICAL * 2;
      pageflipCanvas.current.style.top = `${-CANVAS_PADDING_VERITICAL}px`;
      pageflipCanvas.current.style.left = `${-CANVAS_PADDING_HORIZONTAL}px`;
    }
  }, []);
  // 初始化filps翻页集合对象
  useEffect(() => {
    const pages = pageContainerRef.current;
    flips = [];
    const len = pages.length;
    if (len === 0) {
      return;
    }
    pages.forEach((item: any, index: number) => {
      if (item) {
        item.style.zIndex = len - index;
        item.style.transform = 'translate3d(0px, 0px, 0px)';
        item.getElementsByTagName('div')[0].style.transform =
          'translate3d(0px, 0px, 0px)';
        let flip = {
          progress: 1,
          target: 1,
          page: item,
          dragging: false,
        };
        if (index < PAGENUMBER) {
          item.style.transform = `translate3d(-${PAGE_WIDTH}px, 0px, 0px)`;
          item.getElementsByTagName('div')[0].style.transform =
            `translate3d(${PAGE_WIDTH}px, 0px, 0px)`;
          flip = {
            progress: -1,
            target: -1,
            page: item,
            dragging: false,
          };
        }
        flips.push(flip);
      }
    });
  }, [pageNumber]);
  // 开始执行渲染函数
  useEffect(() => {
    runRender();
  }, [isDrawing]);
  // 重复渲染
  const runRender = () => {
    render();
    isDrawingRef.current && requestAnimationFrame(runRender);
  };
  // 渲染
  function render() {
    pageflipCanvas.current
      ?.getContext('2d')
      .clearRect(
        0,
        0,
        pageflipCanvas.current.width,
        pageflipCanvas.current.height,
      );
    let isDrawing = false;
    flips.forEach((flip: any) => {
      if (flip.dragging) {
        flip.target = Math.max(Math.min(MOVERENDX / PAGE_WIDTH, 1), -1);
      }
      flip.progress += (flip.target - flip.progress) * 0.25;
      if (flip.dragging || Math.abs(flip.progress) < 0.9999) {
        drawFlip(flip);
        isDrawing = true;
      }
    });
    if (!isDrawing) {
      endTurnpages(PAGENUMBER);
    }
    setDrawingRef(isDrawing);
  }
  // 书页绘画
  function drawFlip(flip: any) {
    const context = pageflipCanvas.current?.getContext('2d');
    const strength = 1 - Math.abs(flip.progress);
    foldWidth = PAGE_WIDTH * 0.5 * (1 - flip.progress);
    const foldX = PAGE_WIDTH * flip.progress + foldWidth;
    const verticalOutdent = 20 * strength;
    const widthY = `${-Math.max(foldX, 0) + PAGE_WIDTH}px`;
    flip.page.style.transform = `translate3d(-${widthY}, 0px, 0px)`;
    flip.page.getElementsByTagName('div')[0].style.transform =
      `translate3d(${widthY}, 0px, 0px)`;
    context.save();
    context.translate(CANVAS_PADDING_HORIZONTAL + 2, CANVAS_PADDING_VERITICAL);
    // 阴影宽度计算
    const paperShadowWidth =
      PAGE_WIDTH * 0.5 * Math.max(Math.min(1 - flip.progress, 0.5), 0);
    const rightShadowWidth =
      PAGE_WIDTH * 0.5 * Math.max(Math.min(strength, 0.5), 0);
    const leftShadowWidth =
      PAGE_WIDTH * 0.5 * Math.max(Math.min(strength, 0.5), 0);
    // 勾勒出右侧阴影
    const rightShadowGradient = context.createLinearGradient(
      foldX,
      0,
      foldX + rightShadowWidth,
      0,
    );
    rightShadowGradient.addColorStop(0, `rgba(0,0,0,${strength * 0.2})`);
    rightShadowGradient.addColorStop(0.8, 'rgba(0,0,0,0.0)');
    context.fillStyle = rightShadowGradient;
    context.beginPath();
    context.moveTo(foldX, 0);
    context.lineTo(foldX + rightShadowWidth, 0);
    context.lineTo(foldX + rightShadowWidth, PAGE_HEIGHT);
    context.lineTo(foldX, PAGE_HEIGHT);
    context.fill();
    // 勾勒出左侧阴影
    const leftShadowGradient = context.createLinearGradient(
      foldX - foldWidth - leftShadowWidth,
      0,
      foldX - foldWidth,
      0,
    );
    leftShadowGradient.addColorStop(0, 'rgba(0,0,0,0.0)');
    leftShadowGradient.addColorStop(1, `rgba(0,0,0,${strength * 0.15})`);
    context.fillStyle = leftShadowGradient;
    context.beginPath();
    context.moveTo(foldX - foldWidth - leftShadowWidth, 0);
    context.lineTo(foldX - foldWidth, 0);
    context.lineTo(foldX - foldWidth, PAGE_HEIGHT);
    context.lineTo(foldX - foldWidth - leftShadowWidth, PAGE_HEIGHT);
    context.fill();
    // 画出书页
    const foldGradient = context.createLinearGradient(
      foldX - paperShadowWidth,
      0,
      foldX,
      0,
    );
    foldGradient.addColorStop(0.35, '#fafafa');
    foldGradient.addColorStop(0.73, '#eeeeee');
    foldGradient.addColorStop(0.9, '#fafafa');
    foldGradient.addColorStop(1.0, '#e2e2e2');
    context.fillStyle = foldGradient;
    context.strokeStyle = 'rgba(0,0,0,0.06)';
    context.lineWidth = 0.5;
    context.fillStyle = foldGradient;
    context.strokeStyle = 'rgba(0,0,0,0.06)';
    context.lineWidth = 0.5;
    context.beginPath();
    context.moveTo(foldX, 0);
    context.lineTo(foldX, PAGE_HEIGHT);
    context.quadraticCurveTo(
      foldX,
      PAGE_HEIGHT + verticalOutdent * 2,
      foldX - foldWidth,
      PAGE_HEIGHT + verticalOutdent,
    );
    context.lineTo(foldX - foldWidth, -verticalOutdent);
    context.quadraticCurveTo(foldX, -verticalOutdent * 2, foldX, 0);
    context.fill();
    context.stroke();
    context.restore();
  }
  // 开始触摸
  const onTouchStartHandler = (event: any) => {
    TOUCHSTARTTIME = new Date().getTime();
    MOVESTARTX = event.changedTouches[0].pageX;
    if (audioPlayerRef.current) {
      audioPlayerRef.current.play();
    }
  };
  // 滑动中
  const onTouchMoveHandler = (event: any) => {
    if (!isDrawingRef.current || touchDragging) {
      setDrawingRef(true);
      const { pageX } = event.changedTouches[0];
      const pageXClone = pageX;
      if (touchDragging) {
        MOVERENDX = pageXClone;
      } else {
        const diffdistance = pageXClone - MOVESTARTX;
        touchDragging = Math.abs(diffdistance) > 5;
        currentPosition = diffdistance > 0 ? 'left' : 'right';
        if (touchDragging) {
          const curpage = diffdistance > 0 ? PAGENUMBER - 1 : PAGENUMBER;
          const curfilp = flips[curpage];
          if (curfilp) {
            // 判断极限情况，第一页和最后一页不做处理
            if (
              (currentPosition === 'left' && PAGENUMBER === 0) ||
              (currentPosition === 'right' && curpage === flips.length - 1)
            ) {
              onTouchEndHandler(event);
            } else {
              startTurnpages(
                currentPosition === 'left' ? curpage : curpage + 1,
              );
              // 非极限情况直接标记
              curfilp.dragging = true;
            }
          } else {
            // 不做处理
            onTouchEndHandler(event);
          }
        }
      }
    }
  };

  // 滑动结束
  const onTouchEndHandler = (event: any) => {
    const dragitem = flips.find((i: any) => i.dragging);
    touchDragging = false;
    let stateI = null;
    if (dragitem) {
      const diffX = event.changedTouches[0].pageX - MOVESTARTX;
      const diffTime = new Date().getTime() - TOUCHSTARTTIME;
      Math.abs(diffX / diffTime) > 0.5 && (stateI = diffX > 0 ? 1 : -1);
      if (currentPosition === 'right') {
        stateI = MOVERENDX >= (5 * PAGE_WIDTH) / 6 ? 1 : -1;
      } else {
        stateI = MOVERENDX >= PAGE_WIDTH / 6 ? 1 : -1;
      }
      if (stateI === -1) {
        // 下一页
        dragitem.target = -1;
        PAGENUMBER =
          currentPosition === 'right'
            ? Math.min(PAGENUMBER + 1, flips.length - 1)
            : PAGENUMBER;
      } else {
        // 上一页
        dragitem.target = 1;
        PAGENUMBER =
          currentPosition === 'left' ? Math.max(PAGENUMBER - 1, 0) : PAGENUMBER;
      }
      dragitem.dragging = false;
    }
  };
  // 暴露给外部使用
  useImperativeHandle(
    ref,
    () => ({
      skipPage,
    }),
    [],
  );
  // 跳页
  const skipPage = (value: number) => {
    // 底部滑动条滚时重置参数
    flips.forEach((item: any, i: number) => {
      if (i < value) {
        item.page.style.transform = `translate3d(-${PAGE_WIDTH}px, 0px, 0px)`;
        item.page.getElementsByTagName('div')[0].style.transform =
          `translate3d(${PAGE_WIDTH}px, 0px, 0px)`;
        item.progress = -1;
        item.target = -1;
        item.dragging = false;
      } else {
        item.page.style.transform = 'translate3d(0px, 0px, 0px)';
        item.page.getElementsByTagName('div')[0].style.transform =
          'translate3d(0px, 0px, 0px)';
        item.progress = 1;
        item.target = 1;
        item.dragging = false;
      }
    });
    PAGENUMBER = value;
  };
  return (
    <div
      onTouchMove={onTouchMoveHandler}
      onTouchEnd={onTouchEndHandler}
      onTouchStart={onTouchStartHandler}
      className={isBigScreen() ? styles.book : styles.smallBook}
    >
      <canvas
        className={styles.pageflipCanvas}
        ref={ref => {
          (pageflipCanvas.current = ref);
        }}
      />
      {children}
      <AudioPlayer ref={audioPlayerRef} musicUrl={randomSong} />
    </div>
  );
};
export default forwardRef(Turnbook);
