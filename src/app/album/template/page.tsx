'use client';

import Konva from 'konva';
import React, { useLayoutEffect, useRef, type JSX } from 'react';

import { initData } from '@/lib/initTemplate';
import styles from '@/styles/template.module.css';
import {
  addClickEvent,
  generateString,
  getMessage,
  postMessage,
} from '@/utils';

Konva.pixelRatio = 1.8; // 降低分辨率，解决鸿蒙OS webview卡死问题

const sceneWidth = 2480;
const sceneHeight = 3508;

const App = (): JSX.Element => {
  const currentModifyNode: any = useRef(null);
  const stageDataRef: any = useRef(null);
  const stageRef: any = useRef(null);

  useLayoutEffect(() => {
    getMessage(onMessage);
    // 通知RN页面加载完成
    postMessage({ loaded: true });
    // 模拟数据
    // initData({
    //   type: 'myTeacher3',
    //   name: '老师名字',
    //   avatar:
    //     'https://edu-media.ancda.com/prod/archives/album/common/teacher_img.png',
    //   intro: '老师简介'
    // }).then((res) => {
    //   console.log('vvv', res)
    //   initStage(res)
    // })
  }, []);

  const initStage = (data: any) => {
    if (typeof data !== 'object' || Object.keys(data).length === 0) {
      return;
    }

    const stage = Konva.Node.create(JSON.stringify(data), stageRef.current);

    // 重置画布大小为手机大小
    function fitStageIntoParentContainer() {
      const containerWidth = stageRef.current.offsetWidth;
      const scale = Number((containerWidth / sceneWidth).toFixed(4));
      stage.width(sceneWidth * scale);
      stage.height(sceneHeight * scale);
      stage.scale({ x: scale, y: scale });
    }

    fitStageIntoParentContainer();
    window.addEventListener('resize', fitStageIntoParentContainer);

    // 可编辑的图片添加点击事件
    stage.find('Image').forEach((node: any) => {
      const imageUrl = node.getAttr('source');
      const img = new Image();
      img.src = imageUrl;
      img.onload = () => {
        node.image(img);
      };

      const isEditable = node.getAttr('isEditable');
      if (isEditable) {
        node.setAttrs({
          id: generateString(8),
        });
        currentModifyNode.current = node.attrs;
        addClickEvent(node, false, () => {
          const id = node.getAttr('id');
          const width = node.getAttr('width');
          const height = node.getAttr('height');
          const editId = node.getAttr('editId');
          const data = {
            id,
            editId,
            width,
            height,
            source: imageUrl,
            type: 'image',
          };
          postMessage(data);
        });
      }
    });
    // 可编辑的文字添加点击事件
    stage.find('Text').forEach((node: any) => {
      const isEditable = node.getAttr('isEditable');
      if (isEditable) {
        node.setAttrs({
          id: generateString(8),
        });
        currentModifyNode.current = node.attrs;
        addClickEvent(node, false, () => {
          const id = node.getAttr('id');
          const text = node.getAttr('text');
          const maxLength = node.getAttr('maxLength');
          const editId = node.getAttr('editId');
          const data = {
            id,
            editId,
            text,
            maxLength,
            type: 'text',
          };
          postMessage(data);
        });
      }
    });

    stageDataRef.current = stage;
  };

  // 获取到RN的通知
  const onMessage = (event: any) => {
    try {
      const data = JSON.parse(event.data);
      console.log('获取到RN的通知 ------- ', JSON.stringify(data));
      // 初始化数据
      if (data.initData) {
        initData(data.initData).then((res) => {
          initStage(res);
        });
      }

      const { id, source, text } = data;
      if (id) {
        const node = stageDataRef.current.findOne(`#${id}`);
        // 图片
        if (source) {
          node.setAttrs({
            source,
          });
          const img = new Image();
          img.src = source;
          img.onload = () => {
            node.image(img);
          };
        }
        // 文字
        if (text) {
          node.setAttrs({
            text,
          });
        }
      }
    } catch (error) {
      console.log('onMessage', error);
    }
  };

  return (
    <div className={styles.page}>
      <div ref={stageRef} />
    </div>
  );
};

export default App;
