'use client';

import Konva from 'konva';
import { useLayoutEffect, useRef, type JSX } from 'react';

import { templates } from '@/lib/template';
import styles from '@/styles/template.module.css';
import {
  addClickEvent,
  generateString,
  getMessage,
  postMessage,
} from '@/utils';

Konva.pixelRatio = 1.8; // 降低分辨率，解决鸿蒙OS webview卡死问题

const sceneWidth = 2480;
const sceneHeight = 3508;

type dataType = {
  title: string;
  type: number;
  template: string; // 模板类型
  data: object; // 排版好的json
  extendData: object | Array<any>; // 需要处理的原始数据
};

const App = (): JSX.Element => {
  const stageDataRef: any = useRef(null);
  const stageRef: any = useRef(null);

  useLayoutEffect(() => {
    getMessage(onMessage);
    // 通知RN页面加载完成
    postMessage({ loaded: true });
    // 模拟数据
    // initData({
    //   type: 'myTeacher3',
    //   name: '老师名字',
    //   avatar:
    //     'https://edu-media.ancda.com/prod/archives/album/common/teacher_img.png',
    //   intro: '老师简介'
    // }).then((res) => {
    //   console.log('vvv', res)
    //   initStage(res)
    // })
  }, []);

  const initStage = (data: dataType) => {
    let d = {};
    const { data: templateData } = data;
    if (
      typeof templateData !== 'object' ||
      Object.keys(templateData).length === 0 ||
      data.template === 'photoGallery'
    ) {
      d = templates[data.template];
    } else {
      d = templateData;
    }
    if (typeof d !== 'object' || Object.keys(d).length === 0) {
      return;
    }
    const stage = Konva.Node.create(JSON.stringify(d), stageRef.current);
    // 重置画布大小为手机大小
    function fitStageIntoParentContainer() {
      const containerWidth = stageRef.current.offsetWidth;
      const scale = Number((containerWidth / sceneWidth).toFixed(4));
      stage.width(sceneWidth * scale);
      stage.height(sceneHeight * scale);
      stage.scale({ x: scale, y: scale });
    }

    fitStageIntoParentContainer();
    window.addEventListener('resize', fitStageIntoParentContainer);

    // 可编辑的图片添加点击事件
    stage.find('Image').forEach((node: any) => {
      const imageUrl = node.getAttr('source');
      const img = new Image();
      img.src = imageUrl;
      img.onload = () => {
        node.image(img);
      };

      const editable = node.getAttr('editable');
      if (editable) {
        node.setAttrs({
          id: generateString(8), // 生成随机字符串，方便编辑完更新对应节点
        });
        addClickEvent(node, false, () => {
          const attrs = node.getAttrs();
          postMessage(attrs);
        });
      }
    });
    // 可编辑的文字添加点击事件
    stage.find('Text').forEach((node: any) => {
      const editable = node.getAttr('editable');
      const id = node.getAttr('id');

      if (editable) {
        if (!id) {
          node.setAttrs({
            id: generateString(8),
          });
        }
        addClickEvent(node, false, () => {
          console.log('click');
          const attrs = node.getAttrs();
          postMessage(attrs);
        });
      }
    });

    // 照片墙
    if (data.template === 'photoGallery' && Array.isArray(data.extendData)) {
      let photoSize = 400;
      let margin = 100;
      let textHeight = 50;
      const borderWidth = 20;
      const marginTop = 280; // 顶部距离，放标题
      let fontSize = 60;
      console.log('data.extendData.length', data.extendData.length);
      if (data.extendData.length <= 12) {
        photoSize = 550;
        margin = 100;
      } else if (data.extendData.length <= 20) {
        photoSize = 420;
        margin = 80;
      } else if (data.extendData.length <= 30) {
        photoSize = 340;
        margin = 50;
      } else if (data.extendData.length <= 36) {
        photoSize = 280;
        margin = 50;
        textHeight = 80;
      } else if (data.extendData.length <= 42) {
        photoSize = 280;
        margin = 50;
      } else if (data.extendData.length <= 48) {
        photoSize = 260;
        margin = 40;
        fontSize = 48;
      }
      const groups = stage.find((node: any) => {
        return node.getType() === 'Group';
      });

      groups.forEach((node: any) => {
        const isContent = node.attrs.type && node.attrs.type === 'content';
        if (isContent) {
          const group = node;
          console.log('group height', group.height());
          const photosPerRow = Math.floor(group.width() / (photoSize + margin));
          const maxRows = Math.floor(group.height() / (photoSize + textHeight));

          // 添加照片到照片墙
          const addPhotoToWall = (
            imageUrl: string,
            name: string,
            index: number,
          ) => {
            const row = Math.floor(index / photosPerRow);
            const col = index % photosPerRow;
            console.log('row', row);
            console.log('maxRows', maxRows);
            if (row >= maxRows) {
              console.error('照片墙已满，无法添加更多照片！');
              return;
            }

            const x = col * (photoSize + margin);
            const y = row * (photoSize + margin + textHeight);

            const g = new Konva.Group({
              x,
              y: y + marginTop,
              width: photoSize,
              height: photoSize + textHeight,
            });

            const text = new Konva.Text({
              width: photoSize,
              height: textHeight,
              fontSize,
              fill: '#333',
              x: 0,
              y: photoSize + 20,
              align: 'center',
              lineHeight: 1.5,
              text: name,
            });
            const imageGroup = new Konva.Group({
              x: 0,
              y: 0,
              width: photoSize,
              height: photoSize,
              rounded: true,
              // clipFunc: function (ctx: any) {
              //   ctx.arc(
              //     photoSize / 2,
              //     photoSize / 2,
              //     photoSize / 2,
              //     0,
              //     Math.PI * 2,
              //     false
              //   )
              // }
            });

            const box = new Konva.Rect({
              x: 0,
              y: 0,
              width: photoSize,
              height: photoSize,
              fill: '#FFF',
              shadowColor: 'black',
              shadowBlur: 20,
              shadowOffset: { x: 10, y: 10 },
              shadowOpacity: 0.1,
              // stroke: 'black',
              // strokeWidth: 4
            });

            imageGroup.add(box);

            Konva.Image.fromURL(imageUrl, (image: any) => {
              image.setAttrs({
                x: borderWidth,
                y: borderWidth,
                source: imageUrl,
                width: photoSize - borderWidth * 2,
                height: photoSize - borderWidth * 2,
              });

              imageGroup.add(image);
              g.add(imageGroup);
              g.add(text);
              group.add(g);
            });
          };

          Array.isArray(data.extendData) &&
            data.extendData.forEach((item, index) => {
              if (item.faceImgUrl) {
                addPhotoToWall(item.faceImgUrl, item.name, index);
              }
            });
        }
      });
    }

    stageDataRef.current = stage;
    if (
      (data.template === 'photoGallery' && Array.isArray(data.extendData)) ||
      data.template === 'groupPhoto' ||
      data.template === 'teacherWish'
    ) {
      setTimeout(() => {
        const objectData = stageDataRef.current.toObject();
        const jsonData = JSON.stringify(objectData);
        postMessage({
          modify: true,
          data: jsonData,
        });
      }, 2000);
    }
  };

  // 获取到RN的通知
  const onMessage = (event: any) => {
    try {
      const data = JSON.parse(event.data);

      console.log('获取到RN的通知 ------- ', JSON.stringify(data));

      // 初始化画布数据
      if (data.data) {
        initStage(data);
      }

      // 处理修改数据
      const { id, source, text, url, mediaType } = data;
      // 根据ID更新对应的节点
      if (id) {
        const node = stageDataRef.current.findOne(`#${id}`);
        // 图片
        if (source) {
          node.setAttrs({
            source,
          });
          if (mediaType) {
            node.setAttrs({
              url,
              mediaType,
              width: 1600,
              height: 900,
            });
          }

          const img = new Image();
          img.src = source;
          img.onload = () => {
            node.image(img);
          };
          img.onerror = () => {
            // 视频封面生成需要时间，首先展示默认封面
            const defaultImg = new Image();
            defaultImg.src =
              'https://edu-media.ancda.com/prod/archives/album/common/default-video-cover-width.png';
            defaultImg.onload = () => {
              node.image(defaultImg);
            };
          };
          // 添加老师祝福视频二维码
          if (mediaType && url) {
            stageDataRef.current.find('Image').forEach((imageNode: any) => {
              const mediaType = imageNode.getAttr('mediaType');
              if (mediaType === 'qrcode') {
                const qrcodeImageUrl = `https://crm.ancda.com/api/qrcode?url=${url}`;
                imageNode.setAttrs({
                  source: qrcodeImageUrl,
                });
                const img = new Image();
                img.src = qrcodeImageUrl;
                img.onload = () => {
                  imageNode.image(img);
                };
              }
            });
          }
        }
        // 文字
        if (text) {
          node.setAttrs({
            text,
          });
        }
        const objectData = stageDataRef.current.toObject();
        const jsonData = JSON.stringify(objectData);
        postMessage({
          modify: true,
          data: jsonData,
        });
      }
    } catch (error) {
      console.log('onMessage', error);
    }
  };

  return (
    <div className={styles.page}>
      <div ref={stageRef} />
    </div>
  );
};

export default App;
