'use client';

import React from 'react';

function faceNotice() {
  const data = {
    contentList: {
      title: '采集时请注意以下几点',
      li: [
        '1、首先保证每个学生独立拍照',
        '2、保证学生五官有足够的光照',
        '3、保证学生五官正向面对镜头',
        '4、保证镜头对焦到学生五官上',
        '5、保证学生五官填充预览框',
        '6、保证学生背景干净 至少无其他人物干扰',
        '7、保证学生没有太大的动作',
      ],
    },
    imgList: [
      {
        title: '画面模糊',
        url: '/images/growthArchives/ava1.png',
      },
      {
        title: '光线不足',
        url: '/images/growthArchives/ava2.png',
      },
      {
        title: '拍摄不全',
        url: '/images/growthArchives/ava3.png',
      },
      {
        title: '非宝宝照片',
        url: '/images/growthArchives/ava4.png',
      },
    ],
  };
  return (
    <div>
      <div className="p-4">
        <div className="mt-4 text-base font-bold">亲爱的老师：</div>
        <p className="mt-2">
          为了更精准的将动态照片按学生智能分类，要记得给学生 人脸采集哦！
        </p>
        <div className="flex flex-col items-center">
          <img
            className="max-w-full"
            src="/images/growthArchives/isTrueImg.png"
            alt=""
          />
          <div className="mt-1 flex items-center justify-center">
            <img
              src="/images/growthArchives/isTrueIcon.png"
              alt=""
              className="isTrueIcon"
            />
            <div className="ml-4 font-bold">正确示范</div>
          </div>
        </div>
        <div className="mt-8 flex flex-col rounded-md bg-gray-50 p-5">
          <div className="mb-2 text-base font-bold">
            {data.contentList.title}
          </div>
          {data.contentList.li.map((item, index) => {
            return (
              <p key={`item${index}`} className=" mb-1 text-gray-600">
                {item}
              </p>
            );
          })}
        </div>
        <div className="mt-8">
          <div className="mb-2 text-base font-bold">不合格照片示例</div>
          <div className="flex flex-wrap justify-around">
            {data.imgList.map((item, index) => {
              return (
                <div
                  key={index.toString()}
                  className="flex w-1/2 flex-col items-center"
                >
                  <img src={item.url} alt="" className="imgBox" />
                  <div>{item.title}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <style jsx>{`
        .container {
          background: #9ed7f8;
        }
        .isTrueIcon {
          width: 24px;
          height: 24px;
        }
        .img-box {
          width: 260px;
          height: 260px;
        }
        .down-img {
          background: #6bbfa4;
        }
      `}</style>
    </div>
  );
}

export default faceNotice;
