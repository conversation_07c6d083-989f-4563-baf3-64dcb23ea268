'use client';

import { useSearchParams } from 'next/navigation';
import React from 'react';

export const dynamic = 'force-dynamic';

const parentContent = [
  {
    id: 1,
    title: '第一部分',
    content: '成长档案编辑',
    children: [
      {
        id: 1,
        title: '添加内容',
        contentLi: [
          '在档案内您可通过添加记录按钮选择记录方式。',
          '1.1、从动态导入记录方式：您可从所有发布动态中选择任意一条加入档案。',
          '1.2、直接记录方式：按要求输入内容即可完成内容的添加。',
        ],
      },
      {
        id: 2,
        title: '修改内容',
        contentLi: [
          '在档案内如果对当前页面内容不满意，可通过修改按钮对此条记录进行修改/删除。家长不可以编辑老师记录内容哦。',
        ],
        img: '/images/growthArchives/img5.png',
      },
    ],
  },

  {
    id: 2,
    title: '第二部分',
    content: '常见问题',
    children: [
      {
        id: 1,
        title: '什么是成长档案',
        contentLi: [
          '成长档案为老师家长协同合作记录孩子在园时光、亲子时光、评价报告等内容，自动排版成书籍，是孩子珍贵的童年回忆。',
          '内容由老师记录和家长记录两部分组成。',
          '老师记录的内容放入成长档案后，会按时间顺序在主题中进行展示。',
          '家长记录的内容，会按时间顺序自动归纳至家庭时光主题中。',
        ],
        img: '/images/growthArchives/img4.png',
      },
      {
        id: 2,
        title: '实物档案是什么样的',
        contentLi: [
          '实物档案分精装版、珍藏版，具体可在档案列表点击右上角了解详情',
        ],
        img: '/images/growthArchives/img6.png',
      },
      {
        id: 3,
        title: '如何改变已上传图片顺序？',
        contentLi: ['您可在添加/编辑记录页长按已上传的图片拖动改变顺序'],
        img: '',
      },
      {
        id: 4,
        title: '系统提示成长档案指定页未完成无法打印',
        contentLi: [
          '为了保证打印质量，点击打印时系统会校验档案是否有未填充内容的页面，若有需要家长/老师补充页面内容。',
        ],
        img: '',
      },
      {
        id: 5,
        title: '系统提示成长档案页数不足无法打印',
        contentLi: [
          '因为装订工艺的限制，成长档案36页以上才可以进行制作，您可以通过动态导入/直接记录方式在档案里补充一些内容使页数达到要求即可。',
        ],
        img: '',
      },
      {
        id: 6,
        title: '成长档案购买前需要检查哪些内容',
        contentLi: [
          '提交打印下单后成长档案无法再次进行编辑操作，一定要确认内容完整、正确且全部编辑完成再提交打印哦。检查无误后请于本学期打印截止时间之前提交打印',
        ],
        img: '',
      },
      {
        id: 7,
        title: '历史学期档案可再次编辑吗？',
        contentLi: [
          '历史学期档案不支持编辑，请家长务必在本学期结束前完成档案内容',
        ],
        img: '',
      },
      {
        id: 8,
        title: '成长档案上传图片要求',
        contentLi: [
          '请尽量使用原图、高清图上传，上传的照片保持光线明亮；不要使用从朋友圈、空间保存的图片去上传。',
        ],
        img: '',
      },
      {
        id: 9,
        title: '历史档案可以再次下单购买吗',
        contentLi: ['可以，在本学期开放的打印时间内提交打印就可以啦。'],
        img: '',
      },
      {
        id: 10,
        title: '成长档案什么时候发货',
        contentLi: [
          '成长档案将于提交订单后14天后发货（过年会推迟发货），发货后您可以在APP-我的-我的订单-订单详情中查看物流信息。',
        ],
        img: '',
      },
      {
        id: 11,
        title: '成长档案收货地址可修改吗？',
        contentLi: [
          '目前不支持修改，下单后统一发送至学校，再由学校派发至家长。',
        ],
        img: '',
      },
      {
        id: 12,
        title: '成长档案价格',
        contentLi: [
          '成长档案价格是根据页数来计算的，精装版（36页）158元起，超过36页每页加2.5元；珍藏版（36页）198元起，超过36页每页加3元。',
          '例如精装版（60页）售价为：',
          '158（36页的费用）+60（37页到60页之间的费用）=218元',
        ],
        img: '',
      },
    ],
  },
];
const teacherContent = [
  {
    id: 1,
    title: '第一部分',
    content: '成长档案编辑',
    children: [
      {
        id: 1,
        title: '档案设置',
        contentLi: [
          '老师可在班级幼儿列表页找到档案设置入口，此处可设置班级 档案主题目录、我的老师、评价模板内容。若您是园长，您还 可在成长档案首页的档案设置选择需要显示在全园档案的页面 类型。',
        ],
        img: '/images/growthArchives/img1.png',
      },
      {
        id: 2,
        title: '编辑档案',
        contentLi: [
          '2.1、在档案内您可通过添加记录按钮选择记录方式。',
          '2.1.1、从动态导入记录方式：您可从所有发布动态中选择任 意一条加入档案。',
          '2.1.2、直接记录方式：按要求输入内容即可完成内容的添加。',
          '2.1.3、综合素质评价报告：您可通过此方式对幼儿进行成长 评价生成报告。',
          '2.2、在档案内如果对当前页面内容不满意，可通过修改按钮 对此条记录进行修改/删除。老师不可以编辑家长记录内容哦。',
        ],
        img: '/images/growthArchives/img2.png',
      },
    ],
  },
  {
    id: 2,
    title: '第二部分',
    content: '常见问题',
    children: [
      {
        id: 1,
        title: '成长档案需要家长参与吗？',
        contentLi: [
          '成长档案默认显示需要家长编辑的内容，如家长寄语等，若园 区不需要可在档案设置中关闭此页面的显示开关。',
        ],
        img: '/images/growthArchives/img3.png',
      },
      {
        id: 2,
        title: '什么是主题设置？',
        contentLi: [
          '主题目录指班级老师根据每学期的教学活动梳理的大纲，档案 内添加的内容会按照主题进行归纳，让档案内容更有层次。系 统预设6个主题，皆可编辑使用；家长添加的内容会按时间顺 序自动归纳至家庭时光。',
        ],
        img: '/images/growthArchives/img4.png',
      },
      {
        id: 3,
        title: '园长操作档案设置与老师操作档案设置有什么不同？',
        contentLi: [
          '园长操作的档案设置为设置全园幼儿档案的通用页显示情况， 如我的幼儿园、我的老师等等。老师操作的档案设置为设置 班级档案的有关内容呈现。',
        ],
        img: '',
      },
      {
        id: 4,
        title: '如何改变已上传图片顺序？',
        contentLi: ['您可在添加/编辑记录页长按已上传的图片拖动改变顺序'],
        img: '',
      },
      {
        id: 5,
        title: '如何下载电子档档案？',
        contentLi: [
          '您可点击页面的导出按钮，系统会自动给您导出PDF档案文件，导出完成可下载保存。',
        ],
        img: '',
      },
      {
        id: 6,
        title: '历史学期档案还能编辑吗？',
        contentLi: [
          '历史学期档案不可再次编辑、但不影响导出、分享档案；老师们要及时在本学期结束前完成档案的制作哦；若幼儿园的档案为每年一本，您可在第二学期再开始档案的制作。',
        ],
        img: '',
      },
      {
        id: 7,
        title: '当前学期的某些小朋友的档案为什么不能编辑了？',
        contentLi: [
          '每学期末的前两个星期为开放档案下单打印时间，若档案不能继续编辑了，可能是家长操作下单打印了哦',
        ],
        img: '',
      },
      {
        id: 8,
        title: '成长档案上传图片要求？',
        contentLi: [
          '请尽量使用原图、高清图上传，上传的照片保持光线明亮；不要使用从朋友圈、空间保存的图片去上传。',
        ],
        img: '',
      },
    ],
  },
];
export default function Page() {
  const searchParams = useSearchParams();
  const type = searchParams?.get('type') || '1';
  const contentList = type === '1' ? parentContent : teacherContent;
  return (
    <div>
      <div>
        <div className="p-2">
          <img
            src="/images/growthArchives/banner.png"
            alt=""
            className="w-100"
          />
        </div>
        {contentList.map((item, index) => (
          <div key="item.id" className="p-4">
            <div className="part-title itens-center flex justify-start">
              <span>{item.title}:</span>
              <span className="content">{item.content}</span>
            </div>
            {item.children.map((n, i) => (
              <div key="n.id" className="mt-5 rounded-lg bg-gray-50 p-5">
                <div className="mb-2 flex flex-row  items-center">
                  <div className="title-num">{n.id}</div>
                  <div className="ml-2 flex-1 text-base font-bold leading-5	">
                    {n.title}
                  </div>
                </div>
                {n.contentLi.map((text, index) => (
                  <p key="text.id" className="prose">
                    {text}
                  </p>
                ))}
                <div v-if="n.img" className="itens-center flex justify-center">
                  <img className="mt-2" src={n.img} alt="" />
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      <style jsx>{`
        .part-title {
          position: relative;
          width: 330px;
          font-size: 20px;
          font-weight: 600;
          display: inline;
          z-index: 0;
          .content {
            margin-left: 20px;
          }

          &:after {
            content: '';
            position: absolute;
            z-index: -1;
            top: 60%;
            left: -0.1em;
            right: -0.1em;
            bottom: -10%;
            transition: top 200ms cubic-bezier(0, 0.8, 0.13, 1);
            background-color: #daf5ec;
          }
        }
        .title-num {
          background: #5ac6a7;
          width: 24px;
          height: 24px;
          line-height: 24px;
          color: #fff;
          border-radius: 50%;
          font-size: 16px;
          text-align: center;
        }
      `}</style>
    </div>
  );
}
