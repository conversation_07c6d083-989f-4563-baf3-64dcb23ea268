'use client';

import React, { useState } from 'react';

export default function Page() {
  const [active, setActive] = useState(0);
  const data = {
    active: 1,
    bannerUrl: '/images/growthArchives/specification/banner.png',
    chooseTab: '',
    isChoose: '/images/growthArchives/specification/line1.png',
    notChoose: '/images/growthArchives/specification/line2.png',
    spe1ImgList: [
      '/images/growthArchives/specification/spe1Img1.png',
      '/images/growthArchives/specification/spe1Img2.png',
      '/images/growthArchives/specification/spe1Img3.png',
      '/images/growthArchives/specification/spe1Img4.png',
      '/images/growthArchives/specification/spe1Img5.png',
    ],
    spe2ImgList: [
      '/images/growthArchives/specification/spe2Img1.png',
      '/images/growthArchives/specification/spe2Img2.png',
      '/images/growthArchives/specification/spe1Img3.png',
      '/images/growthArchives/specification/spe1Img4.png',
      '/images/growthArchives/specification/spe1Img5.png',
    ],
  };
  return (
    <div>
      <div>
        <img src={data.bannerUrl} className="w-full" alt="" />
      </div>
      <div className="flex">
        <img
          src="/images/growthArchives/specification/spe1.png"
          alt=""
          onClick={() => setActive(0)}
          className="w-1/2"
        />
        <img
          src="/images/growthArchives/specification/spe2.png"
          alt=""
          onClick={() => setActive(1)}
          className="w-1/2"
        />
      </div>
      {active === 0 ? (
        <div>
          <div className="flex">
            <img src={data.isChoose} alt="" className="w-1/2" />
            <img src={data.notChoose} alt="" className="w-1/2" />
          </div>
          <div className="mt-3">
            <div className="flex flex-col">
              {data.spe1ImgList.map((img, index) => (
                <img key={`img1${index}`} src={img} alt="" className="w-full" />
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div>
          <div className="flex">
            <img src={data.notChoose} alt="" className="w-1/2" />
            <img src={data.isChoose} alt="" className="w-1/2" />
          </div>
          <div className="mt-3">
            <div className="flex flex-col">
              {data.spe2ImgList.map((img, index) => (
                <img key={`img2${index}`} src={img} alt="" className="w-full" />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
