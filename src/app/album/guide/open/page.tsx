'use client';

import React from 'react';

export default function page() {
  return (
    <div className="container">
      <div>
        <img src="/images/growthArchives/top-part.png" alt="" />
      </div>
      <div className="flex flex-col items-center justify-center p-4">
        <video
          id="video"
          className="overflow-hidden rounded-lg border-8 border-white"
          poster="https://edu-media.ancda.com/prod/archives/app/video/introduction-cover.jpg"
          controls
          preload="none"
          webkit-playsinline="true"
          x5-video-player-type="h5"
          x5-video-player-fullscreen="false"
          width="100%"
          height="100%"
        >
          <source
            src="https://edu-media.ancda.com/prod/archives/app/video/introduction.mp4"
            type="video/mp4"
          />
        </video>
        <div className="flex justify-center">
          <a
            className="mt-4 flex items-center justify-center rounded-full bg-white px-6 py-2 text-lg text-amber-500	"
            href="https://baby-mobile.ancda.com/album?archivesId=9114988868591616"
          >
            查看样册本
          </a>
        </div>
      </div>
      <div className="down-img flex flex-col items-center">
        <img src="/images/growthArchives/down-part.png" alt="" />
        <div className="img-box">
          <img src="/images/growthArchives/qr-code.png" alt="" />
        </div>
        <div className="my-4 text-white">微信扫码添加客服了解更多</div>
      </div>
      <div className="footer">
        <img src="/images/growthArchives/down-btn.png" alt="" />
      </div>
      <style jsx>{`
        .container {
          background: #9ed7f8;
        }
        .img-box {
          width: 260px;
          height: 260px;
        }
        .down-img {
          background: #6bbfa4;
        }
      `}</style>
    </div>
  );
}
