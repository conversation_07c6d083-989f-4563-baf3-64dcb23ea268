'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { hinaTrack, navigationToNativePage } from '@/utils';
import { Button } from 'antd-mobile';

if (typeof document !== 'undefined') {
  document.title = '欢迎使用成长档案';
}

function page() {
  useEffect(() => {
    hinaTrack('growthProfile_open');
  }, []);

  const navigatorToPublic = () => {
    navigationToNativePage('app://app/moment/publish');
    hinaTrack('growthProfile_button');
  };

  return (
    <div className="relative w-full h-screen overflow-y-scroll">
      <Image
        src="https://edu-media.ancda.com/prod/archives/album/intro/intro.jpg"
        alt="intro"
        width={1000}
        height={6505}
        className="w-full"
      />
      <div className="fixed bottom-0 left-0 right-0 px-4 pb-4">
        <Button
          shape="rounded"
          type="submit"
          block
          color="primary"
          className="w-[200px]"
          onClick={() => navigatorToPublic()}
        >
          立即记录
        </Button>
      </div>
    </div>
  );
}

export default page;
