'use client';

import { El<PERSON><PERSON>, Image, ImageViewer, Tabs } from 'antd-mobile';
import { format } from 'date-fns';
import Head from 'next/head';
import React, { useEffect, useState } from 'react';

import { getCommentList, getDetailInfo, getLikeList } from '@/api/moment';
import FooterQrCode from '@/components/FooterQrcode';
import { PiHeartFill } from '@/components/Icons';
import LaunchWeApp from '@/components/LaunchWeApp';
import { defaultAvatar } from '@/constant/config';
import { wechatShare } from '@/utils/wechat'; 
import { useSearchParams } from 'next/navigation';
import { useImmer } from 'use-immer';


const Page = () => {
  const searchParams = useSearchParams();
  const id = searchParams?.get('id') || '';
  const instId = searchParams?.get('instId') || '';
  const [content, setContent] = useState();
  const [userName, setUserName] = useState('老师');
  const [avatar, setAvatar] = useState('');
  const [createTime, setCreateTime] = useState(new Date().getTime());
  const [image, setImage] = useState<string[]>([]);
  const [videoUrl, setVideoUrl] = useState('');
  const [audio, setAudio] = useState('');
  const [tabs, setTabs] = useImmer([
    { title: '热门评论', id: 1, number: 0 },
    { title: '点赞', id: 2, number: 0 },
  ]);
  const [comment, setComment] = useState([]);
  const [like, setLike] = useState([]);

  useEffect(() => {
    document.title = '动态详情'
    if (id) {
      getDetail();
      getComment();
      getLike();
    }
  }, [id]);

  // 获取详情信息
  const getDetail = () => {
    getDetailInfo({ momentId: id, instId }).then((data: any) => {
      setUserName(data.userName);
      setCreateTime(data.createTime * 1000);
      setAvatar(data.avatar || defaultAvatar);
      setContent(data.content);
      const images: string[] = [];
      data.media?.map((i: any) => {
        if (i.type === 1) {
          images.push(i.url);
        }
        if (i.type === 2) {
          setVideoUrl(i.url);
        }
        if (i.type === 3) {
          setAudio(i.url);
        }
      });
      if (data.commentCount > 0) {
        setTabs((draft)=> {
          draft[0].number = data.commentCount;
        })
      }
      if (data.likeCount > 0) {
        setTabs((draft) => {
          draft[1].number = data.likeCount;
        });
      }
      setImage(images);
      const wxData = {
        title: `班级动态 - ${data.userName}`,
        desc: data.content,
        link: window.location.href,
        imgUrl: images.length > 0 ? images[0] : '',
      };
      wechatShare(wxData);
    });
  };
  // 获取评论
  const getComment = () => {
    getCommentList({ momentId: id, instId }).then((res: any) => {
      setComment(res.data);
    });
  };
  // 获取点赞
  const getLike = () => {
    getLikeList({ momentId: id, instId }).then((res: any) => {
      setLike(res.data);
    });
  };
  // 图片预览
  const onImagePreview = (index: number) => {
    ImageViewer.Multi.show({
      images: image,
      defaultIndex: index,
    });
  };
  // 评论,回复 内容
  const onComment = (item: any) => {
    return (
      <div className="mt-[-8px]">
        <div>
          {item.replyId === '0' ? (
            item.content
          ) : (
            <span>
              回复
              <span
                style={{
                  color: '#000000',
                  marginLeft: '5px',
                  marginRight: '5px',
                }}
              >
                {item.replyUserName}:
              </span>
              {item.content}
            </span>
          )}
        </div>
        <div className="mt-2 text-[#C4C4C4]" />
      </div>
    );
  };
  return (
    <div className="flex min-h-screen flex-col">
      <Head>
        <title>动态详情</title>
      </Head>
      <div className="flex-1 px-4 pt-4">
        <div className="mb-5 flex items-center">
          <Image
            width={44}
            height={44}
            src={avatar}
            style={{ borderRadius: 22 }}
            alt=""
          />
          <div className="ml-2">
            <div>{userName}</div>
            <div className="text-stone-400">
              {format(createTime || 0, 'yyyy-MM-dd')}
            </div>
          </div>
        </div>

        {!!content && (
          <Ellipsis
            rows={3}
            content={content}
            expandText="展开"
            collapseText="收起"
          />
        )}

        <div className="mt-2 flex flex-row flex-wrap">
          {image.map((item, index) => {
            return (
              <div
                key={index}
                className="mb-2 flex items-center justify-center overflow-hidden px-1"
                style={
                  image.length === 1
                    ? {
                        width: '100vw',
                        height: 'auto',
                      }
                    : {
                        width: '30vw',
                        height: '30vw',
                      }
                }
              >
                <Image
                  src={item}
                  alt=""
                  className="rounded-lg"
                  onClick={() => onImagePreview(index)}
                />
              </div>
            );
          })}
          {videoUrl && (
            <div className=" w-full overflow-hidden rounded-xl">
              <video
                controls
                width="100%"
                height="auto"
                poster={`${videoUrl}?x-workflow-graph-name=video-thumbnail`}
              >
                <source src={videoUrl} type="video/mp4" />
                你的浏览器不支持
              </video>
            </div>
          )}
          {audio && (
            <div className="max-h-[400px] w-full overflow-hidden rounded-xl">
              <audio src={audio} controls className="w-full">
                你的浏览器不支持
              </audio>
            </div>
          )}
        </div>
        <div className="mt-2">
          <Tabs>
            {tabs.map((item, index) => (
              <Tabs.Tab
                key={item.id}
                title={
                  item.number > 0 ? `${item.title} ${item.number}` : item.title
                }
              >
                {index === 0 && (
                  <div>
                    {comment.length === 0 && (
                      <div className="text-center my-4">暂无评论</div>
                    )}
                    {comment.map((item: any) => {
                      return (
                        <div key={item?.commentId}>
                          <div className="flex">
                            <Image
                              width={44}
                              height={44}
                              src={item?.avatar}
                              style={{ borderRadius: 22 }}
                              alt=""
                            />
                            <div className="ml-2 mt-1">
                              <div>{item.userName}</div>
                              <div className="text-stone-400">
                                {format(
                                  item.createTime * 1000 || 0,
                                  'yyyy-MM-dd',
                                )}
                              </div>
                              <div className="mt-4">{onComment(item)}</div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}

                {index === 1 &&
                  like.map((item: any) => {
                    return (
                      <div
                        key={item.id}
                        className="mb-4 flex items-center justify-between"
                      >
                        <div className="flex items-center">
                          <Image
                            width={44}
                            height={44}
                            src={item.avatar}
                            style={{ borderRadius: 22 }}
                            alt=""
                          />
                          <div className="ml-2">
                            <div>{item.userName}</div>
                            <div className="text-stone-400">
                              {format(
                                item.createTime * 1000 || 0,
                                'yyyy-MM-dd',
                              )}
                            </div>
                          </div>
                        </div>
                        <div>
                          <PiHeartFill color="#FF6767" fontSize={22} />
                        </div>
                      </div>
                    );
                  })}
              </Tabs.Tab>
            ))}
          </Tabs>
        </div>
      </div>
      <div className="fixed bottom-14 left-[50%] z-50 translate-x-[-50%]">
        <LaunchWeApp />
      </div>
      <FooterQrCode />
    </div>
  );
};
export default Page;
