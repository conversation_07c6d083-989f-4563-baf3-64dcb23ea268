import '@/styles/global.css';

import type { Metadata } from 'next';

import { ConditionalWechatAuth } from '@/components/ConditionalWechatAuth';
import Footer from '@/components/layout/Footer';
import { siteConfig } from '@/constant/config';

import Providers from './providers';

const includePaths = ['/securityPatrol/register', '/securityPatrol/form'];
export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url || ''),
  title: {
    default: '',
    template: '%s'
  },
  description: siteConfig.description,
  robots: { index: true, follow: true },
  icons: {
    icon: '/favicon/favicon.ico',
    shortcut: '/favicon/favicon-16x16.png',
    apple: '/favicon/apple-touch-icon.png'
  },
  manifest: '/favicon/site.webmanifest',
  openGraph: {
    url: siteConfig.url,
    title: siteConfig.title,
    description: siteConfig.description,
    siteName: siteConfig.title,
    images: [`${siteConfig.url}/images/og.jpg`],
    type: 'website',
    locale: 'en_US'
  }
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false
};

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh">
      <body>
        <Providers>
          <ConditionalWechatAuth includePaths={includePaths}>
            {children}
          </ConditionalWechatAuth>
        </Providers>
        <Footer />
      </body>
    </html>
  );
}
