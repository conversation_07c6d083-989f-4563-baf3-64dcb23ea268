.container {
  min-height: 100vh;
  position: relative;
  padding: 20px;
  padding-top: 800px;
  background-color: #99dbfb;
}
.container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("/images/cardBindingGuide/bindCardBg.png") no-repeat top
    center;
  background-size: 100% auto;
  z-index: 1;
}

.content {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 24px 34px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  border: 20px solid #fdcd5f;
}
.title {
  text-align: center;
  color: #333;
  font-size: 24px;
  margin-bottom: 24px;
}

.overview {
  margin-bottom: 32px;
  padding: 16px 0;
  background: #f8f9fa;
  border-radius: 8px;
}

.overview h2 {
  color: #333;
  margin-bottom: 12px;
}

.overview p {
  color: #666;
  line-height: 1.6;
}

.detailedSteps {
  margin-top: 32px;
}

.detailedSteps h2 {
  color: #333;
  margin-bottom: 24px;
}

.step {
  flex-direction: column;
  display: flex;
  margin-bottom: 32px;
  gap: 24px;
  align-items: flex-start;
}

.stepImage {
  flex: 0 0 300px;
}

.stepImage img {
  width: 100%;
  height: auto;
}

.stepContent {
  flex: 1;
}

.stepContent h3 {
  color: #333;
  margin-bottom: 12px;
}

.stepContent p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
}

.tip {
  background: #fff3cd;
  border-left: 4px solid #ffc107;
  padding: 12px;
  border-radius: 4px;
  margin-top: 12px;
}

.tip p {
  color: #856404;
  margin: 0;
  line-height: 1.5;
}
