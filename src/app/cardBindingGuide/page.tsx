'use client';

import React, { useEffect } from 'react';

import LaunchWeApp from '@/components/LaunchWeApp';
import { isPalmBaby } from '@/lib/utils';
import { hinaTrack } from '@/utils/index';
import { weChatInit } from '@/utils/wechat';

import styles from './page.module.css';

const CardBindingGuide = () => {
  useEffect(() => {
    hinaTrack('invite_bindcard_pageview');
    weChatInit();
  }, []);
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <section className={styles.overview}>
          <h2 className="text-xl">步骤总览</h2>
          <p className="text-sm">
            【登录家长端APP→在校园页面点击
            <strong>"校园签到"图标</strong>
            →校园签到点击 <strong>"设置"</strong>
            →设置页面点击 <strong>"绑定考勤卡"</strong>
            →输入考勤卡背面的卡号→点击 <strong>"确定"</strong>】
          </p>
        </section>

        <section className={styles.detailedSteps}>
          <h2 className="text-xl">详细步骤</h2>

          <div className={styles.step}>
            <div className={styles.stepContent}>
              <h3 className="text-base">第一步：登录家长端APP(已登录请忽略)</h3>
              <p className="text-sm">
                在校园页面点击 <strong>校园签到</strong>
              </p>
              <div className={styles.tip}>
                <p className="text-sm">
                  温馨提示：如您在登录时突然提示"账号不存在，请先联系幼儿园添加！"遇到这种情况时请您联系学校的老师/负责人将您的孩子添加进入系统中，在添加成功后会通过短信的形式将登录账号及初始密码发送给您。
                </p>
              </div>
            </div>
            <div className={styles.stepImage}>
              <img
                src="/images/cardBindingGuide/card1.png"
                alt="登录家长端APP"
              />
            </div>
          </div>

          <div className={styles.step}>
            <div className={styles.stepContent}>
              <h3>第二步：进入设置</h3>
              <p>
                校园签到中点击底部导航栏中的 <strong>"设置"</strong>
              </p>
            </div>
            <div className={styles.stepImage}>
              <img
                src="/images/cardBindingGuide/card2.png"
                alt="点击校园签到图标"
              />
            </div>
          </div>

          <div className={styles.step}>
            <div className={styles.stepContent}>
              <h3>第三步：进入绑卡页面</h3>
              <p>
                设置页面中点击 <strong>"绑定考勤卡"</strong>
              </p>
            </div>
            <div className={styles.stepImage}>
              <img src="/images/cardBindingGuide/card3.png" alt="点击设置" />
            </div>
          </div>

          <div className={styles.step}>
            <div className={styles.stepContent}>
              <h3>第四步：绑定考勤卡</h3>
              <p>输入考勤卡背面的卡号，再点击"确定"</p>
              <div className={styles.tip}>
                <p>
                  温馨提示：如您在绑定时显示考勤卡已被占用或者显示学生已离园，遇到这种情况请您联系学校老师/负责人处理哦~
                </p>
              </div>
            </div>
            <div className={styles.stepImage}>
              <img src="/images/cardBindingGuide/card4.png" alt="绑定考勤卡" />
            </div>
          </div>
        </section>
      </div>
      {/* 底部按钮 水平剧中 层级设置为3 */}
      <div
        className="fixed inset-x-0 bottom-28 z-30 mx-auto
       flex  w-4/5 items-center justify-center
      "
      >
        <LaunchWeApp
          text={isPalmBaby() ? '去掌心宝贝APP绑卡' : '去掌心智校APP绑卡'}
          style={{
            width: '280px',
          }}
          textStyle={{
            fontSize: '16px',
          }}
          extinfo={JSON.stringify({
            router: 'rn://FaceStack?initialRoute=BindCardScreen',
          })}
          onClick={() => {
            hinaTrack('invite_bindcard_todo_click');
          }}
        />
      </div>
      {/* <Script
        src="//cdn.jsdelivr.net/npm/eruda"
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
      /> */}
    </div>
  );
};

export default CardBindingGuide;
