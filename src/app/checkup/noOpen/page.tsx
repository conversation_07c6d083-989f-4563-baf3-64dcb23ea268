import { headers } from 'next/headers';
import Image from 'next/image';

import { isPalmBaby } from '@/lib/utils';

export default async function Page() {
  const headersList = await headers();
  const hostname = headersList.get('host') || '';

  const isPalmBabyApp = isPalmBaby(hostname);

  return (
    <div>
      <Image
        src={
          isPalmBabyApp
            ? '/images/checkup/baby-poster.jpg'
            : '/images/checkup/kid-poster.jpg'
        }
        alt=""
        width={0}
        height={0}
        sizes="100vw"
        className=" w-screen object-cover"
      />
    </div>
  );
}
