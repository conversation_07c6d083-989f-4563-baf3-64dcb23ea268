'use client';

import { <PERSON>rrorBlock, Image, ImageViewer, NavBar, Tabs } from 'antd-mobile';
import clsx from 'clsx';
import { format } from 'date-fns';
import Cookies from 'js-cookie';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getStudentStatistics } from '@/api/checkup';
import { PiImage } from '@/components/Icons';
import { postMessage } from '@/utils';

import MonthCalendar from './components/MonthCalendar';

type CheckUpItem = {
  name: string;
  title: string;
  image?: string;
  status: string;
  temperature?: string;
};

function statusClass(type) {
  const typeMap = {
    1: 'bg-green-400',
    '-1': 'bg-red-400',
    0: 'bg-neutral-300',
    '-2': 'bg-orange-400',
  };
  return typeMap[type];
}

const statusType = {
  1: '正常',
  '-1': '异常',
  0: '未检',
  '-2': '预警',
};

export default function Page() {
  const searchParams = useSearchParams();
  const studentId = searchParams?.get('studentId') || null;
  const studentName = searchParams?.get('studentName') || '';
  const authorization = searchParams?.get('authorization');

  if (authorization) {
    Cookies.set('Authorization', authorization);
  }
  const router = useRouter();
  const [data, setData] = useState([]);
  const [currentType, setCurrentType] = useState(0);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [imageViewerUrl, setImageViewerUrl] = useState('');
  const [createTime, setCreateTime] = useState('');

  useEffect(() => {
    initData();
  }, [currentDate, currentType]);

  const initData = () => {
    getStudentStatistics({
      type: currentType,
      date: format(currentDate, 'yyyy-MM-dd'),
      studentId,
    }).then((res: any) => {
      const checkInfo = res.check_info;
      const category = res.result_list || [];
      setCreateTime(checkInfo.create_time);
      const checkItems: CheckUpItem[] = [
        {
          name: 'temperature',
          title: '体温',
          temperature: checkInfo.temperature || '',
          status: category.temperature || 0,
        },
        {
          name: 'hand',
          title: '手部',
          image: checkInfo.hand_img_url || '',
          status: category.hand || 0,
        },
        {
          name: 'mouth',
          title: '口腔',
          image: checkInfo.mouth_img_url || '',
          status: category.mouth || 0,
        },
        {
          name: 'eye',
          title: '红眼',
          image: checkInfo.eye_img_url || '',
          status: category.eye || 0,
        },
        {
          name: 'nose',
          title: '流涕',
          status: category.nose || 0,
        },
        {
          name: 'nail',
          title: '指甲',
          status: category.fingernail || 0,
        },
        {
          name: 'cheeks',
          title: '腮部',
          status: category.gill || 0,
        },
        {
          name: 'spirit',
          title: '精神',
          status: category.spirit || 0,
        },
        {
          name: 'cough',
          title: '咳嗽',
          status: category.cough || 0,
        },
        {
          name: 'traumatism',
          title: '外伤',
          status: category.trauma || 0,
        },
        {
          name: 'other',
          title: '其他',
          status: category.other || 0,
        },
      ];
      setData(checkItems);
    });
  };

  return (
    <div className="mx-auto min-h-screen max-w-md bg-gray-100">
      <div className="fixed top-0 z-10 w-full bg-white">
        <NavBar
          onBack={() => {
            if (studentName) {
              router.back();
            } else {
              postMessage({ goBack: 1 });
            }
          }}
        >
          <span className="text-base font-bold">
            {studentName || '晨午检统计'}
          </span>
        </NavBar>
      </div>
      <div className="mt-10 bg-white">
        <Tabs
          defaultActiveKey="0"
          activeLineMode="fixed"
          onChange={(key) => {
            setCurrentType(key);
          }}
        >
          <Tabs.Tab title="晨检" key="0" />
          <Tabs.Tab title="午检" key="1" />
        </Tabs>
      </div>
      <MonthCalendar
        studentId={studentId}
        currentType={currentType}
        onChange={setCurrentDate}
        createTime={createTime}
      />
      <div className="p-4 pt-0">
        <div className="mb-2 flex items-center">
          <Image
            width={18}
            height={18}
            src="/images/checkup/title.png"
            alt=""
          />
          <span className="ml-2 text-base">检测结果</span>
        </div>
        <div className="mb-4 rounded-lg bg-white p-4">
          {data.length === 0 && (
            <ErrorBlock status="empty" title="暂无记录" description="" />
          )}
          <div className="grid grid-cols-4 gap-2">
            {data.map((item: CheckUpItem) => (
              <div
                key={item.id}
                className="relative flex flex-col items-center justify-end rounded-lg bg-blue-50 p-3"
              >
                <div className=" flex h-[80px] items-center justify-center">
                  {item.name === 'temperature' ? (
                    <span>{item.temperature || '无数据'}</span>
                  ) : (
                    <Image
                      width={36}
                      height={36}
                      src={`/images/checkup/item-${item.name}.png`}
                      alt=""
                    />
                  )}
                </div>
                <p className="mb-1 text-sm">{item.title}</p>
                <span
                  className={clsx(
                    'rounded px-2 py-0.5 text-xs text-white',
                    statusClass(item.status),
                  )}
                >
                  {statusType[item.status]}
                </span>
                {item.image ? (
                  <div
                    className="absolute left-0 top-0 rounded-br-md rounded-tl-md bg-stone-400 px-1"
                    onClick={() => {
                      setImageViewerUrl(item.image);
                      setImageViewerVisible(true);
                    }}
                  >
                    <PiImage color="white" size="16" />
                  </div>
                ) : null}
              </div>
            ))}
          </div>
        </div>
      </div>
      <ImageViewer
        image={imageViewerUrl}
        visible={imageViewerVisible}
        onClose={() => {
          setImageViewerVisible(false);
        }}
      />
    </div>
  );
}
