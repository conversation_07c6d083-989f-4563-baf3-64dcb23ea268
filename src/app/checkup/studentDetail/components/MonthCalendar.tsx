'use client';

import { Calendar } from 'antd-mobile';
import clsx from 'clsx';
import { format, isSameDay } from 'date-fns';
import { memo, useEffect, useState } from 'react';

import { getStudentCalendar } from '@/api/checkup';

type Props = {
  studentId: string | null;
  currentType: number | null;
  onChange: (date: Date) => void;
  createTime: string;
};

function MonthCalendar({
  studentId,
  currentType,
  onChange,
  createTime,
}: Props) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [statusMap, setStatusMap] = useState({});
  const [checkDays, setCheckDays] = useState(0);

  useEffect(() => {
    initData();
  }, [currentDate, currentType]);

  const initData = () => {
    getStudentCalendar({
      type: currentType,
      date: format(currentDate, 'yyyy-MM-dd'),
      studentId,
    }).then((res: any) => {
      setCheckDays(res.total_numb || 0);
      if (Array.isArray(res?.list)) {
        const result = res.list.reduce((acc, current) => {
          acc[current.date] = current.check_res;
          return acc;
        }, {});
        setStatusMap(result);
      }
    });
  };

  return (
    <>
      <div className=" bg-white py-4">
        {Object.entries(statusMap).length > 0 && (
          <Calendar
            prevYearButton=""
            nextYearButton=""
            weekStartsOn="Monday"
            renderDate={(date) => {
              const d = new Date(date).getDate();
              const status = statusMap[format(date, 'yyyy-MM-dd')] || 0;
              return (
                <div
                  className="flex flex-col items-center"
                  onClick={() => {
                    setCurrentDate(date);
                    onChange(date);
                  }}
                >
                  <div
                    className={clsx(
                      'flex size-8 items-center justify-center rounded-full text-sm',
                      {
                        'primary-bg text-white': isSameDay(date, currentDate),
                      },
                    )}
                  >
                    {d}
                  </div>
                  <div
                    className={clsx('mt-1 size-1 rounded-full', {
                      'bg-green-400': status === 6,
                      'bg-red-400': status === 4,
                      'bg-orange-400': status === 2,
                    })}
                  />
                </div>
              );
            }}
            onPageChange={(year, month) => {
              console.log('333', year, month);
              const day = new Date(currentDate).getDate();
              setCurrentDate(new Date(year, month - 1, day));
            }}
          />
        )}
      </div>
      <div className="p-4">
        <div className="rounded-lg bg-white p-4">
          <div className="flex items-center justify-between">
            <p className="text-base">
              当月晨检天数：<span className="text-stone-400">{checkDays}</span>
            </p>
            <div className=" flex items-center justify-center space-x-4">
              <span className="flex items-center">
                <div className="mr-1 size-2 rounded-full bg-green-400" />
                正常
              </span>
              <span className="flex items-center">
                <div className="mr-1 size-2 rounded-full bg-red-400" />
                异常
              </span>
              <span className="flex items-center ">
                <div className="mr-1 size-2 rounded-full bg-orange-400" />
                预警
              </span>
            </div>
          </div>
          <p className="mt-2 text-base">
            检测时间：
            <span className="text-stone-400">{createTime || '未检测'}</span>
          </p>
        </div>
      </div>
    </>
  );
}
export default memo(MonthCalendar);
