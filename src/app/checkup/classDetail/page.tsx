'use client';

import {
  Ava<PERSON>,
  DatePicker,
  <PERSON>rrorBlock,
  NavBar,
  SpinLoading,
} from 'antd-mobile';
import clsx from 'clsx';
import { format } from 'date-fns';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getClassStatistics } from '@/api/checkup';
import { IoCaretDown, PiCaretRight } from '@/components/Icons';
import { defaultAvatar } from '@/constant/config';

export default function Page() {
  const router = useRouter();

  const searchParams = useSearchParams();
  const classId = searchParams?.get('classId') || '';
  const className = searchParams?.get('className') || '';
  const [currentType, setCurrentType] = useState(0);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState([]);

  const now = new Date();

  useEffect(() => {
    initData();
  }, [currentType, currentDate]);

  const initData = () => {
    getClassStatistics({
      type: currentType,
      date: format(currentDate, 'yyyy-MM-dd'),
      classId,
    })
      .then((res) => {
        setIsLoading(false);
        if (Array.isArray(res)) {
          setData(res);
        }
      })
      .catch(() => {
        setIsLoading(false);
      });
  };

  return (
    <div className="mx-auto min-h-screen max-w-md bg-gray-100">
      <div className="fixed top-0 z-10 w-full bg-white">
        <NavBar
          onBack={() => {
            router.back();
          }}
        >
          <span className="text-base font-bold">{className}</span>
        </NavBar>
      </div>
      <div className="mt-10 flex items-center justify-between bg-white p-4">
        <div
          className="flex items-center rounded-full bg-slate-100  px-2 py-1 text-sm"
          onClick={() => setDatePickerVisible(true)}
        >
          <div className="mx-1">{format(currentDate, 'yyyy-MM-dd')}</div>
          <IoCaretDown color="#AAA" size="16" />
        </div>
        <div className="flex rounded-full bg-gray-200">
          <div
            className={clsx('rounded-full px-4  py-1', {
              'primary-gradient text-white': currentType === 0,
            })}
            onClick={() => setCurrentType(0)}
          >
            晨检
          </div>
          <div
            className={clsx('rounded-full px-4 py-1', {
              'primary-gradient text-white': currentType === 1,
            })}
            onClick={() => setCurrentType(1)}
          >
            午检
          </div>
        </div>
      </div>

      <div className="p-4">
        <div className="rounded-lg bg-white p-2">
          {isLoading && (
            <div className="flex h-screen w-full items-center justify-center">
              <SpinLoading />
            </div>
          )}
          {!isLoading && data.length === 0 && <ErrorBlock status="empty" />}
          {data.map((item: any) => (
            <Link
              key={item.student_id}
              className="flex items-center border-b border-stone-100 px-2 py-4 text-stone-600 last:border-b-0	"
              href={`/checkup/studentDetail?studentId=${item.student_id}&studentName=${item.name}`}
            >
              <Avatar
                src={item.avatar || defaultAvatar}
                alt=""
                className="mr-4 size-6 overflow-hidden rounded-full"
                style={{
                  '--border-radius': '100px',
                }}
              />
              <div className="grow">
                <p className="font-semibold">{item.name}</p>
              </div>
              <div className="flex items-center">
                {item.check_res === 4 && (
                  <span className="mr-2 text-sm text-gray-500">
                    {item.abnormal_numb}项
                  </span>
                )}
                <span
                  className={`mr-2 rounded px-2 py-0.5 text-sm text-white ${
                    item.check_res === 6
                      ? 'bg-green-400 '
                      : item.check_res === 4
                        ? 'bg-red-400 '
                        : 'bg-neutral-300 '
                  }`}
                >
                  {item.check_res === 6
                    ? '正常'
                    : item.check_res === 4
                      ? '异常'
                      : '未检'}
                </span>
                <PiCaretRight color="#AAA" size="18" />
              </div>
            </Link>
          ))}
        </div>
      </div>
      <DatePicker
        title="时间选择"
        visible={datePickerVisible}
        onClose={() => {
          setDatePickerVisible(false);
        }}
        max={now}
        onConfirm={(val) => {
          setCurrentDate(val);
        }}
      />
    </div>
  );
}
