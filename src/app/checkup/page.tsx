'use client';

import {
  CascadePicker,
  DatePicker,
  NavBar,
  Popover,
  Space,
  Tabs,
} from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/popover';
import { format } from 'date-fns';
import Cookies from 'js-cookie';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getClassesStatistics } from '@/api/checkup';
import { getMyClassList } from '@/api/invite';
import {
  IoCaretDown,
  PiCaretRight,
  PiDotsThreeBold,
  PiLinkSimple,
  PiListBullets,
  PiQuestion,
} from '@/components/Icons';
import { postMessage } from '@/utils';

type CascadePickerOption = {
  label: string;
  value: string;
  children?: CascadePickerOption[];
};

// 转换班级数据结构
function convertToCascadePickerOptions(treeArray) {
  return treeArray.map((item) => {
    const option: CascadePickerOption = {
      label: item.name,
      value: item.id.toString(),
    };
    if (item.children && item.children.length > 0) {
      option.children = convertToCascadePickerOptions(item.children);
    }
    return option;
  });
}

const actions: Action[] = [
  {
    key: '1',
    icon: <PiListBullets fontSize={20} color="#333" />,
    text: '数据列表',
    onClick: () => {
      postMessage({ router: 'app://app/temperatureGun/dataList' });
    },
  },
  {
    key: '2',
    icon: <PiLinkSimple fontSize={20} color="#333" />,
    text: '设备绑定',
    onClick: () => {
      postMessage({ router: 'app://app/temperatureGun/binding' });
    },
  },
  {
    key: '3',
    icon: <PiQuestion fontSize={20} color="#333" />,
    text: '使用帮助',
    onClick: () => {
      postMessage({ router: 'app://app/temperatureGun/helpCenter' });
    },
  },
];

export default function Page() {
  const searchParams = useSearchParams();

  const authorization = searchParams?.get('authorization');

  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [classListData, setClassListData] = useState([]);
  const [data, setData] = useState([]);
  const [currentType, setCurrentType] = useState(0);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentClass, setCurrentClass] = useState({
    label: '全部班级',
    value: '',
  });

  const now = new Date();

  useEffect(() => {
    initClassPickerData();
  }, []);

  useEffect(() => {
    initData();
  }, [currentType, currentDate, currentClass]);

  const initClassPickerData = () => {
    getMyClassList().then((res: any) => {
      if (Array.isArray(res.children)) {
        setClassListData(convertToCascadePickerOptions(res.children));
      }
    });
  };

  const initData = () => {
    getClassesStatistics({
      type: currentType,
      date: format(currentDate, 'yyyy-MM-dd'),
      classIds: currentClass.value ? [currentClass.value] : null,
    }).then((res) => {
      if (Array.isArray(res)) {
        postMessage({
          title: '晨午检统计',
          desc: format(currentDate, 'yyyy-MM-dd'),
        });
        setData(res);
      }
    });
  };
  const right = (
    <div style={{ fontSize: 24 }}>
      <Space style={{ '--gap': '16px' }}>
        <Popover.Menu
          actions={actions}
          placement="bottom-start"
          trigger="click"
        >
          <PiDotsThreeBold fontSize={24} color="#333" />
        </Popover.Menu>
      </Space>
    </div>
  );

  return (
    <div className="mx-auto min-h-screen max-w-md bg-gray-100">
      <div className="fixed top-0 z-10 w-full bg-white">
        <NavBar
          right={right}
          onBack={() => {
            postMessage({ goBack: 1 });
          }}
        >
          <span className="text-base font-bold">晨午检统计</span>
        </NavBar>
      </div>
      <div className="mt-10 bg-white">
        <div className="flex justify-between p-4">
          <div
            className="flex items-center rounded-full bg-slate-100  px-2 py-1 text-sm"
            onClick={() => setDatePickerVisible(true)}
          >
            <div className="mx-1">{format(currentDate, 'yyyy-MM-dd')}</div>
            <IoCaretDown color="#AAA" size="16" />
          </div>
          <div
            className="flex items-center rounded-full bg-slate-100 px-2 py-1 text-sm"
            onClick={() => setVisible(true)}
          >
            <div className="mx-1">{currentClass.label}</div>
            <IoCaretDown color="#AAA" size="16" />
          </div>
        </div>
        <Tabs
          defaultActiveKey="0"
          activeLineMode="fixed"
          onChange={(key) => {
            console.log('key', key);
            setCurrentType(key);
          }}
        >
          <Tabs.Tab title="晨检统计" key="0" />
          <Tabs.Tab title="午检统计" key="1" />
        </Tabs>
      </div>
      <div className="p-4">
        {data.map((item, index) => (
          <Link
            key={item.class_id}
            href={`/checkup/classDetail?classId=${item.class_id}&className=${item.class_name}`}
            className="mb-4 block rounded-lg bg-white p-4 text-stone-600 last:mb-0"
          >
            <div className="mb-4 flex items-center justify-between">
              <span>{item.class_name}</span>
              {/* <ChevronLeftIcon className="h-4 w-4 transform rotate-180" /> */}
              <PiCaretRight color="#AAA" size="18" />
            </div>
            <div className="flex justify-between  space-x-4">
              <span className="flex items-center">
                <div className="mr-1 size-3 rounded bg-green-400" />
                <span className="mr-2 text-stone-400">正常</span>
                <span className="text-base leading-none">
                  {item.normal_numb}
                </span>
              </span>
              <span className="flex items-center">
                <div className="mr-1 size-3 rounded bg-red-400" />
                <span className=" mr-2 text-stone-400">异常</span>
                <span className="text-base leading-none">
                  {item.abnormal_numb}
                </span>
              </span>
              <span className="flex items-center  ">
                <div className="mr-1 size-3 rounded bg-gray-400" />
                <span className=" mr-2 text-stone-400">未检</span>
                <span className="text-base leading-none">
                  {item.uncheck_numb}
                </span>
              </span>
            </div>
          </Link>
        ))}
      </div>
      <DatePicker
        title="时间选择"
        visible={datePickerVisible}
        onClose={() => {
          setDatePickerVisible(false);
        }}
        max={now}
        onConfirm={(val) => {
          setCurrentDate(val);
        }}
      />
      <CascadePicker
        title="选择班级"
        options={classListData}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        onConfirm={(val, extend) => {
          if (extend.items.length === 2) {
            console.log('onConfirm', val, extend.items);
            setCurrentClass(extend.items[1]);
          }
        }}
        onSelect={(val) => {
          console.log('onSelect', val);
        }}
      />
    </div>
  );
}
