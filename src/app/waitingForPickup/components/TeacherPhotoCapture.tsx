'use client';

import { Dialog, Input, Picker, <PERSON><PERSON><PERSON>, Toast } from 'antd-mobile';
import { format } from 'date-fns';
import { ImageUp } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { getAccounts } from '@/api/common';
import { createPickups } from '@/api/waitingForPickup';
import { generateString, getEnv, uploadObs } from '@/utils/obs';

const TeacherPhotoCapture: React.FC = () => {
  const [selectedStudent, setSelectedStudent] = useState('');
  const [note, setNote] = useState('');
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [studentList, setStudentList] = useState([]);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [agentName, setAgentName] = useState('');
  const [visible, setVisible] = useState(false);
  const [errors, setErrors] = useState<{
    agentName?: string;
    phoneNumber?: string;
  }>({});
  // Mock student data
  useEffect(() => {
    getAccounts().then((res) => {
      const resStudents = res.studentList?.map((item) => ({
        label: item.studentName,
        value: item.studentId,
      }));
      setStudentList(resStudents);
    });
  }, []);
  const handleCapture = (imageData: string) => {
    setCapturedImage(imageData);
  };

  // 验证手机号码
  const validatePhoneNumber = (phone: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: {
      agentName?: string;
      phoneNumber?: string;
    } = {};

    if (!agentName.trim()) {
      newErrors.agentName = '请输入代接送人';
      Toast.show({
        content: '请输入代接送人',
        position: 'bottom',
      });
      return false;
    }

    if (!phoneNumber) {
      newErrors.phoneNumber = '请输入手机号码';
      Toast.show({
        content: '请输入手机号码',
        position: 'bottom',
      });
      return false;
    }
    if (!validatePhoneNumber(phoneNumber)) {
      newErrors.phoneNumber = '请输入有效的手机号码';
      Toast.show({
        content: '请输入有效的手机号码',
        position: 'bottom',
      });
      return false;
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      return true;
    }
    return false;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      createPickups({
        studentId: selectedStudent,
        agentName,
        phone: phoneNumber,
        msg: note,
        pictureUrl: capturedImage,
      }).then((res) => {
        Dialog.alert({
          content: '提交成功,点击底部申请记录可以查看',
          confirmText: '确定',
          onConfirm: () => {
            setCapturedImage(null);
            setPhoneNumber('');
            setSelectedStudent('');
            setAgentName('');
            setNote('');
          },
        });
      });
    }
  };
  const handleGalleryClick = () => {
    // 触发隐藏的文件输入框点击
    document.getElementById('gallery-picker')?.click();
  };
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    // 检查文件的类型，如果不是图片则中断
    if (file && !file.type.startsWith('image/')) {
      Toast.show('请选择图片文件');
      return;
    }

    if (file) {
      const env = getEnv();
      const date = format(new Date(), 'yyyy-MM-dd');
      const key = `${env}/waitingForPickup/${date}/${generateString(8)}.png`;
      uploadObs(file, key, false)
        .then((url) => {
          handleCapture(url);
        })
        .catch((err) => {
          console.log('upload error', err);
        })
        .finally(() => {});
    }
  };

  return (
    <div className="px-4 py-2">
      <div className="mb-4 flex flex-col  justify-center rounded-lg border border-gray-50 bg-white p-4 shadow-sm">
        <div className="mb-4">
          <input
            type="file"
            id="gallery-picker"
            accept="*"
            className="hidden"
            onChange={handleFileSelect}
          />
          {capturedImage ? (
            <div className="relative">
              <img
                src={capturedImage}
                alt="已拍照片"
                className="max-h-[400px] w-full rounded-lg object-contain"
              />
              <button
                type="button"
                onClick={handleGalleryClick}
                className="absolute right-4 top-4 rounded-full bg-white p-2 shadow-lg"
              >
                <ImageUp size={20} className="text-blue-600" />
              </button>
            </div>
          ) : (
            <div
              onClick={handleGalleryClick}
              className="flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-[var(--adm-color-primary)] py-8 transition-colors hover:border-blue-500"
            >
              <div className="mb-2 text-[var(--adm-color-primary)]">
                上传照片
              </div>
              <div className="text-sm text-gray-500">点击上传</div>
            </div>
          )}
        </div>
        <div className="mb-4">
          <label
            htmlFor=""
            className="mb-2 block text-sm font-medium text-gray-700"
          >
            宝贝名称
          </label>
          <div
            className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2"
            onClick={() => setVisible(true)}
          >
            {selectedStudent
              ? studentList.find((item) => item.value === selectedStudent)
                  ?.label
              : '请选择宝贝'}
          </div>
          <Picker
            columns={[studentList]}
            visible={visible}
            onClose={() => setVisible(false)}
            value={[selectedStudent]}
            onConfirm={(val) => {
              setSelectedStudent(val[0] as string);
            }}
            style={{ '--font-size': '14px' }}
          />
        </div>
        <div className="mb-4">
          <label
            htmlFor="agentName"
            className="mb-2 block text-sm font-medium text-gray-700"
          >
            代接送人
          </label>
          <Input
            id="agentName"
            value={agentName}
            onChange={(val) => {
              setAgentName(val);
            }}
            placeholder="输入代接送人"
            className="rounded-lg border border-gray-300 px-3 py-2"
            maxLength={11}
            style={{ '--font-size': '14px' }}
          />
        </div>
        <div className="mb-4">
          <label
            htmlFor="phoneNumber"
            className="mb-2 block text-sm font-medium text-gray-700"
          >
            手机号码
          </label>
          <Input
            id="phoneNumber"
            value={phoneNumber}
            onChange={(val) => {
              setPhoneNumber(val);
              if (validatePhoneNumber(val)) {
                setErrors((prev) => ({ ...prev, phoneNumber: undefined }));
              }
            }}
            placeholder="输入手机号码"
            type="tel"
            className="rounded-lg border border-gray-300 px-3 py-2"
            maxLength={11}
            style={{ '--font-size': '14px' }}
          />
        </div>

        <div className="mb-6">
          <label
            htmlFor="note"
            className="mb-2 block text-sm font-medium text-gray-700"
          >
            备注
          </label>
          <TextArea
            id="note"
            value={note}
            onChange={(val) => setNote(val)}
            placeholder="添加备注信息..."
            className="rounded-lg border border-gray-300 px-3 py-2"
            rows={3}
            style={{ '--font-size': '14px' }}
          />
        </div>
      </div>
      <button
        type="button"
        className="w-full rounded-lg 
        bg-[var(--adm-color-primary)] py-2 text-base  font-medium text-white  transition-colors "
        onClick={handleSubmit}
      >
        确认提交
      </button>
    </div>
  );
};

export default TeacherPhotoCapture;
