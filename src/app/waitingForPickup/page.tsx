'use client';

import { Camera, List } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { useState } from 'react';

import ParentList from './components/ParentList';
import TeacherList from './components/TeacherList';
import TeacherPhotoCapture from './components/TeacherPhotoCapture';

function WaitingForPickup() {
  const searchParams = useSearchParams();
  const type = searchParams.get('type'); // 1: teacher, 2: parent
  const tabIndex = Number(searchParams.get('tabIndex') || 0); // 1
  if (typeof window !== 'undefined') {
    document.title = '代接送';
  }
  const [isParent] = useState(Number(type) === 2);
  const [activeTab, setActiveTab] = useState(
    tabIndex === 1 ? 'list' : 'capture',
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {isParent ? (
        // Teacher Interface
        <div className="mx-auto max-w-md">
          <nav className="fixed inset-x-0 bottom-0 z-10 bg-white shadow-sm">
            <div className="mx-auto flex max-w-md">
              <button
                type="button"
                onClick={() => setActiveTab('capture')}
                className={`flex flex-1 flex-col items-center justify-center gap-1 py-3 ${
                  activeTab === 'capture' ? 'text-blue-600' : 'text-gray-600'
                }`}
              >
                <Camera size={24} />
                <span className="text-xs">代接送申请</span>
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('list')}
                className={`flex flex-1 flex-col items-center justify-center gap-1 py-3 ${
                  activeTab === 'list' ? 'text-blue-600' : 'text-gray-600'
                }`}
              >
                <List size={24} />
                <span className="text-xs">申请记录</span>
              </button>
            </div>
          </nav>

          <div className="pb-16">
            {activeTab === 'capture' ? (
              <TeacherPhotoCapture />
            ) : (
              <TeacherList />
            )}
          </div>
        </div>
      ) : (
        // Parent Interface
        <div className="mx-auto max-w-md">
          <ParentList />
        </div>
      )}
    </div>
  );
}

export default WaitingForPickup;
