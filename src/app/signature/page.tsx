'use client';

import { format } from 'date-fns';
import React, { useRef } from 'react';
import SignatureCanvas from 'react-signature-canvas';

import { PiArrowCounterClockwise } from '@/components/Icons';
import { postMessage } from '@/utils';
import { generateString, getEnv, uploadObs } from '@/utils/obs';

export default function Signature() {
  const signatureRef = useRef<SignatureCanvas>(null);

  const submit = () => {
    const dataUrl = signatureRef.current
      ?.getTrimmedCanvas()
      .toDataURL('image/png');
    console.log('🚀 ~ file:     .tsx:11 ~ dataUrl:', dataUrl);
    // 处理签名图片的数据 URL

    const env = getEnv();
    const date = format(new Date(), 'yyyy-MM-dd');
    const key = `${env}/signature/${date}/${generateString(8)}.png`;
    uploadObs(dataUrl, key, true)
      .then((url) => {
        console.log('🚀 ~ file: signature.tsx:21 ~ url:', url);
        postMessage({ url, scrollEnabled: true });
      })
      .catch((err) => {
        console.log('upload error', err);
      });
  };
  const handleClear = () => {
    signatureRef.current?.clear();
  };

  const clearMask = () => {
    // setIsShowMask(false);
  };

  return (
    <div className="relative overflow-hidden rounded-lg border-dashed">
      <div
        className="absolute left-0 top-0 -z-10 flex h-full w-full items-center justify-center"
        onClick={() => clearMask()}
      >
        <span className="text-gray-600">请在此处签名（必填）</span>
      </div>

      <SignatureCanvas
        penColor="black"
        backgroundColor="rgba(247,249,255,40)"
        canvasProps={{
          className: 'w-full h-[300px]',
        }}
        ref={signatureRef}
        onBegin={() => {
          postMessage({ scrollEnabled: false });
        }}
        onEnd={() => {
          submit();
        }}
      />

      <div className="absolute right-3 top-3" onClick={handleClear}>
        <PiArrowCounterClockwise fontSize={20} color="#333" />
      </div>
    </div>
  );
}
