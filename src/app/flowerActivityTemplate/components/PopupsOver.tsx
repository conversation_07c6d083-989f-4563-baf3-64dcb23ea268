import { Button, Image } from 'antd-mobile';
import React from 'react';

import styles from '../index.module.css';
import Popups from './Popups';
// props.status 参与并结束，true
const PopupsOver = (props: any) => {
  return (
    <Popups visible={props.visibleOver} onClose={props.onClose}>
      <div className={styles.popupsContent}>
        {props.status ? (
          <>
            <Image
              className={styles.overImg}
              src="/images/flowerActivityTemplate/over.png"
              alt=""
            />
            <span className="text-center">很遗憾，差一点就中奖了......</span>
          </>
        ) : (
          <>
            <Image
              className={styles.overImg}
              src="/images/flowerActivityTemplate/activeOver.png"
              alt=""
            />
            <span className="text-center">很遗憾，本次活动已结束</span>
          </>
        )}
        <p className={styles.validityPeriod}>敬请期待下次活动</p>
        <div className="flex justify-center">
          <Button
            className={styles.butBoxRight}
            style={{ marginLeft: 0, marginTop: 20 }}
            shape="rounded"
            block
            onClick={() => props.onClose()}
          >
            <div className={styles.butRight}>知道了</div>
          </Button>
        </div>
      </div>
    </Popups>
  );
};
export default PopupsOver;