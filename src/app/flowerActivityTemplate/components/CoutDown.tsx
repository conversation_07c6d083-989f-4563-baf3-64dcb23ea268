import React, { useCallback, useEffect, useState } from 'react';

import styles from './CountDown.module.css';

const CountDown = ({ targetDate, onFinish }) => {
  console.log('🚀 ~ targetDate:', targetDate);
  const calculateTimeLeft = useCallback(() => {
    const difference = +new Date(targetDate) - +new Date();
    let timeLeft = {};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    }

    return timeLeft;
  }, [targetDate]);

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());
  useEffect(() => {
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);

      // 检查是否倒计时结束
      if (Object.keys(newTimeLeft).length === 0) {
        clearInterval(timer);
        if (onFinish && typeof onFinish === 'function') {
          onFinish();
        }
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [calculateTimeLeft, onFinish]);

  const addLeadingZero = (value) => {
    return value < 10 ? `0${value}` : value;
  };

  if (Object.keys(timeLeft).length === 0) {
    return <div className={styles.countDownTime}>倒计时已结束</div>;
  }

  return (
    <div className={styles.countDownTime}>
      <div className={styles.colon}>本场活动</div>
      <div className={styles.block}>{addLeadingZero(timeLeft.days)}</div>
      <div className={styles.colon}>天</div>
      <div className={styles.block}>{addLeadingZero(timeLeft.hours)}</div>
      <div className={styles.colon}>时</div>
      <div className={styles.block}>{addLeadingZero(timeLeft.minutes)}</div>
      <div className={styles.colon}>分</div>
      <div className={styles.block}>{addLeadingZero(timeLeft.seconds)}</div>
      <div className={styles.colon}>秒</div>
      <div className={styles.colon}>倒计时</div>
    </div>
  );
};

export default CountDown;
