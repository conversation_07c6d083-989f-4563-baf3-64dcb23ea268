.popupsBox{
  width: 100%;
  padding: 10px;
  background: linear-gradient(0deg, #83B8FA 0%, #CBB9FE 100%);
  border-radius: 50px;
}
.popups{
  background: linear-gradient(180deg, #CDC0F7 0%, #F3F9FE 5%, #EEFCFE 38%, #C0E0FE 100%);
  width: 100%;
  min-height: 300px;
  height: 100%;
  border-radius: 50px;
  position: relative;
  z-index: 2;
}
.close{
  width:50px;
  height:50px;
  position: absolute;
  top: -100px;
  right: -0px;
}
.alertIcon{
  position: absolute;
  top: -170px;
  width:382px;
  height:382px;
  left: 100px;
}
.title{
  font-size: 34px;
  color: #333;
  font-weight: 600;
  text-align: center;
  padding: 40px 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.titleText{
  margin: 0 30px;
}
.left3Icon{
  width: 9px;
  height: 21px;
  background: #7D85FD;
  transform: rotate(15deg);
  border-radius: 4px;
  margin-left: 8px;
}
.left2Icon{
  width: 9px;
  height: 21px;
  background: rgba(125, 133, 253, 0.7);
  transform: rotate(15deg);
  border-radius: 4px;
  margin-left: 8px;
}
.left1Icon{
  width: 9px;
  height: 21px;
  background: rgba(125, 133, 253, 0.3);
  transform: rotate(15deg);
  border-radius: 4px;
  margin-left: 8px;
}
.right1Icon{
  width: 9px;
  height: 21px;
  background: #7D85FD;
  transform: rotate(-15deg);
  border-radius: 4px;
  margin-left: 8px;
}
.right2Icon{
  width: 9px;
  height: 21px;
  background:rgba(125, 133, 253, 0.7);
  transform: rotate(-15deg);
  border-radius: 4px;
  margin-left: 8px;
}
.right3Icon{
  width: 9px;
  height: 21px;
  background: rgba(125, 133, 253, 0.3);
  transform: rotate(-15deg);
  border-radius: 4px;
  margin-left: 8px;
}
.popupsContent{
  padding:0 15px 40px;
  font-size: 34px;
  font-weight: normal;
}
