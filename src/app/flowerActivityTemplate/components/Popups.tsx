import { Popup } from 'antd-mobile';
import React from 'react';

import styles from './popups.module.css';

const Popups = (props: any) => {
  return (
    <Popup
      visible={props.visible}
      bodyStyle={{ 'background-color': 'transparent', overflow: 'initial' }}
      onClose={props.onClose}
    >
      <img
        src="/images/flowerActivityTemplate/close.png"
        onClick={props.onClose}
        className={styles.close}
        alt=""
      />
      <img
        src="/images/flowerActivityTemplate/alertIcon.png"
        className={styles.alertIcon}
        alt=""
      />
      <div className={styles.popupsBox}>
        <div className={styles.popups}>
          {props.title && (
            <div className={styles.title}>
              <div className={styles.left1Icon} />
              <div className={styles.left2Icon} />
              <div className={styles.left3Icon} />
              <div className={styles.titleText}>{props.title}</div>
              <div className={styles.right1Icon} />
              <div className={styles.right2Icon} />
              <div className={styles.right3Icon} />
            </div>
          )}
          <div className={styles.popupsContent}>{props.children}</div>
        </div>
      </div>
    </Popup>
  );
};
export default Popups;
