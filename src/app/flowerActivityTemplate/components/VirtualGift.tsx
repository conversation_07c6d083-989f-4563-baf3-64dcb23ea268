import { Button } from 'antd-mobile';
import React from 'react';

import { activityPrizeAddress } from '@/api/flowerActivityTemplate';

import styles from '../index.module.css';
import Popups from './Popups';

const VirtualGift = (props: any) => {
  const receivePrize = () => {
    activityPrizeAddress({ id: props.id }).then((res: any) => {
      props.onClose(true);
    });
  };
  return (
    <Popups
      visible={props.visibleAward}
      title={`恭喜您获得${props.prizeInfo.prize_title}`}
      onClose={() => props.onClose()}
    >
      <div className={styles.popupsContent}>
        <div className={styles.prizeName}>
          奖品为：{props.prizeInfo.prize_name}
        </div>
        <div className={`flex justify-center ${styles.prizeImg}`}>
          <img src={props.prizeInfo.url_img} alt="" />
        </div>
        <div className="flex justify-center">
          <Button
            className={styles.butBoxRight}
            style={{ marginLeft: 0, marginTop: 20 }}
            shape="rounded"
            type="submit"
            color="primary"
            block
            onClick={receivePrize}
          >
            <div className={styles.butRight}>开心收下</div>
          </Button>
        </div>
        <p className={styles.validityPeriod}>
          奖品有效期为{props.prizeInfo.expire_day}天
        </p>
      </div>
    </Popups>
  );
};
export default VirtualGift;
