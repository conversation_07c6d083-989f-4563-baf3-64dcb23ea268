import { Button, Form, Input, Toast } from 'antd-mobile';
import React, { useState } from 'react';

import { activityPrizeAddress } from '@/api/flowerActivityTemplate';
import AreaSelect from '@/components/AreaSelect';

import styles from '../index.module.css';
import Popups from './Popups';

const Address = (props: any) => {
  const [form] = Form.useForm();
  const [formData, setFormData] = useState({
    name: '',
    userMobile: '',
    provinceCode: '',
    cityCode: '',
    districtCode: '',
    area: [],
    address: '',
    city: '',
  });
  const formSubmit = () => {
    if (!formData.name) {
      Toast.show('请填写收货人姓名');
      return;
    }
    if (!formData.userMobile) {
      Toast.show('请填写手机号');
      return;
    }
    if (!/^1\d{10}$/.test(formData.userMobile)) {
      Toast.show('请输入正确的手机号');
      return;
    }
    if (!formData.city) {
      Toast.show('请填写收货地址');
      return;
    }
    if (!formData.address) {
      Toast.show('请填写详细地址');
      return;
    }
    const params = {
      name: formData.name,
      address: formData.address,
      mobile: formData.userMobile,
      city: formData.city,
    };
    activityPrizeAddress({ id: props.id, prize_address: [params] }).then(
      (res: any) => {
        props.onClose(true);
      },
    );
  };
  return (
    <Popups
      visible={props.visibleAddress}
      title={`恭喜您获得${props.prizeInfo.prize_title}`}
      onClose={() => props.onClose()}
    >
      <div className={styles.popupsContent}>
        <div className={styles.prizeName}>
          奖品为：{props.prizeInfo.prize_name}
        </div>
        <div>
          <p className={styles.tips}>请填写您的收货地址</p>
          <Form
            form={form}
            style={{ background: 'transparent' }}
            footer={
              <div className="mt-[20px] flex justify-center">
                <Button
                  className={styles.butBoxRight}
                  shape="rounded"
                  block
                  onClick={formSubmit}
                >
                  <div className={styles.butRight}>确认提交</div>
                </Button>
              </div>
            }
          >
            <Form.Item name="name" label="收件人" className="!bg-transparent">
              <Input
                className={styles.classInput}
                placeholder="收件人姓名"
                onChange={(value) =>
                  setFormData((draft) => ({
                    ...draft,
                    name: value,
                  }))
                }
                maxLength={10}
              />
            </Form.Item>
            <Form.Item
              name="userMobile"
              className="!bg-transparent"
              label="手机号码"
            >
              <Input
                placeholder="请输入手机号"
                className={styles.classInput}
                type="tel"
                maxLength={11}
                onChange={(value) =>
                  setFormData((draft) => ({
                    ...draft,
                    userMobile: value,
                  }))
                }
              />
            </Form.Item>
            <Form.Item className="!bg-transparent" name="area" label="收货地址">
              <AreaSelect
                classInput={styles.classInput}
                value={[
                  formData.provinceCode,
                  formData.cityCode,
                  formData.districtCode,
                ]}
                onChange={(value) => {
                  setFormData((draft) => ({
                    ...draft,
                    provinceCode: value[0]?.value || '',
                    cityCode: value[1]?.value || '',
                    districtCode: value[2]?.value || '',
                    city: value.map((el) => el.label).join(''),
                  }));
                }}
              />
            </Form.Item>
            <Form.Item
              className="!bg-transparent"
              name="address"
              label="详细地址"
            >
              <Input
                className={styles.classInput}
                onChange={(value) =>
                  setFormData((draft) => ({
                    ...draft,
                    address: value,
                  }))
                }
                placeholder="请输入详细地址"
                maxLength={50}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </Popups>
  );
};
export default Address;
