  .bg {
  background: url('/images/flowerActivityTemplate/bigBg.png') no-repeat top center;
  background-size: cover;
  min-height: 100vh;
  padding:0 32px;
  overflow: hidden;
}
/* 顶部达成条件 */
.condition{
  font-size: 30px;
  color: #fff;
  padding:44px  0;
  justify-content: space-between;
  align-items: center;
  display: flex;
} 
.conditionTitle{
  background-color: #9925FF;
  height: 14px;
  line-height: 0;
}
.ruleBox{
  width: 200px;
  height: 72px;
  border-radius: 36px;
  background:linear-gradient(to right, #FF7BCB, #FEB659);
  justify-content: center;
  align-items: center;
  display: flex;
  overflow:hidden;
  margin-right: -66px;
}
.rule{
  width: 190px;
  height: 62px;
  background-color: #fff;
  border-radius: 31px;
  color:#FD484E;
  justify-content: flex-start;
  align-items: center;
  padding-left: 20px;
  display: flex;
}
/* 活动内容区域 */
.contentBox{
  min-height: 254px;
  background: linear-gradient(to bottom, #CBB9FE, #83B8FA);
  border-radius:25px;
  position: relative;
  overflow: hidden;
  padding:15px;
  margin-bottom: 25px;
}
.content{
  background:#fff;
  border-radius:25px;
  width: 100%;
  min-height: 234px;
  padding:0 25px 25px;
}
.tagBox{

}
.activeTagBg{
  position: absolute;
  border: 100px solid transparent;
  border-top: 100px solid rgba(208, 255, 37,.8);
  transform: rotate(135deg);
  width:0px;
  height:0px;
  left: -100px;
  top:-100px
}
.endTagBg{
  position: absolute;
  border: 100px solid transparent;
  border-top: 100px solid rgba(114, 114, 114,.8);
  transform: rotate(135deg);
  width:0px;
  height:0px;
  left: -100px;
  top:-100px
}
.activeTagFont{
  position: absolute;
  left: 0;
  top:26px;
  transform: rotate(-45deg);
  font-size: 30px;
  color: #333;
  font-weight: 500;
}
.endTagFont{
  position: absolute;
  left: 0;
  top:26px;
  transform: rotate(-45deg);
  font-size: 30px;
  color: #fff;
  font-weight: 500;
}
.activeTitle{
  font-size: 34px;
  font-weight: 500;
  color: #333333;
  text-align: center;
  padding: 30px 0 25px;
}
.contentInfo{
  font-size: 28px;
  font-weight: normal;
}
/* 活动倒计时 */
.countDown{
  background: url('/images/flowerActivityTemplate/countDownBg.png') no-repeat top center;
  background-size: cover;
  width:calc(100vw - 64px);
  height:calc((100vw - 64px) * 0.2765 );
  margin: 0px auto 25px;
  overflow: hidden;
}
.countDownTitle{
  text-align: center;
  margin: 0 75px;
  background-color: rgba(255, 27, 27, 0.28);
  border-radius: 50px;
  margin-top: 20px;
  color: #fff;
  font-size: 30px;
}
.presenceNum{
  font-size: 40px;
  color: #E8FC92;
  padding: 0 10px;
}

/* 本人排行 */

.frameBox{
  background:rgba(255, 255, 255,0.8);
  border:6px solid #F4DCC9;
  min-height: 297px;
  border-radius: 24px;
}
.info{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 44px;
  padding-left:18px;
  color:#333;
  margin-bottom: 35px;
}
.pickUpStatusBox{
  width: 590px;
  height: 163px;
  border-radius: 80px;
  margin: 60px auto 80px;
  padding: 8px;
  position: relative;
  background: linear-gradient(90deg, #83B8FA 3%, #CBB9FE 100%);
}
.pickUpStatus{
  width: 100%;
  height: 100%;
  border-radius: 80px;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding-left: 30px;
}
.overStatusBox{
  width: 590px;
  height: 163px;
  border-radius: 80px;
  margin: 60px auto 80px;
  border: 8px solid #fff;
  background:rgba(219, 219, 219, 0.55);
  display: flex;
  flex-direction:row;
  align-items: center;
}
.overStatusBox2{
  width: 100%;
  height: 144px;
}
.overStatusImg{
  width: 72px;
  height: 72px;  
  margin: 0px 15px;
}
.overStatusImg2{
  width: 118px;
  height: 118px; 
  margin: 0px;
}
.overUpStatus{
  width: 100%;
  height: 100%;
  border-radius: 80px;
  background:  rgba(219, 219, 219, 0.55);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.statusImg{
  position: absolute;
  right: 0;
  bottom:-60px;
  width:158px;
  height: 120px;
}
.overUpStatusTitle{
  font-size: 34px;
  color: #474747;
}
.overUpStatusTip{
  font-size: 30px;
  color: #999999;
  margin-top: 8px;
}
.overUpStatusTip2{
  font-size: 30px;
  color: #868686;
}
.getPrizeTitle{
  font-size: 30px;
  color: #FD484E;
}
.getPrizeTip{
  font-size: 30px;
  color:#999;
}
.rank{
  color:#333;
  font-size: 30px;
}
.avatar{
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin:0 18px;
}
.tagStatus{
  font-size: 36px;
  color: #B5B5B5;
  font-weight: 600;
  margin-left: 10px;
}
.flower{
  padding-right: 32px;
}
.flowerNum{
  display: flex;
  align-items: center;
  color:#B5B5B5;
  font-size: 28px;
  margin-bottom: 10px;
  color:#333;
}
.friends{
  color:#999999;
  font-size: 24px;
}
.redFlowerNum{
  color:#FC847D;
  margin-left: 10px;
  font-family: PingFang SC;
}
.getButBox{
  width: 149px;
  height: 44px;
  border-radius: 50px;
  background: linear-gradient(to right, #FEB659 0%, #FF7BCB 100%);
  padding:3px;
  margin-left: 35px;
  margin-top: 10px;
}
.getBut{
  font-size: 28px;
  color: #FD484E;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 50px;
}
.butBox{
  width: 244px;
  height: 79px;
  border-radius: 50px;
  background: linear-gradient(to right, #FEB659 0%, #FF7BCB 100%);
  padding:3px
}
.but{
  font-size: 34px;
  color: #FD484E;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 50px;
}
.butBoxRight{
  width: 244px;
  height: 79px;
  background: url('/images/flowerActivityTemplate/butBg.png') no-repeat center;
  background-size: cover;
  margin-left: 32px;
}
.butRight{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 34px;
  font-weight: 600;
  color: #fff;
}
.topRankBox{
  width: 602px;
  height: 376px;
  /* background: url('/images/flowerActivityTemplate/rankBg.png') no-repeat center; */
  background-size: cover;
  margin:42px auto 0;
  position: relative;
}
.topRankBg{
  position:absolute;
  left:0;
  top:0;
  z-index:2;
  width: 100%;
}
.secondPlace{
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  z-index: 2;
  height: 100%;
  top:0;
  left:20px;
  padding:30px 0 70px
}
.firstPlace{
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  z-index: 2;
  height: 100%;
  top:-30px;
  left: 50%;
  transform: translateX(-50%);
  padding:0px 0 100px
}
.thirdPlace{
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  z-index: 2;
  height: 100%;
  top:30px;
  right:10px;
  padding:0px 0 80px
}
.topRankRankName{
  font-size: 26px;
  color: #DF8632;
}
.secondRankName{
  font-size: 26px;
  color: #C88888;
}
.thirdRankName{
  font-size: 26px;
  color: #536DE1;
}
.secondRankAvatar{
  position: absolute;
  width: 120px;
  height: 120px;
  z-index: 1;
  top: 85px;
  left: 35px;
}
.firstRankAvatar{
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 60px;
  z-index: 1;
  top:25px;
  left: 50%;
  transform: translateX(-50%);
}
.thirdRankAvatar{
  position: absolute;
  width: 120px;
  height: 120px;
  z-index: 1;
  right:30px;
  top:110px
}
.topRankFlowerNum{
  font-size: 22px;
  color: #333;
  margin-bottom: 5px;
}
.topRankRedFlowerNum{
  color:#FC847D;
}
.topRankFriends{
  font-size: 22px;
  color: #666;
}
.footerButBox{
  width: 100%;
  height: 178px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom:0;
  left:0;
  z-index: 11;
  background: linear-gradient(180deg, #C5B2FF 0%, #F3F9FE 6%, #EEFCFE 38%, #C0E0FE 100%);
}
.footerFrameBox{
  margin-bottom:190px;
  background:rgba(255, 255, 255);
}
.noRank{
  margin-top:40px;
  justify-content: center;
  align-items: center;
  display: flex;
  font-size: 30px;
  color: #999;
}
@keyframes breathing {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
.footerButBoxRight{
  width: 400px;
  height: 129px;
  background: url('/images/flowerActivityTemplate/butBg.png') no-repeat center;
  background-size: cover;
  animation: breathing 1s ease-in-out infinite;
}
.popupsContent{
  padding: 0 20px;
  display: flex;
  flex-direction: column;
}
.popupButton{
  padding: 50px 0;
}
.prizeName{
  width: 405px;
  height: 48px;
  line-height: 48px;
  border-radius: 24px;
  font-size: 30px;
  opacity: 1;
  margin:0 auto;
  background: #7D85FD;
  color: #ffff;
  text-align: center;
  margin-bottom: 30px;
}
.tips{
  font-size: 30px;
  padding:0px 20px 0px;
}
.classInput{
  background: rgba(205, 205, 205, 0.48);
  height: 69px;
  border-radius: 50px;
  font-size: 28px;
  padding-left: 20px;
}
.classInputBox{
  overflow: hidden;
}
.prizeImg{
  max-width: 100%;
}
.validityPeriod{
  font-size: 30px;
  font-weight: normal;
  line-height: 51px;
  letter-spacing: 1px;
   color: #898989;
   text-align: center;
   margin-top: 20px;
}
.overImg{
  align-self: center;
  width: 190px;
  height: 193px;
}
