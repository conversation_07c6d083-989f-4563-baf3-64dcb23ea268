'use client';

import { Image } from 'antd-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';

import {
  activityPrize,
  activitySign,
  getActivityInfo,
  getActivityRank,
} from '@/api/flowerActivityTemplate';
import OverLayLoad from '@/components/OverLayLoad/Index';
import { getMessage, hinaTrack } from '@/utils';

import Address from './components/Address';
import CountDown from './components/CoutDown';
import Popups from './components/Popups';
import PopupsOver from './components/PopupsOver';
import VirtualGift from './components/VirtualGift';
import styles from './index.module.css';

const Index = () => {
  const searchParams = useSearchParams() as any;
  const [id] = useState(searchParams.get('id') || '');
  const [title] = useState(searchParams.get('title') || '');
  const [Authorization] = useState(searchParams.get('Authorization') || '');
  const [countDownTimestamp, setCountDownTimestamp] = useState(0);
  const [status, setStatus] = useState(1); // 1未开始，2进行中，3已结束
  const [active] = useState(['未开始', '进行中', '已结束']);
  const [eligibility, setEligibility] = useState(0); // 距离参赛的小红花数，0则满足
  const [rankData, setRankData] = useState([]);
  const [visibleParticipation, setVisibleParticipation] = useState(false);
  const [ruleVisible, setRuleVisible] = useState(false);
  const [visibleAddress, setVisibleAddress] = useState(false);
  const [visibleAward, setVisibleAward] = useState(false);
  const [visibleOver, setVisibleOver] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isStatistics, setIsStatistics] = useState<any>(false);
  const [detail, setDetail] = useState<any>({});
  const [prizeType, setPrizeType] = useState(1); // 1为实物，2虚拟
  const [receiveStatus, setReceiveStatus] = useState(0); // 0未领取，1已领取
  const [isWin, setIsWin] = useState(1); // 是否中奖
  const [isParticipate, setIsParticipate] = useState(1); // 1,为参加过活动的，0则没有参加
  const [prizeInfo, setPrizeInfo] = useState<any>({}); // 奖品信息
  const [currentUser, setCurrentUser] = useState<any>({}); // 当前用户排名
  const [prizeIsExpire, setPrizeIsExpire] = useState<any>(false);
  const statusRef = useRef(1);
  const isStatisticsRef = useRef(false);
  useEffect(() => {
    hinaTrack('flowers_activity_open_view', {
      title: decodeURIComponent(title),
    });
    if (Authorization) {
      Cookies.set('Authorization', Authorization);
    }
    getMessage(() => {
      initData();
    });
    initData();
  }, []);
  const initData = async () => {
    await getData();
    await getActivityPrize();
    await getRankData();
  };
  // 获取活动详情
  const getData = async () => {
    setLoading(true);
    await getActivityInfo({ id })
      .then((res: any) => {
        setDetail(res);
        statusRef.current = res.activity_status;
        setIsParticipate(res.is_sign);
        const diffTimestamp =
          new Date(res.time_end).getTime() - new Date().getTime();
        setCountDownTimestamp(res.time_end);
        setIsStatistics(diffTimestamp > -120000 && diffTimestamp <= 0); // 两分钟内都显示统计中
        isStatisticsRef.current = diffTimestamp > -120000 && diffTimestamp <= 0;
        setStatus(res.activity_status === 1 ? 1 : diffTimestamp > 0 ? 2 : 3);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  // 获取中奖情况
  const getActivityPrize = async () => {
    await activityPrize({ id }).then((res: any) => {
      setIsWin(res.prize_name ? 1 : 0);
      setPrizeType(res.prize_type);
      setReceiveStatus(res.status);
      const isExpire =
        new Date(res.expire_time).getTime() < new Date().getTime();
      setPrizeIsExpire(isExpire);
      setVisibleAddress(
        res.prize_type === 1 && res.status === 0 && !isStatisticsRef.current,
      );
      setVisibleAward(
        !isExpire &&
          res.prize_type !== 1 &&
          res.status === 0 &&
          !isStatisticsRef.current,
      );
      setPrizeInfo(res.prize_name ? res : {});
      setVisibleOver(
        statusRef.current === 3 && !res.prize_name && !isStatisticsRef.current,
      );
    });
  };
  // 活动排行榜
  const getRankData = async () => {
    await getActivityRank({ id }).then((res: any) => {
      const currentUser = res.find((item) => item.is_self === 1);
      setCurrentUser(currentUser);
      setRankData(res);
    });
  };
  const getPrize = (e) => {
    // 防止冒泡
    e.stopPropagation();
    if (prizeType === 1) {
      setVisibleAddress(true);
    } else {
      setVisibleAward(true);
    }
  };
  // 虚拟奖品跳转
  const openPrizeUrl = () => {
    if (prizeInfo.prize_type !== 1) {
      window.ReactNativeWebView.postMessage(prizeInfo.url);
    }
  };
  // 参加活动
  const handleParticipate = () => {
    hinaTrack('flowers_activity_join_click', {
      title: decodeURIComponent(title),
    });
    activitySign({ id }).then((res: any) => {
      setEligibility(res.diff);
      setIsParticipate(1);
      setVisibleParticipation(true);
      initData();
    });
  };
  // 领奖弹出回调
  const receivePrize = (val) => {
    setVisibleAward(false);
    setVisibleAddress(false);
    val && setReceiveStatus(1);
  };
  const HandleVertical = (val) => {
    receivePrize(val);
    if (val) {
      openPrizeUrl();
    }
  };
  const postMessageRN = (val: string) => {
    if (val === 'Do_task') {
      hinaTrack('flowers_activity_todo_click', {
        title: decodeURIComponent(title),
      });
    }
    if (val === 'Invite_family') {
      hinaTrack('flowers_activity_invite_click', {
        title: decodeURIComponent(title),
      });
    }
    try {
      setVisibleParticipation(false);
      window.ReactNativeWebView.postMessage(val);
    } catch (error) {
      console.log('出错了', error);
    }
  };
  const countDownOnFinish = async () => {
    setIsStatistics(true);
    setStatus(3);
    // initData()
  };

  return (
    <>
      <OverLayLoad visible={loading} />
      <div className={styles.bg}>
        {/* 顶部达成条件 */}
        <div className={styles.condition}>
          <div className={styles.conditionTitle}>
            参与条件：小红花数量达{detail.limit_numb}个
          </div>
          <div className={styles.ruleBox} onClick={() => setRuleVisible(true)}>
            <div className={styles.rule}>活动规则</div>
          </div>
        </div>
        {/* 活动内容区域 */}
        <div className={styles.contentBox}>
          <div className={styles.tagBox}>
            <div
              className={status === 2 ? styles.activeTagBg : styles.endTagBg}
            />
            <span
              className={
                status === 2 ? styles.activeTagFont : styles.endTagFont
              }
            >
              {active[status - 1]}
            </span>
          </div>
          <div className={styles.content}>
            <p className={styles.activeTitle}>{detail.title}</p>
            <div
              className={styles.contentInfo}
              dangerouslySetInnerHTML={{ __html: detail.content }}
            />
          </div>
        </div>
        {/* 活动倒计时 */}
        {status === 2 && (
          <div className={styles.countDown}>
            <div className={styles.countDownTitle}>
              前<span className={styles.presenceNum}>{detail.prize_numb}</span>{' '}
              名可获得丰厚奖品
            </div>
            <CountDown
              targetDate={countDownTimestamp <= 0 ? 0 : countDownTimestamp}
              onFinish={() => countDownOnFinish()}
            />
          </div>
        )}
        {/* 本人排行 */}
        {isParticipate === 1 && (
          <div className={styles.frameBox}>
            <div className={styles.info}>
              <div className={`flex flex-row items-center ${styles.rank}`}>
                <span>{currentUser?.rank}</span>
                <Image
                  className={styles.avatar}
                  fit="cover"
                  src={currentUser?.avatar}
                  alt=""
                />
                <span>{currentUser?.name}</span>
                {detail.prize_numb < currentUser?.rank && (
                  <span className={styles.tagStatus}>暂未上榜</span>
                )}
              </div>
              <div className={styles.flower}>
                <div className={styles.flowerNum}>
                  <span>小红花数</span>
                  <span className={styles.redFlowerNum}>
                    {currentUser?.score}
                  </span>
                </div>
                <div className={styles.friends}>
                  {currentUser?.parent_numb} 位亲友团助力
                </div>
              </div>
            </div>
            {/* 活动结束参与并中奖 */}
            {status === 3 && isWin === 1 && !isStatistics && (
              <div className={styles.pickUpStatusBox}>
                <div className={styles.pickUpStatus} onClick={openPrizeUrl}>
                  <p className={styles.getPrizeTitle}>
                    恭喜获得{prizeInfo.prize_title}：{prizeInfo.prize_name}
                  </p>
                  <div className="flex flex-row items-center justify-between">
                    {receiveStatus === 1 ? (
                      <div className={styles.getPrizeBut}>
                        {prizeType === 1
                          ? '已提交地址，等待收货吧～'
                          : `奖品有效期为 ${prizeInfo.expire_day} 天`}
                      </div>
                    ) : !prizeIsExpire || prizeType === 1 ? (
                      <>
                        <p className={styles.getPrizeTip}>快去领取奖品吧～</p>
                        <div className={styles.getButBox} onClick={getPrize}>
                          <div className={styles.getBut}>去领取</div>
                        </div>
                      </>
                    ) : (
                      <p className={styles.getPrizeTip}>奖品已过期</p>
                    )}
                  </div>
                </div>
                {receiveStatus === 1 && (
                  <Image
                    src="/images/flowerActivityTemplate/status.png"
                    className={styles.statusImg}
                    alt=""
                  />
                )}
              </div>
            )}
            {/* 活动结束参与但没有中奖 */}
            {status === 3 && isWin === 0 && !isStatistics && (
              <div className={styles.overStatusBox}>
                <Image
                  src="/images/flowerActivityTemplate/over.png"
                  className={styles.overStatusImg}
                  alt=""
                />
                <div className={styles.overUpStatus}>
                  <p className={styles.overUpStatusTitle}>
                    很遗憾，差一点就中奖了......
                  </p>
                  <p className={styles.overUpStatusTip}>期待您下次再来</p>
                </div>
              </div>
            )}
            {status === 2 && (
              <div className="flex flex-row items-center justify-center">
                <div
                  className={styles.butBox}
                  onClick={() => postMessageRN('Invite_family')}
                >
                  <div className={styles.but}>邀请家人助力</div>
                </div>
                <div
                  className={styles.butBoxRight}
                  onClick={() => postMessageRN('Do_task')}
                >
                  <div className={styles.butRight}>做任务助力</div>
                </div>
              </div>
            )}
            {isStatistics && (
              <div className={styles.overStatusBox}>
                <div className={`${styles.overUpStatus} !items-center`}>
                  <p className={styles.overUpStatusTip}>
                    活动结果正在统计中,请稍再来查看...
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
        {isParticipate === 0 && status === 3 && (
          <div className={`${styles.overStatusBox} ${styles.overStatusBox2}`}>
            <Image
              src="/images/flowerActivityTemplate/isOver.png"
              className={`${styles.overStatusImg} ${styles.overStatusImg2}`}
              alt=""
            />
            <div className={styles.overUpStatus}>
              <p className={styles.overUpStatusTip2}>
                本次活动已结束，敬请期待下次活动～
              </p>
            </div>
          </div>
        )}
        {/* 1,2,3名排行 */}
        {rankData.length > 0 && (
          <div className={styles.topRankBox}>
            <Image
              className={styles.topRankBg}
              src="/images/flowerActivityTemplate/rankBg.png"
              alt=""
            />
            {rankData.map((item: any, index: number) => {
              if (item.rank < 4) {
                return (
                  <div
                    key={item.user_id + index.toString()}
                    className="flex flex-col items-center"
                  >
                    <div
                      className={
                        item.rank === 1
                          ? styles.firstPlace
                          : item.rank === 2
                            ? styles.secondPlace
                            : styles.thirdPlace
                      }
                    >
                      <div
                        className={
                          item.rank === 1
                            ? styles.topRankRankName
                            : item.rank === 2
                              ? styles.secondRankName
                              : styles.thirdRankName
                        }
                      >
                        {item.name}
                      </div>
                      <div className="flex flex-col items-center">
                        <p className={styles.topRankFlowerNum}>
                          小红花数{' '}
                          <span className={styles.topRankRedFlowerNum}>
                            {item.score}
                          </span>
                        </p>
                        <p className={styles.topRankFriends}>
                          {item.parent_numb} 位亲友团助力
                        </p>
                      </div>
                    </div>
                    <Image
                      className={
                        item.rank === 1
                          ? styles.firstRankAvatar
                          : item.rank === 2
                            ? styles.secondRankAvatar
                            : styles.thirdRankAvatar
                      }
                      alt=""
                      fit="cover"
                      src={
                        item.avatar ||
                        'https://edu-media.ancda.com/test/contactList/2023-06-28/sS6teAPQ.png'
                      }
                    />
                  </div>
                );
              }
              return null;
            })}
          </div>
        )}
        {/* 剩余排行 */}
        {rankData.length > 0 ? (
          <div className={`${styles.frameBox} ${styles.footerFrameBox}`}>
            {rankData.map((item: any, index) => {
              return (
                <div
                  className={styles.info}
                  key={item.user_id + index.toString()}
                >
                  <div className={`flex flex-row items-center ${styles.rank}`}>
                    <span>{item.rank}</span>
                    <Image
                      className={styles.avatar}
                      fit="cover"
                      src={item.avatar}
                      alt=""
                    />
                    <span>{item.name}</span>
                  </div>
                  <div className={styles.flower}>
                    <div className={styles.flowerNum}>
                      <span>小红花数</span>
                      <span className={styles.redFlowerNum}>{item.score}</span>
                    </div>
                    <div className={styles.friends}>
                      {item.parent_numb} 位亲友团助力
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div
            onClick={() => postMessageRN('Invite_family')}
            className={`${styles.frameBox} ${styles.footerFrameBox} ${styles.noRank}  `}
          >
            {status === 1
              ? '活动尚未开始,敬请期待！'
              : '还没有人报名赶紧去报名吧！'}
          </div>
        )}
        {/* 底部按钮 */}
        {status === 2 && isParticipate === 0 && (
          <div className={styles.footerButBox}>
            <div className={styles.footerButBoxRight}>
              <div className={styles.butRight} onClick={handleParticipate}>
                立即参加
              </div>
            </div>
          </div>
        )}
      </div>
      {/* 立即参加弹窗 */}
      <Popups
        visible={visibleParticipation}
        title="立即参加"
        onClose={() => setVisibleParticipation(false)}
      >
        <div className={styles.popupsContent}>
          <div>
            {eligibility ? (
              <div>
                <span>很遗憾，你没有达到本次活动的参与条件，还差</span>
                <span style={{ color: '#FD484E' }}>{eligibility}</span>
                <span>
                  朵小红花，快去做任务或邀请亲友团助力，获取小红花吧～
                </span>
              </div>
            ) : (
              '已报名成功，快去做任务或者邀请亲友团助力，获取小红花吧～'
            )}
          </div>
          <div
            className={`flex flex-row items-center justify-center ${styles.popupButton}`}
          >
            <div
              className={styles.butBox}
              onClick={() => postMessageRN('Invite_family')}
            >
              <div className={styles.but}>邀请家人助力</div>
            </div>
            <div
              className={styles.butBoxRight}
              onClick={() => postMessageRN('Do_task')}
            >
              <div className={styles.butRight}>做任务助力</div>
            </div>
          </div>
        </div>
      </Popups>
      {/* 活动规则弹窗 */}
      <Popups
        visible={ruleVisible}
        title="活动规则"
        onClose={() => setRuleVisible(false)}
      >
        <div className={styles.popupsContent}>
          <div
            dangerouslySetInnerHTML={{ __html: detail.rule }}
            className="text-wrap break-words"
          />
        </div>
      </Popups>
      {/* 收货地址 */}
      <Address
        visibleAddress={visibleAddress}
        prizeInfo={prizeInfo}
        id={id}
        onClose={(val) => receivePrize(val)}
      />
      {/* 领取虚拟物品 */}
      <VirtualGift
        visibleAward={visibleAward}
        prizeInfo={prizeInfo}
        id={id}
        onClose={HandleVertical}
      />
      {/* 活动结束 */}
      <PopupsOver
        visibleOver={visibleOver}
        onClose={() => setVisibleOver(false)}
        status={isParticipate == 1 && status == 3}
      />
    </>
  );
};
export default Index;
