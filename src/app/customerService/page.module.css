.download{
  background-color: aqua;
  width: 120px;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: #fff;
  cursor: pointer;
  border-radius: 12px;
  align-self: center;
  margin: 0 auto;
}
.bodyContent{
  background-color: #FAFBFB;
}
.contentPadding{
  padding: 32px 32px 0 32px;
  flex: 1;
}
.avatar{
  width: 89px;
  height: 89px;
}

.message{
  background: #FDEDB1;
  padding: 20px 34px;
  border-radius: 20px;
  margin-left: 30px;
  position: relative;
}
.triangle{
  border:20px solid transparent;
  border-right-color: #FDEDB1;
  width: 0;
  height: 0;
  position: absolute;
  top: 20px;
  left: -38px;
}

.message h3{
  font-size: 30px;
  color: #333333;
}
.message p{
  font-size: 26px;
  color: #333333;
}
.title{
  font-size: 36px;
  color: #333333;
  margin-top: 40px;
  margin-bottom: 20px;
  padding-left: 10px;
}
.question{
  background-color: #fff;
  padding: 20px 34px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
}
.questionItem{
  padding:36px 0 ;
}
.questionItem p{
  margin-left: 30px;
  font-size: 32px;
  color: #333333;
}
.questionNum{
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #FADE6B;
  position: relative;
}
.questionNum span{
  position: absolute;
  left:8px
}
.questionItem img{
  width: 30px;
  height: 30px;
}
.service{
  background-color: #fff;
  margin-top: 30px;
}
.service img{
  width: 59px;
  padding: 30px 0;
  margin-right: 20px;
}
.service span{
  font-size: 32px;
  color: #333333;
}