"use client";

import React, { useEffect, useState, useRef } from "react";
import styles from "./page.module.css";
import YSF from "@neysf/qiyu-web-sdk";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";

const CustomerService = () => {
  const router = useRouter();

  const searchParams = useSearchParams() as any;
  const [mobile] = useState(searchParams.get("mobile") || "");
  const [name] = useState(searchParams.get("name") || "");
  const [uid] = useState(searchParams.get("uid") || "");
  const [templateId] = useState(searchParams.get("templateId") || "");
  const ysfInstance = useRef<any>(undefined);
  const [questionData] = useState([
    {
      id: 1,
      title: "错题如何组卷导出？",
    },
    {
      id: 2,
      title: "组成的练习卷可以二次打印吗？",
    },
    {
      id: 3,
      title: "如何成为VIP？",
    },
    {
      id: 4,
      title: "开通会员后可以取消吗？",
    },
    {
      id: 5,
      title: "为什么VIP会自动扣费？",
    },
    {
      id: 6,
      title: "苹果设备如何取消连续订阅",
    },
    {
      id: 7,
      title: "安卓设备如何取消连续包月/年",
    },
    {
      id: 8,
      title: "不小心开通了两次怎么办",
    },
  ]);
  const initYSF = async () => {
    if (!ysfInstance.current) {
      ysfInstance.current = await YSF.init(
        `5486e9261b85af09ba48accfc5f4dcaf`,
        {
          hidden: 1,
        },
        null,
      );
    }
    if (ysfInstance.current) {
      ysfInstance.current("config", {
        uid,
        name,
        mobile,
        success: function () {
          console.log("信息上报成功");
        },
        error: function () {
          console.log("信息上报失败");
        },
      });
    }
  };
  useEffect(() => {
    initYSF();
  }, [mobile, name, uid]);
  const handleService = () => {
    // ysfInstance.current('open', {
    //   templateId,
    // });
    window.location.href = `${ysfInstance.current(
      "url",
    )}&templateId=${templateId}`;
  };
  const goDetail = () => {
    router.push("/content/commonProblem");
  };
  return (
    <div className={`flex flex-col min-h-screen ${styles.bodyContent}`}>
      <div className={`flex flex-col ${styles.contentPadding}`}>
        <div className={`flex flex-row `}>
          <img
            className={styles.avatar}
            src="/images/service/logo.png"
            alt=""
          />
          <div className={`flex flex-col justify-center ${styles.message}`}>
            <div className={styles.triangle}></div>
            <h3>{name}您好，我是小牛</h3>
            <p>您的专属管家，有什么问题随时问我哦～</p>
          </div>
        </div>
        <h2 className={styles.title}>猜你想问</h2>
        <div className={styles.question} onClick={goDetail}>
          {questionData.map((item, index) => {
            return (
              <div
                key={item.id}
                className={`flex flex-row items-center justify-between ${styles.questionItem}`}
              >
                <div className="flex flex-row justify-center">
                  <div className={styles.questionNum}>
                    <span>Q{index + 1}</span>
                  </div>
                  <p>{item.title}</p>
                </div>
                <img src="/images/service/arrow.png" alt="" />
              </div>
            );
          })}
        </div>
      </div>
      <div
        className={`flex flex-row items-center justify-center ${styles.service}`}
        onClick={handleService}
      >
        <img src="/images/service/service.png" alt="" />
        <span>联系在线客服</span>
      </div>
    </div>
  );
};
export default CustomerService;