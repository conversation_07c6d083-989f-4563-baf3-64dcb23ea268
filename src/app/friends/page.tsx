'use client';

import { ActionSheet, Button, Dialog, Form, Input, Toast } from 'antd-mobile';
import Head from 'next/head';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { addParent, getStudentInfo, getUnActiveParent } from '@/api/friend';
import VerificationCodeButton from '@/components/VerificationCodeButton';
import { isPalmBaby } from '@/lib/utils';
import { getAge } from '@/utils';
import { relationsFilter } from '@/utils/filters';

import styles from './index.module.css';

const Friend = () => {
  const searchParams = useSearchParams();
  const isPalmBabyApp = isPalmBaby(window.location.hostname);
  const sid = searchParams?.get('sid') || null;
  const instId = searchParams?.get('instId') || null;
  const userName = searchParams?.get('userName') || null;
  const userId = searchParams?.get('userId') || null;

  const [parent, setParent] = useState({ name: '', relation: '' });
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [submitLoading, setSubmitLoading] = useState(false);
  const [resSuccessInfo, setResSuccessInfo] = useState({
    mobile: '',
    pwd: '',
  });
  const [hadInvitedFriends, setHadInvitedFriends] = useState<string[]>([]);
  const [userInfo, setUserinfo] = useState({
    name: '宝贝',
    birthday: '',
    avatar: '',
  });
  const [isFull, setIsFull] = useState(false);
  const [relation, setRelation] = useState([]);
  const [verificationCodeDisabled, setVerificationCodeDisabled] =
    useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [relationActionSheetVisible, setRelationActionSheetVisible] =
    useState(false);

  useEffect(() => {
    if (sid && instId) {
      initData();
    }
  }, [sid, instId]);

  const getStudentInfoFun = async () => {
    getStudentInfo({ sid, instId }).then((res: any) => {
      setUserinfo({
        name: res?.name,
        birthday: getAge(res?.birthday),
        avatar: res.avatar,
      });
      setHadInvitedFriends(res.parentList);
    });
  };

  const getUnActive = async () => {
    getUnActiveParent({ sid, instId }).then((res: any) => {
      setRelation(
        res.list?.map((item, index) => ({
          text: relationsFilter(item),
          key: item,
          onClick: () => {
            setParent({ name: relationsFilter(item), relation: item });
            setRelationActionSheetVisible(false);
          },
        })),
      );
      setIsFull(!res.list?.length);
    });
  };

  const initData = async () => {
    await getUnActive();
    await getStudentInfoFun();
  };

  const updatePhone = ({ text }: { text: string }) => {
    let disabled = true;
    if (/1\d{10}/.test(text)) {
      disabled = false;
    }
    setVerificationCodeDisabled(disabled);
    setPhone(text);
  };
  const updateCode = ({ text }: { text: string }) => {
    setCode(text);
  };

  const submitAction = () => {
    if (!parent.relation || !phone || !code) {
      Toast.show('请完善信息');
      return false;
    }
    if (!/1\d{10}/.test(phone)) {
      Toast.show('手机号码格式错误');
      return false;
    }
    setSubmitLoading(true);
    addParent({
      sid, // 学生id
      instId, // 学校id
      userId, // 谁分享谁的id
      userName, // 谁分享谁的姓名
      mobile: phone,
      verifyCode: code,
      scene: 6, // 场景(1找回密码 2绑定账号 3注册机构（园所） 4更换手机号 5修改登录密码、6-亲友团邀请)
      name: userInfo.name, // 学生名
      relation: parent.relation,
      source: 4, // 家长来源 (1-后台添加、2-扫码入班、3-园丁端APP添加、4-亲友团邀请
    })
      .then(async (res: any) => {
        if (res.reason) {
          showDialog(res.reason);
        } else {
          setIsSuccess(true);
          setResSuccessInfo({ mobile: res.mobile, pwd: res.pwd });
        }
      })
      .finally(() => {
        setSubmitLoading(false);
      });
  };
  const showDialog = (reason: string) => {
    Dialog.alert({
      title: '加入失败',
      content: (
        <div style={{ textAlign: 'left', margin: '16px' }}>
          <p className="py-2">失败原因:{reason}</p>
        </div>
      ),
    });
  };
  return (
    <div className={styles.friends}>
      <Head>
        <title>{`${userInfo.name}的亲友团`}</title>
      </Head>
      {isFull ? (
        <div className="px-4 pt-24">
          <div className={styles.mainContent}>
            <div className={styles.mainContentBgImg} />
            <div className={styles.fullTxt}>
              {userInfo.name}宝贝的亲友团已满
            </div>
          </div>
        </div>
      ) : (
        <div>
          <div className={styles.topImg} />
          <div className={styles.topPart}>
            <div className={styles.avatarBox}>
              <img
                className={styles.avatarBoxImg}
                src={userInfo.avatar}
                alt=""
                width="80%"
              />
            </div>
            <div className={styles.name}>{userInfo.name}</div>
            <div className={styles.age}>{userInfo.birthday}</div>
            <div className={styles.inviteTxt}>
              <img src="/images/invite/inviteText.png" alt="" width="100%" />
            </div>
            <div className={styles.tip}>
              <div className={styles.tipBg} />
              <div className={styles.tipText}>
                加入亲友团，全面了解宝贝在园情况，共同呵护宝贝成长
              </div>
            </div>
          </div>
          {isSuccess ? (
            <div
              style={{
                textAlign: 'left',
                margin: '16px',
                background: '#fff',
                padding: '20px',
              }}
            >
              <p className="flex items-center justify-center">加入成功</p>
              <p className="py-2">登录账号：{resSuccessInfo.mobile}</p>
              <p className="py-2">
                登录密码：
                {resSuccessInfo.pwd
                  ? resSuccessInfo.pwd
                  : '前面已经注册了学生，则密码还是使用原来的密码登录'}
              </p>
            </div>
          ) : (
            <>
              <div className={styles.firstStep}>
                <Form>
                  <Form.Item>
                    <div onClick={() => setRelationActionSheetVisible(true)}>
                      {parent.name ? (
                        <span className={styles.relation}>{parent.name}</span>
                      ) : (
                        <span className={styles.relationDefault}>
                          请选择您和宝贝的关系
                        </span>
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item>
                    <Input
                      value={phone}
                      type="number"
                      onChange={(text) => updatePhone({ text })}
                      placeholder="请输入您的手机号"
                      maxLength={11}
                    />
                  </Form.Item>
                  <Form.Item
                    extra={
                      <VerificationCodeButton
                        isPalmBaby={isPalmBabyApp}
                        phoneNumber={phone}
                        disabled={verificationCodeDisabled}
                        optionConfig={null}
                        scene={6}
                        className={styles.codeBtn}
                      />
                    }
                  >
                    <div className="flex flex-row">
                      <Input
                        type="number"
                        maxLength={6}
                        value={code}
                        onChange={(text) => updateCode({ text })}
                        placeholder="请输入验证码"
                      />
                    </div>
                  </Form.Item>
                </Form>
              </div>
              <div className="px-4">
                <Button
                  block
                  color="primary"
                  className="!rounded-full"
                  loading={submitLoading}
                  onClick={() => submitAction()}
                >
                  立即加入亲友团
                </Button>
              </div>
            </>
          )}
        </div>
      )}
      {!isSuccess && (
        <div
          className={`${styles.footer} flex flex-col items-start justify-center`}
        >
          <div style={{ color: '#999' }}>
            已经加入的{userInfo.name}的亲友团有：
            {hadInvitedFriends.map((item, index) => (
              <span key={item} className="text-stone-500">
                {index !== 0 && '、'}
                <span>{item}</span>
              </span>
            ))}
          </div>
        </div>
      )}

      <ActionSheet
        visible={relationActionSheetVisible}
        extra="选中与宝贝的关系"
        actions={relation}
        onClose={() => {
          setRelationActionSheetVisible(false);
        }}
      />
    </div>
  );
};
export default Friend;