.friends {
  min-height: 100vh;
  background: rgb(248, 248, 248);
  font-size: 26px;
}
.topImg {
  width: 750px;
  height: 504px;
  background: url('/images/invite/bg.png') no-repeat center 0px;
  background-size: cover;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-top: -100px;
}
.topPart {
  width: 690px;
  margin-top: -100px;
  margin-left: 30px;
  height: 332px;
  background: #fff;
  border-radius: 21px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatarBox {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #fff;
  margin-top: -50px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.avatarBoxImg {
  border-radius: 50%;
}
.name {
  margin-top: 12px;
  font-size: 30px;
  color: #333;
  font-weight: 500;
}
.age {
  margin-top: 6px;
  font-size: 22px;
  color: #999;
}
.inviteTxt {
  width: 120px;
  height: 40px;
  margin-top: 32px;
  font-size: 30px;
  color: #fff;
  text-shadow: 1px 1px 1px #ff9e2d;
  font-weight: 500;
}
.tip {
  width: 610px;
  display: flex;
  margin-top: 40px;
  font-size: 24px;
  color: #000;
  font-weight: 400;
  height: 20px;
  position: relative;
  text-align: center;
  background-color: rgba(254, 202, 45, 0.2);
}
.tipText {
  width: 100%;
  margin-top: -20px;
}

.firstStep {
  margin: 30px;
  margin-top: 26px;
  background: #fff;
  border-radius: 21px;
  padding-left: 29px;
  padding-right: 39px;
  padding-top: 53px;
  padding-bottom: 40px;
}
.relation {
  color: #323232;
}
.relationDefault {
  color: #ccc;
}
.codeBtn {
  text-align: right;
  /*color: #feca2d;*/
  border: none;
}
.footer {
  margin-left: 60px;
  margin-top: 60px;
  margin-right: 60px;
}
.fullTitle {
  margin: 30px;
  margin-top: 0;
  padding-top: 30px;
  color: #999999;
  background-color: #fff;
}
.mainContent {
  height: auto;
  padding-bottom: 60px;
  padding-top: 10px;
}
.mainContentBgImg {
  margin-top: 73px;
  margin-left: 180px;
  width: 309px;
  height: 309px;
  background: url('/images/invite/full.png') no-repeat center 0;
  background-size: cover;
}
.fullTxt {
  margin-top: 24px;
  margin-left: 73px;
  margin-right: 73px;
  color: #999999;
  font-size: 28px;
  text-align: center;
}