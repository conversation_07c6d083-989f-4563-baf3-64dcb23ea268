'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';

import { hinaTrack } from '@/utils';

const Button = () => {
  const router = useRouter();
  useEffect(() => {
    hinaTrack('liveStreamPage');
  }, []);
  const goRegister = () => {
    router.push('/register?sourceType=5');
    hinaTrack('liveStreamBtnClick');
  };
  return (
    <div
      className="fixed bottom-0 left-0 h-[150px] w-full bg-[#f1f6ff]"
      onClick={goRegister}
    >
      <Image
        src="/images/liveStreamBtn.png"
        width={0}
        height={0}
        sizes="100vw"
        className="pulse absolute inset-x-0 mx-auto h-[155px] w-[800px]"
        alt=""
      />
    </div>
  );
};

export default Button;
