import Image from 'next/image';

import Button from './components/Button';
import { siteConfigKid } from '@/constant/config';

export const metadata = {
  title: siteConfigKid.title,
  description: siteConfigKid.description,
  openGraph: {
    title: siteConfigKid.title,
    description: siteConfigKid.description,
  },
};

const liveStream = () => {
  return (
    <div className="flex h-screen flex-col pb-[150px]">
      <div className="overflow-y-scroll">
        <Image
          src="/images/liveStream.png"
          width={0}
          height={0}
          sizes="100vw"
          className="w-screen object-cover"
          alt=""
        />
      </div>
      <Button />
    </div>
  );
};

export default liveStream;
