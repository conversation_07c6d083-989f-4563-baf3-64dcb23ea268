'use client';

import { SpinLoading } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { contractSignSuccess } from '@/api/callback';
import { PiCheckCircleFill, PiWarningCircleFill } from '@/components/Icons';

const Page = () => {
  const searchParams = useSearchParams();
  const contractId = searchParams?.get('id') || null;
  const instId = searchParams?.get('instId') || null;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  useEffect(() => {
    if (contractId && instId) {
      setLoading(true);
      contractSignSuccess({ contractId, instId })
        .then((res: any) => {
          setLoading(false);
        })
        .catch((err) => {
          setLoading(false);
          setError(err);
        });
    }
  }, [contractId, instId]);
  return (
    <div className="flex h-screen flex-col items-center justify-center bg-white">
      {loading ? (
        <SpinLoading
          className="!absolute h-screen w-full items-center justify-center bg-white"
          color="#4E78FF"
        />
      ) : !error ? (
        <div className="flex flex-col items-center justify-center">
          <PiCheckCircleFill color="#4E78FF" fontSize="64" />
          <div className="mt-8 text-xl">签署完成</div>
          <div className="mt-2 text-gray-500">
            您已签署完成,可以返回合同列表查看
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center">
          <PiWarningCircleFill color="#FF3141" fontSize="64" />
          <div className="mt-8 text-xl">签署失败</div>
          <div className="mt-2 text-gray-500">
            签署失败,请返回重新签署或者咨询客服
          </div>
        </div>
      )}
    </div>
  );
};
export default Page;