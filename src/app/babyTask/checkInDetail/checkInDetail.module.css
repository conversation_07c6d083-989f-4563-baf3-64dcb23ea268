.container {
  position: relative;
  z-index: 2;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.sectionMain {
  padding: 28px 3.5vw;
  height: auto;
  flex: 1;
}
.main {
  font-size: 26px;
  line-height: 45px;
  padding-top: 29px;
  word-break: break-all;
  background-color: #fff;
}
.oneImgBox {
  width: 100%;
  height: auto;
}
.twoImgBox {
  position: relative;
  z-index: 1;
  width: 339px;
  height: 339px;
  margin-right: 16px;
  overflow: hidden;
}
.twoImgBoxImg {
  margin: 0;
  padding: 0;
  width: 339px;
  min-height: 339px;
  object-fit: cover;
}
.threeMoreImgBox {
  position: relative;
  z-index: 1;
  width: 224px;
  height: 224px;
  margin-right: 10px;
  margin-bottom: 10px;
  overflow: hidden;
}
.threeMoreImgBoxImg {
  margin: 0;
  padding: 0;
  width: 224px;
  min-height: 224px;
  object-fit: cover;
}

.mask {
  position: absolute;
  top: 0;
  z-index: 2;
  width: 224px;
  height: 224px;
  line-height: 224px;
  text-align: center;
  font-size: 50px;
  color: #fff;
  font-weight: bold;
  background: rgba(0, 0, 0, 0.5);
}
.userInfo {
  display: flex;
}
.userInfoAvatar {
  width: 90px;
  height: 90px;
  border-radius: 45px;
  margin-right: 20px;
}
.userInfoAvatarName {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.name {
  height: 44px;
  line-height: 44px;
  font-size: 28px;
  font-weight: 600;
}
.time {
  font-size: 20px;
}
