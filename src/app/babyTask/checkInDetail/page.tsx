'use client';

import { Image, ImageViewer } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getClockInDetailInfo } from '@/api/babyTask';
import FooterQrCode from '@/components/FooterQrcode';
import { wechatShare } from '@/utils/wechat';

import styles from './checkInDetail.module.css';

export const dynamic = 'force-dynamic';

const Page = () => {
  const searchParams = useSearchParams();
  const checkInId = searchParams?.get('checkInId') || null;
  const [contentData, setContentData] = useState({
    clockinId: 2,
    content: '',
    clockinResource: [],
    createTime: '',
    studentName: '',
    parentName: '',
    relation: '',
    avatar: '',
    needClockinNum: 0,
    clockinTimes: 1,
    likeNum: 0,
    commentNum: 0,
    isLike: 0,
    isMyClockin: 0,
  });
  const [photos, setPhotos] = useState<string[]>([]);
  const [videos, setVideos] = useState();
  const [audios, setAudios] = useState();

  useEffect(() => {
    if (checkInId) {
      getInfoFun();
    }
  }, [checkInId]);

  const getInfoFun = async () => {
    console.log('clockInId: ', checkInId);
    await getClockInDetailInfo({ clockinId: checkInId }).then((res: any) => {
      console.log('res: ', res);
      const imagesTemp: string[] = [];

      res.data.clockinResource.map((item: any) => {
        if (item.resourceType === 1) {
          imagesTemp.push(item.url);
        } else if (item.resourceType === 2) {
          setVideos(item.url);
        } else if (item.resourceType === 3) {
          setAudios(item.url);
        }
      });
      const wxData = {
        title: `${res.data?.studentName || '宝贝'}完成了打卡任务`,
        desc: res.data?.content,
        link: window.location.href,
        imgUrl: imagesTemp?.length > 0 ? imagesTemp[0] : '',
      };
      setPhotos(imagesTemp);
      setContentData(res.data);
      wechatShare(wxData);
    });
  };
  return (
    <div className="flex min-h-screen flex-col">
      <div className={styles.container}>
        <div className={styles.sectionMain}>
          <div className={styles.userInfo}>
            <Image
              className={styles.userInfoAvatar}
              src={contentData.avatar || '/images/avatar.png'}
              alt=""
            />
            <div className={styles.userInfoAvatarName}>
              <p className={styles.name}>{contentData.studentName}</p>
              <span className={styles.time}>{contentData.createTime}</span>
            </div>
          </div>
          <div className={styles.main}>
            <p style={{ textIndent: '1em' }}>{contentData.content}</p>
            <div className="mt-2 flex flex-wrap justify-start">
              {photos.map((item, index) => {
                return (
                  <div
                    style={{ width: '31vw', height: '31vw' }}
                    className="flex items-center justify-center "
                    key={index}
                  >
                    <Image
                      src={item}
                      width="29vw"
                      height="29vw"
                      onClick={() => {
                        ImageViewer.Multi.show({
                          images: photos,
                          defaultIndex: index,
                        });
                      }}
                      alt=""
                    />
                  </div>
                );
              })}
            </div>
            {!!videos && (
              <div className="mt-5">
                <video
                  src={videos}
                  controls
                  width="100%"
                  height="auto"
                  poster={`${videos}?x-workflow-graph-name=video-thumbnail`}
                >
                  你的浏览器不支持
                </video>
              </div>
            )}
            {!!audios && (
              <div className="mt-5 flex justify-center">
                <audio controls src={audios} className="w-full">
                  Your browser does not support the
                  <code>audio</code> element.
                </audio>
              </div>
            )}
          </div>
        </div>
      </div>
      <FooterQrCode />
    </div>
  );
};
export default Page;
