'use client';

import React, { useEffect, useState } from 'react';

import { getClockInDetail } from '@/api/babyTask';

export const dynamic = 'force-dynamic';

if (typeof document !== 'undefined') {
  document.title = '21天亲子任务打卡';
}

const Page = () => {
  const [isAward, setIsAward] = useState(true);

  useEffect(() => {
    getInfoFun();
  }, []);

  const getInfoFun = () => {
    getClockInDetail({ taskId: 471960 }).then((res: any) => {
      if (res.data.isAward === 1 && res.data.awardUrl) {
        window.location.href = res.data.awardUrl;
      } else {
        setIsAward(false);
      }
    });
  };

  return (
    <div className="flex min-h-screen flex-col">
      {!isAward && (
        <div className="flex justify-center items-center h-screen">
          很遗憾你未完成本期任务，请期待下次活动哦！
        </div>
      )}
    </div>
  );
};
export default Page;
