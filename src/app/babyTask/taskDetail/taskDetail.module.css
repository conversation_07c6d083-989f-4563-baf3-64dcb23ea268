.container {
  color: #333;
  flex: 1;
}
.bodyContent {
  background: url('https://file.ancda.com/public/file/app/task/background.png')
    no-repeat top center;
  background-size: cover;
  height: 418px;
}
.userinfo {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.userHeader {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.userName {
  font-size: 26px;
  margin-left: 2vw;
}
.taskNumber {
  flex: 1;
  text-align: right;
}
.taskTitledec {
  padding-left: 5vw;
  padding-right: 5vw;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-top: 25px;
  margin-top: 8px;
}
.leftTitle {
  flex: 1;
  flex-direction: column;
}
.leftTitleTitle {
  margin-bottom: 8px;
  font-size: 20px;
}
.subRow {
  display: flex;
  margin-bottom: 8px;
  flex-direction: row;
}
.subRowSubfont {
  margin-right: 8px;
}
.subRowSubnomfont {
  font-size: 14px;
}
.subRowSubsmfont {
  font-size: 12px;
}
.rightImg {
  width: 15vw;
  height: 15vw;
}
.cover {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
.otherContent {
  flex: 1;
  margin-top: -200px;
}
.taskdec {
  box-shadow: 0 0 10px 0px #ccc;
  margin: 0 5vw 5vw;
  padding: 15px 25px;
  background-color: #fff;
  border-radius: 20px;
  height: 200px;
}
.declabel {
  color: #000;
  font-size: 28px;
  font-weight: 400;
}
.decdetail {
  display: flex;
  flex-direction: row;
  margin-top: 15px;
}
.decdetailinfobox {
  max-height: 80px;
  flex: 1;
  letter-spacing: 1px;
}
.decdetailinfo {
  font-size: 26px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.decdetailimg {
  width: 15vw;
  height: 15vw;
  margin-left: 5vw;
}
.decdetailImage {
  border-radius: 10px;
  width: 100%;
  height: 100%;
}
.goDetail {
  color: #4e78ff;
  font-size: 28px;
  margin-top: 10px;
}
.taskSub {
  border-bottom: 1px solid #eee;
  padding: 20px 0vw 10px;
  margin: 0 5vw;
}
.subheader {
  flex-direction: row;
  display: flex;

  justify-content: flex-start;
  align-items: center;
  box-sizing: border-box;
}
.taskDetail {
  font-size: 26px;
  padding: 10px 0;
  text-indent: 2em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.moredetail {
  padding: 0 5vw;
  width: 80px;
  color: #5ebea3;
  font-size: 14px;
}
.centerVideo {
  width: 100%;
  margin: 15px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: 300px;
}
.video {
  width: 100%;
  border-radius: 20px;
}
.centerImage {
  flex-direction: row;
  display: flex;
  flex-wrap: wrap;
}
.imageSubclass {
  padding: 1.5vw;
  width: 30vw;
  height: 30vw;
  box-sizing: border-box;
  overflow: hidden;
}
.imageSubclassImg {
  border-radius: 10px;
}
.centerAudio {
  flex-direction: row;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center;
  margin: 25px 0;
}
.audio {
  width: 100%;
}
.footerTime {
  flex-direction: row;
  display: flex;
  padding: 20px 0 10px;
}
.dateTime {
  flex: 1;
}
.iconrow {
  flex: 1;
  text-align: right;
  font-size: 18px;
}
.iconrowcomment {
  margin-right: 20px;
  font-size: 28px;
}
