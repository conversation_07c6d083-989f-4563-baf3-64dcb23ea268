'use client';

import { Image, ImageViewer } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getClockInList, getTaskDetailInfo } from '@/api/babyTask';
import FooterQrCode from '@/components/FooterQrcode';
import { wechatShare } from '@/utils/wechat';

import styles from './taskDetail.module.css';

export const dynamic = 'force-dynamic';

// 类型定义
interface Resource {
  resourceType: number;
  url: string;
}

interface TaskInfo {
  title: string;
  content: string;
  cover: string;
  clockinPeopleNum: number;
  teacherName: string;
  createTime: string;
  startDate: string;
  endDate: string;
  resource: Resource[];
}

interface CheckInItem {
  avatar: string;
  studentName: string;
  clockinTimes: number;
  needClockinNum: number;
  content: string;
  clockinResource: Resource[];
}

interface ApiResponse<T> {
  data: T;
}

const Page = () => {
  const searchParams = useSearchParams();
  const taskId = searchParams?.get('taskId') || null;
  const [taskInfo, setTaskInfo] = useState<TaskInfo | null>(null);
  const [checkInList, setCheckInList] = useState<CheckInItem[]>([]);
  const [endDays, setEndDays] = useState<string>('');
  const [taskDescriptionImg, setTaskDescriptionImg] = useState<string>('');

  useEffect(() => {
    if (taskId) {
      getDetail();
      getCheckList();
    }
  }, [taskId]);

  const getCheckList = () => {
    getClockInList({ taskId, type: 1 }).then(
      (res: ApiResponse<{ clockinList: CheckInItem[] }>) => {
        setCheckInList(res.data?.clockinList || []);
      }
    );
  };

  const getDetail = () => {
    getTaskDetailInfo({ taskId }).then((res: ApiResponse<TaskInfo>) => {
      const taskData = res.data;
      const wxData = {
        title: taskData?.title || '',
        desc: taskData?.content || '',
        link: window.location.href,
        imgUrl: taskData?.cover || ''
      };
      wechatShare(wxData);
      setTaskInfo(taskData);
      countdown(taskData.startDate, taskData.endDate);
      setTaskDescriptionImg(
        taskData.resource.find((i: Resource) => i.resourceType === 1)?.url || ''
      );
    });
  };

  const countdown = (startDate: string, endDate: string) => {
    const now = new Date().getTime();
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    // 距离结束时间
    const nowToEndDays = end - now;
    setEndDays(formatSecToStr(nowToEndDays));
  };

  // 参数：毫秒
  const formatSecToStr = (seconds: number) => {
    const daySec = 24 * 60 * 60 * 1000;
    const hourSec = 60 * 60 * 1000;
    const minuteSec = 60 * 1000;
    const dd = Math.round(seconds / daySec);
    const hh = Math.round((seconds % daySec) / hourSec);
    const mm = Math.round((seconds % hourSec) / minuteSec);
    const ss = seconds % minuteSec;
    if (dd > 0) {
      return `${dd}天后截止`;
    }
    if (hh > 0) {
      return `${hh}小时后截止`;
    }
    if (mm > 0) {
      return `${mm}分钟后截止`;
    }
    if (ss > 0) {
      return `${ss}秒后截止`;
    }
    return '已结束';
  };

  const showImage = (photos: Resource[], index: number) => {
    ImageViewer.Multi.show({
      images: photos.map((i: Resource) => i.url),
      defaultIndex: index
    });
  };

  // 判断是否为HTML内容并处理显示
  const getDisplayContent = (content: string) => {
    if (!content) return '';

    // 简单判断是否包含HTML标签
    const hasHtmlTags = /<[^>]*>/g.test(content);

    if (hasHtmlTags) {
      // 如果是HTML内容，提取纯文本
      const textContent = content.replace(/<[^>]*>/g, '').trim();
      if (textContent.length > 70) {
        return `${textContent.slice(0, 70)}...`;
      }
      return textContent;
    }
    // 如果不是HTML内容，按原逻辑处理
    if (content.length > 70) {
      return `${content.slice(0, 70)}...`;
    }
    return content;
  };

  return (
    <div className="flex min-h-screen flex-col">
      <div className={styles.container}>
        <div className={styles.bodyContent}>
          <div className={styles.taskTitledec}>
            <div className={styles.leftTitle}>
              <div
                className={styles.title}
                style={{ fontSize: '18px', marginBottom: '10px' }}
              >
                {taskInfo?.title}
              </div>
              <div className={styles.subRow}>
                <div className={`${styles.subfont} ${styles.subnomfont}`}>
                  {endDays}
                </div>
                {taskInfo?.clockinPeopleNum !== 0 && (
                  <div className={styles.subnomfont}>
                    {taskInfo?.clockinPeopleNum}人参与
                  </div>
                )}
              </div>
              <div className={styles.subRow}>
                <div className={`${styles.subfont} ${styles.subsmfont}`}>
                  {taskInfo?.teacherName}
                </div>
                <div className={styles.subsmfont}>{taskInfo?.createTime}</div>
              </div>
            </div>
            <div className={styles.rightImg}>
              <img
                className={styles.cover}
                src={taskInfo?.cover}
                alt={taskInfo?.title || '任务封面'}
              />
            </div>
          </div>
        </div>
        <div className={styles.otherContent}>
          <div className={styles.taskdec}>
            <div className={styles.declabel}>任务介绍</div>
            <div className={styles.decdetail}>
              <div className={styles.decdetailinfobox}>
                <div className={styles.decdetailinfo}>
                  {getDisplayContent(taskInfo?.content || '')}
                </div>
              </div>
              {!!taskDescriptionImg && (
                <div className={styles.decdetailimg}>
                  <img
                    className={styles.cover}
                    src={taskDescriptionImg}
                    alt="任务描述图片"
                  />
                </div>
              )}
            </div>
            <div
              className={styles.goDetail}
              onClick={() => getDetail()}
            >{`详情>>`}</div>
          </div>
          {checkInList?.map((item: CheckInItem, index: number) => {
            return (
              <div
                className={styles.taskSub}
                key={`checkin-${item.studentName}-${index}`}
              >
                <div className={styles.subheader}>
                  <div className={styles.userinfo}>
                    <img
                      className={styles.userHeader}
                      src={item.avatar}
                      alt={`${item.studentName}的头像`}
                    />
                    <div className={styles.userName}>{item.studentName}</div>
                  </div>
                  <div className={styles.taskNumber}>
                    第{item.clockinTimes}/{item.needClockinNum}次
                  </div>
                </div>
                <div className={styles.taskDetail}>
                  {item.content.length > 50
                    ? `${item.content.slice(0, 50)}...`
                    : item.content}
                </div>
                <div className={styles.centerImage}>
                  {item.clockinResource
                    .filter((i: Resource) => i.resourceType === 1)
                    .map((imgItem: Resource, imgIndex: number) => {
                      return (
                        <div
                          className={styles.imageSubclass}
                          key={`img-${imgItem.url}-${imgIndex}`}
                        >
                          <Image
                            onClick={() =>
                              showImage(
                                item.clockinResource.filter(
                                  (i: Resource) => i.resourceType === 1
                                ),
                                imgIndex
                              )
                            }
                            src={imgItem.url}
                            alt={`打卡图片${imgIndex + 1}`}
                          />
                        </div>
                      );
                    })}
                </div>
                {item.clockinResource
                  .filter((i: Resource) => i.resourceType === 2)
                  .map((videoItem: Resource, videoIndex: number) => {
                    return (
                      <div
                        className={styles.centerVideo}
                        key={`video-${videoItem.url}-${videoIndex}`}
                      >
                        <video
                          src={videoItem.url}
                          className={styles.video}
                          controls
                          width="100%"
                          height="auto"
                          poster={`${videoItem.url}?x-workflow-graph-name=video-thumbnail`}
                          aria-label={`打卡视频${videoIndex + 1}`}
                        >
                          <track
                            kind="captions"
                            srcLang="zh"
                            label="中文字幕"
                          />
                          你的浏览器不支持
                        </video>
                      </div>
                    );
                  })}
                {item.clockinResource
                  .filter((i: Resource) => i.resourceType === 3)
                  .map((audioItem: Resource, audioIndex: number) => {
                    return (
                      <div
                        className={styles.centerAudio}
                        key={`audio-${audioItem.url}-${audioIndex}`}
                      >
                        <audio
                          src={audioItem.url}
                          controls
                          className={styles.audio}
                          aria-label={`打卡音频${audioIndex + 1}`}
                        >
                          <track
                            kind="captions"
                            srcLang="zh"
                            label="中文字幕"
                          />
                          你的浏览器不支持
                        </audio>
                      </div>
                    );
                  })}
              </div>
            );
          })}
        </div>
      </div>
      <FooterQrCode />
    </div>
  );
};

export default Page;
