.content {
  /* 渐变 */
  /* background: linear-gradient(180deg, #4e78ff 3%, #31c5ff 100%); */
  background-color: #fbb63e;
  padding-bottom: 80px;
}
.contentHeader {
  width: 100%;
  position: relative;
  min-height: 200px;
}
.headerIcon {
  position: absolute;
  left: 40px;
  top: 50%;
  transform: translate(0, -50%);
  width: calc(378px * 0.75);
  height: calc(416px * 0.75);
}
.headerTitle {
  position: absolute;
  right: 0%;
  top: 40%;
  font-size: 68px;
  transform: translate(-5%, -50%);
  color: #fff;
  padding: 20px 10px;
  width: 420px;
}
.respectTitle {
  font-size: 28px;
}
.letterContent {
  font-size: 26px;
  text-indent: 2em;
  /* 字体间距 */
  letter-spacing: 6px;
  line-height: 1.8;
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
}
.headerDesc {
  position: absolute;
  right: 30px;
  bottom: 30px;
  font-size: 20px;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.headerDesc .desc {
  background-color: #6d55da;
  margin-bottom: 5px;
  padding: 0 5px;
}
.formContent {
  background-color: #fff;
  border-radius: 20px;
  padding: 60px 30px;
}
.formItem {
  margin-bottom: 40px;
}
.formLabel {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  font-size: 30px;
  color: #323232;
  margin-bottom: 10px;
}
.formInput {
  border: 1px solid #f2f2f2;
  border-radius: 10px;
  min-height: 70px;
  padding: 0px 10px;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  display: flex;
}
.formInput span {
  width: 100% !important;
}
.inputText {
  --font-size: 28px;
  color: #323232;
  padding: 0 10px;
  width: 100%;
  height: 70px;
  border: none;
  outline: none;
}
.radioItem {
  height: 70px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  font-size: 24px;
  color: #323232;
  margin-bottom: 10px;
  padding: 0px 10px;
}
.radioText {
  --icon-size: 36px;
  --font-size: 28px;
  --gap: 10px;
}
.textarea {
  padding: 10px 15px;
  border: 1px solid #f2f2f2;
  margin: 20px 0;
  --font-size: 28px;
  border-radius: 20px;
}
