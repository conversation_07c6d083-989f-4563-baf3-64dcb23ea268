'use client';

import {
  Bad<PERSON>,
  Button,
  Card,
  FloatingBubble,
  Form,
  Input,
  Modal,
  SearchBar,
  SwipeAction,
  Toast
} from 'antd-mobile';
import Cookies from 'js-cookie';
import QRCode from 'qrcode';
import React, { useEffect, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';

import {
  addPosition,
  deletePosition,
  editPosition,
  exportPatrolQRcode,
  getPositions
} from '@/api/securityPatrol';

// 自定义导出确认弹窗组件
interface CustomExportModalProps {
  showModal: boolean;
  downloadUrl: string;
  onClose: () => void;
  onDownload: () => void;
}

const CustomExportModal = ({
  showModal,
  downloadUrl,
  onClose,
  onDownload
}: CustomExportModalProps) => {
  if (!showModal) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-4/5 max-w-md rounded-lg bg-white p-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-medium">导出文件已准备好</h3>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            关闭
          </button>
        </div>
        <div className="mb-4">
          <p className="mb-2">如果下载无响应，试试复制链接到浏览器下载</p>
        </div>
        <div className="flex justify-end space-x-3">
          <CopyToClipboard
            text={downloadUrl}
            onCopy={(result) => {
              onClose();
              Toast.show({
                content: '复制成功'
              });
            }}
          >
            <button
              type="button"
              className="rounded bg-gray-100 px-4 py-2 text-gray-700 hover:bg-gray-200"
            >
              复制链接
            </button>
          </CopyToClipboard>
          <button
            type="button"
            onClick={onDownload}
            className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            下载文件
          </button>
        </div>
      </div>
    </div>
  );
};

interface PatrolPoint {
  posId: string;
  posCode: string;
  posName: string;
  location: string;
  description: string;
  status: 1 | 2;
  qrCode: string;
  createTime: string;
}

export default function PatrolPointsPage() {
  const [points, setPoints] = useState<PatrolPoint[]>([]);
  const [originalPoints, setOriginalPoints] = useState<PatrolPoint[]>([]);
  const [searchText, setSearchText] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingPoint, setEditingPoint] = useState<PatrolPoint | null>(null);
  const [form] = Form.useForm();

  // 导出相关状态
  const [showExportModal, setShowExportModal] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '巡检点管理';
    }
    getData();
  }, []);

  // 生成二维码链接
  const generateQrCodeUrl = (posCode: string, posName: string) => {
    const baseUrl = window.location.origin;
    const instId = Cookies.get('instId');
    console.log('instId', instId);
    return `${baseUrl}/securityPatrol/form?posCode=${encodeURIComponent(posCode)}&posName=${encodeURIComponent(posName)}&instId=${instId}`;
  };

  const getData = (status: number | null = null) => {
    getPositions({ page: 1, pageSize: 100, status }).then((res: any) => {
      setPoints(res.items);
      setOriginalPoints(res.items);
    });
  };
  const filteredPoints = points.filter(
    (point) =>
      point.posName.includes(searchText) || point.posCode.includes(searchText)
  );

  const handleAdd = () => {
    setEditingPoint(null);
    form.resetFields();
    setShowAddModal(true);
  };

  const handleEdit = (point: PatrolPoint) => {
    setEditingPoint(point);
    form.setFieldsValue(point);
    setShowAddModal(true);
  };

  const handleDelete = (pointId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后将无法恢复，确定要删除这个巡检点吗？',
      onConfirm: () => {
        deletePosition({ posId: pointId }).then((res) => {
          setPoints((prev) => prev.filter((p) => p.posId !== pointId));
          setOriginalPoints((prev) => prev.filter((p) => p.posId !== pointId));
        });
        Toast.show({
          icon: 'success',
          content: '删除成功'
        });
      }
    });
  };

  const handleToggleStatus = (editData: PatrolPoint) => {
    editPosition({ ...editData, status: editData.status === 1 ? 2 : 1 })
      .then((res) => {
        setPoints((prev) =>
          prev.map((p) =>
            p.posId === editData.posId
              ? { ...p, status: editData.status === 1 ? 2 : 1 }
              : p
          )
        );
        setOriginalPoints((prev) =>
          prev.map((p) =>
            p.posId === editData.posId
              ? { ...p, status: editData.status === 1 ? 2 : 1 }
              : p
          )
        );
        Toast.show({
          icon: 'success',
          content: '更新成功'
        });
      })
      .catch((error) => {
        Toast.show({
          icon: 'fail',
          content: '操作失败，请重试'
        });
      });
  };
  const filterToggleStatus = (status: number | null) => {
    if (status === null) {
      setPoints(originalPoints);
    } else {
      setPoints(originalPoints.filter((p) => p.status === status));
    }
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const qrcodeUrl = generateQrCodeUrl(values.posCode, values.posName);
      if (editingPoint) {
        // 编辑巡视点
        const editData = {
          posId: editingPoint.posId,
          posCode: values.posCode,
          posName: values.posName,
          status: editingPoint.status,
          qrcodeUrl
        };
        editPosition(editData)
          .then((res) => {
            setPoints((prev) =>
              prev.map((p) =>
                p.posId === editingPoint.posId ? { ...p, ...editData } : p
              )
            );
            setOriginalPoints((prev) =>
              prev.map((p) =>
                p.posId === editingPoint.posId ? { ...p, ...editData } : p
              )
            );
            Toast.show({
              icon: 'success',
              content: '更新成功'
            });
          })
          .catch((error) => {
            Toast.show({
              icon: 'fail',
              content: '操作失败，请重试'
            });
          });
      } else {
        // 新增巡视点
        const addData = {
          posCode: values.posCode,
          posName: values.posName,
          status: 1,
          qrcodeUrl
        };

        addPosition(addData)
          .then((res) => {
            getData();
            Toast.show({
              icon: 'success',
              content: '添加成功'
            });
          })
          .catch((error) => {
            console.error('添加失败:', error);
            Toast.show({
              icon: 'fail',
              content: '操作失败，请重试'
            });
          });
      }

      setShowAddModal(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
      Toast.show({
        icon: 'fail',
        content: '请完善信息'
      });
    }
  };

  const generateQrCode = async (code: string, name: string) => {
    console.log('generateQrCode', code, name);
    try {
      // 构建二维码的链接地址
      const qrCodeUrl = generateQrCodeUrl(code, name);
      // 生成二维码
      const qrCodeDataURL = await QRCode.toDataURL(qrCodeUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      // 显示二维码弹窗
      const modal = Modal.show({
        title: `${name} (${code})`,
        content: (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <img
              src={qrCodeDataURL}
              alt="二维码"
              style={{ width: '200px', height: '200px', margin: '0 auto' }}
            />
          </div>
        ),
        actions: [
          {
            key: 'download',
            text: '下载二维码',
            onClick: () => {
              const link = document.createElement('a');
              link.download = `${code}_${name}_二维码.png`;
              link.href = qrCodeDataURL;
              link.click();
            }
          },
          {
            key: 'close',
            text: '关闭',
            primary: true,
            onClick: () => {
              modal.close();
            }
          }
        ]
      });
    } catch (error) {
      console.error('生成二维码失败:', error);
      Toast.show({
        icon: 'fail',
        content: '生成二维码失败'
      });
    }
  };

  // 导出二维码
  const handleExport = async () => {
    setIsExporting(true);
    try {
      const response = await exportPatrolQRcode({});
      // 处理API响应，从response.data中获取下载链接
      const downloadUrl = response?.zipUrl;
      if (downloadUrl && typeof downloadUrl === 'string') {
        setDownloadUrl(downloadUrl);
        setShowExportModal(true);
      } else {
        Toast.show({
          icon: 'fail',
          content: '导出失败，请重试'
        });
      }
    } catch (error) {
      console.error('导出失败:', error);
      Toast.show({
        icon: 'fail',
        content: '导出失败，请重试'
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleCloseExportModal = () => {
    setShowExportModal(false);
  };

  const handleDownloadClick = () => {
    // 实际下载逻辑
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `patrol-qrcodes-${Date.now()}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    setShowExportModal(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <CustomExportModal
        showModal={showExportModal}
        downloadUrl={downloadUrl}
        onClose={handleCloseExportModal}
        onDownload={handleDownloadClick}
      />

      {/* 搜索栏 */}
      <div className="bg-white p-4">
        <div className="flex items-center space-x-3">
          <div className="flex-1">
            <SearchBar
              placeholder="搜索巡检点..."
              value={searchText}
              onChange={setSearchText}
              style={{
                '--height': '40px'
              }}
            />
          </div>
          <Button
            size="small"
            fill="outline"
            onClick={handleExport}
            loading={isExporting}
            className="flex items-center space-x-1 whitespace-nowrap"
          >
            <span className="text-sm">导出二维码</span>
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="p-4">
        <Card>
          <div className="grid grid-cols-3 divide-x divide-gray-200 ">
            <div
              className="py-3 text-center"
              onClick={() => filterToggleStatus(null)}
            >
              <div className="text-2xl font-bold text-blue-600">
                {originalPoints.length}
              </div>
              <div className="text-sm text-gray-500">总数</div>
            </div>
            <div
              className="py-3 text-center"
              onClick={() => filterToggleStatus(1)}
            >
              <div className="text-2xl font-bold text-green-600">
                {originalPoints.filter((p) => p.status === 1).length}
              </div>
              <div className="text-sm text-gray-500">启用</div>
            </div>
            <div
              className="py-3 text-center"
              onClick={() => filterToggleStatus(2)}
            >
              <div className="text-2xl font-bold text-gray-600">
                {originalPoints.filter((p) => p.status === 2).length}
              </div>
              <div className="text-sm text-gray-500">禁用</div>
            </div>
          </div>
        </Card>
      </div>

      {/* 巡检点列表 */}
      <div className="space-y-3 px-4">
        {filteredPoints.map((point) => (
          <SwipeAction
            key={point.posId}
            rightActions={[
              {
                key: 'edit',
                text: '编辑',
                color: 'primary',
                onClick: () => handleEdit(point)
              },
              {
                key: 'toggle',
                text: point.status === 1 ? '禁用' : '启用',
                color: point.status === 1 ? 'warning' : 'success',
                onClick: () => handleToggleStatus(point)
              },
              {
                key: 'delete',
                text: '删除',
                color: 'danger',
                onClick: () => handleDelete(point.posId)
              }
            ]}
            style={{
              backgroundColor: 'transparent'
            }}
          >
            <div className="rounded-2xl bg-white  p-4">
              <div className="relative py-2">
                <div className="flex items-start space-x-3 ">
                  <div className="flex size-12 shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg">
                    <span className="text-sm font-bold text-white">
                      {point.posCode}
                    </span>
                  </div>
                  <div className="flex flex-1 flex-col">
                    <div className="mb-2 flex items-center justify-start">
                      <h3 className="text-base font-semibold text-gray-900">
                        {point.posName}
                      </h3>
                      <Badge
                        content={point.status === 1 ? '正常' : '禁用'}
                        color={point.status === 1 ? '#00b578' : '#999'}
                        style={{
                          borderRadius: '4px'
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="mb-1 truncate text-sm text-gray-500">
                        编号：{point.posCode}
                      </div>
                      <Button
                        size="mini"
                        color="primary"
                        onClick={() =>
                          generateQrCode(point.posCode, point.posName)
                        }
                      >
                        二维码
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SwipeAction>
        ))}

        {filteredPoints.length === 0 && (
          <div className="py-16 text-center">
            <div className="mb-2 text-base text-gray-400">暂无巡检点数据</div>
            <div className="text-sm text-gray-500">
              点击右下角按钮添加巡检点
            </div>
          </div>
        )}
      </div>

      {/* 添加按钮 */}
      <FloatingBubble
        style={{
          '--initial-position-bottom': '24px',
          '--initial-position-right': '24px',
          '--edge-distance': '24px'
        }}
        onClick={handleAdd}
      >
        <div className="text-lg">+</div>
      </FloatingBubble>

      {/* 添加/编辑弹窗 */}
      <Modal
        visible={showAddModal}
        title={editingPoint ? '编辑巡检点' : '添加巡检点'}
        onClose={() => {
          setShowAddModal(false);
          form.resetFields();
        }}
        content={
          <Form form={form} layout="vertical" style={{ padding: '16px 0' }}>
            <Form.Item
              name="posCode"
              label="点位编号"
              rules={[{ required: true, message: '请输入点位编号' }]}
            >
              <Input placeholder="如：A101" />
            </Form.Item>

            <Form.Item
              name="posName"
              label="点位名称"
              rules={[{ required: true, message: '请输入点位名称' }]}
            >
              <Input placeholder="如：反恐器械区" />
            </Form.Item>
          </Form>
        }
        actions={[
          {
            key: 'cancel',
            text: '取消',
            onClick: () => {
              setShowAddModal(false);
              form.resetFields();
            }
          },
          {
            key: 'confirm',
            text: editingPoint ? '更新' : '添加',
            primary: true,
            onClick: handleSubmit
          }
        ]}
      />
    </div>
  );
}
