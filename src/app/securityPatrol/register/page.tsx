'use client';

import { Button, Card, Form, Input, Radio, Toast } from 'antd-mobile';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { addVisitRecord, getVisitRecords } from '@/api/securityPatrol';
import { useWechatStore } from '@/store/useWechatStore';

export default function VisitorRegisterPage() {
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showExitSuccess, setShowExitSuccess] = useState(false);
  const searchParams = useSearchParams();
  const instId = searchParams.get('instId') || '';
  const { unionId } = useWechatStore();

  // 表单数据状态
  const [formData, setFormData] = useState({
    visitorName: '', // 来访者姓名
    gender: 1, // 性别
    phone: '', // 联系电话
    company: '', // 工作单位
    purpose: '', // 来访事由
    visitee: '' // 被访问人
  });

  // 性别选项
  const genderOptions = [
    { label: '男', value: 1 },
    { label: '女', value: 2 }
  ];
  // 重置表单
  const handleReset = async () => {
    form.resetFields();
    setFormData({
      visitorName: '',
      gender: 1,
      phone: '',
      company: '',
      purpose: '',
      visitee: ''
    });
  };
  // 提交表单
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // 验证必填字段
      if (
        !values.visitorName ||
        !values.gender ||
        !values.phone ||
        !values.purpose ||
        !values.visitee
      ) {
        Toast.show({
          content: '请填写完整的必填信息',
          position: 'center'
        });
        return;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(values.phone)) {
        Toast.show({
          content: '请输入正确的手机号码',
          position: 'center'
        });
        return;
      }
      await addVisitRecord({
        instId,
        unionId: unionId || '',
        visitorName: values.visitorName,
        phone: values.phone,
        gender: values.gender,
        status: 1,
        matter: values.purpose,
        employer: values.company,
        interviewee: values.visitee,
        visitTime: Math.floor(Date.now() / 1000) // 获取时间戳秒
      });
      handleReset();
      Toast.show({
        content: '来访登记成功！',
        position: 'center'
      });
    } catch (error) {
      console.error('提交失败:', error);
      Toast.show({
        content: '登记失败，请重试',
        position: 'center'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (unionId) {
      getVisitRecords({
        instId,
        unionId: unionId || ''
      }).then((res: any) => {
        if (res.isLeave) {
          setShowExitSuccess(true);
        }
      });
    }
  }, [instId, unionId]);
  // 离开登记成功页面
  const ExitSuccessPage = () => (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md text-center">
        <div className="py-8">
          {/* 成功图标 */}
          <div className="mb-6 flex justify-center">
            <div className="flex size-20 items-center justify-center rounded-full bg-green-100">
              <svg
                className="size-10 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-label="成功图标"
              >
                <title>成功图标</title>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>

          {/* 成功标题 */}
          <h2 className="mb-4 text-2xl font-bold text-gray-800">
            离开登记成功
          </h2>
          {/* 成功描述 */}
          <p className="mb-6 text-gray-600">感谢您的配合，祝您一路平安！</p>
        </div>
      </Card>
    </div>
  );

  // 如果显示离开成功页面，则渲染成功页面
  if (showExitSuccess) {
    return <ExitSuccessPage />;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="mb-2 text-center text-2xl font-bold text-gray-800">
          来访登记表
        </h1>
        <p className="text-center text-sm text-gray-600">请如实填写以下信息</p>
      </div>

      <Card className="mb-4">
        <Form
          form={form}
          layout="vertical"
          footer={
            <div className="flex gap-3 pt-4">
              <Button
                block
                color="default"
                onClick={handleReset}
                disabled={loading}
              >
                重置
              </Button>
              <Button
                block
                color="primary"
                onClick={handleSubmit}
                loading={loading}
              >
                提交登记
              </Button>
            </div>
          }
        >
          <Form.Item
            name="visitorName"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入真实姓名" clearable />
          </Form.Item>

          <Form.Item
            name="gender"
            label="性别"
            rules={[{ required: true, message: '请选择性别' }]}
          >
            <Radio.Group>
              {genderOptions.map((option) => (
                <Radio
                  style={{ marginRight: 12 }}
                  key={option.value}
                  value={option.value}
                >
                  {option.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="phone"
            label="电话"
            rules={[
              { required: true, message: '请输入电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]}
          >
            <Input placeholder="请输入手机号码" type="number" clearable />
          </Form.Item>

          <Form.Item name="company" label="工作单位">
            <Input placeholder="请输入工作单位" clearable />
          </Form.Item>

          <Form.Item
            name="purpose"
            label="来访事由"
            rules={[{ required: true, message: '请填写来访事由' }]}
          >
            <Input placeholder="请填写来访事由" type="textarea" />
          </Form.Item>

          <Form.Item
            name="visitee"
            label="访问人"
            rules={[{ required: true, message: '请输入访问人姓名' }]}
          >
            <Input placeholder="请输入要访问的老师或工作人员姓名" clearable />
          </Form.Item>
        </Form>
      </Card>

      {/* 温馨提示 */}
      <Card className="border-blue-200 bg-blue-50">
        <div className="text-sm text-blue-700">
          <h4 className="mb-2 font-semibold">温馨提示：</h4>
          <ul className="space-y-1 text-xs">
            <li>• 请如实填写个人信息</li>
            <li>• 确保联系方式准确有效</li>
            <li>• 请遵守相关管理规定</li>
          </ul>
        </div>
      </Card>
    </div>
  );
}
