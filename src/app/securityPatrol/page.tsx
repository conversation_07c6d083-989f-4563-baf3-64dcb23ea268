'use client';

import {
  <PERSON>ge,
  Button,
  ImageViewer,
  Modal,
  SwipeAction,
  Toast
} from 'antd-mobile';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { ChevronRight, MapPin } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getPatrolRecords } from '@/api/securityPatrol';

interface PatrolRecord {
  /**
   * 巡视时间 (Unix时间戳, 秒，对应数据库 patrol.create_time)
   */
  createTime: number;
  /**
   * 巡视说明 (对应数据库 patrol.description)
   */
  description: string;
  /**
   * 巡视记录ID (对应数据库 patrol.patrol_id)
   */
  patrolId: string;
  /**
   * 巡视人ID (对应数据库 patrol.patrolman_id)
   */
  patrolmanId: number;
  /**
   * 巡视人姓名
   */
  patrolmanName: string;
  /**
   * 图片URL (对应数据库 patrol.photo_url)
   */
  photoUrl: string;
  /**
   * 巡视点名称 (需要从 patrol_position 表关联查询得到)
   */
  posName: string;
  /**
   * 巡视点编号 (需要从 patrol_position 表关联查询得到)
   */
  posCode: string;
  /**
   * 巡检人
   */
  staffName: string;
  /**
   * 状态 (对应数据库 patrol.status, 使用 PatrolRecordStatus 枚举) 1-正常 2-异常
   */
  status: number;
}

export default function SecurityPatrolPage() {
  const router = useRouter();
  const [records, setRecords] = useState<PatrolRecord[]>([]);
  const [imageVisible, setImageVisible] = useState(false);
  const [currentImageUrl, setCurrentImageUrl] = useState('');
  // 模拟数据
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '安防巡查';
    }
    getPatrolRecords({
      pageNum: 1,
      pageSize: 15
    }).then((res) => {
      setRecords(res.data?.items || res.items || []);
    });
  }, []);

  const handleRecordClick = (record: PatrolRecord) => {
    router.push(`/securityPatrol/record/${record.patrolId}`);
  };

  const handleManagePoints = () => {
    router.push('/securityPatrol/points');
  };

  const handleCreateItems = () => {
    router.push('/securityPatrol/form');
  };

  const handleImagePreview = (imageUrl: string) => {
    setCurrentImageUrl(imageUrl);
    setImageVisible(true);
  };

  const getStatusColor = (status: number) => {
    return status === 1 ? '#00b578' : '#ff3141';
  };

  const getStatusText = (status: number) => {
    return status === 1 ? '正常' : '异常';
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000); // 转换为毫秒
    const now = new Date();

    // 获取年月日部分进行比较，忽略时分秒
    const dateOnly = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );
    const nowOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 计算日期差（天数）
    const days = Math.floor(
      (nowOnly.getTime() - dateOnly.getTime()) / (1000 * 60 * 60 * 24)
    );

    // 使用date-fns格式化时间部分，包含秒
    const timeStr = format(date, 'HH:mm:ss', { locale: zhCN });

    if (days === 0) return `今天 ${timeStr}`;
    if (days === 1) return `昨天 ${timeStr}`;
    if (days === 2) return `前天 ${timeStr}`;

    // 其他时间显示完整日期和时间
    return format(date, 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN });
  };

  const handleDelete = (recordId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后将无法恢复，确定要删除这条巡检记录吗？',
      onConfirm: () => {
        setRecords((prev) => prev.filter((r) => r.patrolId !== recordId));
        Toast.show({
          icon: 'success',
          content: '删除成功'
        });
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-gradient-to-br from-slate-600 via-blue-600 to-slate-700 p-4 text-white shadow-lg">
        <div className="flex justify-start gap-3">
          {/* <div className='w-44 rounded-xl bg-white/15 p-4 backdrop-blur-sm transition-all duration-300'>
            <div
              className='flex h-full cursor-pointer flex-col items-center justify-center text-white'
              onClick={handleCreateItems}
            >
              <div className='mb-3 flex size-10 items-center justify-center rounded-full bg-white/25 transition-all duration-300 '>
                <Plus size={18} className='drop-shadow-sm' />
              </div>
              <span className='text-center text-xs font-semibold tracking-wide'>
                添加巡检记录
              </span>
            </div>
          </div> */}
          <div className="w-44 flex-1 rounded-xl bg-white/15 p-4 backdrop-blur-sm transition-all duration-300">
            <div
              className="flex h-full cursor-pointer flex-col items-center justify-center text-white"
              onClick={handleManagePoints}
            >
              <div className="mb-3 flex size-10 items-center justify-center rounded-full bg-white/25">
                <MapPin size={18} className="drop-shadow-sm" />
              </div>
              <span className="text-center text-xs font-semibold tracking-wide">
                巡检点管理
              </span>
            </div>
          </div>
        </div>
      </div>
      {/* 巡检记录列表 */}
      <div className="px-4">
        <div className="my-3 flex items-center justify-between">
          <h2 className="text-base font-medium text-gray-700">最近巡检记录</h2>
          <Button
            size="small"
            fill="none"
            onClick={() => router.push('/securityPatrol/records')}
          >
            查看全部
          </Button>
        </div>
        <div className="space-y-3">
          {records.map((record) => (
            <SwipeAction
              style={{
                background: 'transparent',
                overflow: 'hidden'
              }}
              closeOnAction={false}
              key={record.patrolId}
              rightActions={[
                {
                  key: 'delete',
                  text: '删除',
                  color: 'danger',
                  onClick: () => handleDelete(record.patrolId)
                }
              ]}
            >
              <div
                className="cursor-pointer rounded-xl  bg-white p-4"
                onClick={() => handleRecordClick(record)}
              >
                <div className="flex items-center gap-3">
                  {/* 图片部分 */}
                  {record.photoUrl && (
                    <div
                      className="size-20 shrink-0 cursor-pointer overflow-hidden rounded-lg bg-gray-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (record.photoUrl) {
                          handleImagePreview(
                            record.photoUrl?.split(',')?.[0] || ''
                          );
                        }
                      }}
                    >
                      <img
                        src={record.photoUrl?.split(',')?.[0] || ''}
                        alt="巡检照片"
                        className="size-full object-cover transition-transform duration-200 hover:scale-105"
                      />
                    </div>
                  )}

                  {/* 内容部分 */}
                  <div className="min-w-0 flex-1">
                    <div className="mb-2 flex items-start justify-between">
                      <h3 className="truncate text-base font-medium text-gray-900">
                        {record.posName}
                      </h3>
                      <div className="ml-2 flex shrink-0 items-center gap-2">
                        <Badge
                          content={getStatusText(record.status)}
                          color={getStatusColor(record.status)}
                        />
                        <ChevronRight size={16} className="text-gray-400" />
                      </div>
                    </div>

                    <div className="space-y-1 text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <span>巡检员：{record.staffName}</span>
                      </div>
                      <div>时间：{formatDate(record.createTime)}</div>
                      {record.description && (
                        <div className="mt-2 line-clamp-2 text-xs text-gray-600">
                          {record.description}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </SwipeAction>
          ))}
        </div>
      </div>

      <ImageViewer
        image={currentImageUrl}
        visible={imageVisible}
        onClose={() => setImageVisible(false)}
      />
    </div>
  );
}
