'use client';

import {
  <PERSON><PERSON>,
  Card,
  Form,
  ImageUploader,
  Picker,
  Radio,
  TextArea,
  Toast
} from 'antd-mobile';
import type { ImageUploadItem } from 'antd-mobile/es/components/image-uploader';
import { format } from 'date-fns';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getWechatUserInfo } from '@/api/common';
import type { PatrolPositionInfo } from '@/api/securityPatrol';
import { getPositions, scanAddPatrolRecord } from '@/api/securityPatrol';
import { useWechatStore } from '@/store/useWechatStore';
import { generateString, getEnv, uploadObs } from '@/utils/obs';
import { disableWechatCopyAndShare } from '@/utils/wechat';

export default function PatrolFormPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const posCode = searchParams.get('posCode');
  const posName = searchParams.get('posName');
  const instId = searchParams.get('instId') || '';
  const unionId = useWechatStore((state) => state.unionId);

  const [form] = Form.useForm();
  const [patrolData, setPatrolData] = useState({
    status: 1 as 1 | 2, // 1-正常, 2-异常
    description: '',
    images: [] as ImageUploadItem[]
  });
  const [pointInfo, setPointInfo] = useState({
    name: '',
    location: '',
    description: '',
    posId: 0
  });

  const [loading, setLoading] = useState(false);

  // 新增巡视点选择器相关状态
  const [positionPickerVisible, setPositionPickerVisible] = useState(false);
  const [availablePositions, setAvailablePositions] = useState<
    PatrolPositionInfo[]
  >([]);
  const [selectedPosition, setSelectedPosition] = useState<string>('');

  // 巡视点选择器数据
  const positionPickerData = [
    availablePositions.map((pos) => ({
      label: `${pos.posCode} - ${pos.posName}`,
      value: pos.posCode || ''
    }))
  ];

  // 禁用微信复制链接功能
  useEffect(() => {
    // 使用项目中的微信工具函数禁用复制链接和分享
    disableWechatCopyAndShare({
      hideAll: false, // 不完全隐藏菜单，只隐藏指定项
      menuList: [
        'menuItem:copyUrl', // 复制链接
        'menuItem:share:appMessage', // 发送给朋友
        'menuItem:share:timeline', // 分享到朋友圈
        'menuItem:share:qq', // 分享到QQ
        'menuItem:share:QZone', // 分享到QQ空间
        'menuItem:openWithQQBrowser', // 在QQ浏览器中打开
        'menuItem:openWithSafari' // 在Safari中打开
      ]
    });
  }, []);

  useEffect(() => {
    // 获取巡视点列表
    // const fetchPositions = async () => {
    //   try {
    //     const response = await getPositions({ pageSize: 100, status: 1 }); // 只获取启用状态的巡视点
    //     setAvailablePositions(response?.items || []);
    //   } catch (error) {
    //     console.error('获取巡视点列表失败:', error);
    //     Toast.show({
    //       icon: 'fail',
    //       content: '获取巡视点列表失败'
    //     });
    //   }
    // };

    // // 初始化获取巡视点列表
    // fetchPositions();

    // 使用URL参数中的posCode和posName，并获取posId
    const fetchPositionInfo = async () => {
      if (posCode && posName) {
        try {
          // 先用posCode搜索获取posId
          const response = await getPositions({ posCode });
          let foundPosition: PatrolPositionInfo | undefined;

          if (response?.data?.items && response.data.items.length > 0) {
            foundPosition = response.data.items.find(
              (item: PatrolPositionInfo) => item.posCode === posCode
            );
          }

          setPointInfo({
            name: posName,
            location: '扫码获取位置',
            description: `位置编码: ${posCode}`,
            posId: foundPosition?.posId || 0
          });
        } catch (error) {
          console.error('获取巡检点信息失败:', error);
          // 即使获取失败也设置基本信息
          setPointInfo({
            name: posName,
            location: '扫码获取位置',
            description: `位置编码: ${posCode}`,
            posId: 0
          });
        }
      }
    };

    // fetchPositionInfo();
  }, [posCode, posName]);

  // 处理巡视点选择
  const handlePositionSelect = (val: (string | number | null)[]) => {
    const selectedPosCode = val[0] as string;
    const position = availablePositions.find(
      (pos) => pos.posCode === selectedPosCode
    );

    if (position) {
      setSelectedPosition(selectedPosCode);
      setPointInfo({
        name: position.posName || '',
        location: '手动选择位置',
        description: `位置编码: ${position.posCode}`,
        posId: position.posId || 0
      });
      setPositionPickerVisible(false);
      Toast.show('巡视点已选择');
    }
  };

  // 获取当前选中的巡视点显示文本
  const getCurrentPositionText = () => {
    if (posCode && posName) {
      // 有URL参数时显示参数信息
      return `${posCode} - ${posName}`;
    }

    if (selectedPosition) {
      // 手动选择时显示选中的位置
      const position = availablePositions.find(
        (pos) => pos.posCode === selectedPosition
      );
      return position ? position.posName : '请选择巡视点';
    }

    return '请选择巡视点';
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      await form.validateFields();

      if (!posCode) {
        Toast.show({
          icon: 'fail',
          content: '无法获取巡检点信息，请重新扫码'
        });
        return;
      }

      // 处理图片URLs
      const photoUrls = patrolData.images.map((img) => img.url).filter(Boolean);
      const photoUrl = photoUrls.length > 0 ? photoUrls.join(',') : undefined;
      const userInfoRes = await getWechatUserInfo({
        unionId: unionId || '',
        userType: 1
      });
      const userInfoFilter = userInfoRes?.userInfo?.find(
        (item: { instId?: string; userId?: string; userName?: string }) =>
          item.instId === instId
      );
      if (!userInfoFilter) {
        Toast.show({
          icon: 'fail',
          content: '请先用微信登录APP关联用户，在扫码登记'
        });
        return;
      }
      // 构造提交数据
      const submitData = {
        instId,
        operatorId: userInfoFilter?.userId || '',
        operatorName: userInfoFilter?.userName || '',
        posCode,
        status: patrolData.status,
        description: patrolData.description || undefined,
        photoUrl: photoUrl?.toString()
      };

      console.log('提交巡检记录:', submitData);

      await scanAddPatrolRecord(submitData);

      Toast.show({
        icon: 'success',
        content: '巡检记录提交成功'
      });
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: `提交失败，请重试：${JSON.stringify(error)}`
      });
    } finally {
      setLoading(false);
    }
  };
  // 图片上传函数
  const uploadImage = async (file: File) => {
    return new Promise((resolve, reject) => {
      const env = getEnv();
      const date = format(new Date(), 'yyyy-MM-dd');
      const key = `${env}/waitingForPickup/${date}/${generateString(8)}.png`;
      uploadObs(file, key, false)
        .then((url) => {
          resolve({
            url
          });
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          Toast.clear();
        });
    });
  };
  const handleImageUpload = async (file: File): Promise<ImageUploadItem> => {
    const response = (await uploadImage(file)) as { url: string };
    return {
      url: response.url
    };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="space-y-4 p-4">
        {/* 巡检点信息 */}
        <Card>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">
              {pointInfo.name || posName || '未选择巡视点'}
            </h3>
            <div className="text-sm text-gray-600">
              <div
                className={`flex items-center justify-between ${
                  !(posCode && posName) ? 'cursor-pointer' : ''
                }`}
                onClick={() => {
                  // 只有在没有URL参数的情况下才能点击选择
                  if (!(posCode && posName)) {
                    setPositionPickerVisible(true);
                  }
                }}
              >
                <span className="text-sm text-gray-600">
                  位置：{getCurrentPositionText()}
                </span>
                {!(posCode && posName) && (
                  <span className="text-sm text-blue-500">点击选择</span>
                )}
              </div>
              <p className="text-sm text-gray-600">
                编号：{posCode || selectedPosition || '未选择'}
              </p>
            </div>
          </div>
        </Card>

        {/* 巡检表单 */}
        <Form
          form={form}
          layout="vertical"
          footer={
            <Button
              block
              type="submit"
              color="primary"
              size="large"
              onClick={handleSubmit}
              loading={loading}
            >
              提交巡检记录
            </Button>
          }
        >
          <Card title="基本信息">
            <Form.Item
              name="status"
              style={
                {
                  borderInner: 'none'
                } as React.CSSProperties
              }
            >
              <div className="space-y-3">
                <Radio
                  value={1}
                  checked={patrolData.status === 1}
                  onChange={(checked) =>
                    checked && setPatrolData((prev) => ({ ...prev, status: 1 }))
                  }
                  className="mr-2"
                >
                  <span className="text-green-600">正常</span>
                </Radio>
                <Radio
                  value={2}
                  checked={patrolData.status === 2}
                  onChange={(checked) =>
                    checked && setPatrolData((prev) => ({ ...prev, status: 2 }))
                  }
                >
                  <span className="text-red-600">异常</span>
                </Radio>
              </div>
            </Form.Item>
          </Card>
          <Card title="现场照片">
            <ImageUploader
              value={patrolData.images}
              onChange={(images) =>
                setPatrolData((prev) => ({ ...prev, images }))
              }
              upload={handleImageUpload}
              multiple
              maxCount={9}
            />
            <div className="mt-2 text-xs text-gray-500">最多上传9张照片</div>
          </Card>

          <Card title="备注说明">
            <Form.Item
              name="description"
              style={
                {
                  borderInner: 'none'
                } as React.CSSProperties
              }
            >
              <TextArea
                placeholder="请输入巡检备注或异常情况说明..."
                rows={4}
                value={patrolData.description}
                onChange={(val: string) =>
                  setPatrolData((prev) => ({ ...prev, description: val }))
                }
              />
            </Form.Item>
          </Card>
        </Form>

        {/* 巡视点选择器 */}
        <Picker
          columns={positionPickerData}
          visible={positionPickerVisible}
          onClose={() => setPositionPickerVisible(false)}
          value={selectedPosition ? [selectedPosition] : []}
          onConfirm={handlePositionSelect}
          title="选择巡视点"
        />
      </div>
    </div>
  );
}
