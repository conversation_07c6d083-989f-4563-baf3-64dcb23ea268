'use client';

import { <PERSON><PERSON>, But<PERSON>, Card, ImageViewer, Modal, Toast } from 'antd-mobile';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Trash2 } from 'lucide-react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import {
  deletePatrolRecord,
  getPatrolRecordDetail,
} from '@/api/securityPatrol';

interface PatrolRecord {
  /**
   * 巡视时间 (Unix时间戳, 秒，对应数据库 patrol.create_time)
   */
  createTime: number;
  /**
   * 巡视说明 (对应数据库 patrol.description)
   */
  description: string;
  /**
   * 巡视记录ID (对应数据库 patrol.patrol_id)
   */
  patrolId: number;
  /**
   * 图片URL (对应数据库 patrol.photo_url)
   */
  photoUrl: string;
  /**
   * 巡视点编号
   */
  posCode: string;
  /**
   * 巡视点ID
   */
  posId: number;
  /**
   * 巡视点名称 (需要从 patrol_position 表关联查询得到)
   */
  posName: string;
  /**
   * 巡视人ID (对应数据库 patrol.patrolman_id)
   */
  staffId: number;
  /**
   * 巡视人姓名
   */
  staffName: string;
  /**
   * 状态 (对应数据库 patrol.status, 使用 PatrolRecordStatus 枚举)
   */
  status: number;
}
/**
 * GetPatrolRecordRsp
 */

export default function PatrolRecordDetailPage() {
  const router = useRouter();
  const params = useParams();
  const recordId = params.id as string;
  const [record, setRecord] = useState<PatrolRecord | null>(null);
  const [loading, setLoading] = useState(true);

  // 图片预览相关状态
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '安防巡查记录详情';
    }
    getPatrolRecordDetail({ posId: recordId }).then((res: any) => {
      setRecord(res);
      setLoading(false);
    });
  }, [recordId]);

  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后将无法恢复，确定要删除这条巡检记录吗？',
      onConfirm: () => {
        deletePatrolRecord({ posId: recordId }).then(() => {
          Toast.show({
            icon: 'success',
            content: '删除成功',
          });
          router.back();
        });
      },
    });
  };

  // 处理图片点击预览
  const handleImagePreview = (images: string[], index: number) => {
    setCurrentImages(images);
    setCurrentImageIndex(index);
    setImageViewerVisible(true);
  };

  const getStatusColor = (status: number) => {
    return status === 1 ? '#00b578' : '#ff3141';
  };

  const getStatusText = (status: number) => {
    return status === 1 ? '正常' : '异常';
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN });
  };

  if (loading) {
    return (
      <div className='flex min-h-screen items-center justify-center bg-gray-50'>
        <div className='text-gray-500'>加载中...</div>
      </div>
    );
  }

  if (!record) {
    return (
      <div className='flex min-h-screen items-center justify-center bg-gray-50'>
        <div className='text-gray-500'>记录不存在</div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='space-y-4 p-4'>
        {/* 基本信息 */}
        <Card
          title='巡查信息'
          extra={
            <Button
              fill='outline'
              color='danger'
              onClick={handleDelete}
              size='small'
            >
              <Trash2 size={16} />
            </Button>
          }
        >
          <div className='space-y-3'>
            <div className='flex items-center justify-between'>
              <span className='text-gray-500 text-sm'>巡检人员</span>
              <span className='text-gray-900 font-medium text-base'>
                {record.staffName}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-gray-500 text-sm'>巡检位置</span>
              <span className='text-gray-900 font-medium text-base'>
                {record.posName}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-gray-500 text-sm'>位置编号</span>
              <span className='text-gray-900 font-medium text-base'>
                {record.posCode}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-gray-500 text-sm'>巡检状态</span>
              <Badge
                content={getStatusText(record.status)}
                color={getStatusColor(record.status)}
              />
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-gray-500 text-sm'>巡检时间</span>
              <span className='text-gray-900 font-medium text-base'>
                {formatDateTime(record.createTime * 1000)}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-gray-500 text-sm'>备注说明</span>
              <span className='flex-1 text-right text-gray-900 font-medium text-base'>
                {record.description || '-'}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-gray-500 text-sm'>现场照片</span>
            </div>
            {/* 现场照片 */}
            {record.photoUrl.split(',').length > 0 && (
              <div className='grid grid-cols-3 gap-2'>
                {record.photoUrl?.split(',')?.map((imageUrl, index) => (
                  <div
                    key={imageUrl}
                    className='aspect-square cursor-pointer overflow-hidden rounded-lg bg-gray-100'
                    onClick={() =>
                      handleImagePreview(record.photoUrl.split(','), index)
                    }
                  >
                    <img
                      src={imageUrl}
                      alt={`现场照片${index + 1}`}
                      className='size-full object-cover transition-transform duration-200 hover:scale-105'
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* 图片预览器 */}
      <ImageViewer
        image={currentImages[currentImageIndex] || ''}
        visible={imageViewerVisible}
        onClose={() => setImageViewerVisible(false)}
      />
    </div>
  );
}
