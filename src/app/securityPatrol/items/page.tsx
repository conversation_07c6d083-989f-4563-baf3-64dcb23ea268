'use client';

import {
  Badge,
  Card,
  Checkbox,
  FloatingBubble,
  Form,
  Input,
  List,
  Modal,
  SearchBar,
  Selector,
  SwipeAction,
  TextArea,
  Toast,
} from 'antd-mobile';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

interface CheckItem {
  id: string;
  name: string;
  category: string;
  description: string;
  required: boolean;
  order: number;
  status: 'active' | 'inactive';
  createTime: string;
}

export default function CheckItemsPage() {
  const router = useRouter();
  const [items, setItems] = useState<CheckItem[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingItem, setEditingItem] = useState<CheckItem | null>(null);
  const [form] = Form.useForm();

  const categories = [
    { label: '全部', value: 'all' },
    { label: '食品安全', value: '食品安全' },
    { label: '环境卫生', value: '环境卫生' },
    { label: '设备安全', value: '设备安全' },
    { label: '人员管理', value: '人员管理' },
  ];

  // 模拟数据
  useEffect(() => {
    const mockItems: CheckItem[] = [
      {
        id: '1',
        name: '门窗完好',
        category: '设备安全',
        description: '检查门窗是否完好无损，锁具是否正常',
        required: true,
        order: 1,
        status: 'active',
        createTime: format(new Date('2024-01-15'), 'yyyy-MM-dd'),
      },
      {
        id: '2',
        name: '照明正常',
        category: '设备安全',
        description: '检查照明设备是否正常工作',
        required: true,
        order: 2,
        status: 'active',
        createTime: format(new Date('2024-01-15'), 'yyyy-MM-dd'),
      },
      {
        id: '3',
        name: '消防设施完好',
        category: '设备安全',
        description: '检查消防器材是否齐全、有效',
        required: true,
        order: 3,
        status: 'active',
        createTime: format(new Date('2024-01-15'), 'yyyy-MM-dd'),
      },
      {
        id: '4',
        name: '环境整洁',
        category: '环境卫生',
        description: '检查环境是否整洁干净',
        required: false,
        order: 4,
        status: 'active',
        createTime: format(new Date('2024-01-15'), 'yyyy-MM-dd'),
      },
      {
        id: '5',
        name: '食品储存',
        category: '食品安全',
        description: '检查食品储存是否符合规范',
        required: true,
        order: 5,
        status: 'inactive',
        createTime: format(new Date('2024-01-15'), 'yyyy-MM-dd'),
      },
    ];
    setItems(mockItems);
  }, []);

  const filteredItems = items.filter((item) => {
    const matchesSearch =
      item.name.includes(searchText) || item.description.includes(searchText);
    const matchesCategory =
      selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    setShowAddModal(true);
  };

  const handleEdit = (item: CheckItem) => {
    setEditingItem(item);
    form.setFieldsValue(item);
    setShowAddModal(true);
  };

  const handleDelete = (itemId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后将无法恢复，确定要删除这个检查项目吗？',
      onConfirm: () => {
        setItems((prev) => prev.filter((i) => i.id !== itemId));
        Toast.show({
          icon: 'success',
          content: '删除成功',
        });
      },
    });
  };

  const handleToggleStatus = (itemId: string) => {
    setItems((prev) =>
      prev.map((i) =>
        i.id === itemId
          ? { ...i, status: i.status === 'active' ? 'inactive' : 'active' }
          : i,
      ),
    );
    Toast.show({
      icon: 'success',
      content: '状态更新成功',
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingItem) {
        // 编辑
        setItems((prev) =>
          prev.map((i) => (i.id === editingItem.id ? { ...i, ...values } : i)),
        );
        Toast.show({
          icon: 'success',
          content: '更新成功',
        });
      } else {
        // 新增
        const newItem: CheckItem = {
          id: Date.now().toString(),
          ...values,
          status: 'active',
          order: items.length + 1,
          createTime: format(new Date(), 'yyyy-MM-dd'),
        };
        setItems((prev) => [...prev, newItem]);
        Toast.show({
          icon: 'success',
          content: '添加成功',
        });
      }

      setShowAddModal(false);
      form.resetFields();
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: '请完善信息',
      });
    }
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* 搜索和筛选 */}
      <div className='space-y-3 bg-white p-4'>
        <SearchBar
          placeholder='搜索检查项目...'
          value={searchText}
          onChange={setSearchText}
        />

        <Selector
          options={categories}
          value={[selectedCategory]}
          onChange={(arr) => setSelectedCategory(arr[0] || 'all')}
          multiple={false}
        />
      </div>

      {/* 统计卡片 */}
      <div className='px-4 pb-4'>
        <Card>
          <div className='grid grid-cols-4 divide-x divide-gray-200'>
            <div className='py-3 text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {items.length}
              </div>
              <div className='text-sm text-gray-500'>总数</div>
            </div>
            <div className='py-3 text-center'>
              <div className='text-2xl font-bold text-green-600'>
                {items.filter((i) => i.status === 'active').length}
              </div>
              <div className='text-sm text-gray-500'>启用</div>
            </div>
            <div className='py-3 text-center'>
              <div className='text-2xl font-bold text-red-600'>
                {items.filter((i) => i.required).length}
              </div>
              <div className='text-sm text-gray-500'>必检</div>
            </div>
            <div className='py-3 text-center'>
              <div className='text-2xl font-bold text-gray-600'>
                {items.filter((i) => i.status === 'inactive').length}
              </div>
              <div className='text-sm text-gray-500'>禁用</div>
            </div>
          </div>
        </Card>
      </div>

      {/* 检查项目列表 */}
      <div className='px-4'>
        <List header='检查项目列表'>
          {filteredItems.map((item) => (
            <SwipeAction
              key={item.id}
              rightActions={[
                {
                  key: 'edit',
                  text: '编辑',
                  color: 'primary',
                  onClick: () => handleEdit(item),
                },
                {
                  key: 'toggle',
                  text: item.status === 'active' ? '禁用' : '启用',
                  color: item.status === 'active' ? 'warning' : 'success',
                  onClick: () => handleToggleStatus(item.id),
                },
                {
                  key: 'delete',
                  text: '删除',
                  color: 'danger',
                  onClick: () => handleDelete(item.id),
                },
              ]}
            >
              <List.Item
                prefix={
                  <div className='flex size-12 items-center justify-center rounded-lg bg-blue-100'>
                    <span className='font-bold text-blue-600'>
                      {item.order}
                    </span>
                  </div>
                }
                extra={
                  <div className='flex flex-col items-end space-y-1'>
                    <div className='flex gap-1'>
                      <Badge
                        content={item.status === 'active' ? '启用' : '禁用'}
                        color={item.status === 'active' ? '#00b578' : '#999'}
                      />
                      {item.required && (
                        <Badge content='必检' color='#ff3141' />
                      )}
                    </div>
                    <div className='text-xs text-gray-400'>{item.category}</div>
                  </div>
                }
                arrow={false}
              >
                <div>
                  <div className='font-medium text-gray-900'>
                    {item.name}
                    {item.required && (
                      <span className='ml-1 text-red-500'>*</span>
                    )}
                  </div>
                  <div className='mt-1 text-sm text-gray-500'>
                    {item.description}
                  </div>
                </div>
              </List.Item>
            </SwipeAction>
          ))}
        </List>

        {filteredItems.length === 0 && (
          <div className='py-8 text-center text-gray-500'>暂无检查项目数据</div>
        )}
      </div>

      {/* 添加按钮 */}
      <FloatingBubble
        style={{
          '--initial-position-bottom': '24px',
          '--initial-position-right': '24px',
          '--edge-distance': '24px',
        }}
        onClick={handleAdd}
      >
        <div className='text-lg'>+</div>
      </FloatingBubble>

      {/* 添加/编辑弹窗 */}
      <Modal
        visible={showAddModal}
        title={editingItem ? '编辑检查项目' : '添加检查项目'}
        onClose={() => {
          setShowAddModal(false);
          form.resetFields();
        }}
        content={
          <Form form={form} layout='vertical' style={{ padding: '16px 0' }}>
            <Form.Item
              name='name'
              label='项目名称'
              rules={[{ required: true, message: '请输入项目名称' }]}
            >
              <Input placeholder='如：门窗完好' />
            </Form.Item>

            <Form.Item
              name='category'
              label='项目分类'
              rules={[{ required: true, message: '请选择项目分类' }]}
            >
              <Selector options={categories.filter((c) => c.value !== 'all')} />
            </Form.Item>

            <Form.Item
              name='description'
              label='项目描述'
              rules={[{ required: true, message: '请输入项目描述' }]}
            >
              <TextArea placeholder='请输入检查项目的具体要求...' rows={3} />
            </Form.Item>

            <Form.Item name='required' label='是否必检'>
              <Checkbox>设为必检项目</Checkbox>
            </Form.Item>
          </Form>
        }
        actions={[
          {
            key: 'cancel',
            text: '取消',
            onClick: () => {
              setShowAddModal(false);
              form.resetFields();
            },
          },
          {
            key: 'confirm',
            text: editingItem ? '更新' : '添加',
            primary: true,
            onClick: handleSubmit,
          },
        ]}
      />
    </div>
  );
}
