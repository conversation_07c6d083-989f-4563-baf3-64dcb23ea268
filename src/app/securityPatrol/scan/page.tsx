'use client';

import { Button, Input, Modal, NavBar, Toast } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

export default function ScanPage() {
  const router = useRouter();
  const [isScanning, setIsScanning] = useState(true);
  const [showManualInput, setShowManualInput] = useState(false);
  const [manualCode, setManualCode] = useState('');

  const handleScan = (qrData: string) => {
    // 解析二维码数据，假设格式为 patrol_point:A101
    if (qrData.startsWith('patrol_point:')) {
      const pointId = qrData.replace('patrol_point:', '');
      Toast.show({
        icon: 'success',
        content: `扫码成功：${pointId}`,
      });

      // 跳转到巡检表单页面
      setTimeout(() => {
        router.push(`/securityPatrol/form?pointId=${pointId}`);
      }, 1000);
    } else {
      Toast.show({
        icon: 'fail',
        content: '无效的巡检点二维码',
      });
    }
  };

  const handleManualInput = () => {
    if (manualCode.trim()) {
      const qrData = `patrol_point:${manualCode.trim()}`;
      handleScan(qrData);
      setShowManualInput(false);
      setManualCode('');
    } else {
      Toast.show({
        icon: 'fail',
        content: '请输入有效的点位编号',
      });
    }
  };

  const handleBack = () => {
    router.back();
  };

  // 模拟扫码成功 - 演示用
  const simulateScan = (pointId: string) => {
    setIsScanning(false);
    handleScan(`patrol_point:${pointId}`);
  };

  return (
    <div className='min-h-screen bg-black'>
      <NavBar
        onBack={handleBack}
        className='text-white'
        style={{ backgroundColor: 'black' }}
      >
        扫码巡检
      </NavBar>

      <div className='relative h-[calc(100vh-45px)]'>
        {/* 摄像头预览区域 */}
        <div className='flex h-full items-center justify-center bg-gray-900'>
          {/* 扫描框 */}
          <div className='relative'>
            <div className='flex size-64 items-center justify-center rounded-lg border-2 border-dashed border-white'>
              <div className='text-center text-white'>
                <div className='mb-4 text-4xl'>📱</div>
                <p className='text-sm'>将二维码对准此区域</p>
              </div>
            </div>

            {/* 扫描框四角 */}
            <div className='absolute left-0 top-0 size-6 rounded-tl-lg border-l-4 border-t-4 border-blue-400' />
            <div className='absolute right-0 top-0 size-6 rounded-tr-lg border-r-4 border-t-4 border-blue-400' />
            <div className='absolute bottom-0 left-0 size-6 rounded-bl-lg border-b-4 border-l-4 border-blue-400' />
            <div className='absolute bottom-0 right-0 size-6 rounded-br-lg border-b-4 border-r-4 border-blue-400' />
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className='absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/80 to-transparent p-6'>
          <div className='mb-6 text-center'>
            <p className='mb-2 text-sm text-white'>将二维码对准扫描框</p>
            <p className='text-xs text-gray-300'>
              扫描巡检点二维码进行安防巡检
            </p>
          </div>

          <div className='flex gap-3'>
            <Button
              block
              size='large'
              fill='outline'
              style={{ borderColor: 'white', color: 'white' }}
              onClick={() => setShowManualInput(true)}
            >
              手动输入
            </Button>

            <Button
              block
              size='large'
              color='primary'
              onClick={() => simulateScan('A101')}
            >
              模拟扫码
            </Button>
          </div>

          {/* 快速测试按钮 */}
          <div className='mt-4 grid grid-cols-3 gap-2'>
            {['A101', 'A102', 'A103'].map((pointId) => (
              <Button
                key={pointId}
                size='small'
                fill='outline'
                style={{
                  borderColor: 'white',
                  color: 'white',
                  fontSize: '12px',
                }}
                onClick={() => simulateScan(pointId)}
              >
                {pointId}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* 手动输入弹窗 */}
      <Modal
        visible={showManualInput}
        title='手动输入点位编号'
        onClose={() => setShowManualInput(false)}
        content={
          <div className='py-4'>
            <Input
              placeholder='请输入点位编号，如：A101'
              value={manualCode}
              onChange={setManualCode}
            />
            <p className='mt-2 text-sm text-gray-500'>
              点位编号可以在巡检点标签上找到
            </p>
          </div>
        }
        actions={[
          {
            key: 'cancel',
            text: '取消',
            onClick: () => {
              setShowManualInput(false);
              setManualCode('');
            },
          },
          {
            key: 'confirm',
            text: '确认',
            primary: true,
            onClick: handleManualInput,
          },
        ]}
      />
    </div>
  );
}
