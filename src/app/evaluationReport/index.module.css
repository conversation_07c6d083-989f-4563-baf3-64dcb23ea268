.bg {
  background: url('/images/evaluation/evaluation-bg.png') no-repeat top center;
  background-size: cover;
  padding: 55px 15px;
}
.container {
  background: url('/images/evaluation/bg.png') no-repeat top center;
  background-size: cover;
  padding: 30px 30px;
  border-radius: 8px;
  overflow: hidden;
}
.header {
  padding-top: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.headerAvatar {
  border-radius: 50%;
  border: 5px solid rgba(198, 132, 80, 0.18);
  width: 84px;
  height: 84px;
  padding: 2px;
  margin-right: 15px;
}
.avatar {
  width: 100%;
  height: 100%;
}
.info {
  font-size: 28px;
  font-weight: 400;
  color: #c68450;
  position: relative;
  line-height: 42px;
  top:4px;
  font-weight: 600;
}
.name {
  font-size: 24px;
}
.title {
  align-self: center;
  font-size: 44px;
  color: #ffffff;
  width: 174px;
  height: 50px;
  position: absolute;
  left: 50%;
  margin-left: -81px;
}

/* 内容 */
.content {
  background-color: #ffffff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-top: 32px;
  border-radius: 22px;
}
.evaluate {
  width: 236px;
  height: 98px;
  align-self: center;
  position: relative;
  top: -24px;
}
.radar {
  padding: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.illustrate {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  margin-bottom: 20px;
}
.talent {
  align-self: center;
  width: 369px;
  height: 97px;
}
.illustrateItem {
  font-size: 28px;
  color: #333333;
  padding-left: 32px;
  padding-right: 32px;
  margin-bottom: 20px;
}
.illustrateImg {
  width: 102px;
  height: 94px;
}
.illustrateTitle {
  font-size: 40x;
  color: #974518;
  position: relative;
  margin-bottom: 20px;
}
.illustrateText {
  position: absolute;
  font-size: 40px;
  top: 50%;
  margin-top: -20px;
  left: 20px;
  font-weight: 600;

}
.illustrateInfo {
  font-size: 28px;
  line-height: 42px;
}
.download{
  position: fixed;
  width: 180px;
  height: 66px;
  line-height: 44px;
  bottom: 80px;
  font-size: 26px;
  right:80px;
  background-color: #FAD8B1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  color: #c68450;
}
