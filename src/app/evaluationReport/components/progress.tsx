import { Image } from 'antd-mobile';
import React from 'react';

import styles from './progress.module.css';

const Progress = (props: any) => {
  return (
    <div className={styles.itemContent}>
      <div className={styles.item}>
        <Image className={styles.icon} src={props.src} alt="" />
        <p className={styles.line} />
        <p className={styles.title}>{props.title}</p>
      </div>
      <div className={styles.processBox}>
        <div
          className={styles.process}
          style={{
            width: props.process,
            background: `linear-gradient(to bottom, ${props.colors[0]}, ${props.colors[1]})`,
          }}
        />
        <p className={styles.rating}>{props.rating}</p>
      </div>
    </div>
  );
};
export default Progress;
