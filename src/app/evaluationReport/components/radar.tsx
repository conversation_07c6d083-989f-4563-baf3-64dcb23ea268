import {
  Chart as ChartJ<PERSON>,
  Filler,
  Legend,
  LineElement,
  PointElement,
  RadialLinearScale,
  Tooltip,
} from 'chart.js';
import React from 'react';
import { Radar } from 'react-chartjs-2';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
);
const RadarComponent = ({ radarsData }:any) => {
  const data = {
    labels: ['语言', '数理', '人际', '空间', '音乐', '认知', '运动'],
    datasets: [
      {
        data: radarsData,
        backgroundColor: '#FED46E9A',
        borderColor: '#FED46E',
        borderWidth: 1,
      },
    ],
  };
  const options = {
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      r: {
        ticks: {
          count: 4,
          maxTicksLimit: 4,
          precision: 0,
          stepSize: 1,
        },
        pointLabels: {
          color: '#333',
          font: {
            size: 14,
          },
        },
        grid: {
          color: ['#EFEFEF', '#E4AF60', '#EFEFEF'],
        },
        max: 3,
        suggestedMin: 0,
      },
    },
  };
  return <Radar data={data} options={options} />;
};
export default RadarComponent;
