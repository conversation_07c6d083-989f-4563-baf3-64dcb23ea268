.itemContent {
  padding-bottom: 30px;
  flex: 1;
  padding-left: 32px;
}
.item {
  flex-direction: row;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.icon {
  width: 104px;
  height: 83px;
}
.line {
  width: 8px;
  height: 28px;
  background: #974518;
  border-radius: 4px;
  margin-left: 26px;
  margin-right: 6px;
}
.title {
  font-size: 31px;
  font-weight: 400;
  color: #974518;
}
.processBox {
  width: 210px;
  height: 13px;
  border-radius: 6px;
  background-color: #efebe6;
  z-index: 2;
  position: relative;
}
.process {
  width: 210px;
  height: 13px;
  border-radius: 6px;
  background: #974518;
  z-index: 1;
  position: absolute;
}
.rating {
  height: 30px;
  font-size: 24px;
  color: #333333;
  z-index: 3;
  right: -56px;
  top: 50%;
  margin-top: -15px;
  position: absolute;
  line-height: 30px;
}
