'use client';

import { Image } from 'antd-mobile';
import html2canvas from 'html2canvas';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getEvaluationReport } from '@/api/evaluationReport';
import OverLayLoad from '@/components/OverLayLoad/Index';

import Progress from './components/progress';
import Radar from './components/radar';
import styles from './index.module.css';

export const dynamic = 'force-dynamic';

const Index = () => {
  const searchParams = useSearchParams() as any;
  const [type] = useState(searchParams.get('type') || 1);
  const [phone] = useState(searchParams.get('phone') || '');
  const [instId] = useState(searchParams.get('instId') || '');
  const [studentName, setStudentName] = useState('');
  const [visible, setVisible] = useState(true);
  const [sex, setSex] = useState('男');
  const [process] = useState(['0%', '30%', '70%', '100%']);
  const [processText] = useState(['普通', '一般', '良好', '优秀']);
  const [radars, setRadars] = useState([0, 0, 0, 0, 0, 0, 0]);
  const [talent, setTalent] = useState([
    {
      title: '语言',
      rating: '优秀',
      colors: ['#FFEA78', '#FEC568'],
      process: '0%',
      src: '/images/evaluation/lang.png',
    },
    {
      title: '数理',
      rating: '优秀',
      colors: ['#92F9AD', '#7AD8A1'],
      process: '0%',
      src: '/images/evaluation/digital.png',
    },
    {
      title: '人际',
      rating: '优秀',
      colors: ['#F0DDFF', '#CAA0EC'],
      process: '0%',
      src: '/images/evaluation/people.png',
    },
    {
      title: '空间',
      rating: '优秀',
      colors: ['#B1E1FF', '#74C0F5'],
      process: '0%',
      src: '/images/evaluation/space.png',
    },
    {
      title: '音乐',
      rating: '优秀',
      colors: ['#92F9AD', '#7AD8A1'],
      process: '0%',
      src: '/images/evaluation/music.png',
    },
    {
      title: '认知',
      rating: '优秀',
      colors: ['#FFBEB4', '#FF695A'],
      process: '0%',
      src: '/images/evaluation/cognition.png',
    },
    {
      title: '运动',
      rating: '优秀',
      colors: ['#92F9AD', '#7AD8A1'],
      process: '0%',
      src: '/images/evaluation/sports.png',
    },
  ]);
  const [info] = useState([
    {
      title: '语言',
      src: '/images/evaluation/langIcon.png',
      text: '如果孩子有很好的语言天赋。具有语言才能的孩子，父母应该常请他描述一些对象、一件事、一个自然现象等等，并经常给他提供这方面的书籍',
    },
    {
      title: '数理',
      src: '/images/evaluation/digitalIcon.png',
      text: '如果孩子在数学、逻辑方面有天赋。他喜爱下跳棋和象棋，能很快明白一些等量关系。如果给他一些完全混乱的玩具，他会分门别类地把它们归类。这种孩子，也许他上学后的数学成绩并不理想（这可能由于他对讲述的课程语言方式不适应，或者注意力太容易分散引起），但他在这方面的潜能是不应怀疑的',
    },
    {
      title: '音乐',
      src: '/images/evaluation/musicIcon.png',
      text: '如果孩子有很好的音乐才能。这类孩子在很小的时候（2、3岁）就特别注意倾听有规律的声音，只要有音乐出现，他就会瞪大眼睛专注地聆听，这时他所表现出来的专注程度，连七八岁的孩子都比不上。这表明他在音乐方面潜能很大。',
    },
    {
      title: '运动',
      src: '/images/evaluation/sportsIcon.png',
      text: '如果孩子有很好的身体动觉才能。通常运动员和舞蹈家都有这方面的天赋',
    },
    {
      title: '空间',
      src: '/images/evaluation/spaceIcon.png',
      text: '如果孩子有很好的空间方面的才能。他有丰富的想象力，他对绘画、机械组装有浓厚了兴趣。应该多带他去远行，并从小让他做画地图的游戏。',
    },
    {
      title: '认知',
      src: '/images/evaluation/cognitionIcon.png',
      text: '如果孩子有很好的自我认识才能。通常剧作家或者导演会有这方面的才能',
    },
    {
      title: '人际',
      src: '/images/evaluation/peopleIcon.png',
      text: '如果孩子很好地认识他人的才能。这类孩子对自我和别人常常不由自主地做出判断和反省，具有与人交往、沟通、组织方面的潜能',
    },
  ]);
  const getFetchData = () => {
    getEvaluationReport({
      type,
      phone,
      instId: instId || Cookies.get('instId'),
      studentId: Cookies.get('studentId'),
    })
      .then((data: any) => {
        setStudentName(data.name);
        setSex(data.sex === 1 ? '男' : '女');
        const score = JSON.parse(data.score || '{}');
        const scoreArray = Object.values(score) as any;
        const talentTemp: any = talent.map((item, index) => {
          return {
            ...item,
            process: process[scoreArray[index]],
            rating: processText[scoreArray[index]],
          };
        });
        setTalent(talentTemp);
        setRadars(scoreArray);
      })
      .finally(() => {
        setVisible(false);
      });
  };
  useEffect(() => {
    getFetchData();
  }, [type, phone, instId]);
  const handleDownload = () => {
    const content = document.getElementById('content');
    html2canvas(content).then(function (canvas) {
      const dataURL = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.download = 'evaluationReport.png';
      link.href = dataURL;
      link.click();
    });
  };
  return (
    <>
      <OverLayLoad
        visible={visible}
        style={{
          backgroundColor: '#fff',
          zIndex: 10,
        }}
      />
      {!studentName ? (
        <div className="flex min-h-screen flex-col items-center  justify-center">
          <Image
            src="/images/nocontent.png"
            alt="logo"
            style={{ width: '150px', height: '150px' }}
          />
          <p className="text-sm text-[#999]">您还没有提交测评！</p>
        </div>
      ) : (
        <div
          id="content"
          className={`relative flex min-h-screen flex-col ${styles.bg}`}
        >
          <div className={styles.container}>
            <div className={styles.header}>
              <div className={styles.headerAvatar}>
                <Image
                  className={styles.avatar}
                  src={
                    sex === '女' ? '/images/avatar2.png' : '/images/avatar1.png'
                  }
                  alt="头像"
                />
              </div>
              <div className={styles.info}>
                <p>{studentName}</p>
                {Number(type) === 2 && <p className={styles.name}>{sex}</p>}
                {/* <p className={styles.name}>班级</p> */}
              </div>
              <Image
                className={styles.title}
                src="/images/evaluation/title.png"
                alt=""
              />
            </div>
            <div className={styles.content}>
              <Image
                className={styles.evaluate}
                src="/images/evaluation/evaluate.png"
                alt=""
              />
              <div className="mt-4 flex flex-row flex-wrap">
                {talent.map((item) => {
                  return (
                    <Progress
                      key={item.title}
                      title={item.title}
                      rating={item.rating}
                      colors={item.colors}
                      process={item.process}
                      src={item.src}
                    />
                  );
                })}
              </div>
              <div className={styles.radar}>
                <Radar radarsData={radars} />
              </div>
              <div className={styles.illustrate}>
                <Image
                  className={styles.talent}
                  src="/images/evaluation/talent.png"
                  alt=""
                />
                {info.map((item) => {
                  return (
                    <div className={styles.illustrateItem} key={item.title}>
                      <div className={styles.illustrateTitle}>
                        <span className={styles.illustrateText}>
                          {item.title}
                        </span>
                        <Image
                          className={styles.illustrateImg}
                          src={item.src}
                          alt=""
                        />
                      </div>
                      <p className={styles.illustrateInfo}>{item.text}</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <div
            data-html2canvas-ignore
            className={styles.download}
            onClick={handleDownload}
          >
            保存为图片
          </div>
        </div>
      )}
    </>
  );
};
export default Index;
