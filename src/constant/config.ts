import { generateKey, uploadObs } from '@/utils/obs';

export const siteConfig = {
  title: '掌心宝贝',
  description:
    '掌心宝贝 APP 是一款多功能、综合性的创新解决方案，专为幼儿园打造，旨在提供更优质的管理和服务方式。我们以先进技术和用户友好的界面为基础，构建了一个紧密而便捷的校园平台，为教师、学生家长创造无缝互动体验。我们不断努力创新，推动学前教育的高质量发展，感谢您的支持，期待您亲身体验！',
  url: process.env.NEXT_PUBLIC_BABY_WEB_URL_HOST
};

export const siteConfigKid = {
  title: '掌心智校',
  description:
    '掌心智校 APP 是一款多功能、综合性的创新解决方案，专为幼儿园打造，旨在提供更优质的管理和服务方式。我们以先进技术和用户友好的界面为基础，构建了一个紧密而便捷的校园平台，为教师、学生家长创造无缝互动体验。我们不断努力创新，推动学前教育的高质量发展，感谢您的支持，期待您亲身体验！',
  url: process.env.NEXT_PUBLIC_WEB_URL_HOST
};
export const siteConfigAdmin = {
  serverUrl: process.env.NEXT_APP_HANI_API_HOST
};
// 默认头像
export const defaultAvatar =
  'https://unicorn-media.ancda.com/production/app/avatar/default_avator.png';

// 部门头像
export const departmentAvatar =
  'https://unicorn-media.ancda.com/production/app/avatar/department.png';

export const initFullProps = {
  menubar: 'edit view format tools table help insert',
  formats: {
    tindent_format: { selector: 'p', styles: { 'text-indent': '40mm' } },
  },
  toolbar:
    'keyboard_btn | image_btn video_btn | undo redo | bold italic forecolor backcolor |  \
       alignleft aligncenter alignright alignjustify tindent_bttn | fontsizeselect | tfecha_bttn | \
       bullist numlist outdent indent | removeformat ',
  plugins: ['lists image media table code wordcount textcolor'],
  // mobile: {
  //   theme: 'mobile',
  //   toolbar: ['undo', 'bold', 'italic', 'styleselect, restoredraft'],
  // },
  language: 'cn',
  statusbar: false,
  language_url: '/tinymce/langs/cn.js',
  // paste_data_images: false,
  valid_elements: '+*[*]',
  force_p_newlines: false,
  branding: false,
  toolbar_location: 'bottom',
  forced_root_block: '',
  setup: (editor: any) => {
    editor.on('focus', (e) => {
      console.log('focus.', e);
    });
    editor.on('blur', (e) => {
      console.log('blur.', e);
    });
    editor.ui.registry.addIcon(
      'keyboard',
      `<svg width="24" height="24" ><path d="M20,3 L4,3 C2.9,3 2.01,3.9 2.01,5 L2,15 C2,16.1 2.9,17 4,17 L20,17 C21.1,17 22,16.1 22,15 L22,5 C22,3.9 21.1,3 20,3 Z M20,15 L4,15 L4,5 L20,5 L20,15 Z M11,6 L13,6 L13,8 L11,8 L11,6 Z M11,9 L13,9 L13,11 L11,11 L11,9 Z M8,6 L10,6 L10,8 L8,8 L8,6 Z M8,9 L10,9 L10,11 L8,11 L8,9 Z M5,9 L7,9 L7,11 L5,11 L5,9 Z M5,6 L7,6 L7,8 L5,8 L5,6 Z M8,12 L16,12 L16,14 L8,14 L8,12 Z M14,9 L16,9 L16,11 L14,11 L14,9 Z M14,6 L16,6 L16,8 L14,8 L14,6 Z M17,9 L19,9 L19,11 L17,11 L17,9 Z M17,6 L19,6 L19,8 L17,8 L17,6 Z M12,23 L16,19 L8,19 L12,23 Z"></path></svg>`,
    );
    editor.ui.registry.addButton('keyboard_btn', {
      text: '',
      icon: 'keyboard',
      tooltip: '收起键盘',
      onAction: () => {
        editor.fire('blur', {});
        document.activeElement && document.activeElement.blur();
      },
    });
    editor.ui.registry.addIcon(
      'imageUpload',
      '<svg width="24" height="24" focusable="false"><path d="M5 15.7l3.3-3.2c.3-.3.7-.3 1 0L12 15l4.1-4c.3-.4.8-.4 1 0l2 1.9V5H5v10.7zM5 18V19h3l2.8-2.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 3h16c.6 0 1 .4 1 1v16c0 .6-.4 1-1 1H4a1 1 0 01-1-1V4c0-.6.4-1 1-1zm6 8a2 2 0 100-4 2 2 0 000 4z" fill-rule="nonzero"></path></svg>',
    );
    editor.ui.registry.addButton('image_btn', {
      text: '',
      icon: 'imageUpload',
      tooltip: '上传图片',
      onAction: () => {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute('accept', 'image/*');
        input.onchange = async () => {
          const file = input.files?.[0];
          if (file) {
            const key = generateKey(file.name, 'edit');
            await uploadObs(file, key, false, (progress: number) => {}).then(
              (url) => {
                editor.insertContent(
                  `<p><br/></p><p><img src="${url}" alt="" style="max-width: 100%;"/></p><p><br/></p>`,
                );
              },
            );
          }
        };
        input.click();
      },
    });
    editor.ui.registry.addIcon(
      'videoUpload',
      '<svg width="24" height="24" focusable="false"><path d="M4 3h16c.6 0 1 .4 1 1v16c0 .6-.4 1-1 1H4a1 1 0 01-1-1V4c0-.6.4-1 1-1zm1 2v14h14V5H5zm4.8 2.6l5.6 4a.5.5 0 010 .8l-5.6 4A.5.5 0 019 16V8a.5.5 0 01.8-.4z" fill-rule="nonzero"></path></svg>',
    );
    editor.ui.registry.addButton('video_btn', {
      text: '',
      icon: 'videoUpload',
      tooltip: '上传视频',
      onAction: () => {
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.setAttribute('accept', 'video/*');
        input.onchange = async () => {
          const file = input.files?.[0];
          if (file) {
            const key = generateKey(file.name, 'edit');
            await uploadObs(file, key, false, (progress: number) => {}).then(
              (url) => {
                editor.insertContent(
                  `<p><br/></p><p><video src="${url}" controls="controls" style="width: 100%;height: auto" poster="${url}?x-workflow-graph-name=video-thumbnail"></video></p><p><br/></p>`,
                );
              },
            );
          }
        };
        // 触发文件选择
        input.click();
      },
    });
  },
  height: '100%',
  // content_css: 'document',
  content_style: `
     * {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
     }
      `,
};
