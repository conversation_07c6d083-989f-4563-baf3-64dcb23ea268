export const isProd = process.env.NODE_ENV === 'production';
export const isLocal = process.env.NODE_ENV === 'development';

export const showLogger = isLocal
  ? true
  : (process.env.NEXT_PUBLIC_SHOW_LOGGER === 'true' ?? false);

export const captchaId = 'c961660434ec42d194721fc57e580e8c';
export const babyCaptchaId = 'dadd7febd7954b6daceb0e0477e1d0d3';
export const apiHost = process.env.NEXT_APP_API_HOST;
export const webUrlHost = process.env.NEXT_PUBLIC_WEB_URL_HOST;
export const babyWebUrlHost = process.env.NEXT_PUBLIC_BABY_WEB_URL_HOST;
export const haniApiHost = process.env.NEXT_APP_HANI_API_HOST;
export const aiWebUrlHost = process.env.NEXT_APP_AI_HOST;
