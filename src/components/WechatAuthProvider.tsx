'use client';

import { useWechatAuth } from '@/hooks/useWechatAuth';

interface WechatAuthProviderProps {
  children: React.ReactNode;
  repeatApiFn?: () => void;
  appId?: string;
  disabled?: boolean; // 是否禁用微信认证
}

export const WechatAuthProvider: React.FC<WechatAuthProviderProps> = ({
  children,
  repeatApiFn,
  appId,
  disabled = false,
}) => {
  // 只有在未禁用时才执行微信认证
  if (!disabled) {
    useWechatAuth({ repeatApiFn, appId });
  }

  return <>{children}</>;
};
