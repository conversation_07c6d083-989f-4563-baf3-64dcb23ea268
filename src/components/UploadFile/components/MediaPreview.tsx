import type React from 'react';
import { useRef, useEffect, useState } from 'react';
import { X, Play, Pause } from 'lucide-react';
import type { FileType } from '@/components/UploadFile/types';
import clsx from 'clsx';

interface MediaPreviewProps {
  file: FileType;
  onClose: () => void;
}

const MediaPreview: React.FC<MediaPreviewProps> = ({ file, onClose }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const mediaRef = useRef<HTMLVideoElement | HTMLAudioElement | null>(null);

  // 获取文件 URL（优先使用远程 URL，如果没有则使用本地 URL）
  const fileUrl = file.status === 'success' ? file.url : file.localUrl;

  // 格式化时长
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 播放/暂停媒体
  const togglePlay = () => {
    if (!mediaRef.current) return;

    if (isPlaying) {
      mediaRef.current.pause();
    } else {
      mediaRef.current.play().catch((error) => {
        console.error('播放失败：', error);
      });
    }
  };

  // 监听媒体播放状态变化
  useEffect(() => {
    const mediaElement = mediaRef.current;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    if (mediaElement) {
      mediaElement.addEventListener('play', handlePlay);
      mediaElement.addEventListener('pause', handlePause);
      mediaElement.addEventListener('ended', handleEnded);
    }

    return () => {
      if (mediaElement) {
        mediaElement.removeEventListener('play', handlePlay);
        mediaElement.removeEventListener('pause', handlePause);
        mediaElement.removeEventListener('ended', handleEnded);
      }
    };
  }, []);

  // 阻止点击事件冒泡
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // 渲染媒体内容
  const renderMediaContent = () => {
    if (!fileUrl) return <div className="text-white">文件不可用</div>;

    switch (file.type) {
      case 'image':
        return (
          <img
            src={fileUrl}
            alt={file.name}
            className="max-h-[80vh] max-w-full object-contain"
            onClick={handleContentClick}
          />
        );
      case 'video':
        return (
          <video
            ref={mediaRef as React.RefObject<HTMLVideoElement | null>}
            src={fileUrl}
            controls
            className="max-h-[80vh] max-w-full"
            onClick={handleContentClick}
          >
            <track kind="captions" src="" label="中文" />您的浏览器不支持视频播放
                      </video>
        );
      case 'audio':
        return (
          <div
            className="bg-gradient-to-r from-green-100 to-blue-100 p-8 rounded-lg flex flex-col items-center"
            onClick={handleContentClick}
          >
            <div className="w-24 h-24 rounded-full bg-white/30 backdrop-blur-sm flex items-center justify-center mb-4">
              <button
                type="button"
                onClick={togglePlay}
                className="w-16 h-16 flex items-center justify-center bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors"
              >
                {isPlaying ? (
                  <Pause className="w-8 h-8" />
                ) : (
                  <Play className="w-8 h-8" />
                )}
              </button>
            </div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">
              {file.name}
            </h3>
            {file.duration && (
              <div className="text-gray-600">
                {formatDuration(file.duration || 0)}
              </div>
            )}
            <audio
              ref={mediaRef as React.RefObject<HTMLAudioElement | null>}
              src={fileUrl}
              className="hidden"
            >
              <track kind="captions" src="" label="中文" />
              您的浏览器不支持音频播放
            </audio>
          </div>
        );
      default:
        return <div className="text-white">不支持的文件类型</div>;
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
      onClick={onClose}
    >
      <button
        type="button"
        onClick={onClose}
        className="absolute top-4 right-4 text-white hover:text-gray-300"
      >
        <X className="w-8 h-8" />
      </button>
      <div
        className={clsx(
          'max-w-4xl w-full mx-4 flex items-center justify-center',
          file.type === 'audio' ? 'max-w-md' : 'max-w-4xl'
        )}
      >
        {renderMediaContent()}
      </div>
    </div>
  );
};

export default MediaPreview;
