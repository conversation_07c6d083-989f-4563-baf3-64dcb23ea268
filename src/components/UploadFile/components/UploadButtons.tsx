import type React from 'react';
import { useRef } from 'react';
import { Image, Video, Mic } from 'lucide-react';

interface UploadButtonsProps {
  onImageSelect: (files: FileList) => void;
  onVideoSelect: (files: FileList) => void;
  onRecordClick: () => void;
}

const UploadButtons: React.FC<UploadButtonsProps> = ({
  onImageSelect,
  onVideoSelect,
  onRecordClick
}) => {
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // 处理图片选择
  const handleImageClick = () => {
    imageInputRef.current?.click();
  };

  // 处理视频选择
  const handleVideoClick = () => {
    videoInputRef.current?.click();
  };

  // 处理图片文件选择
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onImageSelect(e.target.files);
      // 重置 input，允许选择相同文件
      e.target.value = '';
    }
  };

  // 处理视频文件选择
  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onVideoSelect(e.target.files);
      // 重置 input，允许选择相同文件
      e.target.value = '';
    }
  };

  return (
    <div className="grid grid-cols-3 gap-4">
      {/* 图片上传按钮 */}
      <div className="relative">
        <button
          onClick={handleImageClick}
          type="button"
          className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-sm border border-gray-200 hover:bg-blue-50 transition-colors w-full"
        >
          <Image className="w-8 h-8 text-blue-500 mb-2" />
          <span className="text-xs">上传图片</span>
        </button>
        <input
          ref={imageInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          aria-label="上传图片"
        />
      </div>

      {/* 视频上传按钮 */}
      <div className="relative">
        <button
          onClick={handleVideoClick}
          type="button"
          className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-sm border border-gray-200 hover:bg-purple-50 transition-colors w-full"
        >
          <Video className="w-8 h-8 text-purple-500 mb-2" />
          <span className="text-xs">上传视频</span>
        </button>
        <input
          ref={videoInputRef}
          type="file"
          accept="video/*"
          multiple
          onChange={handleVideoChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          aria-label="上传视频"
        />
      </div>

      {/* 录音按钮 */}
      <button
        onClick={onRecordClick}
        type="button"
        className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-sm border border-gray-200 hover:bg-green-50 transition-colors"
      >
        <Mic className="w-8 h-8 text-green-500 mb-2" />
        <span className="text-xs">录音</span>
      </button>
    </div>
  );
};

export default UploadButtons;
