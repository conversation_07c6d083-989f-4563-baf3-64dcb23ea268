import type React from 'react';
import { memo, useState } from 'react';
import type { FileType } from '../types';
import { Video, Mic, CheckCircle2, Trash2 } from 'lucide-react';
import MediaPreview from './MediaPreview';

interface FileListProps {
  files: FileType[];
  onDelete: (id: string) => void;
}

const FileList: React.FC<FileListProps> = ({ files, onDelete }) => {
  console.log('🚀 ~ files:', files);
  const [previewFile, setPreviewFile] = useState<FileType | null>(null);

  // 将文件分成三列
  const chunkedFiles = chunkArray(files, 3);

  // 打开预览
  const handlePreview = (file: FileType) => {
    setPreviewFile(file);
  };

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewFile(null);
  };

  // 格式化文件大小
  const formatFileSize = (bytes = 0) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  // 格式化时长
  const formatDuration = (seconds = 0) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 渲染单个文件项
  const renderFileItem = (file: FileType) => {
    console.log('🚀 ~ file:', file);
    const fileUrl = file.url || file.localUrl;

    return (
      <div
        key={file.id}
        className="relative bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden"
      >
        {/* 文件预览 */}
        <div className="cursor-pointer" onClick={() => handlePreview(file)}>
          {/* 文件预览内容 */}
          <div className="relative w-full aspect-[4/3] bg-gray-100 rounded-t-lg overflow-hidden">
            {file.type === 'image' && (
              <img
                src={fileUrl}
                alt={file.name}
                className="w-full h-full object-cover"
              />
            )}

            {file.type === 'video' && (
              <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                {fileUrl && (
                  <video
                    src={fileUrl}
                    className="w-full h-full object-cover opacity-80"
                  >
                    <track kind="captions" src="" label="中文" />
                    您的浏览器不支持视频播放
                  </video>
                )}
                <div className="absolute inset-0 flex items-center justify-center">
                  <Video className="w-10 h-10 text-white bg-black/50 rounded-full p-2" />
                </div>
              </div>
            )}

            {file.type === 'audio' && (
              <div className="w-full h-full bg-gradient-to-r from-green-50 to-blue-50 flex items-center justify-center">
                <Mic className="w-10 h-10 text-green-500" />
                {file.duration && (
                  <span className="absolute bottom-1 right-2 text-xs text-gray-500 bg-white/70 px-1 rounded">
                    {formatDuration(file.duration)}
                  </span>
                )}
              </div>
            )}

            {/* 上传成功标识 */}
            {file.status === 'success' && (
              <div className="absolute top-1 right-1">
                <CheckCircle2 className="w-5 h-5 text-green-500 bg-white rounded-full" />
              </div>
            )}
          </div>
        </div>

        {/* 文件信息 */}
        <div className="p-2">
          <div className="flex items-center justify-between">
            <span className="text-xs font-medium truncate max-w-[100%]">
              {file.name}
            </span>
          </div>
          <div className="flex items-center justify-between">
            {/* 文件大小 */}
            {file.size ? (
              <div className="text-xs text-gray-400 mt-1">
                {formatFileSize(file.size)}
              </div>
            ) : (
              <div className="text-xs text-gray-400 mt-1">未知大小</div>
            )}
            <button
              type="button"
              onClick={() => onDelete(file.id)}
              className="text-gray-400 hover:text-red-500"
            >
              <Trash2 className="w-4 h-4 text-red-400" />
            </button>
          </div>
        </div>

        {/* 上传进度条 */}
        {file.status === 'uploading' && (
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gray-100">
            <div
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${file.progress}%` }}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex-1">
      {files.length === 0 ? (
        <div className="flex items-center justify-center h-40 text-gray-400">
          暂无文件，请点击下方按钮上传
        </div>
      ) : (
        <div className="grid grid-cols-3 gap-4">
          {chunkedFiles.map((column, columnIndex) => (
            <div key={`column-${columnIndex}`} className="space-y-4">
              {column.map((file) => renderFileItem(file))}
            </div>
          ))}
        </div>
      )}

      {/* 媒体预览模态框 */}
      {previewFile && (
        <MediaPreview file={previewFile} onClose={handleClosePreview} />
      )}
    </div>
  );
};

// 将数组分成指定列数的二维数组
function chunkArray<T>(array: T[], columns: number): T[][] {
  if (array.length === 0) return Array(columns).fill([]);

  const result: T[][] = Array(columns)
    .fill(0)
    .map(() => []);

  array.forEach((item, index) => {
    const columnIndex = index % columns;
    if (result[columnIndex]) {
      result[columnIndex].push(item);
    }
  });

  return result;
}

export default memo(FileList);
