import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { Mic, Pause, Play, Square, Check, X, AlertCircle } from 'lucide-react';
import clsx from 'clsx';

interface RecordingModalProps {
  onClose: () => void;
  onComplete: (audioBlob: Blob, duration: number) => void;
}

const RecordingModal: React.FC<RecordingModalProps> = ({
  onClose,
  onComplete
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [amplitudeValues, setAmplitudeValues] = useState(Array(20).fill(2));
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 开始录音
  const startRecording = async () => {
    setIsRequestingPermission(true);
    setPermissionError(null);

    try {
      // 检查浏览器是否支持 getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('您的浏览器不支持录音功能，请使用现代浏览器');
      }

      // 请求麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: 'audio/wav'
        });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
      };

      // 每秒收集数据
      mediaRecorder.start(1000);
      setIsRecording(true);
      setIsPaused(false);
      setRecordingTime(0);

      // 开始计时
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);

      // 模拟音频振幅动画
      const amplitudeInterval = setInterval(() => {
        if (!isPaused) {
          setAmplitudeValues(
            Array(20)
              .fill(0)
              .map(() => Math.random() * 40 + 2)
          );
        }
      }, 100);

      return () => {
        clearInterval(amplitudeInterval);
      };
    } catch (error: unknown) {
      setIsRequestingPermission(false);

      console.error('获取麦克风权限失败：', error);
      // 设置错误信息
      if (
        error instanceof Error &&
        (error.name === 'NotAllowedError' ||
          error.name === 'PermissionDeniedError')
      ) {
        setPermissionError('麦克风权限被拒绝，请在浏览器设置中允许访问麦克风');
      } else {
        setPermissionError(
          error instanceof Error ? error.message : '录音初始化失败，请重试'
        );
      }
    } finally {
      setIsRequestingPermission(false);
    }
  };

  // 暂停录音
  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      if (!isPaused) {
        mediaRecorderRef.current.pause();
        setIsPaused(true);
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      } else {
        mediaRecorderRef.current.resume();
        setIsPaused(false);
        timerRef.current = setInterval(() => {
          setRecordingTime((prev) => prev + 1);
        }, 1000);
      }
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // 停止所有音轨
      if (streamRef.current) {
        for (const track of streamRef.current.getTracks()) {
          track.stop();
        }
      }
    }
  };

  // 播放录音
  const playRecording = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  // 确认录音
  const confirmRecording = () => {
    if (audioChunksRef.current.length > 0) {
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
      onComplete(audioBlob, recordingTime);
    }
  };

  // 监听音频播放结束
  useEffect(() => {
    const audioElement = audioRef.current;

    const handleEnded = () => {
      setIsPlaying(false);
    };

    if (audioElement) {
      audioElement.addEventListener('ended', handleEnded);
    }

    return () => {
      if (audioElement) {
        audioElement.removeEventListener('ended', handleEnded);
      }
    };
  }, [audioUrl]);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      if (streamRef.current) {
        for (const track of streamRef.current.getTracks()) {
          track.stop();
        }
      }

      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        {/* 模态框头部 */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-medium">录音</h3>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 模态框内容 */}
        <div className="p-6">
          {/* 录音波形图 */}
          <div className="flex items-end justify-center h-24 mb-6 space-x-1">
            {amplitudeValues.map((height, index) => (
              <div
                key={index}
                className={clsx(
                  'w-1 bg-green-500 rounded-t transition-all duration-100',
                  isRecording && !isPaused ? 'opacity-100' : 'opacity-50'
                )}
                style={{ height: `${height}px` }}
              />
            ))}
          </div>

          {/* 录音时间 */}
          <div className="text-center text-2xl font-medium mb-6">
            {formatTime(recordingTime)}
          </div>

          {/* 错误提示 */}
          {permissionError && (
            <div className="mb-6 p-3 bg-red-50 text-red-600 rounded-lg flex items-start">
              <AlertCircle className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
              <p>{permissionError}</p>
            </div>
          )}

          {/* 录音控制按钮 */}
          <div className="flex items-center justify-center space-x-6">
            {!isRecording && !audioUrl ? (
              // 开始录音按钮
              <button
                type="button"
                onClick={startRecording}
                disabled={isRequestingPermission}
                className={clsx(
                  'flex items-center justify-center w-16 h-16 text-white rounded-full transition-colors',
                  isRequestingPermission
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-red-500 hover:bg-red-600'
                )}
              >
                {isRequestingPermission ? (
                  <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Mic className="w-8 h-8" />
                )}
              </button>
            ) : isRecording ? (
              // 录音中的控制按钮
              <>
                <button
                  type="button"
                  onClick={pauseRecording}
                  className="flex items-center justify-center w-12 h-12 bg-gray-200 text-gray-700 rounded-full hover:bg-gray-300 transition-colors"
                >
                  {isPaused ? (
                    <Play className="w-6 h-6" />
                  ) : (
                    <Pause className="w-6 h-6" />
                  )}
                </button>
                <button
                  type="button"
                  onClick={stopRecording}
                  className="flex items-center justify-center w-16 h-16 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <Square className="w-6 h-6" />
                </button>
              </>
            ) : audioUrl ? (
              // 录音完成后的控制按钮
              <>
                <button
                  type="button"
                  onClick={playRecording}
                  className="flex items-center justify-center w-12 h-12 bg-gray-200 text-gray-700 rounded-full hover:bg-gray-300 transition-colors"
                >
                  {isPlaying ? (
                    <Pause className="w-6 h-6" />
                  ) : (
                    <Play className="w-6 h-6" />
                  )}
                </button>
                <button
                  type="button"
                  onClick={confirmRecording}
                  className="flex items-center justify-center w-16 h-16 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors"
                >
                  <Check className="w-8 h-8" />
                </button>
              </>
            ) : null}
          </div>

          {/* 音频元素（隐藏） */}
          {audioUrl && (
            <audio ref={audioRef} src={audioUrl} controls className="hidden" />
          )}
        </div>

        {/* 模态框底部 */}
        <div className="p-4 border-t text-sm text-gray-500">
          {!isRecording && !audioUrl
            ? '点击麦克风图标开始录音'
            : isRecording
              ? '点击方块图标停止录音'
              : audioUrl
                ? '点击播放按钮试听，确认无误后点击对勾确认'
                : null}
        </div>
      </div>
    </div>
  );
};

export default RecordingModal;
