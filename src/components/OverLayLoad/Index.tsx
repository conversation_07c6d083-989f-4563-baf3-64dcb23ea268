import { Mask, SpinLoading } from 'antd-mobile';

const Load = (props: any) => {
  return (
    <Mask
      visible={props.visible}
      z-index="9999"
      opacity="thick"
      style={props.style}
    >
      <div className="flex h-screen w-screen flex-col items-center justify-center">
        <SpinLoading
          style={{ '--size': '32px' }}
          color={props.color || '#17c5a6'}
        />
        <div className="mt-4 text-white">{props.text || '请稍后...'}</div>
      </div>
    </Mask>
  );
};
export default Load;
