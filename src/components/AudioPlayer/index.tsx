"use client";

import clsx from "clsx";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

import styles from "@/styles/audioPlayer.module.css";

import { useAudioPlayerStore } from "@/store/useAudioPlayerStore";

export interface AudioPlayerRef {
  play(): void;
}
interface Props {
  musicUrl?: string;
}

const AudioPlayer = forwardRef<AudioPlayerRef, Props>(({ musicUrl }, ref) => {
  const isWechat = false;
  const audioRef: any = useRef(null);
  const [playing, setPlaying] = useState(false);
  const [paused, setPaused] = useState(false); // 手动暂停
  const { status, setStatus } = useAudioPlayerStore((state: any) => ({
    status: state.status,
    setStatus: state.setStatus,
  }));

  useImperativeHandle(ref, () => ({
    play() {
      if (!playing && !paused) {
        // 手动暂停不播放
        audioRef.current.play();
        setPlaying(!audioRef.current.paused);
      }
    },
  }));

  useEffect(() => {
    console.log("status", status);
    if (status === 1) {
      setPlaying(true);
      audioRef.current.play();
      setPaused(false);
    }

    if (status === 2) {
      setPlaying(false);
      audioRef.current.pause();
      setPaused(true);
    }
  }, [status]);

  function initPlayAudio() {
    if (musicUrl) {
      isWechat ? wxAutoPlayAudio() : otherAutoPlayAudio();
    }
  }

  function otherAutoPlayAudio() {
    if (!playing) {
      audioRef.current.play();
      setPlaying(!audioRef.current.paused);
    }
  }

  function wxAutoPlayAudio() {
    wx.ready(() => {
      setPlaying(true);
      document.getElementsByTagName("audio")[0].play();
    });
  }

  function toggle() {
    setPlaying(!playing);
    if (audioRef.current.paused) {
      audioRef.current.play();
      setPaused(false);
    } else {
      audioRef.current.pause();
      setPaused(true);
    }
  }

  return (
    <div
      className={clsx({
        [styles.music]: true,
        [styles.play]: playing,
      })}
    >
      <audio
        ref={audioRef}
        src={musicUrl}
        loop
        className={styles.audio}
      ></audio>
      <span className={styles.music_icon} onClick={toggle}></span>
    </div>
  );
});

AudioPlayer.displayName = "AudioPlayer";

export default AudioPlayer;