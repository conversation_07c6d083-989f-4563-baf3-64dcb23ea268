import { Cascader } from 'antd-mobile';
import * as React from 'react';
import { useImmer } from 'use-immer';

import api from '@/lib/api';

type areaItemApi = { name: string; areaCode: string };
type areaItem = { text: string; value: string; children: areaItem[] };

async function getArea(parentCode = '0') {
  return new Promise<[]>((resolve, reject) => {
    api
      .get('/v1/contact/areas', { params: { parentCode } })
      .then((response) => resolve(response.data))
      .catch((error) => reject(new Error(error)));
  });
}

type areaCode = [string, string, string];

type Props = {
  value: areaCode;
  classInput?: any;
  onChange: (val: areaCode, address?: areaItem[]) => void;
};

export default function AreaSelect({ onChange, value }: Props) {
  const [area, setArea] = useImmer<Array<areaItem>>([]);
  const onCascaderChange = async (val: any) => {
    if (val.length === 3) {
      return;
    }
    const province = val[0];
    const city = val[1];
    const provinceIndex = area.findIndex(
      (item: areaItem) => item.value === province
    );
    if (provinceIndex > -1) {
      const needRequestCity = area[provinceIndex]?.children.length === 0;
      if (needRequestCity) {
        const data: Array<areaItemApi> = await getArea(province);
        if (Array.isArray(data)) {
          const arr: Array<areaItem> = data.map((item) => ({
            label: item.name,
            value: item.areaCode,
            children: []
          }));
          setArea((draft) => {
            draft[provinceIndex].children = arr;
          });
        }
      }
      if (city) {
        const cityIndex: number | undefined = area[
          provinceIndex
        ]?.children.findIndex((item: areaItem) => item.value === city);
        if (cityIndex !== undefined && cityIndex > -1) {
          const needRequestArea =
            area[provinceIndex]?.children[cityIndex]?.children.length === 0;
          if (needRequestArea) {
            const data: Array<areaItemApi> = await getArea(city);
            if (Array.isArray(data)) {
              const arr: Array<areaItem> = data.map((item) => ({
                label: item.name,
                value: item.areaCode
              }));
              setArea((draft) => {
                draft[provinceIndex].children[cityIndex].children = arr;
              });
            }
          }
        }
      }
    }
  };

  const onConfirm = (val: Array<string | number>, extend: any) => {
    const data = [{}, {}, {}];
    extend.items.forEach((item: any, index: number) => {
      if (index < 3) {
        data[index] = {
          value: item.value,
          label: item.label
        };
      }
    });
    onChange(data);
  };

  return (
    <Cascader
      title="请选择所在地区"
      options={area}
      onConfirm={onConfirm}
      onSelect={onCascaderChange}
    >
      {(items, actions) => {
        if (items.every((item) => item === null)) {
          return (
            <span
              className="text-stone-300"
              onClick={async () => {
                if (area.length === 0) {
                  const data: Array<areaItemApi> = await getArea();
                  if (Array.isArray(data)) {
                    const arr: Array<areaItem> = data.map((item) => ({
                      label: item.name,
                      value: item.areaCode,
                      children: []
                    }));
                    setArea(arr);
                  }
                }
                actions.open();
              }}
            >
              请选择省 / 市 / 区
            </span>
          );
        }
        return (
          <span onClick={() => actions.open()}>
            {items.map((item) => item?.label ?? '未选择').join('-')}
          </span>
        );
      }}
    </Cascader>
  );
}
