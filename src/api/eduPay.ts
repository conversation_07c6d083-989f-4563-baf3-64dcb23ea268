import api from '@/lib/api';

//获取学校教育缴费开通信息
export const getSchoolEduPayInfo = async (data: any) => {
  return api.get('/app/v1/finance/students/login', {
    params: data,
  });
};

// 获取学生待支付账单列表
export const getStudentUnPayList = async (studentId: string) => {
  return api.get(`/app/v1/finance/students/${studentId}/bills/pending`);
};

// 获取学生账单明细
export const getStudentUnPayDetail = async (
  studentId: string,
  billId: string,
) => {
  return api.get(`/app/v1/finance/students/${studentId}/bills/${billId}`);
};

// 获取学生收费记录列表
export const getStudentPayList = async (studentId: string) => {
  return api.get(`/app/v1/finance/students/${studentId}/charge-records`);
};

// 获取学生收费记录详情
export const getStudentPayDetail = async (
  studentId: string,
  recordId: string,
) => {
  return api.get(
    `/app/v1/finance/students/${studentId}/charge-records/${recordId}`,
  );
};

// 获取学生信息
export const getStudentInfo = async () => {
  return api.get('/app/v1/finance/students/info');
};
