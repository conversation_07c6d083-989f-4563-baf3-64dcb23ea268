import api from '@/lib/api';

// 创建
export const createForm = async (data: object) => {
  return api.post('/v1/affairs/customform', data);
};

// 更新
export const updateForm = async (data: object) => {
  return api.put('/v1/affairs/customform', data);
};

// 获取详情
export const getFormDetail = async (instanceId: string) => {
  return api.get(`/v1/affairs/customform/${instanceId}`);
};

// 发起申请
export const submit = async (data: object) => {
  return api.post('/v1/affairs/customform/submit', data);
};

// 修改申请
export const update = async (id: string, data: object) => {
  return api.put(`/v1/affairs/customform/submit/${id}`, data);
};

// 发起申请详情
export const getSubmitDetail = async (submitId: string) => {
  return api.get(`/v1/affairs/customform/submit/${submitId}`);
};

// 获取分组
export const getGroup = async () => {
  return api.get('/v1/affairs/customform/category');
};

// 获取模板详情
export const getTemplate = async (templateId: string) => {
  return api.get(`/v1/affairs/customform/template/${templateId}`);
};
