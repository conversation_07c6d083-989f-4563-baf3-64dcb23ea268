import { apiGet } from '@/lib/fetch';

export const getClockInDetailInfo = async (data: any) => {
  return await apiGet(`/v1/interaction/familyTask/clockin/share`, data);
};

export const getTaskDetailInfo = async (data: any) => {
  return await apiGet(`/v1/interaction/familyTask/info/share`, data);
};

export const getClockInList = async (data: any) => {
  return await apiGet(`/v1/interaction/familyTask/clockin/list/share`, data);
};

// 任务详情
export const getClockInDetail = async (data: any) => {
  return await apiGet(`v1/interaction/familyTask/info`, data);
};
