import api from '@/lib/api';

// 活动详情
export const getActivityInfo = async (data: object) => {
  return api.get('/app/v1/activitys-info', {
    params: data,
  });
};

// 活动排行榜
export const getActivityRank = async (data: object) => {
  return api.get('/app/v1/activitys-rank', {
    params: data,
  });
};

// 活动报名
export const activitySign = async (data: object) => {
  return api.post('/app/v1/activitys-sign', data);
};

// 活动中奖情况
export const activityPrize = async (data: object) => {
  return api.get('/app/v1/activitys-prize', {
    params: data,
  });
};

// 领取奖品或添加地址
export const activityPrizeAddress = async (data: object) => {
  return api.post('/app/v1/activitys-prize-address', data);
};
