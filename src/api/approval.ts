import api from '@/lib/api';

// 创建审批
export const createApproval = async (data: object) => {
  return api.post('/v1/affairs/approval/models', data);
};

// 更新审批
export const updateApproval = async (modelId: string, data: object) => {
  return api.put(`/v1/affairs/approval/models/${modelId}`, data);
};

// 获取审批详情
export const getApproval = async (modelId: string) => {
  return api.get(`/v1/affairs/approval/models/${modelId}`);
};

// 发起申请
export const submitApproval = async (data: object) => {
  return api.post('/v1/affairs/approval/instances', data);
};

// 发起申请详情
export const submitDetail = async (instanceId: string, data: any) => {
  return api.get(`/v1/affairs/approval/instances/${instanceId}`, {
    params: data,
  });
};

// 获取审批分组
export const getGroup = async () => {
  return api.get('/v1/affairs/approval/groups');
};

// 获取审批模板详情
export const getTemplate = async (templateId: string) => {
  return api.get(`/v1/affairs/approval/models/templates/${templateId}`);
};
