import api from '@/lib/api';

// 活动详情
export const activityInfo = async () => {
  return api.get('/app/v1/point/activitys-check');
};

// 往期记录
export const activityRecords = async (data: {
  page: number;
  perPage: number;
}) => {
  return api.get('/app/v1/point/activitys-record', {
    params: data,
  });
};

// 积分总数
export const totalFlowers = async () => {
  return api.get('/app/v1/point/points');
};

// 报名
export const finishTask = async (data: {
  taskId: string;
}) => {
  return api.post('/app/v1/point/tasks', data);
};

// 参与用户列表
export const joinUsersList = async () => {
  return api.get('/app/v1/point/activity/users');
};

// 订阅消息提醒
export const subscribeNotification = async (data: {
  isOpenNotify: 0 | 1;
}) => {
  return api.post('/app/v1/point/activitys-notify', data);
};
