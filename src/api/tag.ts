import api from '@/lib/api';

export const getTags = () => {
  return api.get('/app/v1/pbl/tags');
};

export const createTag = (data: { name: string }) => {
  return api.post('/app/v1/pbl/tags', data);
};

export const updateTag = (tagId: number, data: { name: string }) => {
  return api.put(`/app/v1/pbl/tags/${tagId}`, data);
};

export const deleteTag = (tagId: number) => {
  return api.delete(`/app/v1/pbl/tags/${tagId}`);
};
