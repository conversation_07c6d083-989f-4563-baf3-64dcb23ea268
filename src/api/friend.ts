import { apiGet, apiPost } from '@/lib/fetch';
export const getStudentInfo = async (data: any) => {
  console.log('data: ', data);
  return await apiGet(`/v1/contact/family_group/student/notoken`, data);
};
export const getUnActiveParent = async (data: any) => {
  return await apiGet(`/v1/contact/family_group/uninvited/notoken`, data);
};
export const addParent = async (data: any) => {
  return await apiPost(`/v1/contact/family_group/parent/notoken`, data);
};
// 获取学校配置
export const getSchoolInfo = async (data: any) => {
  return await apiGet(`/v1/contact/institution/setting/notoken`, data);
};
