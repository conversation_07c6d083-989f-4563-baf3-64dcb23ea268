import api from '@/lib/api';

export const getInfo = async (data: any) => {
  return api.get('/v1/contact/inst/existence', data);
};

export const createStudent = async (data: object) => {
  return api.post('/v1/contact/scan/student/join', data);
};

export const checkStudentInfo = async (data: unknown) => {
  return api.get('/v1/contact/scan/student/check', { params: data });
};

export const createTeacher = async (data: object) => {
  return api.post('/v1/contact/scan/teacher/join', data);
};

// 获取班级列表
export const getClassList = async (data: object) => {
  return api.get('/v1/contact/school/h5/gradeClass', {
    params: data,
  });
};

// 获取角色列表
export const getRoleList = async (data: object) => {
  return api.get('/v1/contact/institution/familyGroup/h5', {
    params: data,
  });
};

// 获取部门列表
export const getDepartmentList = async (data: object) => {
  return api.get('/v1/contact/departments/h5', {
    params: data,
  });
};

// 班级列表，带数据权限
export const getMyClassList = async () => {
  return api.get('/v1/contact/school/departments');
};
