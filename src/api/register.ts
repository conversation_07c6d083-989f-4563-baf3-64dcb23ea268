import api from '@/lib/api';
import { apiGet } from '@/lib/fetch';

export const getExistence = async (data: any) => {
  return apiGet('/v1/contact/inst/existence', data);
};
export const getEnterprises = async (data: any) => {
  return apiGet('/v1/contact/enterprises', data);
};
export const postInstitutions = async (data: any, config) => {
  return api.post('/v1/contact/institutions', data, config);
};
export const getSalesman = async () => {
  return api.get('https://crm.ancda.com/api/salesman', {});
};
