import api from '@/lib/api';

export const getProfileInfo = async (data: object) => {
  return api.get(`/v1/base/admissions/${data.instId}/profile`, data);
};
export const updateProfileInfo = async (data: object) => {
  return api.put(`/v1/base/admissions/${data.instId}/profile`, data);
};
export const pickupsListParent = async (data: object) => {
  return api.get('/v1/pickups/parent', {
    params: data,
  });
};
export const getRegistrationsList = async (data: object) => {
  return api.get(`/v1/base/admissions/${data.instId}/registrations`, {
    params: data,
  });
};
export const postRegistration = async (data: object) => {
  return api.post(`/v1/base/admissions/${data.instId}/registrations`, data);
};
export const getRegistrationDetail = async (data: object) => {
  return api.get(
    `/v1/base/admissions/${data.instId}/registrations/${data.registrationId}`,
    {
      params: data,
    },
  );
};
export const updateRegistrationDetail = async (data: object) => {
  return api.put(
    `/v1/base/admissions/${data.instId}/registrations/${data.registrationId}`,
    data,
  );
};
export const addFollowup = async (data: object) => {
  return api.post(
    `/v1/base/admissions/${data.instId}/registrations/${data.registrationId}/followups`,
    data,
  );
};
