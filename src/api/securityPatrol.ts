import { apiDel, apiGet, apiPost, apiPut } from '@/lib/fetch';

/**
 * ListPatrolPositionsRsp，巡视点列表响应
 */
export interface Response {
  /**
   * 巡视点列表
   */
  items?: PatrolPositionInfo[];
  /**
   * 总数量
   */
  total?: number;
  [property: string]: any;
}

/**
 * PatrolPositionInfo，巡视点信息结构体 (用于列表返回等)
 */
export interface PatrolPositionInfo {
  /**
   * 巡视点编号 (对应数据库 patrol_position.pos_code)
   */
  posCode?: string;
  /**
   * 巡视点ID (对应数据库 patrol_position.pos_id)
   */
  posId?: number;
  /**
   * 巡视点名称/位置 (对应数据库 patrol_position.pos_name)
   */
  posName?: string;
  /**
   * 二维码URL (对应数据库 patrol_position.qrcode_url)
   */
  qrcodeUrl?: string;
  /**
   * 状态 (对应数据库 patrol_position.status, 使用 PatrolPositionStatus 枚举)
   */
  status?: number;
  [property: string]: any;
}
// 巡视点列表
export const getPositions = async (data: any) => {
  return apiGet('/v1/safe/patrol/positions', data);
};

//  巡视点 添加
export const addPosition = async (data: {
  posCode?: string;
  /**
   * 巡视点名称
   */
  posName?: string;
  /**
   * 二维码URL
   */
  qrcodeUrl?: string;
}) => {
  return apiPost('/v1/safe/patrol/positions', data);
};

//  巡视点 编辑
export const editPosition = async (data: {
  /**
   * 新的巡视点编号 (可选)
   */
  posCode?: string;
  /**
   * 要修改的巡视点ID (将作为URL路径参数)
   */
  posId?: string;
  /**
   * 新的巡视点名称 (可选)
   */
  posName?: string;
  /**
   * 新的状态 (可选, 使用 PatrolPositionStatus 枚举: 1-启用 2-禁用)
   */
  status?: number;
  /**
   * 二维码URL
   */
  qrcodeUrl?: string;
}) => {
  return apiPut(`/v1/safe/patrol/positions/${data.posId}`, data);
};
// 巡视点 删除
export const deletePosition = async (data: { posId: string }) => {
  return apiDel(`/v1/safe/patrol/positions/${data.posId}`, data);
};

// 巡视记录列表
export const getPatrolRecords = async (data: {
  /**
   * 结束时间筛选 (可选, 格式如 "YYYY-MM-DD")
   */
  endTime?: string;
  /**
   * 页码 (可选, 从1开始)
   */
  pageNum?: number;
  /**
   * 每页数量 (可选, 默认由服务端配置)
   */
  pageSize?: number;
  posId?: number;
  posName?: string;
  /**
   * 巡视点编码
   */
  posCode?: string;
  /**
   * 排序 (可选, 使用 SortOrder 枚举: 1-正序，否则为倒序)。如果为 UNSPECIFIED，服务端可以默认为倒序。
   */
  sort?: number;
  /**
   * 开始时间筛选 (可选, 格式如 "YYYY-MM-DD")
   */
  startTime?: string;
  /**
   * 状态筛选 (可选, 使用 PatrolRecordStatus 枚举)
   */
  status?: number;
}) => {
  return apiGet('/v1/safe/patrol/records', data);
};
// 巡视记录添加
export const addPatrolRecord = async (data: {
  /**
   * 巡视说明 (可选)
   */
  description?: string;
  /**
   * 图片URL (可选)
   */
  photoUrl?: string;
  /**
   * 巡视点ID
   */
  posId?: number;
  /**
   * 状态 (必填, 使用 PatrolRecordStatus 枚举: 1-正常 2-异常)
   */
  status?: number;
}) => {
  return apiPost('/v1/safe/patrol/records', data);
};
// 扫描添加巡视记录
export const scanAddPatrolRecord = async (data: {
  instId?: string;
  operatorId?: string;
  operatorName?: string;
  /**
   * 巡视说明 (可选)
   */
  description?: string;
  /**
   * 图片URL (可选)
   */
  photoUrl?: string;
  /**
   * 巡视点ID
   */
  posId?: number;
  /**
   * 状态 (必填, 使用 PatrolRecordStatus 枚举: 1-正常 2-异常)
   */
  status?: number;
}) => {
  return apiPost('/v1/safe/patrol/records/qr', data);
};
// 巡查记录详情
export const getPatrolRecordDetail = async (data: { posId: string }) => {
  return apiGet(`/v1/safe/patrol/records/${data.posId}`, data);
};
// 巡查记录删除
export const deletePatrolRecord = async (data: { posId: string }) => {
  return apiDel(`/v1/safe/patrol/records/${data.posId}`, data);
};
// 巡查点二维码 导出
export const exportPatrolQRcode = async (data) => {
  return apiGet('/v1/safe/patrol/positions/exports/qrcodes', data);
};
// 来访登记
export const addVisitRecord = async (data: {
  /**
   * 居住地址
   */
  addr?: string;
  /**
   * 工作单位
   */
  employer?: string;
  /**
   * 性别（1-男，2-女）
   */
  gender?: number;
  /**
   * 来访人身份证号
   */
  idCard?: string;
  /**
   * 学校id
   */
  instId?: string;
  /**
   * 受访人姓名
   */
  interviewee?: string;
  /**
   * 来访事由
   */
  matter?: string;
  /**
   * 来访人unionId
   */
  unionId?: string;
  /**
   * 来访人电话
   */
  phone?: string;
  /**
   * 状态（1-正常，2-异常）
   */
  status?: number;
  /**
   * 体温
   */
  temperature?: string;
  /**
   * 来访人姓名
   */
  visitorName?: string;
  /**
   * 来访时间
   */
  visitTime?: number;
}) => {
  return apiPost('/v1/safe/visit/records/qr', data);
};
// 是否离园所
export const getVisitRecords = async (data: {
  instId?: string;
  unionId?: string;
}) => {
  return apiGet('/v1/safe/visit/records/is-leave', data);
};
