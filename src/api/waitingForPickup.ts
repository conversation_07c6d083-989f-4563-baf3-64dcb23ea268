import api from '@/lib/api';

// 创建代接送单
export const createPickups = async (data: object) => {
  return api.post('/v1/pickups', data);
};
export const pickupsListParent = async (data: object) => {
  return api.get('/v1/pickups/parent', {
    params: data,
  });
};
export const pickupsListTeacher = async (data: object) => {
  return api.get('/v1/pickups/depts', {
    params: data,
  });
};
// 确认代接送单
export const confirmPickups = async (data: object) => {
  return api.patch(`/v1/pickups/${data.proxyId}:confirm`, data);
};
