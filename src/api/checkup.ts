import api from '@/lib/api';

// 班级列表统计
export const getClassesStatistics = async (data: object) => {
  return api.get('/app/v1/checkBody/statistics', {
    params: data,
  });
};

// 单个班级统计
export const getClassStatistics = async (data: object) => {
  return api.get('/app/v1/checkBody/class', {
    params: data,
  });
};

// 学生统计
export const getStudentStatistics = async (data: object) => {
  return api.get('/app/v1/checkBody/students-detail', {
    params: data,
  });
};

// 月历
export const getStudentCalendar = async (data: object) => {
  return api.get('/app/v1/checkBody/students', {
    params: data,
  });
};
