import api from '@/lib/api';


// 活动报名-下单
export const activitySign = async (data: any) => {
  return api.post('/v1/base/order/activity-sign/order', data);
};  


// 活动报名- 状态获取
export const activityStatus = async () => {
  return api.get('/v1/base/order/activity-sign/status');
};

// 查询亲子任务id
export const summerTasks = async () => {
  return api.get('/app/v1/summer-activity/tasks');
};

// 查询亲子任务详情
export const getFamilyTaskInfo = async (taskId: string) => {
  return api.get('/v1/interaction/familyTask/info', { params: { taskId } });
};
