import { apiGet, apiPost } from '@/lib/fetch';

export const getInsurance = async (data: any = {}) => {
  console.log('data: ', data);
  return apiGet(`/v1/interaction/insurance/info`, data);
};
export const submitApply = async (data: any = {}) => {
  console.log('data: ', data);
  return apiPost(`/v1/interaction/insurance/apply`, data);
};
export const getInsuranceUrl = async (data: any = {}) => {
  console.log('data: ', data);
  return apiGet(`/v1/interaction/insurance/url`, data);
};
