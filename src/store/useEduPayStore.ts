import { create } from 'zustand';

interface State {
  instName: string;
  studentId: string;
  studentName: string;
  className: string;
  avatar: string;
  setInstName: (instName: string) => void;
  setStudentId: (studentId: string) => void;
  setStudentName: (studentName: string) => void;
  setClassName: (className: string) => void;
  setAvatar: (avatar: string) => void;
}

export const useEduPayStore = create<State>((set) => ({
  instName: '',
  studentId: '',
  studentName: '',
  className: '',
  avatar: '',
  setInstName: (instName: string) => set(() => ({ instName })),
  setStudentId: (studentId: string) => set(() => ({ studentId })),
  setStudentName: (studentName: string) => set(() => ({ studentName })),
  setClassName: (className: string) => set(() => ({ className })),
  setAvatar: (avatar: string) => set(() => ({ avatar })),
}));
