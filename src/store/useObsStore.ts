import { create } from 'zustand';

type Credentials = {
  ak: string;
  sk: string;
  securityToken: string;
  domain: string;
  bucket: string;
  bucketUrl: string;
  endpoint: string;
  expiresAt: string;
};

export interface State {
  credentials: Credentials;
  progress: number;
  fetch: () => Promise<void>;
  setProgress: (value: number) => void;
  getToken: () => Promise<Credentials>;
}

export const useObsStore = create<State>((set, get) => ({
  credentials: {
    ak: '',
    sk: '',
    securityToken: '',
    domain: '',
    bucket: '',
    bucketUrl: '',
    endpoint: '',
    expiresAt: '',
  },
  progress: 0,
  fetch: async () => {
    // const credentials: any = await getObsToken({ code: 'media' });
    const credentials = {
      ak: 'HBXFOYBA43X4KL6BPR6R',
      sk: '5ulJAro65PgCSclKGpMBqVVAxxVeaxketeOfwW7A',
      securityToken: '',
      domain: 'https://unicorn-media.ancda.com',
      bucket: 'unicorn-media',
      endpoint: 'obs.cn-north-4.myhuaweicloud.com',
      expiresAt: `${new Date().getTime() + 3600 * 1000}`,
      bucketUrl: '',
    };
    set({ credentials });
  },
  setProgress: (value) => set(() => ({ progress: value })),
  getToken: async () => {
    const { credentials } = get();
    const now = new Date().getTime();

    // Check if the token exists and is not expired
    if (
      credentials.securityToken &&
      new Date(credentials.expiresAt).getTime() > now
    ) {
      return credentials;
    }

    // Fetch a new token from the server
    await get().fetch();

    // Return the new token
    return get().credentials;
  },
}));
